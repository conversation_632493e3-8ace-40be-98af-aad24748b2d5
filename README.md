# 风险洞察

## 功能模块

- 企业尽调
  - 尽调入口 `/app/investigation/start`
  - 尽调历史 `/app/investigation/history`
  - 尽调结果 `/app/investigation/detail/:investigationId`
  - 模型设置
    - 模型列表 `/app/risk-model/list`
    - 模型设置 `/app/risk-model/detail/:riskModelId`
- 工商
  - 工商搜索 `/app/company/search`
  - 工商详情 `/app/company/detail/:companyId`
- 设置中心
  - 帐号设置 `/app/account/setting`
  - 组织信息 `/app/account/organization`
- 切换组织 `/app/switch`
- 企业中心(外链) `/e`
- ~~风险监控~~

## 对外嵌入页面

- 企业尽调
  - 尽调结果 `/app/external/investigation/detail/:keyNo?diligenceId=:diligenceId` (示例：https://i.qcc.com/app/external/investigation/detail/f625a5b661058ba5082ca508f99ffe1b?diligenceId=********)
  - 尽调结果卡片 `/app/external/investigation/detail-card/:keyNo?diligenceId=:diligenceId` (示例：https://i.qcc.com/app/external/investigation/detail-card/f625a5b661058ba5082ca508f99ffe1b?diligenceId=********)
- 尽调模型
  - 模型列表 `/app/external/risk-model/list` (示例：https://i.qcc.com/app/external/risk-model/list)
  - 模型设置 `/app/external/risk-model/detail/:riskModelId` (示例：https://i.qcc.com/app/external/risk-model/detail/1062)

## 网址

- 开发环境 [i.dev.greatld.com](http://i.dev.greatld.com)
- 测试环境 [i.test.greatld.com](http://i.test.greatld.com)
- 预发环境 [ri-r.qcc.com](http://ri-r.qcc.com)
- 正式环境 [i.qcc.com](https://i.qcc.com)

## 文档

- [迭代任务](https://www.teambition.com/project/62fcd3af1d49858e939a7a3d/sprint/section/670f840b05abe5d51dd8d30e)
- [前端项目](http://gitlab.greatld.com:18888/kezhaozhao/rover/frontend-projects/qcc-insights-web)
- [UI 设计](https://mastergo.com/file/138739737945532?page_id=588%3A100999&shareId=138739737945532&devMode=true)
- API 文档
  - [Insights API](http://i.dev.greatld.com/insights/swagger/)

## 项目简介

项目基于 vue-cli 创建，支持 vue、jsx、typescript 等语法，具有 HMR、生成代码片段、DDD 等特性，轻量、快捷。

### 开发前准备工作

修改本地 `/etc/hosts`, 使开发服务器转发后的接口保持同域（共享 cookie session）

```
127.0.0.1 local.greatld.com
```

**访问 [http://local.greatld.com:8080](http://local.greatld.com:8080)**

## CLI

| Command         | Description                              |
| --------------- | ---------------------------------------- |
| yarn install    | 安装依赖                                 |
| yarn dev        | 启动开发模式                             |
| yarn dev:ui     | 启动组件文档模式                         |
| yarn build      | 构建 (默认项目会在 CI/CD 过程中自动构建) |
| yarn g:co name  | 创建组件                                 |
| yarn g:cov name | 创建组件(.vue)                           |
| yarn g:p name   | 创建页面                                 |
| yarn g:pv name  | 创建页面(.vue)                           |
| yarn g:dv name  | 创建公共维度                             |
| yarn g:s name   | 创建 story                               |

## GIT 分支管理

- develop -- 开发分支
- release-\* -- 测试环境分支
- feature-\* -- 特性分支, 根据需求创建
- master -- 预发环境及生产环境分支

## 目录介绍

```
src
 ├── assets
 ├── bootstrap               -- 启动文件
 ├── components              -- 通用组件
 │   ├── global              -- 全局组件自动挂载
 │   ├── common              -- 项目中的通用组件
 │   ├── dimensions          -- 项目中的公共维度
 ├── config                  -- 公共配置
 ├── directives              -- 指令
 ├── entry                   -- 入口文件
 ├── interfaces              -- TS 类型接口
 ├── layouts                 -- 布局
 ├── mixins                  -- Mixin
 ├── pages                   -- 业务页面
 ├── router                  -- 路由
 ├── services                -- 请求
 ├── store                   -- 全局状态
 ├── styles                  -- 全局样式，包含全局变量声明
 ├── types                   -- TS 声明文件
 └── utils                   -- 工具函数
```
