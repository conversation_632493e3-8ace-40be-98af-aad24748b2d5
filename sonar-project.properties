sonar.projectKey=k<PERSON><PERSON><PERSON><PERSON>_rover_frontend-projects_qcc-insights-web_AZKnjpXf0kLYketfcL4r
sonar.projectName=qcc-insights-web

sonar.projectVersion=1.1
sonar.language=typescript
sonar.sources=src
sonar.sourceEncoding=UTF-8

# 排除无需扫描的代码: locales, services, router, store, test-utils, bootstrap, trial.ts
sonar.exclusions= \
  **/*.jpg, \
  **/*.png, \
  src/**/*.js, \
  src/**/*.vue, \
  src/components/dimensions/**/*, \
  src/shared/locales/**/*, \
  src/shared/services/**/*, \
  src/**/*.routes.ts, \
  src/router/**/*, \
  src/store/**/*, \
  src/test-utils/*, \
  src/bootstrap/*
sonar.test.inclusions= \
  src/**/*.spec.ts, \
  src/**/*.spec.tsx
sonar.coverage.exclusions= \
  src/**/*.spec.ts, \
  src/**/*.spec.tsx, \
  src/**/*.mock.ts, \
  build/**/*, \
  public/**/*, \
  node_modules/*, \
  test-report/*


sonar.javascript.file.suffixes=.js,.jsx,.vue
sonar.typescript.file.suffixes=.ts,.tsx
sonar.javascript.lcov.reportPaths=test-report/coverage/lcov.info
sonar.typescript.lcov.reportPaths=test-report/coverage/lcov.info

sonar.host.url=http://**************:9000
sonar.testExecutionReportPaths=test-report/coverage/sonar-reporter.xml
sonar.token=sqp_8616a5c23b5ddf0ea6f8820806ad1a0c6ba363bc
