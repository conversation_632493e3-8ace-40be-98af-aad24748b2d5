export type SelectOption = { label: string; value: any };
export type CascaderOption = { label: string; value: any; children?: CascaderOption[] };
export type AreaValue = {
  pr?: string;
  ct?: string;
  dt?: string;
};
export type NumberRangeSource = { max?: number; min?: number };
export type NumberRangeValue = Array<number | undefined>;

export type DateRangeSource = {
  flag: number;
  max?: string;
  min?: string;
  currently: boolean;
  number: number;
  unit: string;
};

export type DateRangeValue = Array<moment.Moment | undefined>;

export type Label = {
  field: string;
  text: string;
  label?: string;
};

export interface Relation {
  block: number[][];
  data: Array<{
    graph: {
      nodes: Array<{
        id: string;
        labels: string[];
        properties: {
          keyNo: string;
          name: string;
        };
      }>;
      relationships: Array<{
        startNode: string;
        endNode: string;
        id: string;
        properties: {
          inLineText: string;
        };
        type: string;
      }>;
    };
  }>;
}
export interface SearchSelectSource {
  value: string;
  type: 'input' | 'select';
}

export type AreaSource = string[];

export type Area = {
  pr?: string;
  ct?: string;
  dt?: string;
};

export interface StepCascaderSource {
  value: Array<string[]>;
  logic: 'and' | 'or';
}
