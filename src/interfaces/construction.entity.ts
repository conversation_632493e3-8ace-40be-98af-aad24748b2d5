export interface Person {
  Name: string;
  PerId: string;
  // Org: number;
}
export interface Company {
  KeyNo: string;
  Name: string;
  // Org: number;
}

export interface Tender {
  name: string;
  id: string;
}

export interface Achievement {
  projectid: string;
  projectname: string;
}

export interface Role {
  Role: string;
}

export type PersonEntity = {
  changearray?: any[];
  compkeywords: string[];
  createdate: number;
  credentialarray: Array<{
    CredentialNo: string;
    KeyNo: string;
    Name: string;
    Org: number;
    RegNo: string;
    RegProfession: string;
    RegType: string;
    RegTypeCode: string;
    ValidityDate: string;
  }>;
  credentialnum: number;
  datastatus: number;
  essynctime: number;
  favorites: number;
  highlight: Record<string, string[]>;
  id: string;
  idcard: string;
  idtype: string;
  keynocomp: Array<{ KeyNo: string; Name: string; Org: number }>;
  perid: string;
  pername: string;
  projectnum: number;
  regcomp: string;
  regno: string;
  regprofession: string;
  regtypecode: string[];
  regtypes: string[];
  relationship: { name: string };
  sex: number;
  updatedate: number;
  dynamicjson?: {
    TENDER: {
      groupId: string;
      id: string;
      publishDate: string;
      tenderId: string;
      title: string;
    };
  };
  wtbdynamicjson: {
    TENDER: {
      dataStatus: number;
      id: string;
      ifbUnit: string;
      ifbUnitContact: string;
      ifbUnitKeyno: string;
      industry: number;
      perName: string;
      performanceType: string;
      projectId: string;
      projectName: string;
      projectNo: string;
      projectUnion: string;
      province: string;
      provinceCode: string;
      publishDate: number;
      relatedReg: string;
      updateDate: number;
      wtbAmt: number;
      wtbInfoId: string;
      wtbUnit: string;
      wtbUnitKeyno: string;
    };
  };
  wtblatestdate: number;
  wtbperformancenum: number;
  manualUpdateTime?: number;
};

export type CompanyEntity = {
  address: string;
  ambiguity: string;
  areacode: string[];
  creditcode: string;
  datastatus: string;
  econkind: string;
  econkindcode: string[];
  englishname: string;
  essynctime: number;
  favorites: number;
  hasimage: number;
  highlight: Record<string, string[]>;
  id: string;
  name: string;
  operinfo: { k: string; o: number; t: number };
  opername: string;
  originalname: string[];
  product: string;
  projectnum: number;
  awardsnum: number;
  securenum: number;
  powerqualificationnum: number;
  province: string;
  provincedesc: string;
  qualificationnum: number;
  projectTotal: number;
  rareacode: string[];
  registcapi: string;
  registcapiamount: number;
  regno: string;
  regpernum: number;
  relationship: { name: string };
  rprovincecode?: string[];
  scope: string;
  searchkeyword: string;
  shortname: string;
  sname: string;
  startdatecode: number;
  startdateyear: number;
  status: string;
  statuscode: number;
  stockinfo: string;
  tagsinfo: Array<{
    d: string;
    n: string;
    s: string;
    t: number;
  }>;
  tellist: Array<{ t: string; s: string }> | null;
  type: number;
  weight: number;
  wtbdynamicjson: {
    TENDER: {
      dataStatus: number;
      id: string;
      ifbUnit: string;
      ifbUnitContact: string;
      ifbUnitKeyno: string;
      industry: number;
      perName: string;
      performanceType: string;
      projectId: string;
      projectName: string;
      projectNo: string;
      projectUnion: string;
      province: string;
      provinceCode: string;
      publishDate: number;
      relatedReg: string;
      updateDate: number;
      wtbAmt: number;
      wtbInfoId: string;
      wtbUnit: string;
      wtbUnitKeyno: string;
    };
  };
  location?: string;
  manualUpdateTime?: number;
  wtblatestdate: number;
  wtbperformancenum: number;
  // 临时使用
  tenderTelCount: number;
  importcustomernum: number;
};
export type CompanyQualification = {
  id: string;
  datastatus: number;
  compname: string;
  keynocomp: string;
  qualificationtype: number;
  qualificationno: string;
  qualificationname: string;
  qualificationcode: string;
  createdate: number;
  updatedate: number;
  essynctime: number;
  startdate: string;
  enddate: string;
  qualificationscope: string;
  issuingauthority: string;
  certificatestatus: string | null;
  keynoauthority: string;
};

export interface AchievementEntity {
  areacode: string;
  compkeywords: string[];
  completionnum: number;
  compnames: string[];
  compnamesperformance: string[];
  constructnature: number;
  constructunit: string;
  contractnum: number;
  datastatus: number;
  drawexamnum: number;
  id: string;
  itemlevel: number;
  itemno: string;
  keynocomparray: string;
  keynocomparrayperf: Array<Company & Role>;
  keynounitarray: Company[];
  peridarray: string;
  peridarrayperf: Array<Person & Role>;
  perkeywords: string;
  permitnum: number;
  pernames: string;
  pernamesperformance: string;
  projectarea: string;
  projectid: string;
  projectname: string;
  projectpurposes: string;
  projecttype: string;
  provincecode: string;
  tendernum: number;
  participantsnum: number;
  totalinvest: number;
  highlight: Record<string, string[]>;
  favorites?: number;
  datalevel: string;
}

export type ConstructionTender = {
  createdate: number;
  datastatus: number;
  essynctime: number;
  id: string;
  ifbunit: Array<string>;
  ifbunitcontact: Array<{ contact: string; tel: string }>;
  ifbunitkeyno: string;
  industry: string;
  industryCode: number;
  performancetype: Array<string>;
  pername: string;
  perid: string;
  projectid: string;
  projectname: string;
  projectno: string;
  projectunion: Array<string>;
  province: string;
  provincecode: string;
  publishdate: string;
  relatedreg: string;
  tendertype: null | number;
  updatedate: number;
  wtbamt: null | number;
  wtbinfoid: Array<string>;
  wtbunit: string;
  wtbunitkeyno: string;
  wtbtellist: Array<{ t: string; s: string }>;
  ownercontactnum: number;
};
export type AwardEntity = {
  pername: string[];
  awardingdate: number;
  highlight?: {
    awardsname: string;
    projectname: string;
  };
  id: string;
  createdate: number;
  updatedate: number;
  essynctime: number;
  area: string;
  areacode: string;
  honortype: number;
  honorlevel: number;
  awardsname: string;
  awardstype: string;
  awardslevel: string;
  compname: string;
  comprole: string;
  keynocomp: string;
  projectname: string;
  competentunit: string;
  keynocompetent: string;
  awardingunit: string;
  keynoaward: string;
  url: string;
  tag: string;
  company?: CompanyEntity;
};
