export interface IUserBundle {
  entSaasServiceId: string;
  serviceCode: 'SAAS_KZZ' | 'SAAS_CZB';
  serviceName: string;
  serviceCategory: string;
  startDate: string;
  endDate: string;
  userId: string;
  role: number;
  status: number;
}

export interface IOrganizationSetting {
  validateBusinessName: boolean;
  enableLeadsManagement: boolean;
  validateCustomerPhone: boolean;
}

export interface IUserProfile {
  id: string;
  username: string;
  organizationId: number;
  guid: string;
  parentGuid: string;
  userType: number;
  accessGroupId: number;
  userId: number;
  isSVIP: boolean;
  isVIP: boolean;
  isLogin: boolean;
  source: string;
  level: number;
  nickname: string;
  phonePrefix: string;
  phone: string;
  faceimg: string;
  groupid: number;
  sex: number;
  email: string;
  registTime: string;
  vipstartdate: string;
  vipenddate: string;
  bundle: IUserBundle;
  teams: string[];
  guest: 1 | 0;
  expired: 1 | 0;
  loginUserId: number;
  currentOrg: number;
  createDate: string;
  roleScope: number;
  permissions: string[];
  departments: string[];
  orgSettings: IOrganizationSetting;
}

export interface IProToken {
  key: string;
  timespan: number;
  token: string;
}
