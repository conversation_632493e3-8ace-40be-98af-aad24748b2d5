type GroupItem = {
  count: number;
  desc?: string;
  value: string | number;
  Count: number;
  Desc?: string;
  Value: string | number;
};

export type GroupItemMap = Record<string, GroupItem[]>;
export type GroupItemArray = Array<{
  key?: string;
  Key?: string;
  items?: GroupItem[];
  Items?: GroupItem[];
}>;

export type GroupItems = GroupItemMap | GroupItemArray;
export interface ListResponse<T> {
  Paging: {
    PageIndex: number;
    PageSize: number;
    TotalRecords: number;
  };
  Result: T[];
  GroupItems: GroupItems;
}

export interface DetailResponse<T> {
  result: T;
  status: 200 | 404;
}
