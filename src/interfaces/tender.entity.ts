/* eslint-disable camelcase */

type TenderTel = {
  CompanyKeyNo: string;
  CompanyName: string;
  Contact: string;
  Id: string;
  TelNo: string;
  Type: number;
};

type Company = {
  KeyNo: string;
  Name: string;
};

export type TenderGroup = {
  ifbprogress: string[];
  publishdate: number;
  id: string;
  title: string;
};

type WtbAmt = {
  Amt: string;
  OriginalAmt: string;
  PackageName: string;
  PackageNumber: string;
};

export type WtbRecord = WtbAmt & {
  WtbComps: Company[];
};

export type TenderWtbAmt = Company & WtbAmt;

export type TenderEntity = {
  ifbprogress: string[];
  city: string[];
  ownertype: string[];
  teltypes: string[];
  id: string;
  title: string;
  content: string;
  publishdate: number;
  publishyear: number;
  province: string;
  projectno: string;
  industry: string;
  industryv2: string;
  ifbunit?: string;
  wtbunit?: string;
  agent: string;
  tel: string;
  channelname: string;
  teljson: TenderTel[];
  budget: string;
  budgetvalue: number;
  wtbamtinfo: TenderWtbAmt[];
  opendate: number;
  ifbinfo: Company[];
  wtbinfo: Company[];
  agentinfo: Company[];
  proposedinfo: [];
  relatecompany: Company[];
  wtbcandidate: null;
  wtbcandidateinfo: [];
  openbidrecord: null;
  openbidrecordinfo: '[]';
  originalurl: string;
  constructunit?: string;
  hasattachment: 0 | 1;
  groupid: string;
  projectinvest: string;
  projectinvestvalue: null;
  constructaddress: null;
  projecttelinfo?: { project_contact: string; project_phone: string };
  comptelinfo: [];
  createdate: 1603871582;
  groups: TenderGroup[];
  buyfileenddate?: number;
  startdate?: string;
  enddate?: string;
  highlight?: Record<string, string[]>;
  wtbrecord?: WtbRecord[];
  _detail_query?: {
    keyword: string;
    type: string;
  };
};

export type TenderAttachMent = {
  FormatUrl: string;
  Name: string;
  OssFileKey: string;
  Type: string;
  Url: string;
};

export type TenderDetailEntity = TenderEntity & {
  contentData: {
    contentUrl: string;
    dataType: 'html';
    content: string;
    attachments?: TenderAttachMent[];
  };
};
