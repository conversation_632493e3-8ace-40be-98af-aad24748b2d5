export type HitReason = {
  Field: string;
  Value: string;
};
export type Operinfo = {
  h: boolean;
  k: string;
  o: number;
  t: number;
};
export type Tag = {
  d: string;
  n: string;
  s: string;
  t: number;
};
export type Tel = {
  s: string;
  t: string;
};
export type Email = {
  s: string;
  e: string;
};

export interface ICompany {
  hasimage: 0 | 1;
  highlight: Record<string, []>;
  hitReasons: HitReason[];
  id: string;
  name: string;
  operinfo: Operinfo;
  opername: string;
  registcapi: string;
  startdatecode: number;
  tagsinfo: Tag[];
  tellist: Tel[];
  emaillist: Email[];
  address: string;
  creditcode: string;
  gw2?: string;
  status?: string;
  statuscode?: string;
  commonlist: any;
  isHide: boolean;
}
export interface ITransfromedCompany extends ICompany {
  hitReason: HitReason;
}

export interface TagsInfo {
  t: number;
  n: string;
  s: string;
  d: string;
  d2?: string;
}

export interface TagsInfoV2 {
  DataExtend: string;
  DataExtend2?: string;
  Name: string;
  ShortName: string;
  Type: number;
}

interface Area {
  Province: string;
  ProvinceCode: string;
  CityCode: string;
  City: string;
  CountyCode: string;
  County: string;
}

interface EmailList {
  e: string;
  s: string;
}
export interface ICompanyQcc {
  HitReason: HitReason;
  HitReasons: HitReason[];
  KeyNo: string;
  Name: string;
  Type: number;
  No: string;
  CreditCode: string;
  OperName: string;
  Status: string;
  StartDate: number;
  Address: string;
  RegistCapi: string;
  ContactNumber: string;
  Email: string;
  ImageUrl: string;
  TelList: string;
  OperInfo: string;
  TagsInfo?: TagsInfo[];
  TagsInfoV2?: TagsInfoV2[];
  Flag: string;
  Tag: string;
  EmailList: EmailList[];
  IsHide: boolean;
  AreaCode: string;
  QccCode: string;
  GW: string;
  CountInfo: Record<string, any>[];
  Industry: Record<string, string>;
  EconKind: string;
  QccIndustry: Record<string, string>;
  ShortStatus: string;
  Introduction: string;
  Scale: string;
  TermStart: number;
  TermEnd: number;
  InsuredYear?: string;
  Area: Area;
  X: number;
  Y: number;
}

export interface CompanyLogo {
  Color: string;
  HasImage: 0 | 1;
  ImageUrl: string;
  KeyNo: string;
  ShortName: string;
}
