import dateformat from './dateformat';
import dateago from './dateago';
import truncate from './truncate';
import limitText from './limit-text';
import numberformat from './numberformat';
import { numOriginFormat } from './amount';

export default (Vue) => {
  Vue.filter('dateformat', dateformat);
  Vue.filter('dateago', dateago);
  Vue.filter('limitext', limitText);
  Vue.filter('truncate', truncate);
  Vue.filter('numberformat', numberformat);
  Vue.filter('amountTrs', numOriginFormat);
};
