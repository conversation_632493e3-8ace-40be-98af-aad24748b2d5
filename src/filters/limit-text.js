// html剔除富文本标签，留下纯文本
const getSimpleText = (html) => {
  const re1 = new RegExp('<.+?>', 'mg');
  return html.replace(re1, '');
};

export default function (text, limitLength = 50) {
  // text可能为富文本 需要解析成纯文本
  const plainText = getSimpleText(text);
  if (plainText.length <= limitLength) {
    return text;
  }
  const searchvalue = plainText[limitLength]; // 截止位置的字符
  const pos = text.indexOf(searchvalue, limitLength); // 从截止的位置数 匹配到搜索的值所在的位置
  if (pos >= limitLength) {
    return `${text.slice(0, pos)}...`;
  }
  return text;
}
