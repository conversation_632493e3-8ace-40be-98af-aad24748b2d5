/**
 * Created by <PERSON> on - 2019/11/26.
 */

import moment from 'moment';
import _ from 'lodash';

export default function (dataStr, pattern = 'YYYY-MM-DD', defaultVal = '-', x1000) {
  // YYYY-MM-DD HH:mm:ss' x1000 的话 固定是秒级时间戳
  let tmp = `${dataStr}`; // 时间戳转为字符串
  if (pattern === false) {
    return dataStr;
  }
  if (pattern === true) {
    pattern = 'YYYY-MM-DD';
  } else if (pattern === 'second') {
    // 秒级要转成毫秒
    x1000 = true;
    pattern = 'YYYY-MM-DD';
  } else if (pattern === 'dp' && tmp.length === 8) {
    // 参数时间格式
    pattern = 'YYYY-MM-DD';
    return moment(dataStr, 'YYYYMMDD').format(pattern);
  }
  if (_.isString(tmp)) {
    tmp = _.trim(tmp); // 去除左右的空格
  }

  if (tmp === '0') {
    return defaultVal; // 数据兼容
  }
  if (tmp.length <= 10 || x1000) {
    // 不是毫秒 秒转为毫秒
    tmp += '000';
  }
  if (tmp) {
    tmp = +tmp; // 字符串回转为时间戳
    if (tmp) {
      return moment(+tmp).format(pattern);
    }
  }
  return defaultVal;
}
