const amount = function (str) {
  if (str === undefined || str === '') {
    return '-';
  }
  return str;
};
const numOriginFormat = function (num) {
  if (num === undefined || num === '') {
    return '-';
  }
  if (num === 0) return 0;
  const res = `${num}`.replace(/\d+/, function (n) {
    // 先提取整数部分
    return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
      return `${$1},`;
    });
  });
  return res;
};
export { numOriginFormat, amount };
