/**
 * Created by <PERSON> on - 2019/11/26.
 */

import moment from 'moment';

export default function (datetime, pattern = 0, defaultVal = '-') {
  if (+datetime === 0 || !datetime) return defaultVal;
  const PublishTime = datetime;
  const now = Math.round(new Date().getTime() / 1000).toString();
  const delta = parseInt(now - PublishTime, 10);
  if (delta <= 0) {
    return '刚刚';
  }
  if (delta < 60) {
    return `${delta}秒前`;
  }
  if (delta < 3600) {
    return `${(delta - (delta % 60)) / 60}分钟前`;
  }
  if (delta < 86400) {
    if (pattern === 2) {
      return `${(delta - (delta % 3600)) / 3600}小时${Math.parseInt((delta % 3600) / 60)}分钟前`;
    }
    return `${(delta - (delta % 3600)) / 3600}小时前`;
  }
  const newPublishTime = Number(`${PublishTime}000`);
  return moment(newPublishTime).format('YYYY-MM-DD');
}
