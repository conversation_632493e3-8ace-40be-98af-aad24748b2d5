/* 输入类型：0 文本框 1 下拉框单选 2 下拉多选 3 单选框 4 复选框 */
export enum DimensionFieldInputTypeEnum {
  /** 文本框 */
  Text = 0,
  /** 下拉框单选 */
  Select = 1,
  /** 下拉多选 */
  MultiSelect = 2,
  /** 单选框 */
  Radio = 3,
  /** 复选框 */
  Checkbox = 4,
}

export const DimensionFieldInputTypeEnumMap = {
  [DimensionFieldInputTypeEnum.Text]: '文本框',
  [DimensionFieldInputTypeEnum.Select]: '下拉框单选',
  [DimensionFieldInputTypeEnum.MultiSelect]: '下拉多选',
  [DimensionFieldInputTypeEnum.Radio]: '单选框',
  [DimensionFieldInputTypeEnum.Checkbox]: '复选框',
};

export const DimensionFieldInputTypeEnumOptions = [
  {
    label: DimensionFieldInputTypeEnumMap[DimensionFieldInputTypeEnum.Text],
    value: DimensionFieldInputTypeEnum.Text,
  },
  {
    label: DimensionFieldInputTypeEnumMap[DimensionFieldInputTypeEnum.Select],
    value: DimensionFieldInputTypeEnum.Select,
  },
  {
    label: DimensionFieldInputTypeEnumMap[DimensionFieldInputTypeEnum.MultiSelect],
    value: DimensionFieldInputTypeEnum.MultiSelect,
  },
  {
    label: DimensionFieldInputTypeEnumMap[DimensionFieldInputTypeEnum.Radio],
    value: DimensionFieldInputTypeEnum.Radio,
  },
  {
    label: DimensionFieldInputTypeEnumMap[DimensionFieldInputTypeEnum.Checkbox],
    value: DimensionFieldInputTypeEnum.Checkbox,
  },
];
