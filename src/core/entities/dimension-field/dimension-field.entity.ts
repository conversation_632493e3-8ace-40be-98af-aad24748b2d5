import type { DataStatusEnum } from '../enums/data-status.enum';
import type { DimensionFieldCompareTypeEnum } from './dimension-field-compare-type.enum';
import type { DimensionFieldInputTypeEnum } from './dimension-field-input-type.enum';
import type { DimensionFieldKeyEnum } from './dimension-field-key.enum';
import type { DimensionFieldTypeEnum } from './dimension-field-type.enum';

export interface DimensionFieldEntity {
  fieldId: number;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'1'",
  //   name: 'input_type',
  // })
  // @ApiPropertyOptional({
  //   description: '输入类型：0 文本框 1 下拉框单选 2 下拉多选 3 单选框 4 复选框',
  //   enum: Object.values(DimensionFieldInputTypeEnums),
  // })
  // @IsIn(Object.values(DimensionFieldInputTypeEnums))
  inputType: DimensionFieldInputTypeEnum;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'comment',
  // })
  // @ApiPropertyOptional({ description: '字段描述' })
  // @IsOptional()
  // @MinLength(0)
  // @MaxLength(200)
  comment?: string;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'field_key',
  // })
  // @ApiProperty({
  //   description: '字段key',
  //   enum: getUniqueFieldKeys(),
  // })
  // @IsString()
  // @IsIn(getUniqueFieldKeys())
  fieldKey: DimensionFieldKeyEnum;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'data_type',
  // })
  // @ApiProperty({ description: '数据类型', enum: Object.values(DimensionFieldTypeEnums) })
  // @IsIn(Object.values(DimensionFieldTypeEnums))
  dataType: DimensionFieldTypeEnum;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'0'",
  //   name: 'is_array',
  // })
  // @ApiProperty({ description: '是否是数组', enum: [0, 1] })
  // @IsIn([0, 1])
  isArray: number;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'0'",
  //   name: 'field_order',
  // })
  // @ApiPropertyOptional({ description: '排序' })
  // @IsOptional()
  // @IsNumber()
  fieldOrder?: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'dimension_id',
  // })
  // @ApiProperty({ description: '维度ID' })
  dimensionId: number;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecated_date',
  // })
  // @ApiPropertyOptional({ description: '废弃日期' })
  // @IsOptional()
  deprecatedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecate_start_date',
  // })
  // @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  // @IsOptional()
  deprecateStartDate?: string | null;

  // @Column('json', {
  //   nullable: true,
  //   name: 'options',
  // })
  // @ApiPropertyOptional({ description: '如果该字段是选择框，这里可以配置选项' })
  // @IsOptional()
  // @IsArray()
  // @ArrayMinSize(1)
  // @ArrayMaxSize(20)
  options?: any[] | null;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'name',
  // })
  // @ApiProperty({ description: '字段名称' })
  fieldName: string;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  // @ApiProperty({ description: '创建时间' })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  // @ApiProperty({ description: '更新时间' })
  updateDate: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'modified_date',
  // })
  // @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  // @IsOptional()
  modifiedDate?: string | null;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'1'",
  //   name: 'status',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: Object.values(DataStatusEnums),
  // })
  // @IsIn(Object.values(DataStatusEnums))
  status: DataStatusEnum;

  defaultFieldValue?: any[];

  defaultCompareType?: DimensionFieldCompareTypeEnum;

  // @ManyToOne(() => DimensionDefinitionEntity, (dimensionDef) => dimensionDef.dimensionFields)
  // @JoinColumn({ name: 'dimension_id' })
  // dimensionDef: DimensionDefinitionEntity;

  // @OneToMany(() => DimensionHitStrategyFieldsEntity, (strategyField) => strategyField.dimensionField)
  // strategyFieldEntity: DimensionHitStrategyEntity[];
}
