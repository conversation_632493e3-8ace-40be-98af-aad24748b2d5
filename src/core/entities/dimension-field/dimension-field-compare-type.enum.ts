export enum DimensionFieldCompareTypeEnum {
  Equal = 'Equal',
  NotEqual = 'NotEqual',
  GreaterThan = 'GreaterThan',
  LessThan = 'LessThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
  LessThanOrEqual = 'LessThanOrEqual',
  Between = 'Between',
  ContainsAny = 'ContainsAny',
  ContainsAll = 'ContainsAll',
  ExceptAny = 'ExceptAny',
  ExceptAll = 'ExceptAll',
}

export const DimensionFieldCompareTypeEnumMap = {
  [DimensionFieldCompareTypeEnum.Equal]: '等于',
  [DimensionFieldCompareTypeEnum.NotEqual]: '不等于',
  [DimensionFieldCompareTypeEnum.GreaterThan]: '大于',
  [DimensionFieldCompareTypeEnum.LessThan]: '小于',
  [DimensionFieldCompareTypeEnum.GreaterThanOrEqual]: '大于等于',
  [DimensionFieldCompareTypeEnum.LessThanOrEqual]: '小于等于',
  [DimensionFieldCompareTypeEnum.Between]: '介于',
  [DimensionFieldCompareTypeEnum.ContainsAny]: '包含任意',
  [DimensionFieldCompareTypeEnum.ContainsAll]: '包含全部',
  [DimensionFieldCompareTypeEnum.ExceptAny]: '排除任意',
  [DimensionFieldCompareTypeEnum.ExceptAll]: '排除全部',
};

export const DimensionFieldCompareTypeEnumOptions = [
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.Equal],
    value: DimensionFieldCompareTypeEnum.Equal,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.NotEqual],
    value: DimensionFieldCompareTypeEnum.NotEqual,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.GreaterThan],
    value: DimensionFieldCompareTypeEnum.GreaterThan,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.LessThan],
    value: DimensionFieldCompareTypeEnum.LessThan,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.GreaterThanOrEqual],
    value: DimensionFieldCompareTypeEnum.GreaterThanOrEqual,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.LessThanOrEqual],
    value: DimensionFieldCompareTypeEnum.LessThanOrEqual,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.Between],
    value: DimensionFieldCompareTypeEnum.Between,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.ContainsAny],
    value: DimensionFieldCompareTypeEnum.ContainsAny,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.ContainsAll],
    value: DimensionFieldCompareTypeEnum.ContainsAll,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.ExceptAny],
    value: DimensionFieldCompareTypeEnum.ExceptAny,
  },
  {
    label: DimensionFieldCompareTypeEnumMap[DimensionFieldCompareTypeEnum.ExceptAll],
    value: DimensionFieldCompareTypeEnum.ExceptAll,
  },
];
