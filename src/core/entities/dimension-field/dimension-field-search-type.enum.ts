export enum DimensionFieldSearchTypeEnum {
  General = 0,
  Relation1 = 1,
  Relation2 = 2,
}

export const DimensionFieldSearchTypeEnumMap = {
  [DimensionFieldSearchTypeEnum.General]: '常规查询',
  [DimensionFieldSearchTypeEnum.Relation1]: '关联关系查询',
  [DimensionFieldSearchTypeEnum.Relation2]: '关联关系查询2',
};

export const DimensionFieldSearchTypeEnumOptions = [
  {
    label: DimensionFieldSearchTypeEnumMap[DimensionFieldSearchTypeEnum.General],
    value: DimensionFieldSearchTypeEnum.General,
  },
  {
    label: DimensionFieldSearchTypeEnumMap[DimensionFieldSearchTypeEnum.Relation1],
    value: DimensionFieldSearchTypeEnum.Relation1,
  },
  {
    label: DimensionFieldSearchTypeEnumMap[DimensionFieldSearchTypeEnum.Relation2],
    value: DimensionFieldSearchTypeEnum.Relation2,
  },
];
