export enum DimensionFieldKeyEnum {
  /** 与处罚主体关系类型   */
  relationShips = 'relationShips',
  /** 风险动态类型   */
  riskCategories = 'riskCategories',
  /** 命中记录条数   */
  hitCount = 'hitCount',
  /** 1:当前有效，0:历史, -1:不限 
   * - 0-历史
    - 1-有效
    - 2-逻辑删除
    - 3-重复
    - 4-过滤
    -  5-爬虫异常数据
    - 10-临时屏蔽
    - 6-待审核
    - 11-网信办屏蔽
    - 12-人行屏蔽
    - 13-客户屏蔽
    - 14-网信办屏蔽
    - 91-网信办屏蔽历史
    - 92-人行屏蔽历史
    - 93-客户屏蔽历史
    - 94-法院屏蔽历史

    0-历史
    1-有效
    10-临时屏蔽
    11-网信办屏蔽
    13-客户屏蔽
    91-网信办屏蔽历史
    93-客户屏蔽历史 
    */
  isValid = 'isValid',
  /**  统计周期 不可以传0
   * -1 表示不限
   * 当 compareType = GreaterThan  表示 近x年  发生时间 > 通过cycle取到的时间；
   * 当 compareType = LessThanOrEqual  表示 x年前  发生时间 ＜= 通过cycle取到的时间；
   *  */
  cycle = 'cycle',
  /** 维度列表排序  ['fieldName', 'desc']   */
  sortField = 'sortField',
  /** 股权数额   */
  equityAmount = 'equityAmount', //
  /** 处罚类型   */
  penaltiesType = 'penaltiesType', //
  /** 处罚事由类型   */
  punishReasonType = 'punishReasonType', //
  /** 处罚金额   */
  penaltiesAmount = 'penaltiesAmount', //
  /** 欠税金额   */
  taxArrearsAmount = 'taxArrearsAmount',
  /** 执行标的   */
  executionTarget = 'executionTarget', //
  /** 被执行总金额   */
  executionSum = 'executionSum', //
  /** 注册金额   */
  registrationAmount = 'registrationAmount', //
  /** 资本降幅   */
  capitalReduction = 'capitalReduction', //
  /** 变更阈值   */
  changeThreshold = 'changeThreshold', //
  // operator = 'operator', //运算符
  /** 成立时长 月   */
  duration = 'duration', //
  /** 涉案总金额   */
  amountInvolved = 'amountInvolved', //
  /** 作为被告方占比，如50   */
  percentAsDefendant = 'percentAsDefendant', //
  /** 经营异常类型   */
  businessAbnormalType = 'businessAbnormalType',
  /** 裁判文书身份类型（排除） prosecutor-原告 thirdpartyrole-第三人   */
  judgementRoleExclude = 'judgementRoleExclude',
  /** 裁判文书身份类型（包含） defendant-被告 prosecutor-原告 thirdpartyrole-第三人   */
  judgementRoleInclude = 'judgementRoleInclude',
  /** 投资任职关联类型 */
  types = 'types',
  /** 投资任职关联层级 */
  depth = 'depth',
  /** 持股/投资股权比例 */
  percentage = 'percentage',
  /** 节点排除类型 */
  excludedTypes = 'excludedTypes', // 节点排除类型
  // excludedNodes = 'excludedNodes', //疑似关系节点排除类型
  /** 筛选数据范围（黑名单，第三方）   */
  dataRange = 'dataRange',
  /** 新闻主体类型   */
  topics = 'topics',
  /** 担保金额   */
  guaranteeAmount = 'guaranteeAmount',
  /** 出质比例/总股本   */
  equityPledgeRatio = 'equityPledgeRatio',
  /** 土地抵押金额   */
  landMortgageAmount = 'landMortgageAmount',
  /** 动产抵押 被担保主债权数额   */
  chattelMortgageMainAmount = 'chattelMortgageMainAmount',
  /** 未履行总金额   */
  failure = 'failure',
  /** 案件总金额   */
  caseAmount = 'caseAmount',
  /** 担保风险金额   */
  guaranteedprincipal = 'guaranteedprincipal',
  /** 资质证书   */
  certification = 'certification',
  /** 临近到期时间   */
  nearExpirationType = 'nearExpirationType',
  /** 设置的数量值   */
  limitCount = 'limitCount',
  /** 资产负债率   */
  assetLiabilityRatio = 'assetLiabilityRatio',
  // 营业执照
  businessLicense = 'businessLicense',
  /** 关联对象   */
  associateObject = 'associateObject',
  /** 关联对象排除   */
  associateExclude = 'associateExclude',
  /** 人员分组   */
  personGroups = 'personGroups',
  /**  外部黑名单-出口管制合规风险企业清单  listcodes */
  sanctionslistcodes = 'sanctionslistcodes',
  /**  股权质押状态 */
  pledgeStatus = 'pledgeStatus',
  /** 欠缴金额 */
  AmountOwed = 'AmountOwed',
  /** 企风控风险指标项目 */
  QfkRiskItem = 'QfkRiskItem',
  /** 裁判文书案由类别 */
  CaseReasonType = 'CaseReasonType',
  /** 企业状态  吊销、注销、撤销、停业、歇业、责令关闭 */
  companyStatus = 'companyStatus',
  /** 企业成立日期 */
  CompanyStartDate = 'CompanyStartDate',
  /** 企业注册资本 */
  CompanyRegistrationAmount = 'CompanyRegistrationAmount',
  /** 企业实缴资本 */
  realRegistrationAmount = 'realRegistrationAmount',
  /** 企业性质 */
  companyEconType = 'companyEconType',
}
