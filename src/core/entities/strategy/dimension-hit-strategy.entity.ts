import type { DataStatusEnum } from '../enums/data-status.enum';
import type { DimensionDefinitionEntity } from '../dimension/dimension-definition.entity';
import type { DimensionHitStrategyFieldEntity } from './dimension-hit-strategy-field.entity';
import type { QueryHitStrategyEntity } from './query-hit-strategy.entity';

/**
 * 维度策略
 */
export interface DimensionHitStrategyEntity {
  strategyId: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'dimension_id',
  // })
  // @ApiProperty({ description: '维度ID' })
  dimensionId: number;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'strategy_name',
  // })
  // @ApiProperty({ description: '策略名称' })
  strategyName: string;

  // @ApiProperty({ description: '展示信息时候的模板' })
  // @Column('varchar', {
  //   nullable: false,
  //   length: 200,
  //   name: 'template',
  //   default: '匹配到目标主体 <em class="#level#">【#name#】</em>',
  // })
  template: string | null;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'comment',
  // })
  // @ApiPropertyOptional({ description: '备注' })
  // @IsOptional()
  comment?: string;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  // @ApiProperty({ description: '创建时间' })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  // @ApiProperty({ description: '更新时间' })
  updateDate: string | null;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'1'",
  //   name: 'status',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: Object.values(DataStatusEnums),
  // })
  // @IsIn(Object.values(DataStatusEnums))
  status: DataStatusEnum;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecated_date',
  // })
  // @ApiPropertyOptional({ description: '废弃日期' })
  // @IsOptional()
  deprecatedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecate_start_date',
  // })
  // @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  // @IsOptional()
  deprecateStartDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'modified_date',
  // })
  // @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  // @IsOptional()
  modifiedDate?: string | null;

  // @Column('int', {
  //   nullable: false,
  //   name: 'create_by',
  // })
  // @ApiProperty({ description: '创建者' })
  // @Type(() => Number)
  createBy: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'update_by',
  // })
  // @ApiProperty({ description: '更新者' })
  // @Type(() => Number)
  updateBy?: number | null;

  // @Column('json', {
  //   nullable: false,
  //   name: 'hit_strategy',
  // })
  // @ApiProperty({
  //   description: '命中规则,  维度多字段间的命中规则只能是must, 特殊维度单独开放',
  //   type: QueryHitStrategyPO,
  //   isArray: false,
  // })
  // @Type(() => QueryHitStrategyPO)
  // @IsArray()
  // @ArrayMinSize(1)
  // @ArrayMaxSize(10)
  // @ValidateNested()
  fieldHitStrategy: QueryHitStrategyEntity;

  // @ApiProperty({ description: '维度字段基本信息' })
  // @ManyToOne(() => DimensionDefinitionEntity, (dimensionFieldsEntity) => dimensionFieldsEntity.dimensionHitStrategy)
  // @JoinColumn({ name: 'dimension_id' })
  dimensionDef: DimensionDefinitionEntity;

  // @ApiProperty({ description: '命中条件组', type: DimensionHitStrategyFieldsEntity, isArray: true })
  // @OneToMany(() => DimensionHitStrategyFieldsEntity, (strategyField) => strategyField.dimensionHitStrategyEntity)
  strategyFields: DimensionHitStrategyFieldEntity[];

  // @OneToMany(() => MetricDimensionRelationEntity, (relation) => relation.dimensionHitStrategyEntity)
  // dimensionHitStrategies: MetricDimensionRelationEntity[];
}
