/* eslint-disable camelcase */
export interface QueryHitStrategyEntity {
  // @ApiPropertyOptional({ description: '维度命中策略的ID或者维度命中策略关联的field的ID', type: Number, isArray: true })
  // @IsOptional()
  // @IsArray()
  should?: number[]; // DimensionHitStrategyEntity.strategyId[]

  // @ApiPropertyOptional({ description: '维度命中策略的ID或者维度命中策略关联的field的ID', type: Number, isArray: true })
  // @IsOptional()
  // @IsArray()
  must?: number[]; // DimensionHitStrategyEntity.strategyId[]

  // @ApiPropertyOptional({ description: '维度命中策略的ID或者维度命中策略关联的field的ID', type: Number, isArray: true })
  // @IsOptional()
  // @IsArray()
  must_not?: number[]; // DimensionHitStrategyEntity.strategyId[]

  // @ApiPropertyOptional({ description: '如果使用了should，最少需要命中的个数', type: Number, isArray: true })
  // @IsNumber()
  // @Type(() => Number)
  // @Min(1)
  // @Max(100)
  minimum_should_match?: number;
}

export enum QueryHitStrategyTypeEnum {
  Must = 'must',
  Should = 'should',
  MustNot = 'must_not',
}

export const QueryHitStrategyTypeEnumMap = {
  [QueryHitStrategyTypeEnum.Must]: '所有',
  [QueryHitStrategyTypeEnum.Should]: '任一',
  [QueryHitStrategyTypeEnum.MustNot]: 'must_not',
};

export const QueryHitStrategyTypeEnumOptions = [
  {
    label: QueryHitStrategyTypeEnumMap[QueryHitStrategyTypeEnum.Must],
    value: QueryHitStrategyTypeEnum.Must,
  },
  {
    label: QueryHitStrategyTypeEnumMap[QueryHitStrategyTypeEnum.Should],
    value: QueryHitStrategyTypeEnum.Should,
  },
  {
    label: QueryHitStrategyTypeEnumMap[QueryHitStrategyTypeEnum.MustNot],
    value: QueryHitStrategyTypeEnum.MustNot,
  },
];
