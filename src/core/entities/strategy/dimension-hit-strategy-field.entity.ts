import type { DataStatusEnum } from '../enums/data-status.enum';
import type { DimensionFieldCompareTypeEnum } from '../dimension-field/dimension-field-compare-type.enum';
import type { DimensionFieldSearchTypeEnum } from '../dimension-field/dimension-field-search-type.enum';
import type { DimensionFieldEntity } from '../dimension-field/dimension-field.entity';
import type { DataCategoryEnum } from '../enums/data-category.enum';
import type { DimensionFieldKeyEnum } from '../dimension-field/dimension-field-key.enum';
import type { DataAccessScopeEnum } from '../enums/data-access-scope.enum';

export interface DimensionHitStrategyFieldEntity {
  id: number;

  // @Column({
  //   type: 'int',
  //   name: 'strategy_id',
  // })
  strategyId: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'org_id',
  // })
  // @ApiProperty({ description: '命中规则的值 归属的组织，如果是用户创建的，该字段不为空' })
  orgId?: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'dimension_id',
  // })
  // @ApiProperty({ description: '维度ID' })
  dimensionId: number;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'1'",
  //   name: 'category',
  // })
  // @ApiProperty({ description: '模型类别 1 系统模型， 2 用户模型' })
  category: DataCategoryEnum;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'extend_from',
  // })
  // @ApiPropertyOptional({ description: '完整的继承路径(如果有)' })
  extendFrom: string | null;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'comment',
  // })
  // @ApiPropertyOptional({ description: '备注' })
  // @IsOptional()
  comment?: string;

  // @Column('int', {
  //   nullable: false,
  //   name: 'dimension_field_id',
  // })
  // @ApiProperty({ description: '维度字段ID' })
  dimensionFieldId: number;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'dimension_field_key',
  // })
  // @ApiProperty({
  //   description: '字段key, 同一个风险维度下field_key不能重复',
  //   enum: getUniqueFieldKeys(),
  // })
  // @IsString()
  // @IsIn(getUniqueFieldKeys())
  dimensionFieldKey: DimensionFieldKeyEnum;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'dimension_field_name',
  // })
  // @ApiProperty({ description: '字段name' })
  // @IsString()
  dimensionFieldName: string;

  // @Column('int', {
  //   nullable: false,
  //   name: 'search_type',
  // })
  // @ApiProperty({
  //   description: '0 常规查询\n' + '1 xx 关联关系查询\n' + '2 yy 关联关系查询',
  //   type: DimensionFieldSearchTypeEnums,
  //   enum: Object.values(DimensionFieldSearchTypeEnums),
  // })
  // @IsIn(Object.values(DimensionFieldSearchTypeEnums))
  searchType: DimensionFieldSearchTypeEnum;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  // @ApiProperty({ description: '创建时间' })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  // @ApiProperty({ description: '更新时间' })
  updateDate: string | null;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'1'",
  //   name: 'status',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: Object.values(DataStatusEnums),
  // })
  // @IsIn(Object.values(DataStatusEnums))
  status: DataStatusEnum;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'0'",
  //   name: 'access_scope',
  // })
  // @ApiProperty({
  //   description: '用户端对filed访问权限：0-完整权限, 1-用户不可见不可修改',
  // })
  // @IsIn([0, 1])
  accessScope: DataAccessScopeEnum;

  // @Column('int', {
  //   nullable: false,
  //   name: 'create_by',
  // })
  // @ApiProperty({ description: '创建者' })
  // @Type(() => Number)
  createBy: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'update_by',
  // })
  // @ApiProperty({ description: '更新者' })
  // @Type(() => Number)
  updateBy?: number | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecated_date',
  // })
  // @ApiPropertyOptional({ description: '废弃日期' })
  // @IsOptional()
  deprecatedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecate_start_date',
  // })
  // @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  // @IsOptional()
  deprecateStartDate?: string | null;

  // @Column('int', {
  //   nullable: false,
  //   name: 'publish_by',
  // })
  // @ApiProperty({ description: '发布者' })
  // @Type(() => Number)
  publishBy: number;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'published_date',
  // })
  publishedDate?: Date | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'modified_date',
  // })
  // @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  // @IsOptional()
  modifiedDate?: string | null;

  // @Column('json', {
  //   nullable: true,
  //   name: 'field_value',
  // })
  // @ApiProperty({ description: '字段值数组，如果比较类型区间，则第一个是最小值，第二个是最大值', isArray: true })
  fieldValue: any[];

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'compare_type',
  // })
  // @ApiProperty({ description: '比较类型', enum: Object.values(DimensionFieldCompareTypeEnums) })
  // @IsIn(Object.values(DimensionFieldCompareTypeEnums))
  compareType: DimensionFieldCompareTypeEnum;

  // @ManyToOne(
  //   () => DimensionHitStrategyEntity,
  //   (dimensionHitStrategyEntity) => dimensionHitStrategyEntity.strategyFields
  // )
  // @JoinColumn({ name: 'strategy_id' })
  // dimensionHitStrategyEntity: DimensionHitStrategyEntity;

  // @ApiProperty({ description: '维度字段基本信息' })
  // @ManyToOne(() => DimensionFieldsEntity, (dimensionFieldsEntity) => dimensionFieldsEntity.strategyFieldEntity)
  // @JoinColumn({ name: 'dimension_field_id' })
  dimensionField: DimensionFieldEntity;
}
