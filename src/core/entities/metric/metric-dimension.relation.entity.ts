import type { DimensionHitStrategyEntity } from '../strategy/dimension-hit-strategy.entity';

export interface MetricDimensionRelationEntity {
  id: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'metrics_id',
  // })
  metricsId: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'priority',
  // })
  priority: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'order',
  // })
  order: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'is_pre_condition',
  // })
  isPreCondition: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'dimension_strategy_id',
  // })
  dimensionStrategyId: number;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 200,
  //   name: 'template',
  //   default: '匹配到目标主体 <em class="#level#">【#name#】</em>',
  // })
  template?: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  updateDate: string | null;

  // @ManyToOne(() => MetricsEntity, (metricEntity) => metricEntity.dimensionHitStrategies)
  // @JoinColumn({ name: 'metrics_id' })
  // metricEntity: MetricEntity;

  // @ManyToOne(() => DimensionHitStrategyEntity, (dimensionHitStrategyEntity) => dimensionHitStrategyEntity.dimensionHitStrategies)
  // @JoinColumn({ name: 'dimension_strategy_id' })
  dimensionHitStrategyEntity: DimensionHitStrategyEntity;
}
