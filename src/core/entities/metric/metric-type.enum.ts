export enum MetricTypeEnum {
  Simple = 0, // 单维度指标
  Compound = 1, // 复合指标
}

export const MetricTypeEnumMap = {
  [MetricTypeEnum.Simple]: '单维度指标',
  [MetricTypeEnum.Compound]: '复合指标',
};

/**
 * 模型指标选项
 */
export const MetricTypeEnumOptions = [
  {
    label: MetricTypeEnumMap[MetricTypeEnum.Simple],
    value: MetricTypeEnum.Simple,
  },
  {
    label: MetricTypeEnumMap[MetricTypeEnum.Compound],
    value: MetricTypeEnum.Compound,
  },
];
