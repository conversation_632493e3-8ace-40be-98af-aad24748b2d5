import type { DataStatusEnum } from '../enums/data-status.enum';
import type { ScoreSetting } from '../score/score-setting.entity';
import type { QueryHitStrategyEntity } from '../strategy/query-hit-strategy.entity';

export interface MetricHitStrategyDefinitionEntity extends QueryHitStrategyEntity {
  // @ApiPropertyOptional({ description: '分数及风险设置', type: ScoreSettingPO })
  scoreSettings: ScoreSetting;

  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: DataStatusAllowedStatus,
  // })
  // @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnum;
}
