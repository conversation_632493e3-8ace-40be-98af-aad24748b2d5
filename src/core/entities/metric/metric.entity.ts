import type { DataStatusEnum } from '../enums/data-status.enum';
import type { RiskLevelEnum } from '../enums/risk-level.enum';
import type { ProductCodeEnum } from '../enums/product-code.enum';
import type { MetricDimensionRelationEntity } from './metric-dimension.relation.entity';
import type { MetricTypeEnum } from './metric-type.enum';
import type { MetricHitStrategyDefinitionEntity } from './metric-hit-strategy-definition.entity';

type MetricSettingDetailsEntity = Record<string, any>;

export interface MetricEntity {
  metricsId: number;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'name',
  // })
  // @ApiProperty({ description: '指标名称' })
  // @MinLength(5)
  // @MaxLength(50)
  name: string;

  // @Column('tinyint', {
  //   nullable: false,
  //   name: 'risk_level',
  // })
  // @ApiProperty({ description: '风险等级', enum: Object.values(DimensionRiskLevelEnum) })
  // @IsIn(Object.values(DimensionRiskLevelEnum))
  riskLevel: RiskLevelEnum;

  // @Column('tinyint', {
  //   nullable: true,
  //   default: () => "'0'",
  //   name: 'is_veto',
  // })
  // @ApiProperty({ description: '是否为否决指标', enum: Object.values(DataStatusEnums) })
  // @IsIn(Object.values(DataStatusEnums))
  isVeto: number;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'0'",
  //   name: 'metric_type',
  // })
  // @ApiProperty({ description: '指标类型', enum: Object.values(MetricTypeEnums) })
  // @IsIn(Object.values(MetricTypeEnums))
  metricType: MetricTypeEnum;

  // @Column('int', {
  //   nullable: false,
  //   name: 'score',
  // })
  // @ApiProperty({ description: '指标分数' })
  // @IsNumber()
  score: number;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 45,
  //   name: 'product_code',
  // })
  // @ApiProperty({ description: '产品编码', enum: Object.values(ProductCodeEnums) })
  // @IsIn(Object.values(ProductCodeEnums))
  productCode: ProductCodeEnum;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'1'",
  //   name: 'status',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: Object.values(DataStatusEnums),
  // })
  // @IsIn(Object.values(DataStatusEnums))
  status: DataStatusEnum;

  // @Column('int', {
  //   nullable: true,
  //   name: 'extend_from',
  // })
  // @IsOptional()
  extendFrom?: number | null;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 45,
  //   name: 'comment',
  // })
  // @ApiPropertyOptional({ description: '备注' })
  // @IsOptional()
  comment?: string;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecated_date',
  // })
  // @ApiPropertyOptional({ description: '废弃日期' })
  // @IsOptional()
  deprecatedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecate_start_date',
  // })
  // @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  // @IsOptional()
  deprecateStartDate?: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  updateDate: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'modified_date',
  // })
  // @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  // @IsOptional()
  modifiedDate?: string | null;

  // @Column('int', {
  //   nullable: false,
  //   name: 'create_by',
  // })
  // @ApiProperty({ description: '创建者' })
  // @Type(() => Number)
  createBy: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'update_by',
  // })
  // @ApiProperty({ description: '更新者' })
  // @Type(() => Number)
  updateBy?: number | null;

  // @Column('json', {
  //   nullable: false,
  //   name: 'hit_strategy',
  // })
  // @ApiProperty({ description: '指标命中策略,数组中任意一个命中就算命中', type: QueryHitStrategyPO, isArray: true })
  // @Type(() => QueryHitStrategyPO)
  // @IsArray()
  // @ArrayMinSize(1)
  // @ArrayMaxSize(10)
  // @ValidateNested({ each: true })
  hitStrategy: MetricHitStrategyDefinitionEntity[];

  // @Column('json', {
  //   nullable: true,
  //   name: 'details_json',
  // })
  // @ApiPropertyOptional({ description: '其他设置详情', type: MetricSettingDetailsPO })
  // @IsOptional()
  // @Type(() => MetricSettingDetailsPO)
  // @ValidateNested()
  detailsJson?: MetricSettingDetailsEntity | null;

  // @OneToMany(() => MetricDimensionRelationEntity, (relation) => relation.metricEntity)
  dimensionHitStrategies: MetricDimensionRelationEntity[];

  // @OneToMany(() => GroupMetricRelationEntity, (relation) => relation.metricEntity)
  // groupMetrics: GroupMetricRelationEntity[];
}
