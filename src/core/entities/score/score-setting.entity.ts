import type { RiskLevelEnum } from '../enums/risk-level.enum';
import type { ScoreStrategyEnum } from './score-strategy.enum';

export interface ScoreSetting {
  // @ApiProperty({ description: '分数' })
  maxScore: number;

  // @ApiProperty({ description: '风险级别', enum: DimensionRiskLevelEnumAllowValues })
  riskLevel: RiskLevelEnum;

  // @ApiPropertyOptional({ description: '分数策略', type: ScoreStrategyEnums, enum: ScoreStrategyEnumAllowValues })
  // @IsOptional()
  // @IsIn(ScoreStrategyEnumAllowValues)
  scoreStrategy?: ScoreStrategyEnum;
}
