import type { DataStatusEnum } from '../enums/data-status.enum';
import type { ProductCodeEnum } from '../enums/product-code.enum';
import type { GroupEntity } from '../group/group.entity';

export interface RiskModelEntity {
  orgId: number;
  branchCode: string;
  branchCount: number;
  branchTier: number;

  // @PrimaryGeneratedColumn({
  //   type: 'int',
  //   name: 'model_id',
  // })
  modelId: number;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'model_name',
  // })
  modelName: string;

  // @Column('varchar', {
  //   nullable: false,
  //   name: 'product_code',
  // })
  productCode: ProductCodeEnum;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'comment',
  // })
  comment: string | null;

  // @Column('int', {
  //   nullable: false,
  //   name: 'create_by',
  // })
  // @ApiProperty({ description: '创建者' })
  // @Type(() => Number)
  createBy: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'update_by',
  // })
  // @ApiProperty({ description: '更新者' })
  // @Type(() => Number)
  updateBy?: number | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  createDate: string;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  updateDate: string;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'1'",
  //   name: 'score_strategy',
  // })
  scoreStrategy: number;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'1'",
  //   name: 'version_major',
  // })
  versionMajor: number | null;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'0'",
  //   name: 'version_minor',
  // })
  versionMinor: number | null;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'0'",
  //   name: 'version_patch',
  // })
  versionPatch: number | null;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'0'",
  //   name: 'status',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: Object.values(DataStatusEnums),
  // })
  // @IsIn(Object.values(DataStatusEnums))
  status: DataStatusEnum;

  // @Column('json', {
  //   nullable: false,
  //   name: 'published_details',
  // })
  // @ApiProperty({ description: '模型json' })
  publishedContent: object | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'published_date',
  // })
  publishedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'modified_date',
  // })
  // @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  // @IsOptional()
  modifiedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecated_date',
  // })
  // @ApiPropertyOptional({ description: '废弃日期' })
  // @IsOptional()
  deprecatedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecate_start_date',
  // })
  // @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  // @IsOptional()
  deprecateStartDate?: string | null;

  // @OneToMany(() => RiskModelGroupRelationEntity, (riskModelGroupRelationEntity) => riskModelGroupRelationEntity.riskModelEntity)
  groups?: GroupEntity[];

  // @OneToMany(() => OrgModelEntity, (orgModel) => orgModel.riskModel)
  // orgModels?: OrgModelEntity[];
}
