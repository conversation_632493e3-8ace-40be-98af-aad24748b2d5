export enum DataCategoryEnum {
  /** 系统模型 */
  System = 1,
  /** 用户模型 */
  Business = 2,
}
export const DataCategoryEnumMap = {
  [DataCategoryEnum.System]: '系统模型',
  [DataCategoryEnum.Business]: '用户模型',
};

export const DataCategoryEnumOptions = [
  { label: DataCategoryEnumMap[DataCategoryEnum.System], value: DataCategoryEnum.System },
  { label: DataCategoryEnumMap[DataCategoryEnum.Business], value: DataCategoryEnum.Business },
];
