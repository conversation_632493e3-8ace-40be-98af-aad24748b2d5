/**
 * 警示类型
 */
export enum RiskLevelEnum {
  /**
   * 警示类型：提示风险
   */
  Alert = 0, // 提示
  /**
   * 警示类型：关注风险
   */
  Medium = 1, // 关注
  /**
   * 警示类型：警示风险
   */
  High = 2, // 警示
}

export const RiskLevelEnumMap = {
  [RiskLevelEnum.Alert]: '提示',
  [RiskLevelEnum.Medium]: '关注',
  [RiskLevelEnum.High]: '警示',
};

/**
 * 风险等级选项
 */
export const RiskLevelEnumOptions = [
  {
    label: RiskLevelEnumMap[RiskLevelEnum.High],
    value: RiskLevelEnum.High,
  },
  {
    label: RiskLevelEnumMap[RiskLevelEnum.Medium],
    value: RiskLevelEnum.Medium,
  },
  {
    label: RiskLevelEnumMap[RiskLevelEnum.Alert],
    value: RiskLevelEnum.Alert,
  },
];
