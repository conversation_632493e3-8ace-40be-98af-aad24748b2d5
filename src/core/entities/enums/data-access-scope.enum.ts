/**
 * 用户端对filed访问权限：0-完整权限, 1-用户不可见不可修改
 */
export enum DataAccessScopeEnum {
  All = 0,
  Locked = 1,
}

export const DataAccessScopeEnumMap = {
  [DataAccessScopeEnum.All]: '完整权限',
  [DataAccessScopeEnum.Locked]: '用户不可见不可修改',
};

export const DataAccessScopeEnumOptions = [
  { label: DataAccessScopeEnumMap[DataAccessScopeEnum.All], value: DataAccessScopeEnum.All },
  { label: DataAccessScopeEnumMap[DataAccessScopeEnum.Locked], value: DataAccessScopeEnum.Locked },
];
