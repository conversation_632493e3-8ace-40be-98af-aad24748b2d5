/*
0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃
模型，分组，数据指标，数据维度，数据维度字段，数据维度命中策略 都不允许直接删除，通过状态码来标记他们的状态
*/
export enum DataStatusEnum {
  /**  无效 */
  Disable = 0,
  /**  启用 */
  Enable = 1,
  /**  开发中 像是一个模型，已经创建，正在开发中(只能在平台内部使用),与发布的模型是两个概念，发布模型后会变成Enable状态, */
  Developing = 2,
  /**  待废弃 */
  Deprecating = 3,
  /**  已废弃 */
  Deprecated = 4,
}

export const RiskModelStatusEnumMap = {
  [DataStatusEnum.Disable]: '无效',
  [DataStatusEnum.Enable]: '启用',
  [DataStatusEnum.Developing]: '开发中',
  [DataStatusEnum.Deprecating]: '待废弃',
  [DataStatusEnum.Deprecated]: '已废弃',
};

export const RiskModelStatusEnumOptions = [
  { label: RiskModelStatusEnumMap[DataStatusEnum.Disable], value: DataStatusEnum.Disable },
  { label: RiskModelStatusEnumMap[DataStatusEnum.Enable], value: DataStatusEnum.Enable },
  { label: RiskModelStatusEnumMap[DataStatusEnum.Developing], value: DataStatusEnum.Developing },
  { label: RiskModelStatusEnumMap[DataStatusEnum.Deprecating], value: DataStatusEnum.Deprecating },
  { label: RiskModelStatusEnumMap[DataStatusEnum.Deprecated], value: DataStatusEnum.Deprecated },
];

// Label to value
// export const RiskModelStatusEnumMapReverse
