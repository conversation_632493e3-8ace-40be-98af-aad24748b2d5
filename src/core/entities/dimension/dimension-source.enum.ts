export enum DimensionSourceEnum {
  /**
   * 专业版
   */
  Pro = 'Pro',
  /**
   * 信用用大数据ES
   */
  CreditES = 'CreditES',
  /**
   * 信用用大数据
   */
  CreditAPI = 'CreditAPI',
  /**
   * 企业库
   */
  EnterpriseLib = 'EnterpriseLib',
  /**
   * 企业详情
   */
  CompanyDetail = 'CompanyDetail',

  /**
   * Rover
   */
  Rover = 'Rover',

  /**
   * 标讯
   */
  Tender = 'Tender',
  /**
   * 司法案件
   */
  Case = 'Case',

  /**
   * 负面新闻
   */
  NegativeNews = 'NegativeNews',

  /**
   * 裁判文书
   */
  Judgement = 'Judgement',

  /**
   * 税务公告
   */
  TaxAnnouncement = 'TaxAnnouncement',

  /**
   * 股权出质
   */
  Pledge = 'Pledge',
  /**
   * 风险ES
   */
  RiskChange = 'RiskChange',
  /**
   * 特殊黑名单 仅用来详情搜索
   */
  SpecialBlacklist = 'SpecialBlacklist',
  OuterBlacklist = 'OuterBlacklist',

  /**
   * 行政处罚
   */
  SupervisePunish = 'SupervisePunish',
}

export const DimensionSourceEnumMap = {
  [DimensionSourceEnum.Pro]: '专业版',
  [DimensionSourceEnum.CreditES]: '信用用大数据ES',
  [DimensionSourceEnum.CreditAPI]: '信用用大数据',
  [DimensionSourceEnum.EnterpriseLib]: '企业库',
  [DimensionSourceEnum.CompanyDetail]: '企业详情',
  [DimensionSourceEnum.Rover]: 'Rover',
  [DimensionSourceEnum.Tender]: '标讯',
  [DimensionSourceEnum.Case]: '司法案件',
  [DimensionSourceEnum.NegativeNews]: '负面新闻',
  [DimensionSourceEnum.Judgement]: '裁判文书',
  [DimensionSourceEnum.TaxAnnouncement]: '税务公告',
  [DimensionSourceEnum.Pledge]: '股权出质',
  [DimensionSourceEnum.RiskChange]: '风险ES',
  [DimensionSourceEnum.SpecialBlacklist]: '特殊黑名单',
  [DimensionSourceEnum.OuterBlacklist]: '外部黑名单',
  [DimensionSourceEnum.SupervisePunish]: '行政处罚',
};

/**
 * 维度来源选项
 */
export const DimensionSourceEnumOptions = [
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.Pro],
    value: DimensionSourceEnum.Pro,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.CreditES],
    value: DimensionSourceEnum.CreditES,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.CreditAPI],
    value: DimensionSourceEnum.CreditAPI,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.EnterpriseLib],
    value: DimensionSourceEnum.EnterpriseLib,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.CompanyDetail],
    value: DimensionSourceEnum.CompanyDetail,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.Rover],
    value: DimensionSourceEnum.Rover,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.Tender],
    value: DimensionSourceEnum.Tender,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.Case],
    value: DimensionSourceEnum.Case,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.NegativeNews],
    value: DimensionSourceEnum.NegativeNews,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.Judgement],
    value: DimensionSourceEnum.Judgement,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.TaxAnnouncement],
    value: DimensionSourceEnum.TaxAnnouncement,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.Pledge],
    value: DimensionSourceEnum.Pledge,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.RiskChange],
    value: DimensionSourceEnum.RiskChange,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.SpecialBlacklist],
    value: DimensionSourceEnum.SpecialBlacklist,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.OuterBlacklist],
    value: DimensionSourceEnum.OuterBlacklist,
  },
  {
    label: DimensionSourceEnumMap[DimensionSourceEnum.SupervisePunish],
    value: DimensionSourceEnum.SupervisePunish,
  },
];
