import type { DimensionFieldEntity } from '../dimension-field/dimension-field.entity';
import type { ProductCodeEnum } from '../enums/product-code.enum';
import type { DataStatusEnum } from '../enums/data-status.enum';
import type { DimensionHitStrategyEntity } from '../strategy/dimension-hit-strategy.entity';
import type { DimensionIndicatorTypeEnum } from './dimension-indicator-type.enum';
import type { DimensionSourceEnum } from './dimension-source.enum';
import type { DimensionTypeEnum } from './dimension-type.enum';

export interface DimensionDefinitionEntity {
  dimensionId: number;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'dimension_key',
  // })
  // @ApiProperty({ description: '维度的唯一标识', enum: Object.values(DimensionTypeEnums) })
  // @IsIn(Object.values(DimensionTypeEnums))
  key: DimensionTypeEnum;

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'name',
  // })
  // @ApiProperty({ description: '维度的名称' })
  // @IsString()
  // @MinLength(1)
  // @MaxLength(45)
  name: string;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  // @ApiProperty({ description: '创建时间' })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  // @ApiProperty({ description: '更新时间' })
  updateDate: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'modified_date',
  // })
  // @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  // @IsOptional()
  modifiedDate?: string | null;

  // @Column('int', {
  //   nullable: false,
  //   name: 'product_code',
  // })
  // @ApiProperty({ description: '关联的产品' })
  // @Type(() => Number)
  productCode: ProductCodeEnum;

  // @Column('int', {
  //   nullable: false,
  //   name: 'create_by',
  // })
  // @ApiProperty({ description: '创建者' })
  // @Type(() => Number)
  createBy: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'update_by',
  // })
  // @ApiProperty({ description: '关联的产品' })
  // @Type(() => Number)
  updateBy: number | null;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'1'",
  //   name: 'status',
  //   comment: '1: 有效 0: 无效',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: Object.values(DataStatusEnums),
  // })
  // @IsIn(Object.values(DataStatusEnums))
  status: DataStatusEnum;

  // @Column('int', {
  //   nullable: true,
  //   name: 'extend_from',
  // })
  // @ApiPropertyOptional({ description: '扩展自哪个维度(如果是从其他维度复制过来的)' })
  // @IsOptional()
  extendFrom?: number | null;

  // @ApiProperty({ description: '维度的数据来源，例如 数据接口，指定es等等', enum: Object.values(DimensionSourceEnums) })
  // @IsIn(Object.values(DimensionSourceEnums))
  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'source',
  // })
  source: DimensionSourceEnum;

  // @ApiPropertyOptional({ description: '维度数据接口地址' })
  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'source_path',
  // })
  // @IsOptional()
  sourcePath?: string;

  // @ApiProperty({
  //   description: '维度详情的数据来源 ，例如 数据接口，指定es等等',
  //   enum: Object.values(DimensionSourceEnums),
  // })
  // @IsIn(Object.values(DimensionSourceEnums))
  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'detail_source',
  // })
  detailSource?: DimensionSourceEnum | null;

  // @ApiPropertyOptional({ description: '数据详情接口地址' })
  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'detail_source_path',
  // })
  detailSourcePath?: string | null;

  // @ApiPropertyOptional({ description: '专业版数据维度对应code' })
  // @Column('varchar', {
  //   nullable: true,
  //   length: 20,
  //   name: 'type_code',
  // })
  typeCode?: string | null;

  // @ApiProperty({ description: '指标类型，一般项，关键项', enum: Object.values(IndicatorTypeEnums) })
  // @IsIn(Object.values(IndicatorTypeEnums))
  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'indicator_type',
  //   default: IndicatorTypeEnums.generalItems,
  // })
  type?: DimensionIndicatorTypeEnum;

  // @ApiPropertyOptional({ description: '描述' })
  // // @Column('varchar', {
  // //   nullable: true,
  // //   length: 45,
  // //   name: 'description',
  // // })
  // @IsOptional()
  description?: string;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecated_date',
  // })
  // @ApiPropertyOptional({ description: '废弃日期' })
  // @IsOptional()
  deprecatedDate?: string | null;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'deprecate_start_date',
  // })
  // @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  // @IsOptional()
  deprecateStartDate?: string | null;

  // @OneToMany(() => DimensionFieldsEntity, (dimensionFields) => dimensionFields.dimensionDef)
  dimensionFields: DimensionFieldEntity[];

  // @OneToMany(() => DimensionHitStrategyEntity, (dimensionHitStrategy) => dimensionHitStrategy.dimensionDef)
  dimensionHitStrategy: DimensionHitStrategyEntity[];
}
