/**
 * 指标类型
 */
export enum DimensionIndicatorTypeEnum {
  /**
   * 指标类型：关键项
   */
  keyItems = 'keyItems',
  /**
   * 指标类型：一般项
   */
  generalItems = 'generalItems',
}

export const DimensionIndicatorTypeEnumMap = {
  [DimensionIndicatorTypeEnum.keyItems]: '关键项',
  [DimensionIndicatorTypeEnum.generalItems]: '一般项',
};

/**
 * 维度类型选项
 */
export const DimensionIndicatorTypeEnumOptions = [
  {
    value: DimensionIndicatorTypeEnum.keyItems,
    label: DimensionIndicatorTypeEnumMap[DimensionIndicatorTypeEnum.keyItems],
  },
  {
    value: DimensionIndicatorTypeEnum.generalItems,
    label: DimensionIndicatorTypeEnumMap[DimensionIndicatorTypeEnum.generalItems],
  },
];
