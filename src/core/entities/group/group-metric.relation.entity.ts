import type { MetricEntity } from '../metric/metric.entity';

export interface GroupMetricRelationEntity {
  id: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'metrics_id',
  // })
  // @ApiProperty({ description: '指标的ID' })
  metricsId: number;

  // @Column('int', {
  //   nullable: false,
  //   primary: true,
  //   name: 'group_id',
  // })
  // @ApiProperty({ description: '分组的ID' })
  groupId: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'order',
  // })
  // @ApiProperty({ description: '排序' })
  order?: number;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  createDate: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  updateDate: string | null;

  // @ManyToOne(() => GroupEntity, (groupEntity) => groupEntity.groupMetrics)
  // @JoinColumn({ name: 'group_id' })
  // groupEntity: GroupEntity;

  // @ManyToOne(() => MetricsEntity, (metricEntity) => metricEntity.groupMetrics)
  // @JoinColumn({ name: 'metrics_id' })
  metricEntity: MetricEntity;
}
