import type { DataStatusEnum } from '../enums/data-status.enum';
import type { ProductCodeEnum } from '../enums/product-code.enum';
import type { RiskLevelEnum } from '../enums/risk-level.enum';
import type { DataCategoryEnum } from '../enums/data-category.enum';
import type { GroupMetricRelationEntity } from './group-metric.relation.entity';

interface ScoreDefinitionPO {
  // @ApiProperty({ description: '业务id' })
  businessId: number;

  // @ApiProperty({ description: '分数' })
  score: number;
}

type ScoreStrategyPO = any;

interface ScoreSettingPO {
  // @ApiProperty({ description: '最大分数' })
  maxScore: number;

  // @ApiPropertyOptional({ description: '不同规则分数的定义', type: ScoreDefinitionPO, isArray: true })
  // @IsOptional()
  // @ValidateNested({ each: true })
  scoreDefinition?: ScoreDefinitionPO[];

  // @ApiPropertyOptional({ description: '不同规则分数的计算策略', type: ScoreStrategyPO })
  // @IsOptional()
  // @ValidateNested()
  scoreStrategy?: ScoreStrategyPO;
}

type GroupSettingDetailsPO = ScoreSettingPO;

export interface GroupEntity {
  groupId: number;

  // @Column('int', {
  //   nullable: true,
  //   default: () => "'1'",
  //   name: 'category',
  // })
  // @ApiProperty({ description: '模型类别 1 系统模型， 2 用户模型' })
  category: DataCategoryEnum;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'extend_from',
  // })
  // @ApiPropertyOptional({description:"完整的继承路径(如果有)"})
  // extendFrom: string | null;
  extendFrom: string | null;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'create_date',
  // })
  createDate: Date;

  // @Column('datetime', {
  //   nullable: false,
  //   default: () => 'CURRENT_TIMESTAMP',
  //   name: 'update_date',
  // })
  updateDate: Date;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 45,
  //   name: 'group_name',
  // })
  // @ApiProperty({ description: '组别名称' })
  // @IsString()
  // @MaxLength(45)
  // @MinLength(5)
  groupName: string;

  // @Column('int', {
  //   nullable: true,
  //   name: 'parent_group_id',
  // })
  // @IsOptional()
  // @ApiPropertyOptional({ description: '父级分组id' })
  parentGroupId?: number;

  // @Column('tinyint', {
  //   nullable: true,
  //   name: 'is_virtual',
  // })
  // @ApiPropertyOptional({ description: '是否是虚拟分组' })
  // @IsOptional()
  isVirtual?: number;

  // @Column('json', {
  //   nullable: true,
  //   name: 'details_json',
  // })
  // @ApiPropertyOptional({ description: '其他设置详情', type: GroupSettingDetailsPO })
  // @IsOptional()
  // @ValidateNested()
  detailsJson?: GroupSettingDetailsPO;

  // @Column('int', {
  //   nullable: true,
  //   name: 'org_id',
  // })
  // @ApiProperty({ description: '分组归属的组织,如果是用户创建的分组，该字段不为空' })
  orgId?: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'product_code',
  // })
  // @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  // @IsString()
  // @IsIn(ProductCodeAllowValues)
  productCode: ProductCodeEnum;

  // @Column('int', {
  //   nullable: false,
  //   name: 'publish_by',
  // })
  // @ApiProperty({ description: '发布者' })
  // @Type(() => Number)
  publishBy: number;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'published_date',
  // })
  publishedDate?: Date;

  // @Column('int', {
  //   nullable: false,
  //   name: 'risk_level',
  // })
  // @ApiProperty({ description: '风险等级', enum: Object.values(DimensionRiskLevelEnum) })
  // @IsIn(Object.values(DimensionRiskLevelEnum))
  riskLevel: RiskLevelEnum;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'1'",
  //   name: 'status',
  //   comment: '1: 有效 0: 无效',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: DataStatusAllowedStatus,
  // })
  // @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnum;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'comment',
  // })
  // @ApiPropertyOptional({ description: '分组描述' })
  // @MinLength(0)
  // @MaxLength(200)
  comment?: string;

  // @Column('int', {
  //   nullable: false,
  //   name: 'create_by',
  // })
  // @ApiProperty({ description: '创建者' })
  // @Type(() => Number)
  createBy?: number | null;

  // @Column('int', {
  //   nullable: true,
  //   name: 'update_by',
  // })
  // @ApiProperty({ description: '更新者' })
  // @Type(() => Number)
  updateBy: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'model_id',
  // })
  // @ApiProperty({description:"归属的 risk model的ID"})
  modelId: number;

  // @Column('int', {
  //   nullable: true,
  //   name: 'order',
  // })
  // order: number | null;
  order?: number;

  // @ManyToOne(() => RiskModelEntity, (riskModelEntity) => riskModelEntity.modelId)
  // @JoinColumn({ name: 'model_id' })
  // riskModelEntity: RiskModelEntity;

  // @OneToMany(() => GroupMetricRelationEntity, (groupMetricRelationEntity) => groupMetricRelationEntity.groupEntity)
  groupMetrics: GroupMetricRelationEntity[];
}
