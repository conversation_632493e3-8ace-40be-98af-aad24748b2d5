import { ActionTree, GetterTree, MutationTree } from 'vuex';

import { person } from '@/shared/services';

import { IPersonState, IRootState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<IPersonState> = {
  SET_DETAIL(state, data) {
    state.detail = data;
  },
  SET_NAV(state, data) {
    state.nav = data;
  },
};

export const actions: ActionTree<IPersonState, IRootState> = {
  async getDetail({ commit }, params) {
    let detail: unknown = null;
    try {
      detail = await person.getDetail(params);
    } catch (err: any) {
      if (err.status === 415) {
        throw err;
      } else {
        console.error(err);
      }
    }
    commit('SET_DETAIL', detail);
    return detail;
  },
};

export const getters: GetterTree<IPersonState, IRootState> = {
  detail(state) {
    return state.detail;
  },
  nav(state) {
    return state.nav;
  },
};

export const state: IPersonState = {
  detail: null,
  nav: null,
};
