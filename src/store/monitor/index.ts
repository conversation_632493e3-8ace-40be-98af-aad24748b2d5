import { ActionTree, MutationTree, GetterTree } from 'vuex';

import { IMonitorState, IRootState } from '../interfaces';

export const actions: ActionTree<IMonitorState, IRootState> = {
  // getGroupList({ commit }, params) {
  //   return monitor.getGroupList(params).then((data) => {
  //     commit('onGetGroupList', {
  //       groupList: data?.data || [],
  //       reycleCount: data?.reycleCount || 0,
  //     });
  //   });
  // },
  addRemoveFlag({ commit }) {
    commit('onAddRemoveFlag');
  },
  updateSetting({ commit }, params) {
    commit('onUpdateSetting', params);
  },
};

export const mutations: MutationTree<IMonitorState> = {
  onGetGroupList(state, data) {
    const company = data.groupList.filter((el) => el.type === 1);
    const person = data.groupList.filter((el) => el.type === 2);
    state.groupList = [...company, ...person];
    state.recycleCount = data.reycleCount;
  },
  onAddRemoveFlag(state) {
    state.removeFlag += 1;
  },
  onUpdateSetting(state, data) {
    state.hasUpdateSetting = data.hasUpdateSetting;
  },
};

export const getters: GetterTree<IMonitorState, IRootState> = {
  groupList(state) {
    return state.groupList;
  },
  recycleCount(state) {
    return state.recycleCount;
  },
  removeFlag(state) {
    return state.removeFlag;
  },
};

export const state = () => {
  return {
    groupList: [],
    recycleCount: 0,
    removeFlag: 0,
    hasUpdateSetting: false,
  };
};

export const namespaced = true;
