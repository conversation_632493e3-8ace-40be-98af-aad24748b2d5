import { ActionTree, GetterTree, MutationTree } from 'vuex';

import { IAppState, IRootState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<IAppState> = {
  // HeaderSearch同步keyword
  SET_KEYWORD(state, payload: string) {
    state.keyword = payload;
  },
  SET_READ_RECORDS(state, payload) {
    state.readRecords = payload;
  },
};

export const actions: ActionTree<IAppState, IRootState> = {
  // header-HeaderSearch同步keyword
  syncKeyword({ commit }, keyword: string | undefined) {
    commit('SET_KEYWORD', keyword);
  },
};

export const getters: GetterTree<IAppState, IRootState> = {
  readRecordsMap(state) {
    return Object.keys(state.readRecords).reduce((map, type) => {
      const ids = state.readRecords[type];
      ids.forEach((id) => {
        map.set(`${type}_${id}`);
      });
      return map;
    }, new Map<string, void>());
  },
};

export const state: IAppState = {
  name: 'app',
  keyword: undefined as any,
  readRecords: {},
};
