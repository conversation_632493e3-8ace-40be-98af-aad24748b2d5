import { ActionTree, MutationTree } from 'vuex';

import { message as messageService } from '@/shared/services';

import { IMessageState, IRootState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<IMessageState> = {
  ADD_MESSAGE(state, payload) {
    state.messages = [payload, ...state.messages.slice(0, 9)];
    state.messageCount += 1;
  },
  SET_MESSAGES(state, payload) {
    state.messages = payload;
  },
  SET_MESSAGE_COUNT(state, count) {
    state.messageCount = count;
  },
};

export const actions: ActionTree<IMessageState, IRootState> = {
  /**
   * 获取消息
   */
  async getMessages({ commit }) {
    try {
      const { data, total } = await messageService.search(
        {
          pageSize: 10,
          msgType: 0, // 0-全部消息, 1-任务提醒, 2-下载提醒
          msgStatus: 1, // 0-全部, 1-未读, 2-已读,
        },
        true
      );
      commit('SET_MESSAGES', data);
      commit('SET_MESSAGE_COUNT', total);
    } catch (err) {
      console.error(err);
    }
  },

  /**
   * 设置消息全部已读
   */
  async readAllMessages({ commit, state }, { msgType } = {}) {
    try {
      await messageService.readAll(msgType);

      let data;
      if (msgType === undefined || msgType === 0) {
        data = [];
      } else {
        data = state.messages.filter((item) => item.msgType !== msgType);
      }

      commit('SET_MESSAGES', data);
      commit('SET_MESSAGE_COUNT', data.length);
    } catch (err) {
      console.error(err);
    }
  },

  /**
   * 设置消息已读（单条）
   */
  async readMessage({ dispatch }, messageId) {
    try {
      await messageService.read(messageId);
      dispatch('getMessages');
    } catch (err) {
      console.error(err);
    }
  },

  /**
   * 新增消息（手动插入到消息列表）
   */
  async addMessage({ commit }, message) {
    try {
      commit('ADD_MESSAGE', message);
    } catch (err) {
      console.error(err);
    }
  },
};

export const state: IMessageState = {
  messages: [],
  messageCount: 0,
};
