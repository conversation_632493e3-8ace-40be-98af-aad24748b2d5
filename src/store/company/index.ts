import { ActionTree, GetterTree, MutationTree } from 'vuex';

import { company } from '@/shared/services';

import { ICompanyState, IRootState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<ICompanyState> = {
  SET_DETAIL(state, data) {
    state.detail = data;
  },
  SET_NAV(state, data) {
    state.nav = data;
  },
};

export const actions: ActionTree<ICompanyState, IRootState> = {
  async getDetail({ commit }, params) {
    let detail: unknown = null;
    try {
      detail = await company.getDetail(params);
    } catch (err: any) {
      if (err.status === 415) {
        throw err;
      } else {
        console.error(err);
      }
    }
    commit('SET_DETAIL', detail);
    return detail;
  },
};

export const getters: GetterTree<ICompanyState, IRootState> = {
  detail(state) {
    return state.detail;
  },
  nav(state) {
    return state.nav;
  },
};

export const state: ICompanyState = {
  detail: null,
  nav: null,
};
