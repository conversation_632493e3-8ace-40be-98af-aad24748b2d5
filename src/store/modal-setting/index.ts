import { ActionTree, GetterTree, MutationTree } from 'vuex';

import { setting as settingService } from '@/shared/services';

import { IRootState, IModalSettingState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<IModalSettingState> = {
  SET_SETTING_STATE(state, payload) {
    state.loading = payload;
  },
  SET_SETTING_INFO(state, payload) {
    state.settingInfo = payload;
  },
};

export const actions: ActionTree<IModalSettingState, IRootState> = {
  // 获取模版设置列表
  async getSettingInfo({ commit }) {
    try {
      commit('SET_SETTING_STATE', true);
      const setting = await settingService.info();
      if (setting instanceof Error) {
        throw setting;
      }
      commit('SET_SETTING_INFO', setting);
    } catch (err) {
      commit('SET_SETTING_INFO', null);
      throw err;
    } finally {
      commit('SET_SETTING_STATE', false);
    }
  },
};

export const getters: GetterTree<IModalSettingState, IRootState> = {
  settingInfo(state) {
    return state.settingInfo;
  },
};

export const state: IModalSettingState = {
  settingInfo: null,
  loading: false,
};
