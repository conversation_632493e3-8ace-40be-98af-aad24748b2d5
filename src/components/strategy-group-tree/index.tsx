import { defineComponent, type PropType, type VNode } from 'vue';
import { Button, Icon } from 'ant-design-vue';

import { QueryHitStrategyTypeEnum, type QueryHitStrategyEntity, QueryHitStrategyTypeEnumMap } from '@/core/entities';

import styles from './strategy-group-tree.module.less';

const renderCondition = (metaType, strategyType: QueryHitStrategyTypeEnum) => {
  let content: VNode | null;
  const categoryMap = {
    dimensionStrategy: '命中策略', // (Strategy)
    dimensionStrategyField: '规则', // (StrategyField)
  };
  const categoryLabel = metaType ? categoryMap[metaType] : '';

  switch (strategyType) {
    case QueryHitStrategyTypeEnum.Must:
      content = (
        <span class="color-gray-5 inline-flex items-center gap-2">
          满足以下
          <em class="bg-blue-1 text-blue-5 lh-16px rounded px-2 py-1 not-italic">{QueryHitStrategyTypeEnumMap[strategyType]}</em>
          {categoryLabel}
        </span>
      );
      break;
    case QueryHitStrategyTypeEnum.Should:
      content = (
        <span class="inline-flex items-center gap-2">
          满足以下
          <em class="bg-blue-1 text-blue-5 lh-16px rounded px-2 py-1 not-italic">{QueryHitStrategyTypeEnumMap[strategyType]}</em>
          {categoryLabel}
        </span>
      );
      break;
    case QueryHitStrategyTypeEnum.MustNot:
      content = <span>排除以下所有{categoryLabel}</span>;
      break;
    default:
      content = null;
      break;
  }

  let theme = '';
  switch (metaType) {
    case 'dimensionStrategy':
      theme = 'bg-blue-50 border border-blue-100 border-solid';
      break;
    case 'dimensionStrategyField':
      theme = 'bg-teal-50 border border-teal-100 border-solid';
      break;
    default:
      break;
  }

  return (
    <div class={`${theme} flex items-center gap-2 rounded px-2`}>
      <Icon type="branches" />
      {/* <span>[{props.type}]</span> */}
      {content}
    </div>
  );
};

const StrategyGroupTree = defineComponent({
  name: 'StrategyGroupTree',
  props: {
    type: {
      type: String as PropType<'dimensionStrategy' | 'dimensionStrategyField'>,
      required: false,
    },
    /**
     * 命中策略 type-data(id) 映射: { must: number[]}
     */
    strategy: {
      type: Object as PropType<QueryHitStrategyEntity>,
      required: true,
    },
    /**
     * 策略类型
     */
    strategyTypes: {
      type: Array as PropType<QueryHitStrategyTypeEnum[]>,
      default: () => ['must', 'should', 'must_not'],
    },
    /**
     * 匹配规则
     */
    hitKey: {
      type: String,
      required: true,
    },
    /**
     * 遍历节点时使用的 key
     */
    nodeKey: {
      type: String,
      required: true,
    },
    /**
     * 数据
     */
    data: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    /**
     * 是否为顶层
     */
    isRoot: {
      type: Boolean,
      default: false,
    },
    /**
     * 编辑模式: 目前的需求中 StrategyGroup 为顶层，不支持编辑
     */
    editable: {
      type: Boolean,
      default: true,
    },
    /**
     * 添加文案
     */
    addText: {
      type: String,
      required: false,
    },
  },
  emits: ['add'],
  setup(props, { emit }) {
    const onAdd = (strategyType: QueryHitStrategyTypeEnum) => {
      emit('add', strategyType, props.data);
    };

    return {
      onAdd,
    };
  },
  render() {
    const { nodeKey, addText } = this;
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.root]: this.isRoot,
        }}
      >
        {this.strategyTypes.map((hitStrategyType) => {
          const matchedIds = this.strategy[hitStrategyType] || [];
          const result = this.data.filter((dataItem) => {
            return matchedIds.includes(dataItem[this.hitKey]);
          });
          return (
            <div key={hitStrategyType} class={styles.group}>
              <div class={styles.condition}>{renderCondition(this.type, hitStrategyType)}</div>
              {result.map((record) => {
                return (
                  // 替换 index 为 nodeKey
                  <div class={styles.node} key={record[nodeKey]}>
                    {this.$scopedSlots.node?.({ record, hitStrategyType })}
                  </div>
                );
              })}
              <div class={[styles.node, styles.action]}>
                <Button class="px-0" type="link" size="small" icon="plus-circle" onClick={() => this.onAdd(hitStrategyType)}>
                  <span>{addText}</span>
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    );
  },
});

export default StrategyGroupTree;
