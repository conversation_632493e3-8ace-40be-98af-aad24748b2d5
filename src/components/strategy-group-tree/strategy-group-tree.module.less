@indent-line-color: #eee;
@indent-line-width: 2px;
@indent-line-size: 20px;

.group {
  position: relative;

  &::after {
    position: absolute;
    content: '';
    // width: @indent-line-size;
    height: 100%;
    // top: calc(@indent-line-size);
    top: 0;
    // top: 37px;
    left: 13px;
    bottom: 0;
    border-left: @indent-line-width solid @indent-line-color;
  }
}

.node {
  padding-left: @indent-line-size + 12px;
  position: relative;
  line-height: 37px; // FIXME: 不应该限制高度

  &::after {
    position: absolute;
    content: '';
    width: @indent-line-size;
    height: @indent-line-size;
    top: 0;
    left: 13px;
    border-bottom: @indent-line-width solid @indent-line-color;
  }

  &.action {
    padding-top: 4px;
    padding-bottom: 4px;
  }
}

.condition {
  line-height: 37px;
  position: relative;
  z-index: 1; // 保证条件在线的层级以上
}
