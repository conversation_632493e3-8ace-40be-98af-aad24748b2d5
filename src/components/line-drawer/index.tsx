import { defineComponent } from 'vue';

import CollapseNodes from '@/shared/components/collapse-nodes';
import { createFunctionalEventEmitter } from '@/utils/component';

import RelationalPath from '../relational-path';
import styles from './line-drawer.module.less';

const LineDrawer = defineComponent({
  functional: true,
  props: {
    record: {
      type: Object,
    },
    dataKey: {
      type: String,
      default: 'relations',
    },
    // 是否展示强弱标签
    showTag: {
      type: Boolean,
      default: true,
    },
  },
  render(h, ctx) {
    const { props, listeners } = ctx;
    const emitters = createFunctionalEventEmitter(listeners);
    const { record, dataKey, showTag } = props;
    const relations = record?.[dataKey];
    if (!Array.isArray(relations) || relations.length === 0) {
      return '-';
    }

    return (
      <CollapseNodes
        locale={{
          more: `查看更多(${relations.length - 1})`,
        }}
        dashed
      >
        {/* `relations` 为嵌套数组 */}
        {/* FIXME: 统一 `RelationalPath` 与 `AssociationPath` 组件 */}
        {relations.map((elements, index, source) => {
          const key = [record?.startCompanyKeyno, record?.endCompanyKeyno, index].join('-');
          // 先筛选出egde，取roles或者role,roles
          const lineData = elements.filter((item) => item.type === 'edge');
          // 筛选没有箭头的线
          const unDirectionData = lineData.filter((el) => ['null'].includes(el.direction));
          const calData = unDirectionData.length ? unDirectionData : lineData;
          const typeList = showTag
            ? calData.reduce((arr, cur) => {
                // 当多个关系时
                if (cur.roles && cur.roles.length > 1) {
                  arr = [...arr, ...cur.roles];
                } else {
                  // 上下游的role是客户/供应商，取roleType
                  arr = [...arr, !['0', '1'].includes(cur.roleType) && cur.roleType ? cur.roleType : cur.role];
                }
                return arr;
              }, [])
            : '';
          return (
            <div class={styles.relationWrapper} key={key}>
              <div class={styles.title} v-show={source.length > 1}>
                路径 {index + 1}
              </div>
              <div class={styles.content}>
                <RelationalPath
                  namespace={key}
                  elements={elements}
                  type={typeList}
                  onClickEdge={(ele) => {
                    const { startid, endid } = ele;
                    emitters('edgeClick')({
                      ...ele,
                      keyNoAndNames: [{ companyId: startid }, { companyId: endid }],
                    });
                  }}
                />
              </div>
            </div>
          );
        })}
      </CollapseNodes>
    );
  },
});

export default LineDrawer;
