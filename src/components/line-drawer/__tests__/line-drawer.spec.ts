import { shallowMount } from '@vue/test-utils';

import CollapseNodes from '@/shared/components/collapse-nodes';

import LineDrawer from '../index'; // Adjust the path as necessary
import RelationalPath from '../../relational-path';

describe('LineDrawer.vue', () => {
  const recordMock = {
    startCompanyKeyno: 'a59b3c1f33224db1eac88afb1906efbd',
    endCompanyKeyno: 'a59b3c1f33224db1eac88afb1906efbd',
    relations: [
      [
        {
          type: 'node',
          entityType: 'company',
          id: 'f625a5b661058ba5082ca508f99ffe1b',
          name: '企查查科技股份有限公司',
          label: '企查查科技股份有限公司',
        },
        {
          role: '',
          create_time: 1725646252,
          endid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          startid: 'f625a5b661058ba5082ca508f99ffe1b',
          iscal: '1',
          shouldcapi: 225,
          stockpercent: 0.6211,
          type: 'edge',
          direction: 'left',
          roles: ['投资0.6211%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '001c8df79b2dc181a10a3c1ff3c6b65d',
          name: '中信证券投资有限公司',
          label: '中信证券投资有限公司',
        },
        {
          role: '企业法人',
          create_time: 1725646252,
          endid: '9f1f209c03c53470787a873fadc8c8e8',
          startid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          iscal: '1',
          shouldcapi: 229.6823,
          stockpercent: 0.7856,
          type: 'edge',
          direction: 'right',
          roles: ['投资0.7856%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '9f1f209c03c53470787a873fadc8c8e8',
          name: '国家电投集团氢能科技发展有限公司',
          label: '国家电投集团氢能科技发展有限公司',
        },
        {
          create_time: 1726890862,
          endid: 'a59b3c1f33224db1eac88afb1906efbd',
          startid: '9f1f209c03c53470787a873fadc8c8e8',
          type: 'edge',
          percent: 0.010361,
          direction: 'left',
          roles: [],
          roleType: 'Hold',
        },
        {
          type: 'node',
          entityType: 'company',
          id: 'a59b3c1f33224db1eac88afb1906efbd',
          name: '国家电网有限公司',
          label: '国家电网有限公司',
        },
      ],
      [
        {
          type: 'node',
          entityType: 'company',
          id: 'f625a5b661058ba5082ca508f99ffe1b',
          name: '企查查科技股份有限公司',
          label: '企查查科技股份有限公司',
        },
        {
          role: '',
          create_time: 1725646252,
          endid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          startid: 'f625a5b661058ba5082ca508f99ffe1b',
          iscal: '1',
          shouldcapi: 225,
          stockpercent: 0.6211,
          type: 'edge',
          direction: 'left',
          roles: ['投资0.6211%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '001c8df79b2dc181a10a3c1ff3c6b65d',
          name: '中信证券投资有限公司',
          label: '中信证券投资有限公司',
        },
        {
          role: '',
          create_time: 1725646252,
          endid: '099fe6b7cdf124fe0668efde3dd6ced3',
          startid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          iscal: '1',
          shouldcapi: 499.0401,
          stockpercent: 0.499,
          type: 'edge',
          direction: 'right',
          roles: ['投资0.499%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '099fe6b7cdf124fe0668efde3dd6ced3',
          name: '欧冶云商股份有限公司',
          label: '欧冶云商股份有限公司',
        },
        {
          create_time: 1725646240,
          endid: 'a59b3c1f33224db1eac88afb1906efbd',
          startid: '099fe6b7cdf124fe0668efde3dd6ced3',
          type: 'edge',
          percent: 0.017734,
          direction: 'left',
          roles: [],
          roleType: 'Hold',
        },
        {
          type: 'node',
          entityType: 'company',
          id: 'a59b3c1f33224db1eac88afb1906efbd',
          name: '国家电网有限公司',
          label: '国家电网有限公司',
        },
      ],
      [
        {
          type: 'node',
          entityType: 'company',
          id: 'f625a5b661058ba5082ca508f99ffe1b',
          name: '企查查科技股份有限公司',
          label: '企查查科技股份有限公司',
        },
        {
          role: '',
          create_time: 1725646252,
          endid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          startid: 'f625a5b661058ba5082ca508f99ffe1b',
          iscal: '1',
          shouldcapi: 225,
          stockpercent: 0.6211,
          type: 'edge',
          direction: 'left',
          roles: ['投资0.6211%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '001c8df79b2dc181a10a3c1ff3c6b65d',
          name: '中信证券投资有限公司',
          label: '中信证券投资有限公司',
        },
        {
          role: '企业法人',
          create_time: 1727191281,
          endid: '05ad654a211ccd4b90726164b0eb89a3',
          startid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          iscal: '1',
          shouldcapi: 1773.0496,
          stockpercent: 1.8588,
          type: 'edge',
          direction: 'right',
          roles: ['投资1.8588%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '05ad654a211ccd4b90726164b0eb89a3',
          name: '凯盛玻璃控股有限公司',
          label: '凯盛玻璃控股有限公司',
        },
        {
          create_time: 1727268670,
          endid: 'a59b3c1f33224db1eac88afb1906efbd',
          startid: '05ad654a211ccd4b90726164b0eb89a3',
          type: 'edge',
          percent: 0.01355,
          direction: 'left',
          roles: [],
          roleType: 'Hold',
        },
        {
          type: 'node',
          entityType: 'company',
          id: 'a59b3c1f33224db1eac88afb1906efbd',
          name: '国家电网有限公司',
          label: '国家电网有限公司',
        },
      ],
      [
        {
          type: 'node',
          entityType: 'company',
          id: 'f625a5b661058ba5082ca508f99ffe1b',
          name: '企查查科技股份有限公司',
          label: '企查查科技股份有限公司',
        },
        {
          role: '',
          create_time: 1725646252,
          endid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          startid: 'f625a5b661058ba5082ca508f99ffe1b',
          iscal: '1',
          shouldcapi: 225,
          stockpercent: 0.6211,
          type: 'edge',
          direction: 'left',
          roles: ['投资0.6211%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: '001c8df79b2dc181a10a3c1ff3c6b65d',
          name: '中信证券投资有限公司',
          label: '中信证券投资有限公司',
        },
        {
          role: '',
          create_time: 1725646252,
          endid: 'b0335502875cd0358aab6722b44f8058',
          startid: '001c8df79b2dc181a10a3c1ff3c6b65d',
          iscal: '1',
          shouldcapi: 6100,
          stockpercent: 0.2148,
          type: 'edge',
          direction: 'right',
          roles: ['投资0.2148%'],
          roleType: 'Invest',
        },
        {
          type: 'node',
          entityType: 'company',
          id: 'b0335502875cd0358aab6722b44f8058',
          name: '中国石化销售股份有限公司',
          label: '中国石化销售股份有限公司',
        },
        {
          create_time: 1725646240,
          endid: 'a59b3c1f33224db1eac88afb1906efbd',
          startid: 'b0335502875cd0358aab6722b44f8058',
          type: 'edge',
          percent: 0.053551,
          direction: 'left',
          roles: [],
          roleType: 'Hold',
        },
        {
          type: 'node',
          entityType: 'company',
          id: 'a59b3c1f33224db1eac88afb1906efbd',
          name: '国家电网有限公司',
          label: '国家电网有限公司',
        },
      ],
    ],
  };

  test('renders correctly with relations', () => {
    const wrapper = shallowMount(LineDrawer, {
      propsData: { record: recordMock },
    });
    expect(wrapper.find(CollapseNodes).exists()).toBe(true);
    expect(wrapper.findAllComponents(RelationalPath).length).toBe(1);
    expect(wrapper.text()).toContain('查看更多(3)');
  });

  test('renders "-" when relations are not an array', () => {
    const wrapper = shallowMount(LineDrawer, {
      propsData: { record: { relations: [] } },
    });
    expect(wrapper.text()).toBe('');
  });
});
