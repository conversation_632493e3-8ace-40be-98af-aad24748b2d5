import { defineComponent, PropType } from 'vue';
import { Icon, Tooltip } from 'ant-design-vue';

import QPlainTable from '@/components/global/q-plain-table';
import QLink from '@/components/global/q-link';
import QRoleList from '@/components/global/q-role-list';
import QRelateCases from '@/components/global/q-relate-cases';
import { dateFormat } from '@/utils/format';
interface ViewData {
  Title?: string;
  CaseSearchId?: string[];
  AnNo?: string;
  RoleList?: any[];
  CaseReason?: string;
  CaseReasonDescription?: string;
  PublishDate?: string | number;
  Content?: string;
  Courtname?: string;
}

const DNotice = defineComponent({
  name: 'DNoticeDetail',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      required: true,
    },
  },
  render() {
    const { viewData } = this;
    return (
      <div>
        <QPlainTable>
          <tbody>
            <tr>
              <td class="tb" width="15%">
                公告名称
              </td>
              <td width="35%">{viewData.Title || '-'}</td>
              <td class="tb" width="25%">
                案号
              </td>
              <td width="25%">
                {viewData.CaseSearchId && viewData.CaseSearchId.length !== 0 ? (
                  <QLink to={`/caseDetail/${viewData.CaseSearchId[0]}`}>
                    <span domPropsInnerHTML={viewData.AnNo || '-'}></span>
                  </QLink>
                ) : (
                  <span domPropsInnerHTML={viewData.AnNo || '-'}></span>
                )}
              </td>
            </tr>
            <tr>
              <td class="tb">当事人</td>
              <td>{viewData?.RoleList?.length && <QRoleList list={viewData.RoleList} />}</td>
              <td class="tb">案由</td>
              <td>
                {viewData.CaseReason || '-'}
                {viewData.CaseReasonDescription && (
                  <span>
                    <Tooltip placement="bottom">
                      <template slot="title">{viewData.CaseReasonDescription}</template>
                      <Icon style={{ color: '#d6d6d6' }} class="icon" type="info-circle" />
                    </Tooltip>
                  </span>
                )}
              </td>
            </tr>
            <tr>
              <td class="tb">法院</td>
              <td>{viewData.Courtname || '-'}</td>
              <td class="tb">发布日期</td>
              <td>{viewData.PublishDate ? dateFormat(viewData.PublishDate) : '-'}</td>
            </tr>
            <tr>
              <td class="tb">原文</td>
              <td colspan="3">
                <div v-entity-click domPropsInnerHTML={viewData.Content}></div>
              </td>
            </tr>
          </tbody>
        </QPlainTable>
        <QRelateCases search-params={viewData}></QRelateCases>
      </div>
    );
  },
});

export default DNotice;
