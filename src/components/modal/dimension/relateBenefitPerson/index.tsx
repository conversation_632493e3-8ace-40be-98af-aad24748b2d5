import mixins from 'vue-typed-mixins';

import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin(['current'], {
  rowKey: 'Id',
  pageSizeOptions: ['5'],
  pagination: {
    current: 1,
    pageSize: 5,
    total: 0,
  },
});
const RelateBenefitPerson = mixins(dimensionMixin, listMixin).extend({
  name: 'RelateBenefitPerson',

  props: {
    dialogProps: {
      type: Object,
    },
  },

  computed: {
    // info() {
    //   return {
    //     key: 'RelateBenefitPerson',
    //   };
    // },
    keyNo() {
      return (this as any).dialogProps.keyNo;
    },
    columns() {
      return [
        {
          title: '所在企业',
          dataIndex: 'name',
          // 对齐方式
          align: 'left',
          customRender: (text, item) => {
            return (
              <q-td-coy key-no={item.KeyNo} name={item.Name} image={item.ImageUrl}>
                {item.ShortStatus ? (
                  <q-tag slot="inlineCoy" type={item.statusClass}>
                    {item.ShortStatus}
                  </q-tag>
                ) : null}
              </q-td-coy>
            );
          },
        },
        {
          title: '受益所有人类型',
          width: '300px',
          align: 'left',
          customRender: (text, record) => {
            const list: any = [];
            record.BenefitTypeInfo.forEach((item: any) => {
              if (item.TypeDesc) {
                list.push(item.TypeDesc);
              }
            });
            return Array.from(new Set(list)).join('，');
          },
        },
        {
          title: '最终受益股份',
          width: '140px',
          align: 'center',
          dataIndex: 'StockPercent',
          customRender: (text, item) => {
            if (item.StockPercent === '0.00%') {
              return null;
            }
            return item.StockPercent;
          },
        },
      ];
    },
  },

  methods: {
    fetchDataSource({ pagination }) {
      const params = {
        keyNo: (this as any).keyNo,
        ...pagination,
      };
      return this.mDimenGetList(params, 'getRelateBenefitPerson', 'person').then((r) => {
        const res = r || {};
        return {
          ...res,
          Result: (this as any).formatData(res.Result),
          ...pagination,
        };
      });
    },
    formatData(list) {
      return list.map((item) => {
        item.area = item.Area?.Province;
        if (['注销', '吊销', '停业', '撤销', '清算', '责令关闭'].includes(item.ShortStatus)) {
          item.statusClass = 'danger';
        } else if (['筹建', '迁入', '迁出', '歇业'].includes(item.ShortStatus)) {
          item.statusClass = 'warning';
        } else {
          item.statusClass = 'success';
        }
        if (item.Status === '存续（在营、开业、在册）') {
          item.Status = '存续';
        }
        return item;
      });
    },
  },

  render() {
    const that = this as any;
    const { columns } = that;
    return <q-rich-table {...(this as any).mListGetTableData()} info={{ key: 'current' }} columns={columns} />;
  },
});

export default RelateBenefitPerson;
