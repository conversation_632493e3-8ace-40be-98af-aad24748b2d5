import dateFormat from '@/utils/format/date.ts';
import { numberToHumanWithUnit } from '@/utils/number-formatter';
import QTag from '@/components/global/q-tag';

export default {
  components: {
    QTag,
  },
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    PledgeStatus() {
      return this.getStatus(this.viewData.PledgeStatus);
    },
  },
  methods: {
    dateFormat,
    numberToHumanWithUnit,
    getStatus(status) {
      let formatStatus = status;
      if (status === '存续（在营、开业、在册）') {
        formatStatus = '存续';
      }
      return formatStatus;
    },
    getType(status) {
      let type = 'success';
      if (['注销', '吊销', '停业', '撤销', '清算', '无效', '责令关闭'].includes(status)) {
        type = 'danger';
      } else if (['筹建', '迁入', '迁出', '歇业'].includes(status)) {
        type = 'warning';
      }
      return type;
    },
  },
};
