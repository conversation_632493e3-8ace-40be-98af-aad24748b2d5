<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="20%" class="tb">登记编号</td>
        <td width="30%">{{ viewData.RegisterNo || '-' }}</td>
        <td width="20%" class="tb">状态</td>
        <td width="30%">
          <QTag v-if="PledgeStatus" :type="getType(PledgeStatus)">{{ PledgeStatus }}</QTag>
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td class="tb">出质人</td>
        <td>
          <template v-if="viewData.PledgorInfo">
            <q-entity-link
              v-if="viewData.PledgorInfo.KeyNoList && viewData.PledgorInfo.KeyNoList.length"
              :coy-arr="viewData.PledgorInfo.KeyNoList"
              :coy-maxlength="50"
            ></q-entity-link>
            <q-entity-link v-else :coy-obj="viewData.PledgorInfo"></q-entity-link>
          </template>
          <span v-else-if="viewData.Pledgor">{{ viewData.Pledgor }}</span>
          <span v-else>-</span>
        </td>
        <td class="tb">出质人证件号码</td>
        <td v-if="viewData.PledgorInfo">{{ viewData.PledgorInfo.No || '-' }}</td>
        <td v-else-if="viewData.Pledgor">{{ viewData.Pledgor.No || '-' }}</td>
        <td v-else>-</td>
      </tr>
      <tr>
        <td class="tb">出质股权数额</td>
        <td>{{ numberToHumanWithUnit(viewData.PledgeNumPub) || '-' }}</td>
        <td class="tb">质权人</td>
        <td>
          <template v-if="viewData.PledgeeInfo">
            <q-entity-link
              v-if="viewData.PledgeeInfo.KeyNoList && viewData.PledgeeInfo.KeyNoList.length"
              :coy-arr="viewData.PledgeeInfo.KeyNoList"
              :coy-maxlength="50"
            ></q-entity-link>
            <q-entity-link v-else :coy-obj="viewData.PledgeeInfo"></q-entity-link>
          </template>
          <span v-else-if="viewData.Pledgee">{{ viewData.Pledgee }}</span>
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td class="tb">出质股权标的企业</td>
        <td colspan="3">
          <q-entity-link :coy-obj="viewData.RelatedCompanyInfo"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb">质权人证件号</td>
        <td v-if="viewData.PledgeeInfo">{{ viewData.PledgeeInfo.No || '-' }}</td>
        <td v-else-if="viewData.Pledgee">{{ viewData.Pledgee.No || '-' }}</td>
        <td v-else>-</td>
        <td class="tb">股权出质登记日期</td>
        <td>{{ dateFormat(viewData.RegisterDate) }}</td>
      </tr>
      <tr v-if="viewData.DataStatus === 0">
        <td class="tb">撤销原因</td>
        <td>{{ viewData.LogoutReason || '-' }}</td>
        <td class="tb">撤销日期</td>
        <td>{{ dateFormat(viewData.LogoutDate) }}</td>
      </tr>
    </table>
  </div>
</template>
<script src="./component.js"></script>
