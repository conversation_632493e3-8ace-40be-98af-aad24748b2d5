<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>{{ viewData.Name || '-' }}</span>
        </td>
        <td width="23%" class="tb">更新日期：</td>
        <td width="27%">
          {{ new Date(viewData.CreateDate).getTime() | dateformat('YYYY-MM-DD') }}
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">变更前：</td>
        <td width="27%">{{ viewData.BeforeContent || '-' }}</td>
        <td class="tb" width="23%">变更后：</td>
        <td width="27%">{{ viewData.AfterContent || '-' }}</td>
      </tr>
      <tr v-if="showChangeRate">
        <td class="tb" width="23%">变更幅度：</td>
        <td colspan="3">{{ viewData.Extend1 || '-' }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    // 币种变更，不显示变更幅度
    showChangeRate() {
      if (this.viewData && this.viewData.Desc && this.viewData.Desc.Subtitle) {
        return !this.viewData.Desc.Subtitle.includes('币种变更');
      }
      return true;
    },
  },
};
</script>
