<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%" rowspan="5">企业公告信息</td>
        <td width="20%">企业名称</td>
        <td>
          <q-entity-link :coy-obj="{ Name: viewData.CompanyName, KeyNo: viewData.keyNo }"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td width="20%">统一社会信用代码/注册号</td>
        <td width="27%">{{ viewData.RegNoOrCreditCode || '-' }}</td>
      </tr>
      <tr>
        <td width="20%">登记机关</td>
        <td width="27%">{{ viewData.Registration || '-' }}</td>
      </tr>
      <tr>
        <td width="20%">公告期</td>
        <td>{{ viewData.PublicDate || '-' }}</td>
      </tr>
      <tr>
        <td width="20%">全体投资人承诺书</td>
        <td>
          <a v-if="viewData.DocUrl" target="_blank" :href="viewData.DocUrl">查看详情</a>
          <span v-else>-</span>
        </td>
      </tr>
      <!--异议信息-->
      <template v-for="(item, index) in viewData.DissentList">
        <tr :key="`tr-1-${index}`">
          <td v-if="index === 0" class="tb" width="20%" :rowspan="3 * viewData.DissentList.length">异议信息</td>
          <td width="20%">异议申请人</td>
          <td>{{ item.DissentPerson || '-' }}</td>
        </tr>
        <tr :key="`tr-2-${index}`">
          <td width="20%">异议时间</td>
          <td>{{ item.DissentDate || '-' }}</td>
        </tr>
        <tr :key="`tr-2-${index}`">
          <td width="20%">异议内容</td>
          <td>{{ item.DissentContent || '-' }}</td>
        </tr>
      </template>
      <!--简易注销结果-->
      <template v-for="(item, index) in viewData.CancellationResultList">
        <tr :key="`tr-1-${index}`">
          <td v-if="index === 0" class="tb" width="20%" :rowspan="2 * viewData.CancellationResultList.length">简易注销结果</td>
          <td width="20%">简易注销结果</td>
          <td>{{ item.ResultContent || '-' }}</td>
        </tr>
        <tr :key="`tr-2-${index}`">
          <td width="20%">公告申请日期</td>
          <td>{{ item.PublicDate || '-' }}</td>
        </tr>
      </template>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
