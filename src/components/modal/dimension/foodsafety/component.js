import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin(['current', { rowKey: 'checkName' }]);

export default {
  mixins: [listMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    columns() {
      return [
        {
          title: '检查项目名称',
          dataIndex: 'check_name',
        },
        {
          title: '标准值',
          dataIndex: 'check_normal',
        },
        {
          title: '检测值',
          dataIndex: 'check_result',
        },
      ];
    },
    list() {
      return this.detailParams.UnqualifiedDetails || typeof this.detailParams.AmountDesc === 'string'
        ? JSON.parse(this.detailParams.AmountDesc)
        : JSON.parse(this.detailParams.AmountDesc) || [];
    },
  },
};
