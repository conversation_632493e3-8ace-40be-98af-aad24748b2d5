<template>
  <div>
    <div class="mtcaption">地块基本信息</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%">宗地编号</th>
          <td width="27%">{{ viewData.LandNo || '-' }}</td>
          <th width="23%">土地位置</th>
          <td width="27%">{{ viewData.Address || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">土地面积(公顷)</th>
          <td width="27%">{{ viewData.Acreage || '-' }}</td>
          <th width="23%">土地用途</th>
          <td width="27%">{{ viewData.Purpose || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">成交价格(万元)</th>
          <td width="27%">{{ viewData.TransactionPrice || '-' }}</td>
          <th width="23%">出让年限</th>
          <td width="27%">{{ viewData.SellYears || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">项目名称</th>
          <td width="27%">{{ viewData.ProjectName || '-' }}</td>
          <th width="23%">受让单位</th>
          <td width="27%">
            <template v-if="viewData.AssigneeUnit">
              <q-entity-link :coy-obj="{ KeyNo: viewData.AssigneeUnit.KeyNo, Name: viewData.AssigneeUnit.Name }"></q-entity-link>
            </template>
            <template v-else>-</template>
          </td>
        </tr>
        <tr>
          <th width="23%">备注</th>
          <td width="27%">{{ viewData.Remarks || '-' }}</td>
          <th width="23%">公示期</th>
          <td width="27%">{{ viewData.PublishTerm || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">发布机关</th>
          <td width="27%">{{ viewData.PublishGov || '-' }}</td>
          <th width="23%">发布日期</th>
          <td width="27%">{{ viewData.PublishDate | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <th>意见反馈方式</th>
          <td colspan="5">{{ viewData.Explains || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <div class="mtcaption">联系方式</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%">联系单位</th>
          <td width="27%">{{ viewData.ContactUnit || '-' }}</td>
          <th width="23%">单位地址</th>
          <td width="27%">{{ viewData.UnitAddress || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">邮政编码</th>
          <td width="27%">{{ viewData.PostCode || '-' }}</td>
          <th width="23%">联系电话</th>
          <td width="27%">{{ viewData.ContactNumber || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">联系人</th>
          <td width="27%">{{ viewData.ContactPerson || '-' }}</td>
          <th width="23%">电子邮件</th>
          <td width="27%">{{ viewData.Email || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
  </div>
</template>

<script src="./component.js"></script>
<style lang="less" scoped>
.mtcaption {
  margin: 16px 0 12px 0;
  &:first-child {
    margin-top: 0;
  }
}
</style>
