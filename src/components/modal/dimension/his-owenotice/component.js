import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';
import { dateFormat } from '@/utils/format';

const listMixin = createMixin(['current']);

export default {
  mixins: [dimensionMixin, listMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      pagination: {
        pageIndex: 1,
        current: 1,
        pageSize: 5,
        total: 0,
        onChange: this.pageChange,
        onShowSizeChange: this.pageChange,
      },
      totalCount: 0,
    };
  },
  computed: {
    columns() {
      return [
        {
          title: '欠税税种',
          key: 'titleType',
          scopedSlots: { customRender: 'titleType' },
          width: '18%',
          align: 'left',
        },
        {
          title: '欠税金额(元)',
          dataIndex: 'Amount',
          width: '15%',
          align: 'center',
        },
        {
          title: '新增欠税金额(元)',
          width: '18%',
          align: 'center',
          customRender: (index, record) => {
            return record.NewAmount || '0';
          },
        },
        { title: '发布单位', dataIndex: 'IssuedBy', width: '20%', align: 'center' },
        {
          title: '发布日期',
          dataIndex: 'PublishDate',
          scopedSlots: { customRender: 'date' },
        },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, width: 60, align: 'center' },
      ];
    },
  },

  methods: {
    fetchDataSource() {
      return this.mDimenGetList(
        {
          keyNo: this.detailParams.keyNo,
          groupId: this.detailParams.groupId,
          // 不需要这个参数
          // isValid: 0,
          ...this.pagination,
        },
        'owenoticelist'
      ).then((data) => {
        this.totalCount = data?.Paging?.TotalRecords || 0;
        this.pagination.total = this.totalCount;
        return data;
      });
    },
    pageChange(current, pageSize) {
      this.pagination.current = current;
      this.pagination.pageIndex = current;
      this.pagination.pageSize = pageSize;
      this.refresh();
    },
    refresh() {
      this.mListResetModule(this.mListActiveKey);
      this.mListInit(this.mListActiveKey);
    },
    showDetail(item) {
      this.$modal.showDimension('owenotice', { id: item.Id });
    },
  },
};
