<template>
  <tr v-if="viewData.Extend1">
    <td class="tb">股权链</td>
    <!-- Category === 114 双层 -->
    <td v-if="viewData.Category === 114" colspan="3" class="td-with-path">
      <template v-if="viewData.Extend1.length">
        <template v-for="(item, p) in viewData.Extend1">
          <div v-for="(obj, index) in item" :key="`${p}${index}`">
            <div><q-entity-link :coy-obj="{ KeyNo: obj.KeyNo, Name: obj.Name || '-' }" />，受益股份{{ obj.PercentTotal }}</div>
            <div v-for="(v, k) in obj.Paths" :key="`${index}-${k}`">
              <div>股权路径{{ k + 1 }}（占比{{ v[0].PercentTotal }}）</div>
              <div>
                <span v-for="(vc, ki) in v" :key="`${index}-${k}-${ki}`">
                  <q-entity-link :coy-obj="{ KeyNo: vc.KeyNo, Name: vc.Name || '-' }" />
                  <span v-if="ki < v.length - 1" :class="['arrow', !vc.Percent && 'arrow-no-text']">{{ vc.Percent }}</span>
                </span>
              </div>
            </div>
          </div>
        </template>
      </template>
      <span v-else>-</span>
    </td>
    <!-- other origin -->
    <td v-else colspan="3" class="td-with-path">
      <template v-if="viewData.Extend1.length">
        <div v-for="(item, index) in viewData.Extend1" :key="index">
          <div><q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />，受益股份{{ item.PercentTotal }}</div>
          <div v-for="(v, k) in item.Paths" :key="`${index}-${k}`">
            <div>股权路径{{ k + 1 }}（占比{{ v[0].PercentTotal }}）</div>
            <div>
              <span v-for="(vc, ki) in v" :key="`${index}-${k}-${ki}`">
                <q-entity-link :coy-obj="{ KeyNo: vc.KeyNo, Name: vc.Name || '-' }" />
                <span v-if="ki < v.length - 1" :class="['arrow', !vc.Percent && 'arrow-no-text']">{{ vc.Percent }}</span>
              </span>
            </div>
          </div>
        </div>
      </template>
      <span v-else>-</span>
    </td>
  </tr>
</template>

<script src="./component.js"></script>
