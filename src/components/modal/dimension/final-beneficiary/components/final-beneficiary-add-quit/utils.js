import _ from 'lodash';

export const isShowChangeReason = (item, isPeople, type) => {
  let bool = false;
  const firstBeforeBenefitType = _.head(item.beforeBenefitType);
  const firstAfterBenefitType = _.head(item.afterBenefitType);
  const addBoolByAfter = isPeople ? firstAfterBenefitType === '1' : firstAfterBenefitType !== '2';
  if (type === 'quit') {
    if (item.beforeBenefitType?.length && item.afterBenefitType?.length) {
      bool =
        firstBeforeBenefitType === '1' && ((!isPeople && (firstAfterBenefitType === '1' || firstAfterBenefitType === '2')) || isPeople);
    } else if (item.beforeBenefitType?.length) {
      bool = firstBeforeBenefitType === '1';
    }
  } else {
    if (item.beforeBenefitType?.length && item.afterBenefitType?.length) {
      bool = firstBeforeBenefitType === '1' || addBoolByAfter;
    } else if (item.afterBenefitType?.length) {
      bool = firstAfterBenefitType === '1';
    }
  }
  return bool;
};
