/*
 * Created by <PERSON> on - 2024/12/11.
 */

import _ from 'lodash';
import { isShowChangeReason } from './utils';

export default {
  name: 'final-beneficiary-add-quit',

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },

    /** 是否人员动态 */
    isPeople: {
      type: Boolean,
      default: false,
    },

    /** 类型 默认：add 可选值：quit, change */
    type: {
      type: String,
      default: 'add',
    },
  },

  computed: {
    isEveryNoReason() {
      return this.dataSource.every((el) => !this.isShowChangeReason(el));
    },
  },

  methods: {
    /** 是否展示变更原因 */
    isShowChangeReason(item) {
      if (this.isPeople) {
        return this.dataSource.every((el) => isShowChangeReason(el, this.isPeople, this.type));
      }
      return isShowChangeReason(item, this.isPeople, this.type);
    },

    /** 获取变更理由 */
    getChangeReason(item) {
      let reasonDesc = '';
      const shareholderDesc = item.PathLength > 1 ? '间接' : '';
      const firstAfterBenefitType = _.head(item.afterBenefitType);
      if (this.type === 'quit') {
        reasonDesc = `作为${shareholderDesc}股东退出，退出前最终受益股份为<span>${item.PercentTotal}</span>`;
      } else if (firstAfterBenefitType === '2') {
        reasonDesc = '未能穿透识别出拥有或超过25%公司股权或者表决权的自然人，将其视同为受益所有人';
      } else {
        reasonDesc = `作为${shareholderDesc}股东，最终受益股份增加至<span>${item.PercentTotal}</span>`;
      }
      return reasonDesc;
    },

    /** 获取主体描述 */
    getTargetDesc(keyNo) {
      return keyNo.startsWith('p') || !keyNo ? '人员' : '企业';
    },

    /** 获取主体 */
    getTarget(item) {
      return _.pick(item, ['KeyNo', 'Name', 'Org']);
    },
  },
};
