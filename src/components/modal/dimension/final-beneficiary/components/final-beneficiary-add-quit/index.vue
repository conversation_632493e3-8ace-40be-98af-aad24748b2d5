<template>
  <div class="add-quit-info">
    <div
      v-for="(item, index) in dataSource"
      :key="`${index}_${item.KeyNo}`"
      :class="['sec-item-wrapper', isPeople && 'sec-item-people-wrapper', isPeople && index < dataSource.length - 1 && 'dashed-line']"
    >
      <div v-if="!isPeople" class="sec-item">
        <div class="change-target">
          <q-entity-link :coy-obj="getTarget(item)" disable-class="disable2" />
        </div>
        <div v-if="type === 'change'" class="change-reason" v-html="item.changeDesc || '-'" />
        <div v-else class="change-reason" v-html="getChangeReason(item)" />
      </div>
      <div v-else class="sec-item-people">
        <div>{{ type === 'quit' ? '退出' : '成为' }}<q-entity-link :coy-obj="getTarget(item)" disable-class="disable2" />的受益所有人</div>
        <div v-if="isShowChangeReason(item)" class="change-reason" v-html="getChangeReason(item)" />
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" scoped src="./style.less"></style>
