<template>
  <section>
    <!-- Category: 114 -->
    <template v-if="viewData.Category === 114">
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 13%">变更企业</td>
          <td style="width: 37%">
            <q-entity-link :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          </td>
          <td class="tb" style="width: 13%">更新时间<q-glossary-info info-id="241" /></td>
          <td style="width: 37%">
            {{ viewData.ChangeDate ? viewData.ChangeDate.split(' ')[0] : '-' }}
          </td>
        </tr>
        <template v-if="isOldData">
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">
              <div class="font-bold" v-html="highlightContent" />
            </td>
          </tr>
        </template>
        <template v-else>
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">
              <div
                v-if="highlightContentArr[0]"
                :class="['font-bold', (!isHideChangeReasonBy114 || highlightContentArr.length > 1) && 'highlight-wrap']"
                v-html="highlightContentArr[0]"
              />
              <div class="highlight-sub">
                <template v-if="!isHideChangeReasonBy114">
                  <final-beneficiary-add-quit
                    v-if="extend1AddInfo.length"
                    :is-people="extend1Object.IsPeople"
                    :data-source="extend1AddInfo"
                  />
                  <final-beneficiary-add-quit
                    v-if="extend1QuitInfo.length"
                    :is-people="extend1Object.IsPeople"
                    type="quit"
                    :data-source="extend1QuitInfo"
                  />
                </template>
                <final-beneficiary-add-quit
                  v-if="highlightContentArr.length > 1 && changeExtend.length"
                  :is-people="extend1Object.IsPeople"
                  type="change"
                  :data-source="changeExtend"
                />
              </div>
            </td>
          </tr>
        </template>
      </table>
    </template>
    <!-- Category: 241 -->
    <template v-else-if="viewData.Category === 241">
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 13%">变更人员</td>
          <td style="width: 37%">
            <q-entity-link :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          </td>
          <td class="tb" style="width: 13%">更新时间<q-glossary-info info-id="241" /></td>
          <td style="width: 37%">
            {{ viewData.ChangeDate ? viewData.ChangeDate.split(' ')[0] : '-' }}
          </td>
        </tr>
        <tr v-if="isOldData">
          <td class="tb">变更内容</td>
          <td colspan="3">
            <template v-if="changeExtend.length">
              <final-beneficiary-add-quit :is-people="true" :type="changeStatus === 1 ? 'add' : 'quit'" :data-source="changeExtend" />
            </template>
            <div v-else v-html="highlightContent" />
          </td>
        </tr>
        <tr v-if="!isOldData && (extend1AddInfo.length || extend1QuitInfo.length)">
          <td class="tb">变更内容</td>
          <td colspan="3">
            <final-beneficiary-add-quit v-if="extend1AddInfo.length" :is-people="extend1Object.IsPeople" :data-source="extend1AddInfo" />
            <final-beneficiary-add-quit
              v-else-if="extend1QuitInfo.length"
              :is-people="extend1Object.IsPeople"
              type="quit"
              :data-source="extend1QuitInfo"
            />
          </td>
        </tr>
      </table>
    </template>
    <!-- 其他Category: 21、210，默认展示 -->
    <table v-else class="ntable">
      <tr>
        <td class="tb" style="width: 23%">变更企业</td>
        <td style="width: 27%">
          <template v-if="!isPerson(viewData.KeyNo)">
            <q-entity-link :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          </template>
          <template v-else>
            <q-entity-link :coy-obj="{ KeyNo: changeExtend.KeyNo, Name: changeExtend.Name }" />
          </template>
        </td>
        <td class="tb" style="width: 23%">更新时间<q-glossary-info info-id="241" /></td>
        <td style="width: 27%">
          {{ viewData.ChangeDate ? viewData.ChangeDate.split(' ')[0] : '-' }}
        </td>
      </tr>
      <tr v-if="viewData.DataType === 1">
        <td class="tb">变更前</td>
        <td>
          <template v-if="viewData.Enumeration === 1">
            <q-entity-link v-if="beforeContent && beforeContent.length" :coy-arr="beforeContent" />
            <span v-else>-</span>
          </template>
          <template v-else>
            <q-entity-link v-if="beforeContent" :coy-obj="{ KeyNo: beforeContent.KeyNo, Name: beforeContent.Name }" />
            <span v-else>-</span>
            <span v-if="beforeContent && beforeContent.PercentTotal"> ，受益股份{{ beforeContent.PercentTotal }} </span>
          </template>
        </td>
        <td class="tb">变更后</td>
        <td>
          <template v-if="viewData.Enumeration === 1">
            <q-entity-link v-if="afterContent && afterContent.length" :coy-arr="afterContent" />
            <span v-else>-</span>
          </template>
          <template v-else>
            <q-entity-link v-if="afterContent" :coy-obj="{ KeyNo: afterContent.KeyNo, Name: afterContent.Name }" />
            <span v-else>-</span>
            <span v-if="afterContent && afterContent.PercentTotal"> ，受益股份{{ afterContent.PercentTotal }} </span>
          </template>
        </td>
      </tr>
      <final-beneficiary-stock-chain v-if="!isShowJudgementList" :view-data="viewData" />
    </table>
  </section>
</template>

<script>
import _ from 'lodash';

import { getRiskContent } from '@/utils/content-helper';

import { isShowChangeReason } from './components/final-beneficiary-add-quit/utils';
import finalBeneficiaryAddQuit from './components/final-beneficiary-add-quit';
import finalBeneficiaryStockChain from './components/final-beneficiary-stock-chain';

export default {
  name: 'FinalBeneficiary',

  components: {
    [finalBeneficiaryAddQuit.name]: finalBeneficiaryAddQuit,
    [finalBeneficiaryStockChain.name]: finalBeneficiaryStockChain,
  },

  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      /** Category 241 */
      changeStatus: 1,
    };
  },

  computed: {
    changeExtend() {
      const { ChangeExtend, Category } = this.viewData;
      let ce = {};
      try {
        if (ChangeExtend && _.isString(ChangeExtend)) {
          ce = JSON.parse(ChangeExtend);
        }
      } catch (e) {}
      if (Category === 114) {
        if (_.isEmpty(ce)) {
          ce = [];
        }
        ce = this.formatCeListBy114(ce);
      } else if (Category === 241) {
        if (_.isEmpty(ce)) {
          ce = [];
        }
        ce = this.formatCeList(ce);
      }
      return ce;
    },

    beforeContent() {
      return this.viewData.BeforeContent ? JSON.parse(this.viewData.BeforeContent) : null;
    },

    afterContent() {
      return this.viewData.AfterContent ? JSON.parse(this.viewData.AfterContent) : null;
    },

    extend1List() {
      return this.viewData?.Extend1 || [];
    },

    isShowJudgementList() {
      const percentTotalList = this.getValueListByExtend1('PercentTotal', '');
      return _.compact(percentTotalList).length === 0;
    },

    /** 是否老数据 */
    isOldData() {
      if ([114, 241].includes(this.viewData?.Category)) {
        return _.isArray(this.viewData?.Extend1) || !this.viewData?.Extend1;
      }
      return true;
    },

    /** extend1对象 */
    extend1Object() {
      return _.pick(this.viewData?.Extend1, ['IsPeople', 'AddInfo', 'QuitInfo']);
    },

    /** extend1新增信息 */
    extend1AddInfo() {
      const beforeBenefitType = _.get(_.head(this.extend1Object.QuitInfo), 'BenefitType', []);
      return _.map(this.extend1Object.AddInfo || [], (item) => {
        return {
          ...item,
          beforeBenefitType,
          afterBenefitType: item.BenefitType,
        };
      });
    },

    /** extend1退出信息 */
    extend1QuitInfo() {
      const afterBenefitType = _.get(_.head(this.extend1Object.AddInfo), 'BenefitType', []);
      return _.map(this.extend1Object.QuitInfo || [], (item) => {
        return {
          ...item,
          beforeBenefitType: item.BenefitType,
          afterBenefitType,
        };
      });
    },

    /** 动态内容 */
    highlightContent() {
      let content = this.viewData.Desc?.Content;
      const highlights = this.viewData.Desc?.Highlight;

      content = getRiskContent({
        Content: content,
        Category: this.viewData.Category,
        Highlight: highlights,
      });

      return content;
    },

    /** 动态内容拆分 */
    highlightContentArr() {
      const contentArr = this.viewData.Desc?.ContentArray;
      const highlights = this.viewData.Desc?.Highlight;
      if (_.isArray(contentArr) && contentArr.length) {
        const changeContentObj = contentArr.find((el) => _.startsWith(el?.Value, '从'));
        const percentContentList = contentArr.filter((el) => _.includes(el?.Value, '最终受益股份'));
        const result = [];
        if (changeContentObj) {
          let changeContent = changeContentObj.Value;
          changeContent = getRiskContent({
            Content: changeContent,
            Category: this.viewData.Category,
            Highlight: highlights,
          });
          result.push(changeContent);
        }
        if (percentContentList.length) {
          const percentContentArr = [];
          percentContentList.forEach((el) => {
            let content = el.Value;
            content = getRiskContent({
              Content: content,
              Category: this.viewData.Category,
              Highlight: highlights,
            });
            if (content?.length) {
              percentContentArr.push(content);
            }
          });
          if (percentContentArr.length) {
            result.push(percentContentArr.join('<br/>'));
          }
        }
        return result;
      }
      return [];
    },

    /** 114 是否隐藏变更理由 */
    isHideChangeReasonBy114() {
      if (this.viewData.Category === 114 && !this.isOldData) {
        if (this.extend1AddInfo.length || this.extend1QuitInfo.length) {
          const isHideByAdd = this.extend1AddInfo.length
            ? this.extend1AddInfo.some((el) => !isShowChangeReason(el, this.extend1Object.IsPeople))
            : false;
          const isHideByQuit = this.extend1QuitInfo.length
            ? this.extend1QuitInfo.some((el) => !isShowChangeReason(el, this.extend1Object.IsPeople, 'quit'))
            : false;
          return isHideByAdd || isHideByQuit;
        }
        return true;
      }
      return false;
    },
  },

  mounted() {
    if (this.isOldData) {
      /** Category 241 */
      if (this.viewData.Category === 241 && this.changeExtend?.length) {
        this.changeStatus = this.changeExtend[0].ChangeStatus;
      }
    } else if (this.viewData.Category === 114) {
      this.$nextTick(() => {
        setTimeout(() => {
          const changeTargetDoms = $('.add-quit-info .change-target');
          if (changeTargetDoms.length) {
            const maxWidth = _.max($.map(changeTargetDoms, (el) => $(el).width()));
            changeTargetDoms.each((index, el) => {
              if ($(el).width() < maxWidth) {
                $(el).width(maxWidth);
              }
            });
          }
        }, 300);
      });
    }
  },

  methods: {
    isPerson(keyNo) {
      return _.startsWith(keyNo, 'p');
    },

    parseObjectStr(str) {
      let obj;
      if (_.isString(str) && /^\{.*\}$/.test(str)) {
        try {
          obj = JSON.parse(str);
        } catch (e) {}
      }
      return obj;
    },

    formatCeListBy114(ce) {
      const result = [];
      if (ce.length) {
        ce.forEach((el) => {
          const changeExtendObj = this.parseObjectStr(el.ChangeExtend);
          if (Number(changeExtendObj?.T) === 2) {
            const afterContentObj = this.parseObjectStr(el.AfterContent);
            const beforeContentObj = this.parseObjectStr(el.BeforeContent);
            if (afterContentObj && beforeContentObj) {
              const target = _.pick(beforeContentObj, ['KeyNo', 'Name', 'Org']);
              const beforePercent = _.get(beforeContentObj, 'PercentTotal', '');
              const afterPercent = _.get(afterContentObj, 'PercentTotal', '');
              const increaseDesc = Number(changeExtendObj?.Increase) === 1 ? '上升' : '下降';
              const desc = `最终受益股份从<span>${beforePercent}</span>${increaseDesc}到<span>${afterPercent}</span>`;
              result.push({
                ...target,
                changeDesc: desc,
              });
            }
          }
        });
      }
      return result;
    },

    formatCeList(ce) {
      const result = [];
      if (ce.length) {
        ce.forEach((el) => {
          let item = {
            ChangeStatus: el.ChangeStatus,
            BenefitType: [],
            PathLength: 0,
            Percent: '',
            PercentTotal: '',
            beforeBenefitType: [],
            afterBenefitType: [],
          };
          if (el.ChangeExtend && _.isString(el.ChangeExtend)) {
            try {
              const obj = JSON.parse(el.ChangeExtend);
              if (!_.isEmpty(obj)) {
                item = {
                  ...item,
                  ..._.pick(obj, ['KeyNo', 'Name', 'Org']),
                };
                item.PercentTotal = obj.PercentTotal;
              }
            } catch (e) {}
          }
          if (item.KeyNo || item.Name) {
            result.push(item);
          }
        });
      }
      return result;
    },

    getValueListByExtend1(key, defaultValue) {
      const list = this.extend1List;
      const returnList = [];
      _.forEach(list, (subList) => {
        if (_.isArray(subList) && subList.length) {
          _.forEach(subList, (el) => {
            const target = _.get(el, key, defaultValue || '');
            if (!_.isEmpty(target)) {
              returnList.push(target);
            }
          });
        } else if (_.isObject(subList) && _.has(subList, key)) {
          const target = _.get(subList, key, defaultValue || '');
          if (!_.isEmpty(target)) {
            returnList.push(target);
          }
        }
      });
      return returnList;
    },
  },
};
</script>
