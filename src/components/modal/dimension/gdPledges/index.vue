<template>
  <div>
    <div v-for="(vo, index) in viewData" :key="`gdPledges_${index}`">
      <table class="ntable">
        <tr>
          <td width="20%" class="tb">登记编号</td>
          <td width="30%">{{ vo.RegistNo || '-' }}</td>
          <td width="20%" class="tb">状态</td>
          <td width="30%">{{ vo.Status || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">出质人</td>
          <td>
            <q-entity-link :coy-obj="vo.PledgorInfo || vo.Pledgor"></q-entity-link>
          </td>
          <td class="tb">出质人证件号码</td>
          <td v-if="vo.PledgorInfo">{{ vo.PledgorInfo.No || '-' }}</td>
          <td v-else-if="vo.Pledgor">{{ vo.Pledgor.No || '-' }}</td>
          <td v-else>-</td>
        </tr>
        <tr>
          <td class="tb">出质股权数额</td>
          <td>{{ vo.PledgedAmount || '-' }}</td>
          <td class="tb">出质股权标的企业</td>
          <td>
            <q-entity-link :coy-obj="vo.RelatedCompanyInfo"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb">质权人</td>
          <td colspan="3">
            <q-entity-link :coy-obj="vo.PledgeeInfo || vo.Pledgee"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb">质权人证件号</td>
          <td v-if="vo.PledgeeInfo">{{ vo.PledgeeInfo.No || '-' }}</td>
          <td v-else-if="vo.Pledgee">{{ vo.Pledgee.No || '-' }}</td>
          <td v-else>-</td>
          <td class="tb">股权出质登记日期</td>
          <td>{{ vo.RegDate | dateformat('YYYY-MM-DD') }}</td>
        </tr>
      </table>
    </div>
  </div>
</template>
<script src="./component.js"></script>
