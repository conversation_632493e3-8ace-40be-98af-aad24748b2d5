import moment from 'moment';
import { computed, defineComponent } from 'vue';

const DecreaseCapiNotice = defineComponent({
  name: 'DecreaseCapiNotice',
  props: {
    viewData: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const dataSource = computed(() => props.viewData[0] || {});
    return {
      dataSource,
    };
  },
  render() {
    const { dataSource } = this as any;
    return (
      <q-plain-table>
        <tbody>
          <tr>
            <td class="tb" style="width: 180px">
              做出决定日期
            </td>
            <td>{moment(dataSource.DecideDate * 1000).format('YYYY-MM-DD')}</td>
            <td class="tb" style="width: 180px">
              公告日期
            </td>
            <td>{moment(dataSource.NoticeDate * 1000).format('YYYY-MM-DD')}</td>
          </tr>
          <tr>
            <td class="tb">公告期限</td>
            <td colspan="3">{dataSource.NoticePeriod || '-'}</td>
          </tr>
          <tr>
            <td class="tb">公告内容</td>
            <td colspan="3">{dataSource.Content || '-'}</td>
          </tr>
          <tr>
            <td class="tb">联系地址</td>
            <td colspan="3">{dataSource.Address || '-'}</td>
          </tr>
          <tr>
            <td class="tb">联系人</td>
            <td>{dataSource.Contactor || '-'}</td>
            <td class="tb">联系电话</td>
            <td>{dataSource.ContactNumber || '-'}</td>
          </tr>
        </tbody>
      </q-plain-table>
    );
  },
});

export default DecreaseCapiNotice;
