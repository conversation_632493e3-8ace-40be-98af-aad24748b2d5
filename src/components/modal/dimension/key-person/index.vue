<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%">
          <q-entity-link
            v-if="viewData.KeyNo"
            :coy-obj="{
              KeyNo: viewData.KeyNo,
              Name: viewData.Name || '-',
            }"
          />
          <span v-else-if="viewData.Name">
            {{ viewData.Name }}
          </span>
          <span v-else>-</span>
        </td>
        <td width="23%" class="tb">变更日期<q-glossary-info info-id="241" />：</td>
        <td width="27%">
          {{ viewData.ChangeDate ? new Date(viewData.ChangeDate) : '-' }}
        </td>
      </tr>
      <tr v-if="viewData.BeforeContent && viewData.AfterContent">
        <td class="tb" width="23%">职务调整：</td>
        <td colspan="3">
          <q-entity-link
            v-if="viewData.KeyNo"
            :coy-obj="{
              KeyNo: viewData.KeyNo,
              Name: viewData.Name || '-',
            }"
          />
          <span v-else>{{ viewData.Name || '-' }}</span>
          <span>，从{{ viewData.BeforeContent }}调整为{{ viewData.AfterContent }}</span>
        </td>
      </tr>
      <tr v-if="viewData.ExitObject && viewData.ExitObject.length">
        <td class="tb" width="23%">退出：</td>
        <td colspan="3">
          <span v-for="(item, index) in viewData.ExitObject" :key="index">
            <q-entity-link
              v-if="item.KeyNo"
              :coy-obj="{
                KeyNo: item.KeyNo,
                Name: item.Name || '-',
              }"
            />
            <span v-else>{{ item.Name || '-' }}</span>
            <span>，退出前职务：{{ item.StockPercent }}</span>
            <br v-if="index + 1 !== viewData.ExitObject.length" />
          </span>
        </td>
      </tr>
      <tr v-if="viewData.AddObject && viewData.AddObject.length">
        <td class="tb" width="23%">新增：</td>
        <td colspan="3">
          <span v-for="(item, index) in viewData.AddObject" :key="index">
            <q-entity-link
              v-if="item.KeyNo"
              :coy-obj="{
                KeyNo: item.KeyNo,
                Name: item.Name || '-',
              }"
            />
            <span v-else>{{ item.Name || '-' }}</span>
            <span>，退出前职务：{{ item.StockPercent }}</span>
            <br v-if="index + 1 !== viewData.AddObject.length" />
          </span>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
