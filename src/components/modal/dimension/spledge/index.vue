<template>
  <div>
    <!-- 更新：不依赖于dialogProps传值，因其不同之处只有 质押人 栏目 -->
    <table class="ntable">
      <tr>
        <td width="20%" class="tb">质押人</td>
        <td colspan="3">
          <a v-if="viewData.KeyNo && viewData.Org === 6" target="_blank" :href="`/simu_${viewData.KeyNo}`">
            <span v-html="viewData.Name || '-'"></span>
          </a>
          <q-entity-link v-else-if="viewData.KeyNo" :coy-obj="{ Name: viewData.Name, KeyNo: viewData.KeyNo }"></q-entity-link>
          <span v-else v-html="viewData.Name || '-'"></span>
        </td>
      </tr>
      <tr>
        <td width="20%" class="tb">质押人参股企业</td>
        <td colspan="3">
          <q-entity-link :coy-obj="{ KeyNo: viewData.CompanyKeyNo, Name: viewData.CompanyName }"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb" width="20%">质押股份数量(股)</td>
        <td width="30%">{{ viewData.ShareFrozenNum || '-' }}</td>
        <td class="tb" width="20%">质押股份市值(元)</td>
        <td width="30%">{{ viewData.SZ || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">占所持股份比例</td>
        <td width="30%">{{ viewData.FrozenRatio || '-' }}</td>
        <td class="tb" width="20%">占总股本比例</td>
        <td width="30%">{{ viewData.FrozeninTotal || '-' }}</td>
      </tr>
      <tr>
        <td width="20%" class="tb">质押权人</td>
        <td colspan="3">
          <q-entity-link :coy-obj="{ KeyNo: viewData.JgKeyNo, Name: viewData.JgName }"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td width="20%" class="tb">质押原因</td>
        <td colspan="3">{{ viewData.FrozenReason || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">质押目的</td>
        <td width="30%">{{ viewData.Pledgepur || '-' }}</td>
        <td class="tb" width="20%">质押日收盘价(元)</td>
        <td width="30%">{{ viewData.SPJ || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">预警线(估算)</td>
        <td width="30%">{{ viewData.YJX || '-' }}</td>
        <td class="tb" width="20%">平仓线(估算)</td>
        <td width="30%">{{ viewData.PCX || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">质押开始日期</td>
        <td width="30%">{{ viewData.StartDate || '-' }}</td>
        <td class="tb" width="20%">质押解除日期</td>
        <td width="30%">{{ viewData.EndDate || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">状态</td>
        <td width="30%">{{ viewData.Type || '-' }}</td>
        <td class="tb" width="20%">公告日期</td>
        <td width="30%">{{ viewData.NoticeDate || '-' }}</td>
      </tr>
    </table>
  </div>
</template>
<script src="./component.js"></script>
