<template>
  <div class="app-relat-company">
    <q-plain-table v-if="viewData" class="ntable">
      <tbody>
        <tr>
          <td class="tb" width="23%">股东名称</td>
          <td colspan="3">
            <q-td-coy
              :key-no="viewData.PartnerId"
              :name="viewData.PartnerName"
              :image="viewData.PartnerImageUrl"
              :company-count="viewData.RelatedCount"
              :no-click="true"
              :show-war-link="true"
              :long-td="true"
              :tags="viewData.Tags"
            ></q-td-coy>
          </td>
        </tr>
        <tr>
          <td class="tb" width="23%">参股公司</td>
          <td colspan="3">
            <q-entity-link :coy-obj="{ KeyNo: viewData.CompanyKeyNo, Name: viewData.CompanyName }"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td width="23%" class="tb">参股比例</td>
          <td width="27%">{{ viewData.Proportion || '-' }}</td>

          <template v-if="viewData.PartnerOrg === 2">
            <td width="23%" class="tb">是否为高管</td>
            <td width="27%">{{ viewData.IsManager ? '是' : '否' }}</td>
          </template>

          <template v-else>
            <td width="23%" class="tb">股东类型</td>
            <td width="27%">{{ viewData.StockType || '-' }}</td>
          </template>
        </tr>
        <tr v-if="viewData.PartnerOrg !== 2">
          <td width="23%" class="tb">关联产品</td>
          <td width="27%">
            <q-td-coy
              v-if="viewData.Product"
              :key-no="viewData.Product.Id"
              :name="viewData.Product.Name"
              :org="99"
              :image="viewData.Product.Logo"
            ></q-td-coy>
            <span v-else>-</span>
          </td>
          <td width="23%" class="tb">关联机构</td>
          <td width="27%">
            <q-td-coy
              v-if="viewData.Invest"
              :key-no="viewData.Invest.Id"
              :name="viewData.Invest.Name"
              :org="13"
              :image="viewData.Invest.Logo"
            ></q-td-coy>
            <span v-else>-</span>
          </td>
        </tr>
      </tbody>
    </q-plain-table>

    <template v-if="chartOption">
      <div class="mtcaption" style="margin-top: 15px">持股变化</div>
      <q-echarts :option="chartOption" height="160px" width="928px"></q-echarts>
    </template>
    <template v-if="viewData && viewData.Partners && viewData.Partners.length">
      <div class="mtcaption">出资情况</div>
      <q-plain-table class="ntable">
        <tbody>
          <template v-for="(item, index) in viewData.Partners">
            <tr :key="`Partners1_${index}`">
              <td width="23%" class="tb">认缴出资额</td>
              <td width="27%">
                <span v-if="item.ShouldCapi" v-html="item.ShouldCapi.split(',').join(',<br>')"></span>
                <template v-else>-</template>
              </td>
              <td width="23%" class="tb">认缴出资日期</td>
              <td width="27%">
                <span v-if="item.ShouldDate" v-html="item.ShouldDate.split(',').join(',<br>')"></span>
                <template v-else>-</template>
              </td>
            </tr>
            <tr :key="`Partners2_${index}`">
              <td width="23%" class="tb">实缴出资额</td>
              <td width="27%">
                <span v-if="item.RealCapi" v-html="item.RealCapi.split(',').join(',<br>')"></span>
                <template v-else>-</template>
              </td>
              <td width="23%" class="tb">实缴出资日期</td>
              <td width="27%">
                <span v-if="item.RealCapiDate" v-html="item.RealCapiDate.split(',').join(',<br>')"></span>
                <template v-else>-</template>
              </td>
            </tr>
          </template>
        </tbody>
      </q-plain-table>
    </template>
  </div>
</template>
<script src="./component.js"></script>
