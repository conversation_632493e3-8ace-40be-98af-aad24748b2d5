/**
 * desc: 持股详情
 */
import moment from 'moment';

import dateFormat from '@/utils/format/date.ts';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      chartOption: null,
    };
  },
  created() {
    this.drawChart();
  },
  methods: {
    dateformat(date, options) {
      return dateFormat(date, options);
    },
    drawChart() {
      const list = this.viewData.StockPercentChangeList?.[0]?.l;
      if (list?.length) {
        const xData = [];
        const yData = [];
        list.forEach((v) => {
          if (v.d === 0) {
            xData.push({
              d: '更早',
              r: v.r,
              s: v.s,
            });
          } else {
            xData.push({
              d: moment(v.d * 1000).format('YYYY-MM-DD'),
              r: v.r,
              s: v.s,
            });
          }
          yData.push(parseFloat(v.p));
        });
        const option = {
          grid: {
            top: 60,
            right: 50,
            bottom: 55,
            left: 50,
          },
          color: ['#67aef5'],
          dataZoom: [
            {
              type: 'inside',
              minValueSpan: 7,
            },
          ],
          xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#eee',
                type: 'solid',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: '#333333',
              },
              interval: 0,
              formatter: (v, i) => {
                v = xData[i];
                let html = v.d;
                if (v.r) {
                  v.r = v.r.replace('人民币', '');
                  html += `\n注册资本：${v.r}`;
                }
                if (v.s) {
                  v.s = v.s.replace('人民币', '');
                  html += `\n认缴资本：${v.s}`;
                }
                return html;
              },
            },
            silent: false,
            triggerEvent: true,
            data: xData,
          },
          yAxis: {
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          series: [
            {
              name: '参股比例：',
              type: 'bar',
              symbolSize: 6,
              barWidth: 25,
              symbol: 'circle',
              label: {
                normal: {
                  textStyle: {
                    color: '#333333',
                  },
                  position: 'top',
                  show: true,
                  formatter: '{c}%',
                },
              },
              data: yData,
            },
          ],
        };
        if (list.length >= 5) {
          option.dataZoom[0].start = 50;
        }
        this.chartOption = option;
      }
    },
  },
};
