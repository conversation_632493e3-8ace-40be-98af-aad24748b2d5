// 关联企业
import { defineComponent } from 'vue';

const StockCompany = defineComponent({
  name: 'stockCompany',

  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },

  data() {
    return {
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          title: '报告期',
          width: '110px',
          align: 'center',
          dataIndex: 'EndDate',
        },
        {
          title: '参控关系',
          align: 'center',
          dataIndex: 'Relationship',
        },
        {
          title: '持股比例',
          align: 'center',
          dataIndex: 'HoldRatio',
        },
        {
          title: '投资额',
          align: 'center',
          dataIndex: 'InvestMoney',
        },
        {
          title: '营业收入',
          align: 'center',
          dataIndex: 'OperRev',
        },
        {
          title: '净利润',
          align: 'center',
          dataIndex: 'NetProfit',
        },
        {
          title: '是否合并报表',
          align: 'center',
          dataIndex: 'IfConsolidated',
        },
        {
          title: '是否上市',
          align: 'center',
          dataIndex: 'IfListed',
        },
        {
          title: '主营业务',
          align: 'center',
          dataIndex: 'MainBusiness',
        },
      ],
    };
  },

  computed: {
    keyNo() {
      return (this as any).dialogProps.keyNo;
    },
    realData() {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const { tableData, pagination } = this;
      const tmpData = [...tableData];
      const start = Math.max(pagination.current - 1, 0) * pagination.pageSize;
      const end = pagination.current * pagination.pageSize;
      const result = tmpData.slice(start, end);
      return result;
    },
  },

  methods: {
    async getDetail() {
      const that = this as any;
      const params = {
        keyNo: that.detailParams.KeyNo,
        targetKeyNo: that.detailParams.TargetKeyNo,
      };
      const res = await that.$service.dimension.ipocgkgdetail(params);
      that.tableData = res?.Result || [];
      console.log(that.tableData);
      that.pagination = { ...that.pagination, total: that.tableData.length || 0 };
    },
    pageChange(current, pageSize) {
      const that = this as any;
      that.pagination.current = current;
      that.pagination.pageSize = pageSize;
    },
  },

  mounted() {
    (this as any).getDetail();
  },

  render() {
    const that = this as any;
    const { tableData, pagination, columns, detailParams, pageChange, realData } = that;
    return (
      <div>
        <q-plain-table>
          <tr>
            <td class="tb">参控股企业名称</td>
            <td>
              <q-entity-link coyObj={{ KeyNo: detailParams.company.KeyNo, Name: detailParams.company.Name }}></q-entity-link>
            </td>
            <td class="tb">所属地区</td>
            <td>{tableData[0] ? tableData[0].Area || '-' : '-'}</td>
          </tr>
          <tr>
            <td class="tb">注册地</td>
            <td>{tableData[0] ? tableData[0].RegisterAddress || '-' : '-'}</td>
            <td class="tb">注册资本</td>
            <td>{detailParams.company.RegisterCapital || '-'}</td>
          </tr>
        </q-plain-table>
        <div style="height: 20px"></div>
        <q-rich-table
          dataSource={realData}
          columns={columns}
          pagination={{ ...pagination, onChange: pageChange, onShowSizeChange: pageChange }}
        />
        {/* scroll={{ x: 0, y: 300 }} */}
      </div>
    );
  },
});

export default StockCompany;
