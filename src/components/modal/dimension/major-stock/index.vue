<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>{{ viewData.Name || '-' }}</span>
        </td>
        <td width="23%" class="tb">变更日期：</td>
        <td width="27%">
          {{ viewData.ChangeDate || '-' }}
        </td>
      </tr>
      <tr>
        <td width="23%" class="tb">变更前：</td>
        <td colspan="3">
          <template v-if="viewData.BeforeObject && viewData.BeforeObject.length > 0">
            <div v-for="(item, index) in viewData.BeforeObject" :key="'before' + index">
              <q-entity-link v-if="item.KeyNo" :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              <span v-else>{{ item.Name || '-' }}</span>
              <span v-if="item.StockPercent">，变更前持股{{ item.StockPercent }}</span>
            </div>
          </template>
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td width="23%" class="tb">变更后：</td>
        <td colspan="3">
          <template v-if="viewData.AfterObject && viewData.AfterObject.length > 0">
            <div v-for="(item, index) in viewData.AfterObject" :key="'before' + index">
              <q-entity-link v-if="item.KeyNo" :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              <span v-else>{{ item.Name || '-' }}</span>
              <span v-if="item.StockPercent">，持股{{ item.StockPercent }}</span>
            </div>
          </template>
          <span v-else>-</span>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
