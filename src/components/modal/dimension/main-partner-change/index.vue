<template>
  <div class="main-partner-change">
    <table class="ntable">
      <tr>
        <td class="tb" width="120">变更企业</td>
        <td width="360">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <div v-else>
            {{ viewData.Name }}
          </div>
        </td>
        <td class="tb" width="120">更新时间<q-glossary-info :info-id="241" /></td>
        <td>
          {{ viewData.CreateDate | dateformat }}
        </td>
      </tr>
    </table>
    <!-- 职务变更、主要成员新增、主要成员退出 -->
    <template v-if="isSamePositionChange">
      <template v-if="changeMember && changeMember.length">
        <div class="m-b-xs t-main-title">职务变更</div>
        <table class="ntable">
          <tr>
            <th width="33.33%" class="tb">主要成员</th>
            <th width="33.33%" class="tb">变更前</th>
            <th width="33.33%" class="tb">变更后</th>
          </tr>
          <tr v-for="(item, index) in changeMember" :key="index">
            <td class="text-center">
              <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
            </td>
            <td class="text-center">{{ item.B || '-' }}</td>
            <td class="text-center">{{ item.C || '-' }}</td>
          </tr>
        </table>
      </template>
      <template v-if="isJustMemberChange">
        <div v-for="item in reduceAndAddMember">
          <div :key="`${item.key}_title`" class="m-b-xs t-main-title">{{ item.key }}变更</div>
          <table :key="`${item.key}_table`" class="ntable">
            <tr>
              <th class="tb" width="50%">退出</th>
              <th class="tb" width="50%">新增</th>
            </tr>
            <tr>
              <td class="text-center">
                <q-entity-link :coy-arr="item.reduce" />
              </td>
              <td class="text-center">
                <q-entity-link :coy-arr="item.add" />
              </td>
            </tr>
          </table>
        </div>
      </template>
      <template v-else>
        <div class="m-b-xs t-main-title">人员变更</div>
        <table class="ntable">
          <tr>
            <th class="tb" width="33.33%">职务类型</th>
            <th class="tb" width="33.33%">退出</th>
            <th class="tb" width="33.33%">新增</th>
          </tr>
          <tr v-for="item in reduceAndAddMember" :key="item.key">
            <td class="text-center">
              {{ item.key }}
            </td>
            <td class="text-center">
              <q-entity-link :coy-arr="item.reduce" />
            </td>
            <td class="text-center">
              <q-entity-link :coy-arr="item.add" />
            </td>
          </tr>
        </table>
      </template>
    </template>
    <template v-else>
      <div class="m-b-xs t-main-title">主要成员变更</div>
      <template v-if="changeMember && changeMember.length">
        <div class="margin-b-10 text-dark">职务变更</div>
        <table class="ntable">
          <tr>
            <th width="300" class="tb">主要成员</th>
            <th width="300" class="tb">变更前</th>
            <th class="tb">变更后</th>
          </tr>
          <tr v-for="(item, index) in changeMember" :key="index">
            <td class="text-center">
              <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
            </td>
            <td class="text-center">{{ item.B || '-' }}</td>
            <td class="text-center">{{ item.C || '-' }}</td>
          </tr>
        </table>
      </template>
      <template v-if="reduceMember && reduceMember.length">
        <div class="margin-b-10 text-dark">退出</div>
        <table class="ntable">
          <tr>
            <th width="585" class="tb">主要成员</th>
            <th class="tb">退出前职务</th>
          </tr>
          <tr v-for="(item, index) in reduceMember" :key="index">
            <td class="text-center">
              <q-entity-link :coy-obj="item" />
            </td>
            <td class="text-center">
              {{ item.position || '-' }}
            </td>
          </tr>
        </table>
      </template>
      <template v-if="addMember && addMember.length">
        <div class="margin-b-10 text-dark">新增</div>
        <table class="ntable">
          <tr>
            <th width="585" class="tb">主要成员</th>
            <th class="tb">职务</th>
          </tr>
          <tr v-for="(item, index) in addMember" :key="index">
            <td class="text-center">
              <q-entity-link :coy-obj="item" />
            </td>
            <td class="text-center">
              {{ item.position || '-' }}
            </td>
          </tr>
        </table>
      </template>
    </template>
  </div>
</template>

<script>
import _ from 'lodash';

export default {
  name: 'MainPartnerChange',

  props: {
    viewData: {
      type: Object,

      default() {
        return {};
      },
    },
  },

  data() {
    return {
      sourceData: {},
      changeMember: [],
      addMember: [],
      reduceMember: [],
      // 是否包含相同职位的主要人员变更
      isSamePositionChange: false,
      // 如果包含，则需要按职位聚合变更内容
      reduceAndAddMember: [],
    };
  },

  computed: {
    isJustMemberChange() {
      return this.reduceMember?.length && this.addMember?.length && !this.changeMember?.length;
    },
  },

  mounted() {
    try {
      this.sourceData = this.viewData;
    } catch (err) {}
    this.changeMember = this.sourceData.D || [];
    const { reduceMember, addMember, isSamePositionChange, reduceAndAddMember } = this.formatEmployeeData(this.sourceData);
    this.reduceMember = reduceMember;
    this.addMember = addMember;
    this.isSamePositionChange = isSamePositionChange;
    this.reduceAndAddMember = reduceAndAddMember;
  },

  methods: {
    handleItemType(members, type) {
      if (!members?.length) {
        return [];
      }
      return members.map((item) => ({
        KeyNo: item.K,
        Name: item.A,
        position: item.B,
        type,
      }));
    },

    hasFullPositions(members) {
      return members?.length && members.every((item) => item.position);
    },

    getPositions(members) {
      const positions = members.map((item) => item.position);
      return [...new Set(positions)].sort();
    },

    getGroupMembers(members) {
      const currMembers = _.groupBy(members, 'position');

      return Object.keys(currMembers).map((key) => ({
        key,
        reduce: currMembers[key].filter((item) => item.type === 'reduce'),
        add: currMembers[key].filter((item) => item.type === 'add'),
      }));
    },

    formatEmployeeData(employeeData) {
      const reduceMember = this.handleItemType(employeeData.E, 'reduce');
      const addMember = this.handleItemType(employeeData.F, 'add');
      let isSamePositionChange = false;
      let reduceAndAddMember = [];

      // 退出和新增同时存在 并且 职位去重后完全一样的情况下，特殊展示
      if (this.hasFullPositions(reduceMember) && this.hasFullPositions(addMember)) {
        const reducePositons = this.getPositions(reduceMember);
        const addPositons = this.getPositions(addMember);

        isSamePositionChange = reducePositons.join('') === addPositons.join('');

        if (isSamePositionChange) {
          // 按照职位聚合
          reduceAndAddMember = this.getGroupMembers([...reduceMember, ...addMember]);
        }
      }

      return {
        reduceMember,
        addMember,
        isSamePositionChange,
        reduceAndAddMember,
      };
    },
  },
};
</script>
