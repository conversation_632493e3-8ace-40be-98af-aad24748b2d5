<template>
  <q-plain-table>
    <tbody>
      <tr>
        <th width="20%" class="tb">票号</th>
        <td colspan="3">{{ viewData.PjNo || '-' }}</td>
      </tr>
      <tr>
        <th width="20%" class="tb">申请人</th>
        <td width="30%">
          <q-entity-link :coy-obj="viewData.ApplyComName" />
        </td>
        <th width="20%" class="tb">出票人</th>
        <td width="30%">
          <q-entity-link :coy-obj="viewData.DrawComName" />
        </td>
      </tr>
      <tr>
        <th class="tb">持票人</th>
        <td>
          <q-entity-link :coy-obj="viewData.OwnerComName" />
        </td>
        <th class="tb">票据类型</th>
        <td>{{ viewData.PjType || '-' }}</td>
      </tr>
      <tr>
        <th class="tb">票面金额</th>
        <td>{{ viewData.PmMoney || '-' }}</td>
        <th class="tb">出票日</th>
        <td>{{ dateFormat(viewData.BillBeginDt) || '-' }}</td>
      </tr>
      <tr>
        <th class="tb">到期日</th>
        <td>{{ dateFormat(viewData.BillEndDt) || '-' }}</td>
        <th class="tb">付款银行</th>
        <td>
          <q-entity-link :coy-obj="viewData.PayComName" />
        </td>
      </tr>
      <tr>
        <th class="tb">类别</th>
        <td>{{ viewData.Type || '-' }}</td>
        <th class="tb">公告日期</th>
        <td>{{ dateFormat(viewData.PublishDt) || '-' }}</td>
      </tr>
      <tr>
        <th class="tb">公告内容</th>
        <td colspan="3">{{ viewData.InfoDetail || '-' }}</td>
      </tr>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
