import { chunk, isString } from 'lodash';
import { computed, defineComponent } from 'vue';

import QPlainTable from '@/components/global/q-plain-table';
import { isJSONString } from '@/utils/data-type/is-json-string';

import { SanctionstypeNameMap } from './config';

const fieldsRender = (data) => {
  const oriValue = !data.dataValue && data.dataValue !== 0 ? '-' : data.dataValue;
  let returnValue = isJSONString(oriValue) ? JSON.parse(oriValue) : [oriValue];
  // 制裁种类
  if (['sanctionstype'].includes(data.key)) {
    returnValue = SanctionstypeNameMap[oriValue];
  }
  // 别名信息
  if (['alias'].includes(data.key)) {
    returnValue = returnValue.reduce((str, item) => {
      str += `${item?.Name || '-'}<br>`;
      return str;
    }, '');
  }
  // 认证信息
  if (['identifications'].includes(data.key)) {
    const renzheng = returnValue.filter((item) => item.Type === 'Unified Social Credit Code (USCC)');
    returnValue = renzheng.reduce((str, item) => {
      str += `${item.Content}<br>`;
      return str;
    }, '');
  }
  // 地址信息
  if (['address'].includes(data.key)) {
    returnValue = returnValue.reduce((str, item) => {
      str += `${item.Address}<br>`;
      return str;
    }, '');
  }
  // 企业匹配信息
  if (['keynolist'].includes(data.key)) {
    returnValue = returnValue.reduce((str, item) => {
      str += `${item.CompNameMatch}<br>`;
      return str;
    }, '');
  }
  // 联邦公报通知
  if (['federalregisternotice'].includes(data.key)) {
    returnValue = returnValue.reduce((arr, item) => {
      arr = [
        ...arr,
        <a href={item.NoticeOriginUrl} target="_blank">
          {item.NoticeNumber}
        </a>,
        <br />,
      ];
      return arr;
    }, []);
  }
  return returnValue;
};

/**
 * 国央企采购黑名单
 */
const GovProcurementIllegal = defineComponent({
  name: 'GovProcurementIllegal',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const dataSource = computed(() => chunk(props.viewData?.Details ?? [], 2));
    return {
      dataSource,
    };
  },
  render() {
    return (
      <QPlainTable>
        <tbody>
          {this.dataSource.map((cells, rowIndex) => {
            return (
              <tr key={`tr-${rowIndex}`}>
                {cells.map((cell: any, colIndex) => {
                  // 最后一行如果是单数要撑满3格
                  const isOdd = cells.length % 2 === 0;
                  const tdContent = fieldsRender(cell);
                  return [
                    <td key={`td-key-${colIndex}`} width="18%" class="tb">
                      {cell?.dataKey ?? '-'}
                    </td>,
                    isString(tdContent) ? (
                      <td
                        key={`td-value-${colIndex}`}
                        width={isOdd ? '27%' : 'auto'}
                        colspan={isOdd ? 1 : 3}
                        domPropsInnerHTML={tdContent}
                      ></td>
                    ) : (
                      <td key={`td-value-${colIndex}`} width={isOdd ? '27%' : 'auto'} colspan={isOdd ? 1 : 3}>
                        {tdContent}
                      </td>
                    ),
                  ];
                })}
              </tr>
            );
          })}
        </tbody>
      </QPlainTable>
    );
  },
});

export default GovProcurementIllegal;
