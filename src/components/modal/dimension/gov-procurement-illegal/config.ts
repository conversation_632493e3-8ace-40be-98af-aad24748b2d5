// https://thoughts.teambition.com/workspaces/63edf46da9aceb003ed15724/docs/66a33f8062ca100001c5bad9
// 外部黑名单，涉制裁名单 显示字段
export const OutBlackListDimensionDictionary = {
  nationcode: '国家代码',
  sanctionscode: '制裁代码',
  sanctionstypecode: '制裁种类代码',
  sanctionstype: '制裁种类',
  name: '名称',
  sanctionsregulations: '制裁条例/名单',
  sanctionsmeasure: '制裁措施/项目',
  sanctionsreason: '制裁原因',
  designateddate: '指定日期',
  remark: '备注',
  identifications: '认证信息',
  alias: '别名信息',
  address: '地址信息',
  keynolist: '企业匹配信息',
  federalregisternotice: '联邦公报通知',
  expirationdate: '截止日期',
  startdate: '开始日期',
  lastupdatedate: '最后更新日期',
};

const swappedObject = {
  国家代码: 'nationcode',
  制裁代码: 'sanctionscode',
  制裁种类代码: 'sanctionstypecode',
  制裁种类: 'sanctionstype',
  名称: 'name',
  '制裁条例/名单': 'sanctionsregulations',
  '制裁措施/项目': 'sanctionsmeasure',
  制裁原因: 'sanctionsreason',
  指定日期: 'designateddate',
  备注: 'remark',
  认证信息: 'identifications',
  别名信息: 'alias',
  地址信息: 'address',
  企业匹配信息: 'keynolist',
  联邦公报通知: 'federalregisternotice',
  截止日期: 'expirationdate',
  开始日期: 'startdate',
  最后更新日期: 'lastupdatedate',
};

// 出口管制合规风险企业清单 未用到，做留存 详情字段 RA-8496
const detailKeysArr = {
  1: [
    'nationcode',
    'sanctionscode',
    'sanctionstypecode',
    'sanctionstype',
    'name',
    'sanctionsregulations',
    'sanctionsmeasure',
    'remark',
    // 'identifications',
    'alias',
    'address',
  ],
  3: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'sanctionsreason', 'designateddate'],
  4: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'keynolist'],
  5: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'federalregisternotice'],
  6: [
    'nationcode',
    'sanctionstypecode',
    'sanctionstype',
    'name',
    'sanctionsregulations',
    'sanctionsmeasure',
    'sanctionsreason',
    'alias',
    'address',
    'keynolist',
    'federalregisternotice',
  ],
  7: [
    'nationcode',
    'sanctionstypecode',
    'name',
    'sanctionsregulations',
    'designateddate',
    'address',
    'expirationdate',
    'federalregisternotice',
  ],
  8: ['nationcode', 'sanctionstypecode', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice'],
  9: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice'],
  12: [
    'nationcode',
    'sanctionstypecode',
    'name',
    'sanctionsregulations',
    'sanctionsreason',
    'designateddate',
    'address',
    'expirationdate',
    'startdate',
  ],
  14: [
    'nationcode',
    'sanctionscode',
    'sanctionstypecode',
    'sanctionstype',
    'name',
    'sanctionsregulations',
    'designateddate',
    'lastupdatedate',
    'remark',
    'alias',
    'address',
  ],
  15: [
    'nationcode',
    'sanctionscode',
    'sanctionstypecode',
    'sanctionstype',
    'name',
    'sanctionsregulations',
    'sanctionsmeasure',
    'designateddate',
    'lastupdatedate',
    'remark',
    // 'identifications',
    'alias',
    'address',
  ],
  13: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'address'],
  2: [
    'nationcode',
    'sanctionscode',
    'sanctionstypecode',
    'sanctionstype',
    'name',
    'sanctionsregulations',
    'sanctionsmeasure',
    'sanctionsreason',
    'designateddate',
    'lastupdatedate',
    'alias',
    'address',
  ],
};

export const SanctionstypeNameMap = {
  Entity: '企业',
  Individual: '个人',
  Aircraft: '飞行器',
  Vessel: '船舶',
};
