import { computed, defineComponent, onMounted, reactive } from 'vue';
import QRichTable from '@/components/global/q-rich-table';
import { useFetchState } from '@/hooks/use-fetch-state';
import { dimensionDetail } from '@/shared/services';
import QEntityLink from '@/components/global/q-entity-link';
import CompanyStatus from '@/components/global/q-company-status';

const supplierColumns = [
  {
    title: '供应商',
    scopedSlots: { customRender: 'entities' },
  },
  {
    title: '状态',
    dataIndex: 'ShortStatus',
    scopedSlots: { customRender: 'companyStatus' },
  },
  {
    title: '采购金额(万元)',
    dataIndex: 'ChildRen[0].Quota',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '公开日期',
    dataIndex: 'ChildRen[0].ReportDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '数据来源',
    dataIndex: 'ChildRen[0].Source',
    scopedSlots: { customRender: 'dataSources' },
  },
];

const customerColumns = [
  {
    title: '客户',
    scopedSlots: { customRender: 'entities' },
  },
  {
    title: '状态',
    dataIndex: 'ShortStatus',
    scopedSlots: { customRender: 'companyStatus' },
  },
  {
    title: '销售金额(万元)',
    dataIndex: 'ChildRen[0].Quota',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '公开日期',
    dataIndex: 'ChildRen[0].ReportDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '数据来源',
    dataIndex: 'ChildRen[0].Source',
    scopedSlots: { customRender: 'dataSources' },
  },
];

const SupplierCustomer = defineComponent({
  name: 'SupplierCustomer',
  props: {
    detailParams: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const fetchData = async () => {
      const res = await dimensionDetail.supplierOrCustomer({
        ...props.detailParams,
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
      });
      pagination.total = res.Paging.TotalRecords;
      pagination.current = res.Paging.PageIndex;
      return res;
    };

    const { isLoading, result, execute } = useFetchState(fetchData);

    const handlePageChange = (current: number, pageSize: number) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      execute();
    };

    const pagination = reactive({
      pageSize: 5,
      current: 1,
      total: 0,
      onChange: handlePageChange,
      showSizeChanger: false,
    });

    const isSupplier = computed(() => props.detailParams?.dataType === 0);

    onMounted(() => {
      execute();
    });

    return {
      isSupplier,
      result,
      pagination,
      isLoading,
      handlePageChange,
    };
  },
  render() {
    return (
      <QRichTable
        loading={this.isLoading}
        columns={this.isSupplier ? supplierColumns : customerColumns}
        dataSource={this.result?.Result}
        pagination={this.pagination}
        scopedSlots={{
          entities: (item) => {
            return <QEntityLink coyObj={{ KeyNo: item.KeyNo, Name: item.CompanyName }} />;
          },
          dataSources: (text, item) => {
            const data = item.ChildRen?.[0] || {};
            if (data.SourceId && data.SourceCode === 6) {
              return (
                <a href={`/embed/tenderDetail?id=${data.SourceId}`} target="_blank">
                  {data.Source || '-'}
                </a>
              );
            }
            if (data.SourceId && data.SourceCode === 7) {
              const SourceId_ = data.SourceId.slice(0, data.SourceId.length - 1);
              return (
                <a href={`/embed/judgementInfo?id=${SourceId_}`} target="_blank">
                  ${data.Source || '-'}
                </a>
              );
            }
            return data.Source || '-';
          },
          companyStatus: (val) => {
            return <CompanyStatus status={val} />;
          },
        }}
      />
    );
  },
});

export default SupplierCustomer;
