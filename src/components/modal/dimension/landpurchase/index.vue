<template>
  <div>
    <div class="mtcaption">基本信息</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%">行政区</th>
          <td width="27%">{{ viewData.Region || '-' }}</td>
          <th width="23%">电子监管号</th>
          <td width="27%">{{ viewData.ElecSuNum || '-' }}</td>
        </tr>
        <tr>
          <th>项目名称</th>
          <td colspan="5">{{ viewData.ProjectName || '-' }}</td>
        </tr>
        <tr>
          <th>土地位置</th>
          <td colspan="5">{{ viewData.ProjectLocation || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">面积(公顷)</th>
          <td width="27%">{{ viewData.Area || '-' }}</td>
          <th width="23%">土地来源</th>
          <td width="27%">{{ viewData.LandSource || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">土地用途</th>
          <td width="27%">{{ viewData.LandUse || '-' }}</td>
          <th width="23%">供地方式</th>
          <td width="27%">{{ viewData.LandSupplyWay || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">土地使用年限</th>
          <td width="27%">{{ viewData.LandUseYears || '-' }}</td>
          <th width="23%">行业分类</th>
          <td width="27%">{{ viewData.Industry || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">土地级别</th>
          <td width="27%">{{ viewData.LandLevel || '-' }}</td>
          <th width="23%">成交价格(万元)</th>
          <td width="27%">{{ viewData.TransactionPrice || '-' }}</td>
        </tr>
        <tr>
          <th width="23%">土地使用权人</th>
          <td width="27%">
            <template v-if="viewData.Landholder">
              <q-entity-link :coy-obj="{ KeyNo: viewData.Landholder.KeyNo, Name: viewData.Landholder.Name }"></q-entity-link>
            </template>
            <template v-else>-</template>
          </td>
          <th width="23%">约定交地日期</th>
          <td width="27%">{{ viewData.AgreedLandTime | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <th width="23%">约定开工日期</th>
          <td width="27%">{{ viewData.AgreedStartTime | dateformat('YYYY-MM-DD') }}</td>
          <th width="23%">约定竣工日期</th>
          <td width="27%">{{ viewData.AgreedCompletionTime | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <th width="23%">实际开工日期</th>
          <td width="27%">{{ viewData.ActualStartTime | dateformat('YYYY-MM-DD') }}</td>
          <th width="23%">实际竣工日期</th>
          <td width="27%">{{ viewData.ActualCompletionTime | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <th width="23%">批准单位</th>
          <td width="27%">{{ viewData.ApprovalUnit || '-' }}</td>
          <th width="23%">合同签订日期</th>
          <td width="27%">{{ viewData.SignContractTime | dateformat('YYYY-MM-DD') }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <div class="mtcaption">分期支付约定</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%">支付期号</th>
          <td width="27%">{{ viewData.PaymentIssue || '-' }}</td>
          <th width="23%">约定支付日期</th>
          <td width="27%">{{ viewData.AgreedPaymentDate | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <th width="23%">约定支付金额(万元)</th>
          <td width="27%">{{ viewData.AgreedPayment || '-' }}</td>
          <th width="23%">约定支付日期</th>
          <td width="27%">{{ viewData.mark || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <div class="mtcaption">约定容积率</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%">下限</th>
          <td width="27%">{{ viewData.AgreedRateMin || '-' }}</td>
          <th width="23%">上限</th>
          <td width="27%">{{ viewData.AgreedRateMax || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
  </div>
</template>

<script src="./component.js"></script>
<style lang="less" scoped>
.mtcaption {
  margin: 16px 0 12px 0;
  &:first-child {
    margin-top: 0;
  }
}
</style>
