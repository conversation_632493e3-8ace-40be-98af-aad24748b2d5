import { Icon, Tooltip } from 'ant-design-vue';

import { dateFormat } from '@/utils/format';
import { numberToHuman } from '@/utils/number-formatter';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogProps: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  components: { Icon, Tooltip },
  methods: {
    dateformat(date, options) {
      return dateFormat(date, options);
    },
    numberToHuman(val) {
      return numberToHuman(Number(val), {
        precision: 2,
      });
    },
  },
};
