<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <td width="23%" class="tb">被执行人</td>
          <td width="27%">
            <q-entity-link v-if="viewData.NameKeyNoCollection" :coy-arr="viewData.NameKeyNoCollection"></q-entity-link>
            <template v-else>{{ viewData.Name || '-' }}</template>
            <q-ccxs :ccxs-count="dialogProps.ccxsCount" :key-no="dialogProps.keyNo"></q-ccxs>
          </td>
          <td width="23%" class="tb">证件号码/组织机构代码</td>
          <td width="27%">{{ viewData.Partycardnum || '-' }}</td>
        </tr>
        <tr v-if="viewData.SqrInfo && viewData.SqrInfo.length > 0">
          <td width="23%" class="tb">
            疑似申请执行人
            <Tooltip placement="bottom" title="该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。">
              <Icon style="color: #d6d6d6" class="icon" type="info-circle" />
            </Tooltip>
          </td>
          <td colspan="3">
            <q-entity-link :coy-arr="viewData.SqrInfo"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb">执行标的(元)</td>
          <td>{{ numberToHuman(viewData.Biaodi) || '-' }}</td>
          <td class="tb">执行法院</td>
          <td>{{ viewData.Executegov || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">立案日期</td>
          <td>{{ viewData.Liandate | dateformat }}</td>
          <td class="tb">案号</td>
          <td v-if="viewData.CaseSearchId && viewData.CaseSearchId.length !== 0">
            <q-link :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.Executegov}${viewData.Anno}`">
              <span v-html="viewData.Anno || '-'"></span>
            </q-link>
          </td>
          <td v-else>{{ viewData.Anno || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
