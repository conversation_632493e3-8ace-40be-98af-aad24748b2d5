<template>
  <q-rich-table :dataSource="dataSource" :columns="columns" rowKey="Id" :pagination="pagination">
    <template slot="name" slot-scope="record">
      <q-link v-if="record.Id" :to="`/caseDetail/${record.Id}`">{{ record.CaseName }}</q-link>
      <span v-else> {{ record.CaseName || '-' }}</span>
    </template>
    <template slot="caseType" slot-scope="record">
      <template v-if="record.CaseTypeArray && record.CaseTypeArray.length">
        <div v-for="(caseTypeItem, anNoIndex) in record.CaseTypeArray" :key="'caseType' + anNoIndex">
          {{ caseTypeItem }}
        </div>
      </template>
      <span v-else>-</span>
    </template>
    <template slot="court" slot-scope="record">
      <template v-if="record.CourtList && record.CourtList.length">
        <div v-for="(courtItem, anNoIndex) in record.CourtList" :key="'court' + anNoIndex">
          {{ courtItem }}
        </div>
      </template>
      <span v-else>-</span>
    </template>
    <template slot="anNo" slot-scope="record">
      <template v-if="record.AnNoList && record.AnNoList.length">
        <q-link :to="`/caseDetail/${record.Id}`">
          <div v-for="(anNoItem, anNoIndex) in record.AnNoList" :key="'anNo' + anNoIndex">{{ anNoItem }}</div>
        </q-link>
      </template>
      <span v-else>-</span>
    </template>
  </q-rich-table>
</template>

<script src="./component.jsx"></script>
