import dimensionMixin from '@/shared/mixins/dimension.mixin';
import numberFormat from '@/utils/format/number-format';

export default {
  mixins: [dimensionMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      pagination: {
        size: 'small',
        pageSize: 5,
        defaultPageSize: 5,
        pageIndex: 1,
        showQuickJumper: false,
        showSizeChanger: false,
        total: 0,
        onChange: (current) => {
          this.pagination.pageIndex = current;
          this.pagination.current = current;
          this.getData();
        },
      },
      dataSource: [],
    };
  },
  computed: {
    columns() {
      return [
        {
          title: '案件名称',
          width: 200,
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '案件类型',
          width: 80,
          scopedSlots: { customRender: 'caseType' },
        },
        {
          title: '案件身份',
          width: 80,
          customRender: (item) => {
            let roleRL = [];
            let showInfo = '';
            if (item.CaseRoleSearch) {
              let arr;
              try {
                arr = JSON.parse(item.CaseRoleSearch);
              } catch (e) {
                console.log(e);
              }
              if (arr?.length) {
                arr.forEach((role) => {
                  if (role.N === this.$route.params.id) {
                    // roleD = role.D
                    roleRL = role.RL;
                  }
                });
              }
              if (roleRL?.length) {
                return (
                  <div>
                    {roleRL.map((rl, index) => {
                      showInfo += rl.T + rl.R;
                      if (rl.LRD) {
                        let classtype;
                        if (['获得支持', '对方不被支持', '获得部分支持'].includes(rl.LRD)) {
                          classtype = 'text-success';
                        } else if (['诉讼中止', '被解除查封', '被执行完毕', '被解除查冻扣', '执行中止'].includes(rl.LRD)) {
                          classtype = 'text-warning';
                        } else if (['不被支持', '对方被支持', '被驳回', '被查封', '被查冻扣', '终结本次执行'].includes(rl.LRD)) {
                          classtype = 'text-danger';
                        } else {
                          classtype = 'text-gray';
                        }

                        return (
                          <div>
                            {rl.T + rl.R}
                            <span class={classtype}>[{rl.LRD}]</span>
                          </div>
                        );
                      }
                      return '';
                    })}
                  </div>
                );
              }
              return '-';
            }
            return '-';
          },
        },
        {
          title: '案由',
          width: 80,
          dataIndex: 'CaseReason',
        },
        {
          title: '案号',
          scopedSlots: { customRender: 'anNo' },
        },
        {
          title: '案件金额(元)',
          width: 100,
          customRender: (item) => {
            if (item.AmtInfo?.Amt) {
              return numberFormat(item.AmtInfo.Amt, 2);
            }
            return '-';
          },
        },
        {
          title: '法院',
          scopedSlots: { customRender: 'court' },
        },
        {
          title: '最新案件进程',
          width: 120,
          dataIndex: 'LatestTrialRound',
        },
      ];
    },
  },
  methods: {
    getData() {
      this.$service.dimension
        .relateCaseList({
          ...this.detailParams,
          ...this.pagination,
        })
        .then((data) => {
          if (data.Result) {
            this.dataSource = data.Result;
            this.pagination.total = data.Paging.TotalRecords;
          }
        });
    },
  },
  created() {
    this.getData();
  },
};
