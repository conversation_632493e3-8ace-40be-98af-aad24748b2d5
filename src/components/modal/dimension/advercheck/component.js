export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    getNoTh(type) {
      let str = '';
      switch (type) {
        case 1:
          str = '药品批准文号';
          break;
        case 2:
          str = '医疗器械注册证编号';
          break;
        case 3:
          str = '保健食品注册号';
          break;
        default:
          str = '特殊医学用途配方食品注册号';
          break;
      }
      return str;
    },
  },
};
