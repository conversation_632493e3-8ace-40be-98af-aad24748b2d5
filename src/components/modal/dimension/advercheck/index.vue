<template>
  <q-plain-table>
    <tbody>
      <tr>
        <th width="23%">广告审查机关</th>
        <td width="27%">
          <template v-if="viewData.AuthorityLis">
            <q-entity-link :coy-arr="viewData.AuthorityLis"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
        <th width="23%">广告批准文号</th>
        <td width="27%">{{ viewData.ApprovalNumber || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">广告类别</th>
        <td width="27%">{{ viewData.AdvertType || '-' }}</td>
        <th width="23%">广告时长（秒）</th>
        <td width="27%">{{ viewData.AdvertTime || '-' }}</td>
      </tr>
      <tr>
        <th>广告申请人名称</th>
        <td colspan="5">
          <template v-if="viewData.ApplyList">
            <q-entity-link :coy-arr="viewData.ApplyList"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
      <tr>
        <th>广告申请人地址</th>
        <td colspan="5">{{ viewData.Address || '-' }}</td>
      </tr>
      <tr v-if="viewData.ContentOssId">
        <th>广告发布内容</th>
        <td colspan="5">
          <a :href="viewData.ContentOssId" target="_blank">
            <img :src="viewData.ContentOssId" width="110px" height="60px" />
          </a>
        </td>
      </tr>
      <tr>
        <th width="23%">{{ viewData.ProductType === 1 ? '商品名称' : '产品名称' }}</th>
        <td width="27%">{{ viewData.ProductName || '-' }}</td>
        <th width="23%">品牌名称</th>
        <td width="27%">{{ viewData.BrandName || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">{{ getNoTh(viewData.ProductType) }}</th>
        <td width="27%">{{ viewData.No || '-' }}</td>
        <th width="23%">广告审批日期</th>
        <td width="27%">{{ viewData.AuditDate | dateformat() }}</td>
      </tr>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
