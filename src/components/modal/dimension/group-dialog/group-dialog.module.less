.container {
  :global {
    .q-section-header {
      display: none !important;
    }

    .group-hide {
      display: none;
    }

    .webchatlist {
      position: static;

      .ant-spin-nested-loading {
        position: static;

        .ant-spin-container {
          position: static;

          .ant-table {
            position: static;
          }
        }
      }
    }

    .creditrate-dimension {
      .q-section-header {
        display: flex !important;

        img {
          display: none;
        }
      }

      >.q-section-header {
        display: none !important;
      }
    }
  }

  .dimensionWrapper {
    padding: 0;
    // overflow: auto;
    // max-height: calc(100vh - 260px);
    width: 100%;
  }
}
