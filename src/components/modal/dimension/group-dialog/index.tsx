import Vue, { defineComponent, PropType } from 'vue';
import { mapActions } from 'vuex';

import QEntityLink from '@/components/global/q-entity-link';
import * as companyUtil from '@/utils/firm';
import QLoading from '@/components/global/q-loading';

import styles from './group-dialog.module.less';

const GroupDialog = defineComponent({
  name: 'GroupDialog',
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
    info: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      company: null,
      loaded: false,
    };
  },
  provide() {
    return {
      getEntityDetail: () => this.company,
      getCompanyDetail: () => this.company,
    };
  },
  mounted() {
    this.getDetail({
      keyNo: (this.detailParams as any).KeyNo,
      hasProfile: true,
      hasComInfo: true,
      hasHisCount: true,
      hasSnapshot: true,
    }).then((data) => {
      if (!data?.info?.name) {
        throw new Error('企业不存在');
      }
      companyUtil.formatDetail(data);
      companyUtil.setStockInfo(data, this.$route.query.mtlist);
      companyUtil.setGraphInfo(data);
      companyUtil.setCompanyInfo(data);
      this.company = data;
      this.loaded = true;
    });
  },
  methods: {
    ...mapActions('company', ['getDetail']),
  },
  render() {
    const DimensionInst = Vue.component(`d-${(this.info as any).key}`);

    return (
      <div class={styles.container}>
        <div>
          关联企业：
          <QEntityLink coyObj={{ Name: (this.detailParams as any).Name, KeyNo: (this.detailParams as any).KeyNo }} />
          {this.loaded ? (
            <DimensionInst
              inDialog={true}
              class={styles.dimensionWrapper}
              info={{
                ...(this.info as any),
                api: (this.info as any).dimensionApi ? (this.info as any).dimensionApi : (this.info as any).key,
              }}
              keyNo={(this.detailParams as any).KeyNo}
              defaultModule={(this.detailParams as any).currentKey === 'history' ? 'history' : null}
            />
          ) : (
            <QLoading size="fullsize" />
          )}
        </div>
      </div>
    );
  },
});
export default GroupDialog;
