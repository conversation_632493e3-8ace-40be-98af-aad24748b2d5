<template>
  <div>
    <table v-if="viewData.PermissionStatus" class="ntable">
      <tr>
        <td style="width: 18%" class="tb">上会状态</td>
        <td style="width: 32%">
          <span v-if="viewData.PermissionStatus" :class="['nstatus', getStyleName(viewData.PermissionStatus)]">{{
            viewData.PermissionStatus
          }}</span>
          <span v-else>-</span>
        </td>
        <td style="width: 18%" class="tb">受理日期</td>
        <td style="width: 32%">{{ viewData.ApprovalDate | dateformat }}</td>
      </tr>
      <tr>
        <td class="tb">上市交易所</td>
        <td>{{ viewData.Exchange || '-' }}</td>
        <td class="tb">上市板块</td>
        <td>{{ viewData.ExchangeSection || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">发行前总股本（万股）</td>
        <td>{{ viewData.TotalSharesBeforeIPO || '-' }}</td>
        <td class="tb">预计发行后总股本（万股）</td>
        <td>{{ viewData.TotalSharesAfterIPO || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">预计发行股数（万股）</td>
        <td>{{ viewData.IssueSharesPlan || '-' }}</td>
        <td class="tb">占发行后总股本比（%）</td>
        <td>{{ viewData.IssueSharesPlanRatio || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">主承销商</td>
        <td><q-entity-link :coy-arr="viewData.MainUnderwriterList" /></td>
        <td class="tb">承销方式</td>
        <td>{{ viewData.UnderwritingMethod || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">发审委委员</td>
        <td>{{ viewData.IssuanceAppraisalCommitteeMember || '-' }}</td>
        <td class="tb">利润分配方案</td>
        <td>{{ viewData.DividendPlan || '-' }}</td>
      </tr>
    </table>
    <table v-else class="ntable">
      <tr>
        <td style="width: 18%" class="tb">审核状态</td>
        <td style="width: 32%">
          <span v-if="viewData.RegisterStatus" :class="['nstatus', getStyleName(viewData.RegisterStatus)]">{{
            viewData.RegisterStatus
          }}</span>
          <span v-else>-</span>
        </td>
        <td style="width: 18%" class="tb">注册地</td>
        <td style="width: 32%">{{ viewData.RegisterAddress || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">上市交易所</td>
        <td>{{ viewData.Exchange || '-' }}</td>
        <td class="tb">上市板块</td>
        <td>{{ viewData.ListSection || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">融资金额(亿元)</td>
        <td>{{ viewData.FinancingAmount || '-' }}</td>
        <td class="tb">证监会行业</td>
        <td>{{ viewData.IndustryName || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">受理日期</td>
        <td>{{ viewData.ApprovalDate | dateformat() }}</td>
        <td class="tb">更新日期</td>
        <td>{{ viewData.StatusUpdateDate | dateformat() }}</td>
      </tr>
      <tr>
        <td class="tb">发行前总股本(万股)</td>
        <td>{{ viewData.TotalSharesBeforeIPO || '-' }}</td>
        <td class="tb">拟发行后总股本(万股)</td>
        <td>{{ viewData.TotalSharesAfterIPO || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">拟发行股份数(万股)</td>
        <td>{{ viewData.IssueSharesPlan || '-' }}</td>
        <td class="tb">占总股本比例(%)</td>
        <td>{{ viewData.IssueSharesPlanRatio || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">保荐机构</td>
        <td><q-entity-link :coy-obj="viewData.SponsorInstitution" /></td>
        <td class="tb">保荐代表人</td>
        <td>{{ viewData.SponsorRepresentative || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">会计师事务所</td>
        <td><q-entity-link :coy-obj="viewData.AccountingFirm" /></td>
        <td class="tb">签字会计师</td>
        <td>{{ viewData.SignedAccountant || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">律师事务所</td>
        <td><q-entity-link :coy-obj="viewData.LawFirm" /></td>
        <td class="tb">签字律师</td>
        <td>{{ viewData.SignedLawyer || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">评估机构</td>
        <td><q-entity-link :coy-arr="viewData.AssetEvaluatInstitution" /></td>
        <td class="tb">签字评估师</td>
        <td>{{ viewData.SignedValuer || '-' }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: 'IpoReportPeriod',

  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },

  methods: {
    getStyleName(status) {
      let styleName = '';
      const passArr = ['上市委通过', '注册生效', '已受理', '已问询', '提交注册', '上会通过'];
      const dangerArr = ['终止', '不予注册', '上会未通过', '取消审核'];
      const grayArr = ['暂缓审议', '中止', '中止（其他事项）', '中止（财报更新）', '未上会', '延期审核', '暂缓表决'];
      if (passArr.includes(status)) {
        styleName = 'text-primary';
      } else if (dangerArr.includes(status)) {
        styleName = 'text-danger';
      } else if (grayArr.includes(status)) {
        styleName = 'text-gray';
      }
      return styleName;
    },
  },
};
</script>

<style lang="less" src="../style.less" scoped></style>
