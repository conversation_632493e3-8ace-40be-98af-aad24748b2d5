// 导入 Vue 模块
import { defineComponent } from 'vue';

// 导入需要的组件
import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';

/**
 * 简易注销详情
 * src/components/app-modal/app-risk/components/jyzx.vue
 */
export default defineComponent({
  name: 'JyzxDetail',
  // 接收props
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },

  setup(props) {
    // 使用 render 函数替代模板
    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td class="tb" width="150px" rowspan="5">
              企业公告信息
            </td>
            <td width="220px">企业名称</td>
            <td width="600px">
              <QEntityLink coyObj={{ Name: props.viewData.CompanyName, KeyNo: props.viewData.keyNo }}></QEntityLink>
            </td>
          </tr>
          <tr>
            <td width="220px">统一社会信用代码/注册号</td>
            <td width="600px">{props.viewData.RegNoOrCreditCode || '-'}</td>
          </tr>
          <tr>
            <td width="220px">登记机关</td>
            <td width="600px">{props.viewData.Registration || '-'}</td>
          </tr>
          <tr>
            <td width="220px">公告期</td>
            <td>{props.viewData.PublicDate || '-'}</td>
          </tr>
          <tr>
            <td width="220px">全体投资人承诺书</td>
            <td>
              {props.viewData.DocUrl ? (
                <a target="_blank" href={props.viewData.DocUrl}>
                  查看详情
                </a>
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>

          {/* 异议信息 */}
          {props.viewData.DissentList &&
            props.viewData.DissentList.map((item, index) => [
              <tr key={`tr-${index}`}>
                <td v-show={index === 0} class="tb" width="150px" rowspan={3 * props.viewData.DissentList.length}>
                  异议信息
                </td>
                <td width="220px">异议申请人</td>
                <td>{item.DissentPerson || '-'}</td>
              </tr>,
              <tr key={`tr-${index}`}>
                <td width="220px">异议时间</td>
                <td>{item.DissentDate || '-'}</td>
              </tr>,
              <tr key={`tr-${index}`}>
                <td width="220px">异议内容</td>
                <td>{item.DissentContent || '-'}</td>
              </tr>,
            ])}

          {/* 简易注销结果 */}
          {props.viewData.CancellationResultList &&
            props.viewData.CancellationResultList.map((item, index) => [
              <tr key={`tr-${index}`}>
                <td v-show={index === 0} class="tb" width="150px" rowspan={2 * props.viewData.CancellationResultList.length}>
                  简易注销结果
                </td>
                <td width="220px">简易注销结果</td>
                <td>{item.ResultContent || '-'}</td>
              </tr>,
              <tr key={`tr-${index}`}>
                <td width="220px">{item.ResultContent === '准许简易注销' ? '核准日期' : '公告申请日期'}</td>
                <td>{item.PublicDate || '-'}</td>
              </tr>,
            ])}
        </QPlainTable>
      </div>
    );
  },
});
