import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link'; // 请根据实际组件路径修改
import QPlainTable from '@/components/global/q-plain-table'; // 请根据实际组件路径修改
import dateformat from '@/filters/dateformat'; // 请根据实际过滤器路径修改
import { numberToHumanWithUnit } from '@/utils/number-formatter';

/**
 * 股权质押详情
 * src/components/app-modal/app-risk/components/spledge.vue
 */
export default defineComponent({
  name: 'SpledgeDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    return (
      <QPlainTable>
        <tr>
          <td width="20%" class="tb">
            质押人
          </td>
          <td colspan="3">
            <QEntityLink coy-arr={this.viewData.Holders} />
          </td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            质押人参股企业
          </td>
          <td colspan="3">
            <QEntityLink coy-arr={this.viewData.Companys} />
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">
            质押股份数量(股)
          </td>
          <td width="30%">{numberToHumanWithUnit(this.viewData.ShareFrozenNum) || '-'}</td>
          <td class="tb" width="20%">
            质押股份市值(元)
          </td>
          <td width="30%">{numberToHumanWithUnit(this.viewData.SZ) || '-'}</td>
        </tr>
        <tr>
          <td class="tb">占所持股份比例</td>
          <td>{this.viewData.FrozenRatio || '-'}</td>
          <td class="tb">占总股本比例</td>
          <td>{this.viewData.FrozenInTotal || '-'}</td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            质押权人
          </td>
          <td colspan="3">
            <QEntityLink coy-arr={this.viewData.Pledgees} />
          </td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            质押原因
          </td>
          <td colspan="3">{this.viewData.FrozenReason || '-'}</td>
        </tr>
        <tr>
          <td class="tb">质押目的</td>
          <td>{this.viewData.PledgePur || '-'}</td>
          <td class="tb">质押日收盘价(元)</td>
          <td>{numberToHumanWithUnit(this.viewData.SPJ) || '-'}</td>
        </tr>
        <tr>
          <td class="tb">预警线(估算)</td>
          <td>{this.viewData.YJX || '-'}</td>
          <td class="tb">平仓线(估算)</td>
          <td>{this.viewData.PCX || '-'}</td>
        </tr>
        <tr>
          <td class="tb">质押开始日期</td>
          <td>{dateformat(this.viewData.StartDate)}</td>
          <td class="tb">质押解除日期</td>
          <td>{dateformat(this.viewData.EndDate)}</td>
        </tr>
        <tr>
          <td class="tb">状态</td>
          <td>{this.viewData.Status || '-'}</td>
          <td class="tb">公告日期</td>
          <td>{dateformat(this.viewData.NoticeDate)}</td>
        </tr>
      </QPlainTable>
    );
  },
});
