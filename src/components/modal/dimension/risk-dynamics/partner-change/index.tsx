import { defineComponent } from 'vue';
import { isNil } from 'lodash';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';

/**
 * 股东变更详情
 * src/components/app-modal/app-risk/components/partnerChange.vue
 */

const partnerChange = defineComponent({
  name: 'partnerChange',
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  render() {
    const { viewData } = this;
    const renderAddObjectStart = (item) => {
      if (item.KeyNo && item.KeyNo === 2) {
        return <QEntityLink coyObj={{ Name: item.Name, KeyNo: '' }}></QEntityLink>;
      }
      if (item.KeyNo) {
        return (
          <span>
            <QEntityLink coyObj={{ Name: item.Name, KeyNo: item.KeyNo }}></QEntityLink>
          </span>
        );
      }
      return item.Name || '-';
    };
    const renderAddObjectEnd = (item, text1 = '', text2 = '') => {
      if (item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%') {
        return (
          <span>
            {text1}
            {item.StockPercent}
            <br></br>
          </span>
        );
      }
      return (
        <span>
          {text2}
          <br />
        </span>
      );
    };
    const renderAddObjectEnd2 = (item, text, trend) => {
      const text1 =
        item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%' ? `${item.StockPercent}${trend}到` : '未知';
      const text2 = `${
        item.LessStockPercent && item.LessStockPercent !== '0%' && item.LessStockPercent !== '0.00%' ? item.LessStockPercent : '未知'
      }`;
      return <span domPropsInnerHTML={`${text}${text1}${text2}`}></span>;
    };
    const renderAddObjectEnd3 = (item, text, trend) => {
      const { AfterAmount, BeforeAmount } = item;
      if (AfterAmount || BeforeAmount) {
        const text1 = item.BeforeAmount ? `${item.BeforeAmount}${trend}到` : '未知';
        const text2 = `${item.AfterAmount ? item.AfterAmount : '未知'}`;
        return <span domPropsInnerHTML={`${text}${text1}${text2}`}></span>;
      }
      return null;
    };
    const renderContent = () => {
      if (viewData.IsLess !== 0 && !(viewData.RiseObject && viewData.RiseObject.length)) {
        return (
          <tr>
            <td class="tb" width="23%">
              股份下降
            </td>
            <td colspan="3">
              {viewData.ChangeObject.map((item, k) => {
                return (
                  <span key={k}>
                    {renderAddObjectStart(item)}
                    {renderAddObjectEnd2(item, '持股比例从', '下降')}
                  </span>
                );
              })}
            </td>
          </tr>
        );
      }
      if (viewData.IsLess === 0 && viewData.RiseObject && viewData.RiseObject.length) {
        return (
          <tr>
            <td class="tb" width="23%">
              股份上升
            </td>
            <td colspan="3">
              {viewData.RiseObject.map((item, k) => {
                return (
                  <span key={k}>
                    {renderAddObjectStart(item)}
                    {renderAddObjectEnd2(item, '持股比例从', '上升')}
                    {renderAddObjectEnd3(item, '，认缴金额', '上升')}
                  </span>
                );
              })}
            </td>
          </tr>
        );
      }
      if (viewData?.ChangeObject?.length > 0 || viewData?.RiseObject?.length > 0) {
        return (
          <tr>
            <td class="tb" width="23%">
              股份下降
            </td>
            <td width="27%">
              {viewData?.ChangeObject?.map((item, k) => {
                return (
                  <span key={k}>
                    {renderAddObjectStart(item)}
                    {renderAddObjectEnd2(item, '持股比例从', '下降')}
                  </span>
                );
              })}
            </td>
            <td class="tb" width="23%">
              股份上升
            </td>
            <td width="27%">
              {viewData?.RiseObject?.map((item, k) => {
                return (
                  <span key={k}>
                    {renderAddObjectStart(item)}
                    {renderAddObjectEnd2(item, '持股比例从', '上升')}
                  </span>
                );
              })}
            </td>
          </tr>
        );
      }
      return null;
    };
    return (
      <div>
        <QPlainTable class="ntable">
          <tr>
            <td width="23%" class="tb">
              变更企业
            </td>
            <td width="27%">
              <div>
                <QEntityLink QEntityLink coyObj={{ Name: viewData.Name, KeyNo: viewData.KeyNo }}></QEntityLink>
              </div>
            </td>
            <td width="23%" class="tb">
              发现变更日期
            </td>
            <td width="27%">{viewData.ChangeDate}</td>
          </tr>
          {!isNil(viewData.IsLess) ? renderContent() : null}
          <tr v-show={viewData.IsExit !== 0}>
            <td class="tb" width="23%">
              退出
            </td>
            <td colspan="3">
              {viewData?.ExitObject.map((item, k) => {
                return (
                  <span key={k}>
                    {renderAddObjectStart(item)}
                    {renderAddObjectEnd(item, '，退出前持股', '，退出前持股未知')}
                  </span>
                );
              })}
            </td>
          </tr>
          <tr v-show={viewData.IsAdd !== 0}>
            <td class="tb" width="23%">
              新增
            </td>
            <td colspan="3">
              {viewData?.AddObject.map((item, k) => {
                return (
                  <span key={k}>
                    {renderAddObjectStart(item)}
                    {renderAddObjectEnd(item, '，持股')}
                  </span>
                );
              })}
            </td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});

export default partnerChange;
