import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link'; // 请替换为实际的组件路径
// import AppRelateCases from '@/components/app-relate-cases'; // 请替换为实际的组件路径
// import AppIcon from '@/components/app-icon'; // 请替换为实际的组件路径
import { formatNumber } from '@/utils/format'; // 请替换为实际的工具函数路径
import dateformat from '@/filters/dateformat';

/**
 * 被执行人详情
 * src/components/app-modal/app-risk/components/zhixing.vue
 */
export default defineComponent({
  name: 'ZhixingDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () => (
      <div>
        <table class="ntable">
          <tr>
            <td width="154" class="tb">
              案号
            </td>
            <td width="326">
              {props.viewData.CaseSearchId && props.viewData.CaseSearchId.length !== 0 ? (
                <QEntityLink
                  rel="nofollow"
                  href={`/embed/courtCaseDetail?caseId=${props.viewData.CaseSearchId[0]}&title=${props.viewData.Anno}`}
                  target="_blank"
                  v-html={props.viewData.Anno || '-'}
                ></QEntityLink>
              ) : (
                <span>{props.viewData.Anno || '-'}</span>
              )}
            </td>
            <td class="tb" width="154">
              被执行人
            </td>
            <td>
              {props.viewData.NameKeyNoCollection ? (
                <QEntityLink coy-arr={props.viewData.NameKeyNoCollection}></QEntityLink>
              ) : (
                <span>{props.viewData.Name || '-'}</span>
              )}
            </td>
          </tr>
          <tr>
            <td class="tb">证件号/组织机构代码</td>
            <td>{props.viewData.Partycardnum || '-'}</td>

            <td class="tb">
              疑似申请执行人
              <a-popover placement="bottom">
                <div slot="content" style="width:260px;">
                  该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证
                </div>
                {/* <AppIcon type="zhushi" class="font-16i"></AppIcon> */}
              </a-popover>
            </td>
            <td colspan="3">
              {props.viewData.SqrInfo && props.viewData.SqrInfo.length > 0 ? (
                <QEntityLink coy-arr={props.viewData.SqrInfo}></QEntityLink>
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>
          <tr>
            <td class="tb">执行标的(元)</td>
            <td>{formatNumber(props.viewData.Biaodi)}</td>
            <td class="tb">执行法院</td>
            <td>{props.viewData.Executegov || '-'}</td>
          </tr>
          <tr>
            <td class="tb">立案日期</td>
            <td colspan="3">{dateformat(props.viewData.Liandate, 'YYYY-MM-DD') || '-'}</td>
          </tr>
        </table>
        {/* FIXME: 实现 AppRelateCases */}
        {/* <AppRelateCases search-params={props.viewData}></AppRelateCases> */}
      </div>
    );
  },
});
