/* eslint-disable camelcase */
import { computed, defineComponent, onMounted, reactive, ref } from 'vue';
import _ from 'lodash';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import QRichTable from '@/components/global/q-rich-table';

const pickSAarryKeys = (array, keys) => {
  if (!array || !keys) return {};
  if (_.isString(array)) {
    try {
      array = JSON.parse(array);
    } catch (e) {
      console.log(e);
    }
  }
  if (_.isArray(array)) {
    const temp = {};
    _.forEach(keys, (v) => {
      temp[v] = [];
    });
    try {
      _.forEach(array, (v) => {
        _.forEach(keys, (o) => {
          if (_.isString(v[o])) v[o] = JSON.parse(v[o]);
          if (_.isArray(v[o])) {
            temp[o] = temp[o].concat(v[o]);
          } else if (_.isObject(v[o])) {
            temp[o].push(v[o]);
          }
        });
      });
    } catch (e) {
      console.log(e);
    }
    return temp;
  }
  return {};
};

/**
 * 最终受益人变更详情
 * src/components/app-modal/app-risk/components/finalBeneficiary.vue
 */

const finalBeneficiary = defineComponent({
  name: 'finalBeneficiary',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const BeforeContent_1 = ref([]);
    const BeforeContent_2 = ref([]);
    const AfterContent_1 = ref([]);
    const AfterContent_2 = ref([]);

    const changeStatus = ref(1);

    const columns = reactive([
      {
        title: '企业名称',
        align: 'left',
        customCell: 'company',
      },
      {
        title: '变更后受益股份',
        width: 240,
        dataIndex: 'percent',
      },
    ]);

    const list = ref([]);
    const pageInfo = ref();

    const changeExtend = computed(() => {
      const { ChangeExtend, Category } = props.viewData;
      let ce = {};
      try {
        if (ChangeExtend && _.isString(ChangeExtend)) {
          ce = JSON.parse(ChangeExtend);
        }
      } catch (e) {
        console.log(e);
      }
      if (Category === 241) {
        if (_.isEmpty(ce)) {
          ce = [];
        }
        // eslint-disable-next-line no-use-before-define
        formatCeList(ce);
      }
      return ce;
    });

    const beforeContent = computed(() => {
      return props.viewData.BeforeContent ? JSON.parse(props.viewData.BeforeContent) : null;
    });

    const afterContent = computed(() => {
      return props.viewData.AfterContent ? JSON.parse(props.viewData.AfterContent) : null;
    });

    onMounted(() => {
      let t = props.viewData.ChangeExtend;
      if (props.viewData.Category === 114 && t) {
        if (_.isString(t)) t = JSON.parse(t);
        try {
          _.forEach(
            _.groupBy(t, (v) => {
              if (_.isString(v.ChangeExtend)) v.ChangeExtend = JSON.parse(v.ChangeExtend);
              return v.ChangeExtend.T;
            }),
            (v, k) => {
              const temp = pickSAarryKeys(v, ['BeforeContent', 'AfterContent']);
              // eslint-disable-next-line @typescript-eslint/no-shadow
              Object.keys(temp).forEach((t) => {
                this[`${t}_${k}`] = temp[t];
              });
            }
          );
        } catch (e) {
          console.log(e);
        }
      }

      if (props.viewData.Category === 241 && (changeExtend.value as any)?.length) {
        changeStatus.value = changeExtend.value[0].ChangeStatus;
        if (changeStatus.value !== 1) {
          columns[1].title = '变更前受益股份';
        }
        // eslint-disable-next-line no-use-before-define
        formatList();
      }
    });

    const isPerson = (keyNo) => {
      return _.startsWith(keyNo, 'p');
    };

    const formatCeList = (ce) => {
      if (ce.length) {
        ce.forEach((el) => {
          el.ceObj = null;
          el.percent = '';
          if (el.ChangeExtend && _.isString(el.ChangeExtend)) {
            try {
              const obj = JSON.parse(el.ChangeExtend);
              if (!_.isEmpty(obj)) {
                el.ceObj = _.pick(obj, ['KeyNo', 'Name', 'Org']);
                el.percent = obj.PercentTotal;
              }
            } catch (e) {
              console.log(e);
            }
          }
        });
      }
    };

    const getStatusList = (status) => {
      return _.filter(changeExtend.value, (el: any) => el.ChangeStatus === status);
    };

    const formatList = () => {
      list.value = getStatusList(changeStatus.value) || [];
      if (list.value.length) {
        pageInfo.value = {
          pageIndex: 1,
          pageSize: 5,
          total: list.value.length,
        };
      }
    };

    const pageChange = (p) => {
      pageInfo.value.pageIndex = p;
    };

    return {
      BeforeContent_1,
      BeforeContent_2,
      AfterContent_1,
      AfterContent_2,
      changeStatus,
      columns,
      list,
      pageInfo,
      changeExtend,
      beforeContent,
      afterContent,
      isPerson,
      formatCeList,
      getStatusList,
      formatList,
      pageChange,
    };
  },
  render() {
    const { viewData } = this;
    const changeExtend = this.changeExtend as any;

    const renderTd = (key1, key2, key3) => {
      if (viewData.Category === 114) {
        if (this[key1]) {
          return (
            <span>
              {this[key1].map((el) => {
                return (
                  <span>
                    <a target="_blank" href={`/firm/${el.KeyNo}.html`}>
                      {el.Name}
                    </a>
                    ，受益股份{el.PercentTotal} <br />
                  </span>
                );
              })}
            </span>
          );
        }
        if (this[key2].length) {
          return <QEntityLink coy-arr={this[key2]} />;
        }
        return null;
      }
      if (viewData.Enumeration === 1) {
        if (this[key3] && this[key3].length) {
          return <QEntityLink coy-arr={this[key3]} />;
        }
        return '-';
      }
      return (
        <span>
          <QEntityLink v-if={this[key3]} coy-obj={{ KeyNo: this[key3].KeyNo, Name: this[key3].Name }} />
          <span v-else>-</span>
          <span v-if={this[key3] && this[key3].PercentTotal}>，受益股份{this[key3].PercentTotal}</span>
        </span>
      );
    };

    const render241 = () => {
      if (!Array.isArray(changeExtend)) {
        return null;
      }

      return (
        <div>
          {changeExtend.map((item, index) => {
            const typeDescription = item.ChangeStatus === 1 ? '成为' : '不再是';
            const stageDescription = item.ChangeStatus === 1 ? '变更后' : '变更前';
            return (
              <span key={`changeExtend-${index}`}>
                {' '}
                {typeDescription}
                <QEntityLink coyObj={item.ceObj} />
                的最终受益人
                {item.percent ? (
                  <span>
                    ，{stageDescription}受益股份：{item.percent}
                  </span>
                ) : null}
              </span>
            );
          })}
        </div>
      );
    };

    const renderExtend1 = () => {
      if (viewData.Extend1.length) {
        return (
          <div>
            {viewData.Extend1.map((item, index) => {
              return item.map((obj) => {
                return (
                  <div>
                    <div>
                      <QEntityLink coyObj={{ KeyNo: obj.KeyNo, Name: obj.Name || '-' }} />
                      ，受益股份{obj.PercentTotal}
                    </div>
                    <div>
                      {obj.path.map((v, k) => {
                        return (
                          <div key={`${index}-${k}`}>
                            <div>
                              股权路径{k + 1}（占比约{v[0].PercentTotal}）
                            </div>
                            <div>
                              {v.map((vc) => {
                                return (
                                  <span>
                                    <QEntityLink coyObj={{ KeyNo: vc.KeyNo, Name: vc.Name || '-' }} />
                                    <span v-if="ki < v.length - 1" class={[!vc.Percent && 'arrow-no-text', 'arrow']}>
                                      {vc.Percent}
                                    </span>
                                  </span>
                                );
                              })}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              });
            })}
          </div>
        );
      }
      return '-';
    };

    if (viewData.Category === 241 && changeExtend && changeExtend.length > 3) {
      return (
        <div>
          <QPlainTable class="ntable">
            <tr>
              <td class="tb" style="width:23%;">
                变更人员
              </td>
              <td style="width:27%;">
                <QEntityLink coyObj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
              </td>
              <td class="tb" style="width:23%;">
                更新时间
                <app-glossary-info info-id="241" />
              </td>
              <td style="width:27%;">{viewData.ChangeDate ? viewData.ChangeDate.split(' ')[0] : '-'}</td>
            </tr>
          </QPlainTable>
          {this.list.length > 0 ? (
            <div>
              <div class="m-t-lg m-b-sm">{this.changeStatus === 1 ? '成为以下企业的最终受益人' : '不再是以下企业的最终受益人'}</div>
              <QRichTable
                columns={this.columns}
                data-source={this.list}
                page-info={this.pageInfo}
                local-page
                scroll-top-after-page="0"
                onPageChange={this.pageChange}
                scopedSlots={{
                  company: (item) => {
                    return <QEntityLink coyObj={item} />;
                  },
                }}
              ></QRichTable>
            </div>
          ) : null}
        </div>
      );
    }
    return (
      <QPlainTable>
        <tr>
          <td class="tb" style="width:23%;">
            变更{viewData.Category === 241 ? '人员' : '企业'}
          </td>
          <td style="width:27%;">
            <template v-if="!isPerson(viewData.KeyNo) || viewData.Category === 241">
              <QEntityLink coyObj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
            </template>
            <template v-else>
              <QEntityLink
                v-if="changeExtend && (changeExtend.KeyNo || changeExtend.Name)"
                coy-obj={{ KeyNo: changeExtend.KeyNo, Name: changeExtend.Name }}
              />
              <span v-else>-</span>
            </template>
          </td>
          <td class="tb" style="width:23%;">
            更新时间
            <app-glossary-info info-id="241" />
          </td>
          <td style="width:27%;">{viewData.ChangeDate ? viewData.ChangeDate.split(' ')[0] : '-'}</td>
        </tr>
        {viewData.DataType === 1 && (
          <tr>
            <td class="tb">变更前</td>
            <td>{renderTd('BeforeContent_2', 'BeforeContent_1', 'beforeContent')}</td>
            <td class="tb">变更后</td>
            <td>{renderTd('AfterContent_2', 'AfterContent_1', 'afterContent')}</td>
          </tr>
        )}
        {viewData.Category === 241 && (
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">{render241()}</td>
          </tr>
        )}
        {viewData.Extend1 && (
          <tr>
            <td class="tb">股权链</td>
            <td colspan="3" class="td-with-path">
              {renderExtend1()}
            </td>
          </tr>
        )}
      </QPlainTable>
    );
  },
});

export default finalBeneficiary;
