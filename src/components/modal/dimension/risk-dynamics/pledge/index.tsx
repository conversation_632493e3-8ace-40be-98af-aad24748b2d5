import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link'; // 请根据实际组件路径修改
import QPlainTable from '@/components/global/q-plain-table'; // 请根据实际组件路径修改
import dateformat from '@/filters/dateformat'; // 请根据实际过滤器路径修改

import { handleCompanyStatusTag } from './utils/handle-tag'; // 请根据实际工具函数路径修改

/**
 * 股权出质
 * src/components/app-modal/app-risk/components/pledge.vue
 */
export default defineComponent({
  name: 'PledgeDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const statusTd = (status) => {
      if (status === '存续（在营、开业、在册）') {
        status = '存续';
      }
      const statusClass = handleCompanyStatusTag(status);
      if (status) {
        return <span class={`nstatus ${statusClass}`}>{status}</span>;
      }
      return '-';
    };

    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td class="tb" style="width:150px">
              登记编号
            </td>
            <td style="width:335px">{props.viewData.RegisterNo || '-'}</td>
            <td class="tb" style="width:150px">
              状态
            </td>
            <td domPropsInnerHTML={statusTd(props.viewData.PledgeStatus)} />
          </tr>
          <tr>
            <td class="tb">出质人</td>
            <td>
              {Array.isArray(props.viewData.PledgorList) && props.viewData.PledgorList.length ? (
                <QEntityLink coy-arr={props.viewData.PledgorList} coy-maxlength="999" />
              ) : (
                <QEntityLink coyObj={props.viewData.PledgorInfo} />
              )}
            </td>
            <td class="tb">出质人证件号码</td>
            <td v-if={props.viewData.PledgorNo}>{props.viewData.PledgorNo || '-'}</td>
            <td v-else>-</td>
          </tr>
          <tr>
            <td class="tb">出质股权数额</td>
            <td>{props.viewData.PledgeNumPub || '-'}</td>
            <td class="tb">出质股权标的企业</td>
            <td>
              <QEntityLink coyObj={props.viewData.RelatedCompanyInfo} />
            </td>
          </tr>
          <tr>
            <td class="tb">质权人</td>
            <td colspan="3">
              {Array.isArray(props.viewData.PledgeeList) && props.viewData.PledgeeList.length ? (
                <QEntityLink coy-arr={props.viewData.PledgeeList} coy-maxlength="999" />
              ) : (
                <QEntityLink coyObj={props.viewData.PledgeeInfo} />
              )}
            </td>
          </tr>
          <tr>
            <td class="tb">质权人证件号</td>
            <td v-if={props.viewData.PledgeeNo}>{props.viewData.PledgeeNo || '-'}</td>
            <td v-else>-</td>
            <td class="tb">股权出质登记日期</td>
            <td>{dateformat(props.viewData.RegisterDate, 'YYYY-MM-DD')}</td>
          </tr>
          <tr v-if={props.viewData.DataStatus === 0}>
            <td class="tb">撤销原因</td>
            <td>{props.viewData.LogoutReason || '-'}</td>
            <td class="tb">撤销日期</td>
            <td>{dateformat(props.viewData.LogoutDate, 'YYYY-MM-DD')}</td>
          </tr>
          <tr v-if={props.viewData.Source !== '工商'}>
            <td class="tb">数据来源</td>
            <td colspan="3">{props.viewData.Source || '-'}</td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
