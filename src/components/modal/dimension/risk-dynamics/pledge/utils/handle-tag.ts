import _ from 'lodash';

/**
 *  处理公司登记状态标签颜色的统一方法
 */
const primaryTagList = ['其他', '其它'];

const dangerTagList = [
  '清算',
  '撤销',
  '责令关闭',
  '吊销',
  '已撤销',
  '终止破产',
  '涂销破产',
  '清理完结',
  '清理',
  '破产清算完结',
  '破产程序终结',
  '破产',
  '废止清算完结',
  '废止许可完结',
  '废止许可',
  '废止认许',
  '废止认许完结',
  '废止登记完结',
  '废止登记',
  '废止',
  '撤销完结',
  '撤销无需清算',
  '撤销许可',
  '撤销',
  '撤销认许',
  '撤销认许完结',
  '撤回认许',
  '撤回认许完结',
  '无效',
  '終止破產',
  '涂銷破產',
  '清理完結',
  '破產清算完結',
  '破產程序終結',
  '破產',
  '廢止清算完結',
  '廢止許可完結',
  '廢止許可',
  '廢止認許完結',
  '廢止登記完結',
  '廢止登記',
  '廢止',
  '撤銷完結',
  '撤銷無需清算',
  '撤銷許可',
  '撤銷',
  '撤銷認許',
  '撤銷認許完結',
  '撤回認許',
  '撤回認許完結',
  'ABANDONED',
  'CANCELED',
  'CANCELLED',
  'DELINQUENT',
  'DISSOLVED',
  'EXPIRED',
  'FORFEITED',
  'INACTIVE',
  'REMOVED',
  'SUSPENDED',
  'TERMINATED',
  'WITHDRAWN',
  'REVOKED',
  'LIQUIDATION',
  'STRIKE OFF',
  'STRIKING OFF',
  'DEFUNCT',
  'NOT AVAILabel',
  'DORMANT',
  'CAPTURED',
  'DEREGISTRATION',
  'DUPLICATE',
  'DEREGISTERED',
  'NO STATUS',
  'ARCHIVE',
  '撤銷',
  '廢止',
];

const successTagList = [
  '在业',
  '存续',
  '筹建',
  '新申请用户',
  '已成立事先报批',
  '成立事先报批中',
  '成立中',
  '名称核准发起中',
  '名称核准通过',
  '已成立',
  '正常',
  '仍注册',
  '接管',
  '核准设立',
  '核准认许',
  '核准许可登记',
  '核准许可',
  '核准报备',
  '核准许可报备',
  '核准登记',
  '有效',
  '核准設立',
  '核准認許',
  '核准許可登記',
  '核准許可',
  '核准報備',
  '核准許可報備',
  '核准登記',
  '核準設立',
  '核準認許',
  '核準許可登記',
  '核準許可',
  '核準報備',
  '核準許可報備',
  '核準登記',
  'ACTIVE',
  'CONVERTED',
  'INCORPORATED',
  'MERGED',
  'OTHERS',
  'PERPETUAL',
  'REDEEMED',
  'UNKNOWN',
  'AMALGAMATED',
  'IN BUSINESS',
  'RESERVED',
  'CONVERSION',
  'RE-INSTATEMENT',
  '存续（在营、开业、在册）',
  '迁出',
  '迁入',
];

const warningTagList = [
  '注销',
  '停业',
  '歇业',
  '已告解散',
  '已终止注册',
  '已終止註冊',
  '停業',
  '名称核准不通过',
  '注销中',
  '已终止营业地点',
  '不再是独立的实体',
  '休止活动',
  '重整',
  '解散',
  '解散清算完结',
  '设立但已解散',
  '合并解散',
  '分割解散',
  '撤销设立',
  '撤销登记完结',
  '撤销登记',
  '撤回登记',
  '撤回登记完结',
  '解散清算完結',
  '設立但已解散',
  '合併解散',
  '撤銷設立',
  '撤銷登記完結',
  '撤銷登記',
  '撤回登記完結',
  '撤回登記',
  '撤銷設立',
  '撤銷登記完结',
  '撤銷登記',
  '撤回登記完结',
  '撤回登记',
  '設立但已解散',
  '合並解散',
  '解散清算完結',
];

// statusEmtion兼容海外状态
export const handleCompanyStatusTag = (shortStatus, statusEmtion?) => {
  const colorMap = {
    primaryTagList: 'text-primary',
    dangerTagList: 'text-danger',
    successTagList: 'text-success',
    warningTagList: 'text-warning',
    noTagList: 'text-gray',
  };

  if (_.includes(primaryTagList, shortStatus)) {
    return colorMap.primaryTagList;
  }
  if (_.includes(dangerTagList, shortStatus)) {
    return colorMap.dangerTagList;
  }
  if (_.includes(successTagList, shortStatus)) {
    return colorMap.successTagList;
  }
  if (_.includes(warningTagList, shortStatus)) {
    return colorMap.warningTagList;
  }
  const overseaStatusClassObj = {
    0: 'text-danger',
    1: 'text-success',
    2: 'text-warning',
  };
  if (overseaStatusClassObj[statusEmtion]) {
    return overseaStatusClassObj[statusEmtion];
  }
  return colorMap.noTagList;
};
