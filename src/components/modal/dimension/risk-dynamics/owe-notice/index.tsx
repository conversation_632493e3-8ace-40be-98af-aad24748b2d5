// 导入需要的组件和服务
import { defineComponent } from 'vue';

// import uiService from '@/ui-service';
import QPlainTable from '@/components/global/q-plain-table';
import QEntityLink from '@/components/global/q-entity-link';
import dateformat from '@/filters/dateformat';
import { formatNumber } from '@/utils/format';

/**
 * 欠税公告详情
 * src/components/app-modal/app-risk/components/oweNotice.vue
 */
export default defineComponent({
  // Props，如果有的话
  name: 'OweNoticeDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    // 使用 Composition API 进行数据声明和处理
    const showMapModal = (address) => {
      // FIXME: 弹出地址窗口
      // uiService.showMapModal({ address });
      console.warn('address', address);
    };

    // 返回渲染函数
    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td class="tb" width="23%">
              企业名称
            </td>
            <td colspan="3">
              <div>
                <QEntityLink coyObj={{ KeyNo: props.viewData.KeyNo, Name: props.viewData.CompanyName }} />
              </div>
            </td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              纳税人类型
            </td>
            <td width="27%">{props.viewData.Type || '-'}</td>
            <td width="23%" class="tb">
              纳税人识别号
            </td>
            <td width="27%">{props.viewData.IdentifyNo || '-'}</td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              负责人姓名
            </td>
            <td width="27%">
              <QEntityLink coyObj={props.viewData.Oper} />
            </td>
            <td width="23%" class="tb">
              证件号码
            </td>
            <td width="27%">{props.viewData.IdNo || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="23%">
              经营地点
            </td>
            <td colspan="3">
              {props.viewData.Addr ? <a onClick={() => showMapModal(props.viewData.Addr)}>{props.viewData.Addr || '-'}</a> : '-'}
            </td>
          </tr>
          <tr>
            <td class="tb" width="23%">
              欠税税种
            </td>
            <td colspan="3">{props.viewData.Category || '-'}</td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              欠税余额(元)
            </td>
            <td width="27%">{formatNumber(props.viewData.Balance || '0')}</td>
            <td width="23%" class="tb">
              当前新发生的欠税余额(元)
            </td>
            <td width="27%">{formatNumber(props.viewData.NewBal || '0')}</td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              发布单位
            </td>
            <td width="27%">{props.viewData.IssuedBy || '-'}</td>
            <td width="23%" class="tb">
              发布日期
            </td>
            <td width="27%">{dateformat(props.viewData.PublishDate, 'YYYY-MM-DD')}</td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
