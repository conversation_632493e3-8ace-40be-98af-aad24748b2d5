import { defineComponent } from 'vue';

import QPlainTable from '@/components/global/q-plain-table'; // 请根据实际组件路径修改
import QEntityLink from '@/components/global/q-entity-link'; // 请根据实际组件路径修改
import dateformat from '@/filters/dateformat'; // 请根据实际过滤器路径修改

/**
 * 经营状态变更详情
 * src/components/app-modal/app-risk/components/businessStatus.vue
 */
export default defineComponent({
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    return (
      <div>
        <QPlainTable>
          <tr>
            <td width="23%" class="tb">
              变更企业：
            </td>
            <td width="27%">
              {viewData.KeyNo ? (
                <QEntityLink coyObj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
              ) : (
                <span>{viewData.Name || '-'}</span>
              )}
            </td>
            <td width="23%" class="tb">
              变更日期：
            </td>
            <td width="27%">{dateformat(new Date(viewData.CreateDate).getTime(), 'YYYY-MM-DD')}</td>
          </tr>
          <tr>
            <td class="tb" width="23%">
              变更前：
            </td>
            <td width="27%">{viewData.BeforeContent || '-'}</td>
            <td class="tb" width="23%">
              变更后：
            </td>
            <td width="27%">{viewData.AfterContent || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="23%">
              登记机关：
            </td>
            <td colspan="3">{viewData.Extend1}</td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
