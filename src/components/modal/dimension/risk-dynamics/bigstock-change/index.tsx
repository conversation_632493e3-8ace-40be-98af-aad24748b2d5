import { defineComponent } from 'vue';

import QPlainTable from '@/components/global/q-plain-table';

const bigStockChange = defineComponent({
  name: 'bigStockChange',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    return (
      <div>
        <table class="ntable" style="margin-bottom: 0;position: relative;bottom: -1px;">
          <tr>
            <td width="120" class="tb">
              变更企业
            </td>
            <td width="360">
              <q-entity-link coy-obj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }}></q-entity-link>
            </td>
            <td width="120" class="tb">
              更新时间
              <q-glossary-info
                tooltip={
                  '更新时间为企查查根据公示平台进行大数据分析后，更新展示在企查查平台内的时间；与官网公示的公布、审核、登记等时间定义不同。'
                }
              />
            </td>
            <td>{viewData.ChangeDate || '-'}</td>
          </tr>
        </table>
        {viewData.AfterObject?.length > 0 && viewData.BeforeObject?.length > 0 ? (
          <table class="ntable">
            <tr>
              <td width="120" class="tb">
                变更前
              </td>

              <td>
                {viewData.AfterObject.map((item) => {
                  return (
                    <div>
                      <q-entity-link coy-obj={item}></q-entity-link>
                      {item.StockPercent ? (
                        <span>
                          ，变更前持股{item.StockPercent} <br />
                        </span>
                      ) : null}
                    </div>
                  );
                })}
              </td>
            </tr>
            <tr>
              <td width="120" class="tb">
                变更后
              </td>

              <td>
                {viewData.BeforeObject.map((item) => {
                  return (
                    <div>
                      <q-entity-link coy-obj={item}></q-entity-link>
                      {item.StockPercent ? (
                        <span>
                          ，变更前持股{item.StockPercent} <br />
                        </span>
                      ) : null}
                    </div>
                  );
                })}
              </td>
            </tr>
          </table>
        ) : (
          <table class="ntable">
            <tr>
              <td width="120" class="tb">
                变更后
              </td>
              <td>
                {viewData.BeforeObject?.length > 0 && <q-entity-link coy-arr={viewData.BeforeObject} sep="、"></q-entity-link>}
                {viewData.AfterObject?.length > 0 && <q-entity-link coy-arr={viewData.AfterObject} sep="、"></q-entity-link>}
                {viewData.BeforeObject?.length ? '不再是' : '成为'}
                <q-entity-link coy-obj="{KeyNo: viewData.KeyNo, Name: viewData.Name}" />
                的大股东
              </td>
            </tr>
          </table>
        )}
      </div>
    );
  },
});

export default bigStockChange;
