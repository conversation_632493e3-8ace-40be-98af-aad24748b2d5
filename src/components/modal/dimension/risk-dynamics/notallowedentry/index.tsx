import { computed, defineComponent } from 'vue';

/**
 * 未准入境详情
 * src/components/app-modal/app-risk/components/notallowedentry.vue
 */
export default defineComponent({
  name: 'NotallowedentryDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const type = computed(() => {
      return props.viewData?.ProductType === '工业品' ? 2 : 1;
    });

    return () => (
      <div>
        <table class="ntable" id="flowlist">
          {type.value === 2 ? (
            <template>
              <tr>
                <td width="23%" class="tb">
                  产品名称
                </td>
                <td width="27%">{props.viewData.ProductName || '-'}</td>
                <td width="23%" class="tb">
                  品牌
                </td>
                <td width="27%">{props.viewData.TradeMark || '-'}</td>
              </tr>
              <tr>
                <td class="tb">数量/重量</td>
                <td>{props.viewData.Weight || '-'}</td>
                <td class="tb">原产地</td>
                <td>{props.viewData.Origin || '-'}</td>
              </tr>
              <tr>
                <td class="tb">进口商名称</td>
                <td colspan="3">{props.viewData.ImporterName || '-'}</td>
              </tr>
              <tr>
                <td class="tb">进境口岸</td>
                <td>{props.viewData.Entryport || '-'}</td>
                <td class="tb">处置措施</td>
                <td>{props.viewData.Disposemeasure || '-'}</td>
              </tr>
              <tr>
                <td class="tb">不合格原因</td>
                <td colspan="3">{props.viewData.RefusedReason || '-'}</td>
              </tr>
              <tr>
                <td class="tb">报送时间</td>
                <td>{props.viewData.SubmissionMonth || '-'}</td>
                <td class="tb">附件</td>
                <td>{props.viewData.Attachmentid || '-'}</td>
              </tr>
            </template>
          ) : (
            <template>
              <tr>
                <td width="23%" class="tb">
                  HS编码
                </td>
                <td width="27%">{props.viewData.HsCode || '-'}</td>
                <td width="23%" class="tb">
                  检验检疫编号
                </td>
                <td width="27%">{props.viewData.Quarantinecode || '-'}</td>
              </tr>
              <tr>
                <td class="tb">产品名称</td>
                <td>{props.viewData.ProductName || '-'}</td>
                <td class="tb">产地</td>
                <td>{props.viewData.Origin || '-'}</td>
              </tr>
              <tr>
                <td class="tb">生产企业信息</td>
                <td colspan="3">{props.viewData.TradeMark || '-'}</td>
              </tr>
              <tr>
                <td class="tb">数量/重量</td>
                <td>{props.viewData.Weight || '-'}</td>
                <td class="tb">进境口岸</td>
                <td>{props.viewData.Entryport || '-'}</td>
              </tr>
              <tr>
                <td class="tb">进口商备案号</td>
                <td colspan="3">{props.viewData.Eoriimporter || '-'}</td>
              </tr>
              <tr>
                <td class="tb">未准入境的事实</td>
                <td colspan="3">{props.viewData.RefusedReason || '-'}</td>
              </tr>
              <tr>
                <td class="tb">报送时间</td>
                <td>{props.viewData.SubmissionMonth || '-'}</td>
                <td class="tb">原文</td>
                {/* <td>{props.viewData.Attachment ? <AppFileLogo label="" url={props.viewData.Attachment} /> : '-'}</td> */}
                {/* FIXME: ICON */}
                <td>
                  {props.viewData.Attachment ? (
                    <a href={props.viewData.Attachment} target="_blank">
                      下载
                    </a>
                  ) : (
                    '-'
                  )}
                </td>
              </tr>
            </template>
          )}
        </table>
      </div>
    );
  },
});
