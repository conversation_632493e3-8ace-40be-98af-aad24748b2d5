// 导入 Vue 模块
import { defineComponent } from 'vue';

import { formatNumber } from '@/utils/format';
import dateformat from '@/filters/dateformat';
import QPlainTable from '@/components/global/q-plain-table';
import QEntityLink from '@/components/global/q-entity-link';

/**
 * 票据违约
 * src/components/app-modal/app-risk/components/billdefault.vue
 */
export default defineComponent({
  name: 'BilldefaultDetail',
  // 接收props
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },

  setup(props) {
    // 使用 render 函数替代模板
    return () => (
      <div>
        {/* 替换 <table class="ntable"> 为 <QPlainTable> */}
        <QPlainTable>
          <tr>
            <td class="tb" style="width:160px">
              承兑人
            </td>
            <td colspan="3">
              {/* 替换 <app-coy> 为 <QEntityLink> */}
              {props.viewData.NameAndKeyNo ? (
                <QEntityLink coy-obj={{ Name: props.viewData.NameAndKeyNo.Name, KeyNo: props.viewData.NameAndKeyNo.KeyNo }} />
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>
          <tr>
            <td class="tb" style="width:160px">
              截止日期
            </td>
            <td style="width:325px">{props.viewData.EndDate ? dateformat(props.viewData.EndDate, 'YYYY-MM-DD') : '-'}</td>
            <td class="tb" style="width:160px">
              披露日期
            </td>
            <td style="width:325px">{props.viewData.PublishDate ? dateformat(props.viewData.PublishDate, 'YYYY-MM-DD') : '-'}</td>
          </tr>
          <tr>
            <td class="tb">承兑人开户机构</td>
            <td>
              {/* 替换 <app-coy> 为 <QEntityLink> */}
              {props.viewData.BankNameAndKeyNo ? (
                <QEntityLink coy-obj={{ Name: props.viewData.BankNameAndKeyNo.Name, KeyNo: props.viewData.BankNameAndKeyNo.KeyNo }} />
              ) : (
                <span>-</span>
              )}
            </td>
            <td class="tb">累计承兑发生额</td>
            <td>
              {props.viewData.AccuAcceptanceAmount ? (
                <span>
                  {formatNumber(props.viewData.AccuAcceptanceAmount) ? `${formatNumber(props.viewData.AccuAcceptanceAmount)}元` : '-'}
                </span>
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>
          <tr>
            <td class="tb">承兑余额</td>
            <td>
              {props.viewData.AcceptanceBalance ? (
                <span>{formatNumber(props.viewData.AcceptanceBalance) ? `${formatNumber(props.viewData.AcceptanceBalance)}元` : '-'}</span>
              ) : (
                <span>-</span>
              )}
            </td>
            <td class="tb">累计逾期发生额</td>
            <td>
              {props.viewData.AccuOverdueAmount ? (
                <span>{formatNumber(props.viewData.AccuOverdueAmount) ? `${formatNumber(props.viewData.AccuOverdueAmount)}元` : '-'}</span>
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>
          <tr>
            <td class="tb">逾期余额</td>
            <td>
              {props.viewData.OverdueBalance ? (
                <span>{formatNumber(props.viewData.OverdueBalance) ? `${formatNumber(props.viewData.OverdueBalance)}元` : '-'}</span>
              ) : (
                <span>-</span>
              )}
            </td>
            <td class="tb">票据介质</td>
            <td>{props.viewData.BillMedium || '-'}</td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
