import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link'; // 请根据实际组件路径修改
import QPlainTable from '@/components/global/q-plain-table'; // 请根据实际组件路径修改
import { formatNumber } from '@/utils/format'; // 请根据实际工具函数路径修改
import dateformat from '@/filters/dateformat'; // 请根据实际过滤器路径修改

/**
 * 担保信息详情
 * src/components/app-modal/app-risk/components/guarantor.vue
 */
export default defineComponent({
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td width="20%" class="tb">
              担保方
            </td>
            <td colspan="3">
              <QEntityLink coy-arr={props.viewData.Guarantee} />
            </td>
          </tr>
          <tr>
            <td width="20%" class="tb">
              被担保方
            </td>
            <td colspan="3">
              <QEntityLink coy-arr={props.viewData.Vouchee} />
            </td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              公告日期
            </td>
            <td width="30%">{props.viewData.PublicDate ? dateformat(props.viewData.PublicDate) : '-'}</td>
            <td class="tb" width="20%">
              担保方式
            </td>
            <td width="30%">{props.viewData.GuaranteeType || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              担保金额(万元)
            </td>
            <td width="30%">{formatNumber(props.viewData.GuaranteeMoney)}</td>
            <td class="tb" width="20%">
              币种
            </td>
            <td width="30%">{props.viewData.GuaranteeCurrency || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              担保期限
            </td>
            <td width="30%">{props.viewData.GuaranteePeriod || '-'}</td>
            <td class="tb" width="20%">
              担保起始日
            </td>
            <td width="30%">{props.viewData.GuaranteeStartDate ? dateformat(props.viewData.GuaranteeStartDate) : '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              担保终止日
            </td>
            <td width="30%">{props.viewData.GuaranteeEndDate ? dateformat(props.viewData.GuaranteeEndDate) : '-'}</td>
            <td class="tb" width="20%">
              是否履行完毕
            </td>
            <td width="30%">
              {props.viewData.IfCompleteDesc === '已履行完毕' ? <span class="nstatus text-success">已履行完毕</span> : null}
              {props.viewData.IfCompleteDesc === '未履行完毕' ? <span class="nstatus text-warning">未履行完毕</span> : null}
              {!['已履行完毕', '已履行完毕'].includes(props.viewData.IfCompleteDesc) ? '-' : null}
            </td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              是否关联交易
            </td>
            <td width="30%">{props.viewData.IfRelatedTradeDesc || '-'}</td>
            <td class="tb" width="20%">
              交易日期
            </td>
            <td width="30%">{props.viewData.TradeDate ? dateformat(props.viewData.TradeDate) : '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              报告期
            </td>
            <td width="30%">{props.viewData.ReportDate ? dateformat(props.viewData.ReportDate) : '-'}</td>
            <td class="tb" width="20%">
              报告期类别
            </td>
            <td width="30%">{props.viewData.ReportTypeDesc || '-'}</td>
          </tr>
          <tr>
            <td width="20%" class="tb">
              担保事件说明
            </td>
            <td colspan="3">{props.viewData.GuaranteeIntro || '-'}</td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
