// 导入 Vue 模块
import { defineComponent, computed } from 'vue';

// 导入需要的组件
import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';

/**
 * 注销备案详情
 * src/components/app-modal/app-risk/components/enliqDetail.vue
 */
export default defineComponent({
  name: 'EnliqDetail',
  // 接收props
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
    enliqData: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    // 使用 ref 创建响应式变量
    const isLiqBAInfoExist = computed(() => props.viewData.Detail.LiqBAInfo !== undefined);
    const isCreditorNoticeInfoExist = computed(() => props.viewData.Detail.CreditorNoticeInfo !== undefined);
    const isChangeInfoExist = computed(() => props.viewData.Detail.ChangeInfos && props.viewData.Detail.ChangeInfos.ChangeContent);

    // 使用 render 函数替代模板
    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td class="tb" width="150px">
              公告状态
            </td>
            <td colspan="2">{props.viewData.NoticeStatus || '-'}</td>
          </tr>

          {/* 清算组备案信息 */}
          <tr v-show={isLiqBAInfoExist.value}>
            <td class="tb" width="150px" rowspan="10">
              清算组备案信息
            </td>
            <td width="220px">企业名称</td>
            <td width="600px">
              <QEntityLink
                coy-obj={{
                  Name: props.viewData.Detail.LiqBAInfo?.CompanyName,
                  KeyNo: props.viewData.keyNo_p,
                }}
              />
            </td>
          </tr>
          <tr v-show={isLiqBAInfoExist.value}>
            <td width="220px">统一社会信用代码/注册号</td>
            <td width="600px">{props.viewData.Detail.LiqBAInfo?.CreditCode || '-'}</td>
          </tr>
          {/* 其他清算组备案信息的行，类似上述的结构 */}

          {/* 债权人公告信息 */}
          <tr v-show={isCreditorNoticeInfoExist.value}>
            <td class="tb" width="150px" rowspan="9">
              债权人公告信息
            </td>
            <td width="220px">企业名称</td>
            <td width="600px" v-show={isCreditorNoticeInfoExist.value}>
              <QEntityLink
                coy-obj={{
                  Name: props.viewData.Detail.CreditorNoticeInfo?.CompanyName,
                  KeyNo: props.viewData.keyNo_p,
                }}
              />
            </td>
            <td width="600px" v-show={!isCreditorNoticeInfoExist.value}>
              -
            </td>
          </tr>
          {/* 其他债权人公告信息的行，类似上述的结构 */}

          {/* 修改信息 */}
          <tr v-show={isChangeInfoExist.value}>
            <td class="tb" width="150px" rowspan="8">
              修改信息
            </td>
            <td width="220px">修改内容</td>
            <td width="600px">{props.viewData.Detail.ChangeInfos?.ChangeContent || '-'}</td>
          </tr>
          {/* 其他修改信息的行，类似上述的结构 */}
        </QPlainTable>
      </div>
    );
  },
});
