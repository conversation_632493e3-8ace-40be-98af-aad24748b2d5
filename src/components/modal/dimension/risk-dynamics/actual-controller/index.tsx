import { defineComponent, ref, onMounted } from 'vue';
import _ from 'lodash';

import QEntityLink from '@/components/global/q-entity-link';
import QRichTable from '@/components/global/q-rich-table';

/**
 * 实际控制人变更详情
 * src/components/app-modal/app-risk/components/actualController.vue
 */

const actualController = defineComponent({
  name: 'actualController',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const pathList = ref<any[]>([]);
    const changeStatus = ref(1);
    const columns = [
      {
        title: '企业名称',
        align: 'left',
        customCell: 'company',
      },
    ];
    const list = ref([]);
    const pageInfo = ref();

    const isPerson = (keyNo) => _.startsWith(keyNo, 'p');

    const pageChange = (p) => {
      pageInfo.value.pageIndex = p;
    };

    onMounted(() => {
      if (props.viewData.Category === 240 && props.viewData.ChangeObject?.length) {
        changeStatus.value = props.viewData.ChangeObject[0].ChangeStatus;
        list.value = props.viewData.ChangeObject || [];
        if (list.value.length) {
          pageInfo.value = {
            pageIndex: 1,
            pageSize: 5,
            total: list.value.length,
          };
        }
      }

      if (props.viewData.OriginalData?.Paths?.length) {
        props.viewData.OriginalData.Paths.forEach((el) => {
          if (el?.length) {
            const subList: any[] = [];
            el.forEach((v) => {
              //   const lineAttrs = kzrHelper.handleLinkForKzr(v) || {};
              const lineAttrs = {};
              subList.push({
                ...v,
                ...lineAttrs,
              });
            });
            pathList.value.push(subList);
          }
        });
      }
    });

    return {
      pathList,
      changeStatus,
      columns,
      list,
      pageInfo,
      isPerson,
      pageChange,
    };
  },
  render() {
    return (
      <div>
        {this.viewData.Category === 240 && this.viewData.ChangeObject && this.viewData.ChangeObject.length > 3 ? (
          <div>
            <table class="ntable">{/* ... */}</table>
            {this.list.length > 0 && (
              <div>
                <div class="m-t-lg m-b-sm">{this.changeStatus === 1 ? '成为以下企业的实际控制人' : '不再是以下企业的实际控制人'}</div>
                <QRichTable
                  columns={this.columns}
                  data-source={this.list}
                  page-info={this.pageInfo}
                  local-page
                  scroll-top-after-page={0}
                  onPageChange={this.pageChange}
                  scopedSlots={{
                    company: (item) => {
                      return <QEntityLink coyObj={item} />;
                    },
                  }}
                ></QRichTable>
              </div>
            )}
          </div>
        ) : (
          <table class="ntable">{/* ... */}</table>
        )}
        {this.viewData.Category === 240 && (
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">
              {this.viewData.ChangeObject && this.viewData.ChangeObject.length ? (
                this.viewData.ChangeObject.map((item, index) => (
                  <div key={`co_item_${index}`}>
                    {item.ChangeStatus === 1
                      ? `成为${(<QEntityLink coyObj={item} />)}的实际控制人`
                      : `不再是${(<QEntityLink coyObj={item} />)}的实际控制人`}
                  </div>
                ))
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>
        )}
        {/* ... */}
      </div>
    );
  },
});

export default actualController;
