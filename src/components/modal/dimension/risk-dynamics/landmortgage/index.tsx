import { defineComponent } from 'vue';

import QPlainTable from '@/components/global/q-plain-table'; // 请根据实际组件路径修改
import QEntityLink from '@/components/global/q-entity-link'; // 请根据实际组件路径修改
import dateformat from '@/filters/dateformat'; // 请根据实际过滤器路径修改

/**
 * 土地抵押详情
 * src/components/app-modal/app-risk/components/landmortgage.vue
 */
export default defineComponent({
  name: 'LandmortgageDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },

  setup(props) {
    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td class="tb" width="20%">
              宗地标识
            </td>
            <td width="30%">{props.viewData.LandSign || '-'}</td>
            <td class="tb" width="20%">
              宗地编号
            </td>
            <td width="30%">{props.viewData.LandNo || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              所在行政区
            </td>
            <td width="30%">{props.viewData.AdministrativeAreaName || '-'}</td>
            <td class="tb" width="20%">
              土地面积(公顷)
            </td>
            <td width="30%">{props.viewData.Acreage || '-'}</td>
          </tr>
          <tr>
            <td width="20%" class="tb">
              宗地座落
            </td>
            <td colspan="3">{props.viewData.Address || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              土地他项权利人证号
            </td>
            <td width="30%">{props.viewData.ObligeeNo || '-'}</td>
            <td class="tb" width="20%">
              土地使用权证号
            </td>
            <td width="30%">{props.viewData.UsufructNo || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              土地抵押人名称
            </td>
            <td width="30%">
              <QEntityLink coy-obj={{ Name: props.viewData.MortgagorName.Name, KeyNo: props.viewData.MortgagorName.KeyNo }} />
            </td>
            <td class="tb" width="20%">
              土地抵押人性质
            </td>
            <td width="30%">{props.viewData.MortgagorNature || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              土地抵押权人
            </td>
            <td width="30%">
              <QEntityLink coy-obj={{ Name: props.viewData.MortgagePeople.Name, KeyNo: props.viewData.MortgagePeople.KeyNo }} />
            </td>
            <td class="tb" width="20%">
              抵押土地用途
            </td>
            <td width="30%">{props.viewData.MortgagePurpose || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              抵押土地权属性质与使用权类型
            </td>
            <td width="30%">{props.viewData.NatureAndType || '-'}</td>
            <td class="tb" width="20%">
              抵押面积(公顷)
            </td>
            <td width="30%">{props.viewData.MortgageAcreage || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              评估金额(万元)
            </td>
            <td width="30%">{props.viewData.AssessmentPrice || '-'}</td>
            <td class="tb" width="20%">
              抵押金额(万元)
            </td>
            <td width="30%">{props.viewData.MortgagePrice || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              土地抵押登记起始日期
            </td>
            <td width="30%">{dateformat(props.viewData.OnBoardStartTime, 'YYYY-MM-DD') || '-'}</td>
            <td class="tb" width="20%">
              土地抵押结束日期
            </td>
            <td width="30%">{dateformat(props.viewData.OnBoardEndTime, 'YYYY-MM-DD') || '-'}</td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
