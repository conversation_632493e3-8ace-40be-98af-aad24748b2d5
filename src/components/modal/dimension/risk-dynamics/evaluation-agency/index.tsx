import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link'; // 请替换为实际的组件路径
import QPlainTable from '@/components/global/q-plain-table'; // 请替换为实际的组件路径
import dateformat from '@/filters/dateformat';

/**
 * 询价评估详情
 * src/components/app-modal/app-risk/components/evaluationAgency.vue
 */
export default defineComponent({
  name: 'EvaluationAgencyDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () => (
      <div>
        <QPlainTable>
          <tr>
            <td width="23%" class="tb">
              当事人：
            </td>
            <td width="27%">
              <QEntityLink coy-arr={props.viewData.OwnerInfo} />
            </td>
            <td width="23%" class="tb">
              案号：
            </td>
            <td>{props.viewData.CaseNo || '-'}</td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              标的物：
            </td>
            <td width="27%">{props.viewData.SubjectName || '-'}</td>
            <td width="23%" class="tb">
              财产类型：
            </td>
            <td width="27%">{props.viewData.SubjectType || '-'}</td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              法院名称：
            </td>
            <td width="27%">{props.viewData.CourtName || '-'}</td>
            <td width="23%" class="tb">
              摇号日期：
            </td>
            <td width="27%">{dateformat(props.viewData.LotteryDate, 'YYYY-MM-DD') || '-'}</td>
          </tr>
          <tr>
            <td width="23%" class="tb">
              选定评估机构：
            </td>
            <td colspan="3">
              <QEntityLink coy-arr={props.viewData.AgencyInfo} />
            </td>
          </tr>
        </QPlainTable>
      </div>
    );
  },
});
