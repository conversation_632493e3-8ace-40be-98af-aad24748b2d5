import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import dateformat from '@/filters/dateformat';

/**
 * 税收违法详情
 * src/components/app-modal/app-risk/components/taxIllegal.vue
 */
export default defineComponent({
  name: 'TaxIllegalDetail',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () => (
      <div class="tax-illegal-wrap">
        <div class="mtcaption">注册信息</div>
        <QPlainTable>
          <tr>
            <td class="tb" width="20%">
              纳税人名称
            </td>
            <td width="30%">
              <QEntityLink coyObj={{ KeyNo: props.viewData.TaxpayerId, Name: props.viewData.TaxpayerName }} />
            </td>
            <td class="tb" width="20%">
              纳税人识别号
            </td>
            <td width="30%">{props.viewData.TaxpayerNumber || '-'}</td>
          </tr>
          <tr>
            <td class="tb">组织机构代码</td>
            <td>{props.viewData.OrgCode || '-'}</td>
            <td class="tb">注册地址</td>
            <td>{props.viewData.Address || '-'}</td>
          </tr>
        </QPlainTable>

        <div class="mtcaption">案件信息</div>
        <QPlainTable>
          <tr>
            <td class="tb" width="20%">
              发布日期
            </td>
            <td width="30%">{dateformat(props.viewData.PublishTime, 'YYYY-MM-DD') || '-'}</td>
            <td class="tb" width="20%">
              案件性质
            </td>
            <td width="30%">{props.viewData.CaseNature || '-'}</td>
          </tr>
          <tr>
            <td class="tb">所属税务机关</td>
            <td colspan="3">{props.viewData.TaxGov || '-'}</td>
          </tr>
          <tr>
            <td class="tb">主要违法事实</td>
            <td colspan="3">{props.viewData.IllegalContent || '-'}</td>
          </tr>
          <tr>
            <td class="tb">相关法律依据及税务处理处罚情况</td>
            <td colspan="3">{props.viewData.PunishContent || '-'}</td>
          </tr>
        </QPlainTable>

        {props.viewData.OperInfo && <div class="mtcaption">相关人员及公司</div>}
        {props.viewData.OperInfo && (
          <QPlainTable class="m-b-sm">
            <tr>
              <td class="tb" width="20%">
                法定代表人或负责人
              </td>
              <td width="30%">
                <QEntityLink coy-obj={{ KeyNo: props.viewData.OperInfo.OperId, Name: props.viewData.OperInfo.OperName }} />
              </td>
              <td class="tb" width="20%">
                性别
              </td>
              <td width="30%">{props.viewData.OperInfo.OperGender || '-'}</td>
            </tr>
            <tr>
              <td class="tb">证件名称</td>
              <td>{props.viewData.OperInfo.OperCerType || '-'}</td>
              <td class="tb">证件号码</td>
              <td>{props.viewData.OperInfo.OperCerNo || '-'}</td>
            </tr>
          </QPlainTable>
        )}

        {props.viewData.FinanceChiefInfo && (
          <QPlainTable class="m-b-sm">
            <tr>
              <td class="tb" width="20%">
                负有责任的财务负责人
              </td>
              <td width="30%">
                <QEntityLink
                  coy-obj={{
                    KeyNo: props.viewData.FinanceChiefInfo.FinanceChiefId,
                    Name: props.viewData.FinanceChiefInfo.FinanceChiefName,
                  }}
                />
              </td>
              <td class="tb" width="20%">
                性别
              </td>
              <td width="30%">{props.viewData.FinanceChiefInfo.FinanceChiefGender || '-'}</td>
            </tr>
            <tr>
              <td class="tb">证件名称</td>
              <td>{props.viewData.FinanceChiefInfo.FinanceChiefCerType || '-'}</td>
              <td class="tb">证件号码</td>
              <td>{props.viewData.FinanceChiefInfo.FinanceChiefCerNo || '-'}</td>
            </tr>
          </QPlainTable>
        )}

        {props.viewData.AgencyInfo && (
          <QPlainTable class="m-b-sm">
            <tr>
              <td class="tb" width="20%">
                负有直接责任的中介机构
              </td>
              <td colspan="3">
                <QEntityLink
                  coy-obj={{
                    KeyNo: props.viewData.AgencyInfo.AgencyKeyNo,
                    Name: props.viewData.AgencyInfo.AgencyCompanyName,
                  }}
                />
              </td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                负有直接责任的中介机构从业人员
              </td>
              <td width="30%">
                <QEntityLink
                  coy-obj={{
                    KeyNo: props.viewData.AgencyInfo.AgencyPersonId,
                    Name: props.viewData.AgencyInfo.AgencyPersonName,
                  }}
                />
              </td>
              <td class="tb" width="20%">
                性别
              </td>
              <td width="30%">{props.viewData.AgencyInfo.AgencyPersonGender || '-'}</td>
            </tr>
            <tr>
              <td class="tb">证件名称</td>
              <td>{props.viewData.AgencyInfo.AgencyPersonCerType || '-'}</td>
              <td class="tb">证件号码</td>
              <td>{props.viewData.AgencyInfo.AgencyPersonCerNo || '-'}</td>
            </tr>
          </QPlainTable>
        )}
      </div>
    );
  },
});
