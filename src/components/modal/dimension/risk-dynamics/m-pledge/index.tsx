import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import dateformat from '@/filters/dateformat';

/**
 * 动产抵押详情
 * src/components/app-modal/app-risk/components/mPledge.vue
 */
export default defineComponent({
  name: 'MPledge',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },

  setup(props) {
    return () => (
      <div>
        <div class="mtcaption ">动产抵押登记信息</div>
        <QPlainTable>
          <tbody>
            <tr>
              <td width="170px" class="tb">
                登记机关
              </td>
              <td colspan="3">{props.viewData.RegisterOffice || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="170px">
                登记编号
              </td>
              <td width="315px">{props.viewData.RegisterNo || '-'}</td>
              <td class="tb" width="170px">
                登记日期
              </td>
              <td width="315px">{props.viewData.RegisterDate ? dateformat(props.viewData.RegisterDate, 'YYYY-MM-DD') : '-'}</td>
            </tr>
          </tbody>
        </QPlainTable>

        {Array.isArray(props.viewData?.MPledgeDetail?.PledgeeList) && props.viewData?.MPledgeDetail?.PledgeeList?.length > 0 && (
          <div class="mtcaption ">抵押权人信息</div>
        )}

        {Array.isArray(props.viewData?.MPledgeDetail?.PledgeeList) && props.viewData?.MPledgeDetail?.PledgeeList?.length > 0 && (
          <QPlainTable>
            {props.viewData.MPledgeDetail.PledgeeList.map((obj, index) => [
              <tr key={`tr-1-${index}`}>
                <td class="tb" width="170px">
                  抵押权人名称
                </td>
                <td width="315px" v-if={obj.KeyNo}>
                  <QEntityLink coyObj={{ Name: obj.Name, KeyNo: obj.KeyNo }} />
                </td>
                <td width="315px" v-else>
                  {obj.Name || '-'}
                </td>
                <td class="tb" width="170px">
                  抵押权人证照类型
                </td>
                <td width="315px">{obj.IdentityType || '-'}</td>
              </tr>,
              <tr key={`tr-2-${index}`}>
                <td class="tb">证照号码</td>
                <td>{obj.IdentityNo || '-'}</td>
                <td class="tb">住所地</td>
                <td>{obj.Address || '-'}</td>
              </tr>,
            ])}
          </QPlainTable>
        )}

        {props.viewData?.MPledgeDetail?.GuaranteedCredRight && <div class="mtcaption ">被担保主债权信息</div>}

        {props.viewData?.MPledgeDetail?.GuaranteedCredRight && (
          <QPlainTable>
            <tbody>
              <tr>
                <td width="170px" class="tb">
                  抵押人
                </td>
                <td colspan="3">
                  <QEntityLink coyObj={props.viewData.RelatedCompanyInfo} />
                </td>
              </tr>
              <tr>
                <td width="170px" class="tb">
                  债务人履行债务的期限
                </td>
                <td colspan="3">{props.viewData.MPledgeDetail.GuaranteedCredRight.FulfillObligation || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="170px">
                  种类
                </td>
                <td width="315px">{props.viewData.MPledgeDetail.GuaranteedCredRight.Kind || '-'}</td>
                <td class="tb" width="170px">
                  数额
                </td>
                <td width="315px">{props.viewData.MPledgeDetail.GuaranteedCredRight.Amount || '-'}</td>
              </tr>
              <tr>
                <td class="tb">担保的范围</td>
                <td>{props.viewData.MPledgeDetail.GuaranteedCredRight.AssuranceScope || '-'}</td>
                <td class="tb">备注</td>
                <td>{props.viewData.MPledgeDetail.GuaranteedCredRight.Remark || '-'}</td>
              </tr>
            </tbody>
          </QPlainTable>
        )}

        {Array.isArray(props.viewData?.MPledgeDetail?.GuaranteeList) && props.viewData?.MPledgeDetail?.GuaranteeList?.length > 0 && (
          <div class="mtcaption ">抵押物信息</div>
        )}

        {Array.isArray(props.viewData?.MPledgeDetail?.GuaranteeList) && props.viewData?.MPledgeDetail?.GuaranteeList?.length > 0 && (
          <QPlainTable>
            {props.viewData.MPledgeDetail.GuaranteeList.map((obj, index) => [
              <tr key={`tr-1-${index}`}>
                <td class="tb" width="170px">
                  抵押物名称
                </td>
                <td width="315px">{obj.Name || '-'}</td>
                <td class="tb" width="170px">
                  所有权或使用权归属
                </td>
                <td width="315px" v-if={obj.KeyNoList && obj.KeyNoList.length}>
                  <div v-if={obj.KeyNoList.some((e) => e.position === 4 || e.Position === 4)}>
                    <QEntityLink coy-arr={obj.KeyNoList} />
                  </div>
                  <template v-else>
                    <div>
                      所有权：
                      <QEntityLink coy-arr={obj.KeyNoList.filter((e) => e.position === 2 || e.Position === 2)} />
                    </div>
                    <div>
                      使用权：
                      <QEntityLink coy-arr={obj.KeyNoList.filter((e) => e.position === 3 || e.Position === 3)} />
                    </div>
                  </template>
                </td>
                <td width="315px" v-else>
                  {obj.Ownership || '-'}
                </td>
              </tr>,
              <tr key={`tr-2-${index}`}>
                <td class="tb">数量、质量、状况、所在地等情况</td>
                <td>{obj.Other || '-'}</td>
                <td class="tb">备注</td>
                <td>{obj.Remark || '-'}</td>
              </tr>,
            ])}
          </QPlainTable>
        )}

        {Array.isArray(props.viewData?.MPledgeDetail?.ChangeList) && props.viewData?.MPledgeDetail?.ChangeList?.length > 0 && (
          <div class="mtcaption ">变更信息</div>
        )}

        {Array.isArray(props.viewData?.MPledgeDetail?.ChangeList) && props.viewData?.MPledgeDetail?.ChangeList?.length > 0 && (
          <QPlainTable>
            {props.viewData.MPledgeDetail.ChangeList.map((obj, index) => (
              <tr key={index}>
                <td class="tb" width="170px">
                  变更日期
                </td>
                <td width="315px">{dateformat(obj.ChangeDate, 'YYYY-MM-DD')}</td>
                <td class="tb" width="170px">
                  变更内容
                </td>
                <td width="315px">{obj.ChangeContent || '-'}</td>
              </tr>
            ))}
          </QPlainTable>
        )}

        {props.viewData?.MPledgeDetail?.CancelInfo &&
          (props.viewData.MPledgeDetail.CancelInfo.CancelDate || props.viewData.MPledgeDetail.CancelInfo.CancelReason) && (
            <div class="mtcaption ">注销信息</div>
          )}

        {props.viewData?.MPledgeDetail?.CancelInfo &&
          (props.viewData.MPledgeDetail.CancelInfo.CancelDate || props.viewData.MPledgeDetail.CancelInfo.CancelReason) && (
            <QPlainTable>
              <tbody>
                <tr>
                  <td class="tb" width="170px">
                    注销日期
                  </td>
                  <td width="315px">{dateformat(props.viewData.MPledgeDetail.CancelInfo.CancelDate, 'YYYY-MM-DD')}</td>
                  <td class="tb" width="170px">
                    注销原因
                  </td>
                  <td width="315px">{props.viewData.MPledgeDetail.CancelInfo.CancelReason || '-'}</td>
                </tr>
              </tbody>
            </QPlainTable>
          )}
      </div>
    );
  },
});
