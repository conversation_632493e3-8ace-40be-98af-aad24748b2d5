import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import { dateFormat } from '@/utils/format';
import { defineComponent, PropType } from 'vue';

const OverseasListing = defineComponent({
  name: 'OverseasListing',
  props: {
    viewData: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  setup() {},
  render() {
    return (
      <QPlainTable>
        <tr>
          <td width="20%" class="tb">
            企业名称
          </td>
          <td colspan="3">
            <QEntityLink coyObj={this.viewData.NameAndKeyNo} />
          </td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            更新日期
          </td>
          <td colspan="3">{dateFormat(this.viewData.Publishdate)}</td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            具体内容
          </td>
          <td colspan="3">
            <div domPropsInnerHTML={this.viewData.dynamiccontent || '-'}></div>
          </td>
        </tr>
      </QPlainTable>
    );
  },
});

export default OverseasListing;
