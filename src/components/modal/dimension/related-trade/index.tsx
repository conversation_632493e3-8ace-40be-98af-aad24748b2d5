// 关联企业
import { defineComponent } from 'vue';

const RelatedTrade = defineComponent({
  name: 'relatedTrade',

  props: {
    detailParams: {
      type: Object,
      default: () => null,
    },
  },

  data() {
    return {
      result: {},
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
    };
  },

  methods: {
    async getDetail() {
      const that = this as any;
      const params = {
        keyNo: that.detailParams.KeyNo,
        relatedId: that.detailParams.RelationId,
      };
      const res = await that.$service.finance.relatedTradeDetail(params);
      console.log(res);
      that.result = res?.Result[0] || [];
    },
    pageChange(current, pageSize) {
      const that = this as any;
      that.pagination.current = current;
      that.pagination.pageSize = pageSize;
      that.getDetail();
    },
  },

  mounted() {
    console.log(this);
    (this as any).getDetail();
  },

  render() {
    const that = this as any;
    const { result } = that;
    return (
      <div>
        <q-plain-table>
          <tr>
            <td width="20%" class="tb">
              关联交易方
            </td>
            <td colspan="3">
              <q-entity-link coyArr={result.RelatpartyArray} />
            </td>
          </tr>
          <tr>
            <td class="tb">关联关系</td>
            <td>{result.Relationship || '-'}</td>
            <td class="tb">是否存在控制关系</td>
            <td>{result.IfControl || '-'}</td>
          </tr>
          <tr>
            <td class="tb">交易金额</td>
            <td>{result.TransactionMoney || '-'}</td>
            <td class="tb">交易方式</td>
            <td>{result.TransactionMethod || '-'}</td>
          </tr>
          <tr>
            <td class="tb">公告日期</td>
            <td colspan="3">{result.AnnouncementDate || '-'}</td>
          </tr>
          <tr>
            <td class="tb">支付方式</td>
            <td colspan="3">{result.PaymentMethod || '-'}</td>
          </tr>
          <tr>
            <td class="tb">交易简介</td>
            <td colspan="3" domPropsInnerHTML={result.TransactionStat || '-'}></td>
          </tr>
        </q-plain-table>
      </div>
    );
  },
});

export default RelatedTrade;
