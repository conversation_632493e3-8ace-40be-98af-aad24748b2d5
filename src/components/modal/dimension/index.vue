<template>
  <q-modal
    v-if="isModal"
    v-model="visible"
    :title="title"
    :tooltip="tooltip"
    :size="size"
    :footer="null"
    :destroyOnClose="true"
    @cancel="onHide"
    @ok="onHide"
    wrapClassName="dimensionModal"
  >
    <component
      v-if="viewDetail"
      :is="culComponent"
      :dialog-props="dialogProps"
      :view-data="viewDetail"
      :parameter="parameter"
      @visibleChange="onVisibleChange"
    ></component>
    <component
      v-else-if="detailParams"
      :info="dialogProps.info || {}"
      :is="culComponent"
      :detail-params="detailParams"
      :dialog-props="dialogProps"
      @visibleChange="onVisibleChange"
    ></component>
    <q-no-data v-else old :padding="100"></q-no-data>
  </q-modal>
  <q-drawer
    v-else
    v-model="visible"
    :title="title"
    :tooltip="tooltip"
    :size="size"
    :footer="null"
    :destroyOnClose="true"
    @cancel="onHide"
    @ok="onHide"
    wrapClassName="dimensionModal"
  >
    <component
      v-if="viewDetail"
      :is="culComponent"
      :dialog-props="dialogProps"
      :view-data="viewDetail"
      :parameter="parameter"
      @visibleChange="onVisibleChange"
    ></component>
    <component
      v-else-if="detailParams"
      :info="dialogProps.info || {}"
      :is="culComponent"
      :detail-params="detailParams"
      :dialog-props="dialogProps"
      @visibleChange="onVisibleChange"
    ></component>
    <q-no-data v-else old :padding="100"></q-no-data>
  </q-drawer>
</template>

<script src="./component.js"></script>
<style lang="less">
.dimensionModal {
  .ant-drawer-mask {
    background-color: rgba(0, 0, 0, 0);
  }
  .ant-modal-content {
    // .ant-modal-header {
    // padding: 9px 40px 9px 16px;
    // }
    .ant-modal-body {
      max-height: calc(100vh - 215px);
      padding: 16px;
      overflow: auto;
    }
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(50, 50, 50, 0.25);
      border: 2px solid transparent;
      border-radius: 10px;
      background-clip: padding-box;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: rgba(50, 50, 50, 0.5);
    }

    ::-webkit-scrollbar-track {
      background-color: rgba(50, 50, 50, 0.05);
    }
  }

  .ant-drawer-content-wrapper {
    .ant-drawer-header {
      .ant-drawer-close {
        display: none;
      }
    }
    .ant-drawer-body {
      padding: 15px;
    }
  }
}

.ant-drawer-open {
  transform: translateX(0) !important;
}
</style>
