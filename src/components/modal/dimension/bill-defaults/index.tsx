import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import numberformat from '@/filters/numberformat';
import formatDate from '@/utils/format/date';

function moneyParser(value?: number) {
  if (!value) {
    return '-';
  }
  const formattedStr = numberformat(value);
  return formattedStr ? `${formattedStr}元` : formattedStr;
}

const BillDefaultsDimension = defineComponent({
  name: 'BillDefaultsDimension',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const item = this.viewData;
    return (
      <div>
        <QPlainTable>
          <tbody>
            <tr>
              <td width="23%" class="tb">
                承兑人
              </td>
              <td colspan="3">
                <QEntityLink coyObj={item.NameAndKeyNo}></QEntityLink>
              </td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                截止日期
              </td>
              <td>{formatDate(item.EndDate)}</td>
              <td width="23%" class="tb">
                披露日期
              </td>
              <td>{formatDate(item.PublishDate)}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                承兑人开户机构
              </td>
              <td>{item?.BankNameAndKeyNo?.Name}</td>
              <td width="23%" class="tb">
                累计承兑发生额
              </td>
              <td>{moneyParser(item.AccuAcceptanceAmount)}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                承兑余额
              </td>
              <td>{moneyParser(item.AcceptanceBalance)}</td>
              <td width="23%" class="tb">
                累计逾期发生额
              </td>
              <td>{moneyParser(item.AccuOverdueAmount)}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                逾期余额
              </td>
              <td>{moneyParser(item.OverdueBalance)}</td>
              <td width="23%" class="tb">
                票据介质
              </td>
              <td>{item.BillMedium}</td>
            </tr>
          </tbody>
        </QPlainTable>
      </div>
    );
  },
});

export default BillDefaultsDimension;
