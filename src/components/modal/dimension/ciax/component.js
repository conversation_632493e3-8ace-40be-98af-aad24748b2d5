import moment from 'moment';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    formatRegDate(str) {
      if (str) {
        return moment(str).format('YYYY-MM-DD');
      }
      return '-';
    },
    formatData(str) {
      if (str) {
        return str.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3');
      }
      return '-';
    },
  },
};
