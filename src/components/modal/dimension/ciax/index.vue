<template>
  <q-plain-table>
    <tbody>
      <tr>
        <th>公司</th>
        <td colspan="5">{{ viewData.company_name || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">海关注册编码</th>
        <td width="27%">{{ viewData.reg_no || '-' }}</td>
        <th width="23%">注册海关</th>
        <td width="27%">{{ viewData.reg_gov || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">行政地区</th>
        <td width="27%">{{ viewData.administrative_area || '-' }}</td>
        <th width="23%">经济地区</th>
        <td width="27%">{{ viewData.economic_area || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">经营类别</th>
        <td width="27%">{{ viewData.trade_type || '-' }}</td>
        <th width="23%">特殊贸易区域</th>
        <td width="27%">{{ viewData.special_trade_area || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">海关注销标志</th>
        <td width="27%">{{ viewData.cancellation_flag || '-' }}</td>
        <th width="23%">年报情况</th>
        <td width="27%">{{ viewData.annual_status || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">注册日期</th>
        <td width="27%">{{ formatRegDate(viewData.reg_date) }}</td>
        <th width="23%">报关有效期</th>
        <td width="27%">{{ viewData.expire_date || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">行业种类</th>
        <td width="27%">{{ viewData.industry_type || '-' }}</td>
        <th width="23%">跨境贸易电子商务类型</th>
        <td width="27%">{{ viewData.e_business_type || '-' }}</td>
      </tr>
      <template v-if="viewData.credit_grade && viewData.credit_grade.length">
        <template v-for="(item, index) in viewData.credit_grade">
          <tr :key="`item-${index}`">
            <th width="23%">认定时间</th>
            <td width="27%">{{ item.time || '-' }}</td>
            <th width="23%">信用等级</th>
            <td width="27%">{{ item.grade || '-' }}</td>
          </tr>
          <tr :key="`item-code-${index}`">
            <th>认证证书编码</th>
            <td colspan="5">{{ item.code || '-' }}</td>
          </tr>
        </template>
      </template>
      <template v-else>
        <tr>
          <th>信用等级</th>
          <td colspan="5">-</td>
        </tr>
      </template>
      <template v-if="viewData.punish_info && JSON.parse(viewData.punish_info).length">
        <tr>
          <th colspan="6" align="center">海关行政处罚</th>
        </tr>
        <template v-for="(item, index) in JSON.parse(viewData.punish_info)">
          <tr :key="`item-no-${index}`">
            <th width="23%" rowspan="4" :style="{ 'text-align': 'right' }">{{ index + 1 }}</th>
            <td width="23%">行政处罚决定书编号</td>
            <td colspan="2">{{ item.decisionNo || '-' }}</td>
          </tr>
          <tr :key="`item-party-${index}`">
            <td>当事人</td>
            <td colspan="5">{{ item.party || '-' }}</td>
          </tr>
          <tr :key="`item-penaltDate-${index}`">
            <td>处罚日期</td>
            <td colspan="5">{{ formatData(item.penaltDate) }}</td>
          </tr>
          <tr :key="`item-case-${index}`">
            <td>案件性质</td>
            <td colspan="5">{{ item.case || '-' }}</td>
          </tr>
        </template>
      </template>
      <template v-else>
        <tr>
          <th>海关行政处罚</th>
          <td colspan="5">-</td>
        </tr>
      </template>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
