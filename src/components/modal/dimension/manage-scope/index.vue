<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>{{ viewData.Name || '-' }}</span>
        </td>
        <td width="23%" class="tb">变更日期：</td>
        <td width="27%">
          {{ viewData.ChangeDate || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">变更前：</td>
        <td colspan="3">
          <span v-if="viewData.BeforeContent" v-html="viewData.BeforeContent"></span>
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">变更后：</td>
        <td colspan="3">
          <span v-if="viewData.AfterContent" v-html="viewData.AfterContent"></span>
          <span v-else>-</span>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>
