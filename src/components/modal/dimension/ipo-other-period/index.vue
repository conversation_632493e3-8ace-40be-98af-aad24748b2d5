<template>
  <div>
    <template v-if="viewData.basicInfo && viewData.basicInfo.BasicInfo">
      <section class="text-dark m-b-sm font-bold">基本信息</section>
      <table v-if="viewData.basicInfo && viewData.basicInfo.BasicInfo" class="ntable">
        <tr>
          <td style="width: 13%" class="tb">上市日期</td>
          <td style="width: 20%">{{ viewData.basicInfo.BasicInfo.ListDate | dateformat('YYYY-MM-DD') }}</td>
          <td style="width: 13%" class="tb">股票简称</td>
          <td style="width: 20%">{{ viewData.basicInfo.BasicInfo.Sname || '-' }}</td>
          <td style="width: 13%" class="tb">股票代码</td>
          <td style="width: 20%">{{ viewData.basicInfo.DerivedSymbol || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">上市交易所</td>
          <td>{{ viewData.basicInfo.BasicInfo.ExchangeDesc || '-' }}</td>
          <td class="tb">上市板块</td>
          <td>{{ viewData.basicInfo.BasicInfo.ListSectionDesc || '-' }}</td>
          <td class="tb">上市曾用名</td>
          <td>{{ viewData.basicInfo.BasicInfo.OriginalSname || '-' }}</td>
        </tr>
        <template v-if="viewData.listStatusLabel !== '0'">
          <tr>
            <td class="tb">总市值</td>
            <td>{{ viewData.basicInfo.BasicInfo.MarketValueTotal || '-' }}</td>
            <td class="tb">总股本</td>
            <td>{{ viewData.basicInfo.BasicInfo.TotalShares || '-' }}</td>
            <td class="tb">预测市盈率 <q-glossary-info info-id="330" /></td>
            <td>{{ viewData.basicInfo.BasicInfo.PredictedPeRatio || '-' }}</td>
          </tr>
          <tr>
            <td class="tb">流通值</td>
            <td>{{ viewData.basicInfo.BasicInfo.FloatValueTotal || '-' }}</td>
            <td class="tb">流通股</td>
            <td>{{ viewData.basicInfo.BasicInfo.TotalFloatShares || '-' }}</td>
            <td class="tb">市净率 <q-glossary-info info-id="331" /></td>
            <td>{{ viewData.basicInfo.BasicInfo.PbRatio || '-' }}</td>
          </tr>
          <tr>
            <td class="tb">EPS</td>
            <td>{{ viewData.basicInfo.BasicInfo.EPS || '-' }}</td>
            <td class="tb">表决权差异 <q-glossary-info info-id="333" /></td>
            <td>
              <span v-if="viewData.basicInfo.BasicInfo.VotingRightsDiff === 1">是</span>
              <span v-else-if="viewData.basicInfo.BasicInfo.VotingRightsDiff === 0">否</span>
              <span v-else>-</span>
            </td>
            <td class="tb">是否注册制</td>
            <td>
              <span v-if="viewData.basicInfo.BasicInfo.IsRegister === 1">是</span>
              <span v-else-if="viewData.basicInfo.BasicInfo.IsRegister === 0">否</span>
              <span v-else>-</span>
            </td>
          </tr>
        </template>
      </table>
    </template>
    <template v-if="viewData.IPOInfo">
      <section class="text-dark m-b-sm font-bold">首发信息</section>
      <table class="ntable">
        <tr>
          <td style="width: 13%" class="tb">成立日期</td>
          <td style="width: 20%">{{ viewData.IPOInfo.EstablishDate | dateformat('YYYY-MM-DD') }}</td>
          <td style="width: 13%" class="tb">上市日期</td>
          <td style="width: 20%">{{ viewData.IPOInfo.ListDate | dateformat('YYYY-MM-DD') }}</td>
          <td style="width: 13%" class="tb">网上发行日期</td>
          <td style="width: 20%">{{ viewData.IPOInfo.IssueDateOnline | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <td class="tb">发行方式</td>
          <td>
            <span>{{ viewData.IPOInfo.IssueMethod || '-' }}</span>
          </td>
          <td class="tb">发行市盈率</td>
          <td>{{ viewData.IPOInfo.IssuePERatio || '-' }}</td>
          <td class="tb">发行总量</td>
          <td>
            <span v-if="viewData.IPOInfo.IssueNum">{{ `${viewData.IPOInfo.IssueNum}股` }}</span>
            <span v-else>-</span>
          </td>
        </tr>
        <tr>
          <td class="tb">每股发行价</td>
          <td>{{ viewData.IPOInfo.IssuePrice || '-' }}</td>
          <td class="tb">发行总市值</td>
          <td>{{ viewData.IPOInfo.IssueMarketValue || '-' }}</td>
          <td class="tb">募集资金净额</td>
          <td>{{ viewData.IPOInfo.IssueActualAmount || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">每股面值</td>
          <td>{{ viewData.IPOInfo.IssueParValue || '-' }}</td>
          <td class="tb">定价中签率</td>
          <td>{{ viewData.IPOInfo.IssueOnlineRate || '-' }}</td>
          <td class="tb">网下配售中签率</td>
          <td>{{ viewData.IPOInfo.IssueOfflineRate || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">首日开盘价</td>
          <td>{{ viewData.IPOInfo.OpenPriceFirstDay || '-' }}</td>
          <td class="tb">首日收盘价</td>
          <td>{{ viewData.IPOInfo.ClosePriceFirstDay || '-' }}</td>
          <td class="tb">首日换手率</td>
          <td>{{ viewData.IPOInfo.ExchangeRateFirstDay || '-' }}</td>
        </tr>
      </table>
    </template>
  </div>
</template>

<script>
export default {
  name: 'IpoOthersPeriod',

  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style lang="less" src="../style.less" scoped></style>
