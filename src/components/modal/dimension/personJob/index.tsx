// 任职企业
import _ from 'lodash';
import moment from 'moment';
import { defineComponent } from 'vue';

import styles from './style.module.less';

const PersonJobs = defineComponent({
  name: 'PersonJobs',
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
        pageSizeOptions: ['5'],
        showSizeChanger: false,
      },
    };
  },
  computed: {
    columns() {
      return [
        {
          title: '企业名称',
          width: 421,
          customRender: (text, item) => {
            return (
              <div class={styles.namew}>
                <div>
                  <q-entity-avatar src={item.ImageUrl} size={40} name={item.Name}></q-entity-avatar>
                </div>
                <span class={styles.right}>
                  <q-entity-link coy-obj={{ KeyNo: item.KeyNo, Name: item.Name }} />
                  {item.stockRole ? (
                    <div class={styles.stock}>
                      <span>持股比例：</span>
                      <span> {item.stockRole.Value}</span>
                    </div>
                  ) : (
                    <div class={styles.stock}>持股比例：未持股</div>
                  )}
                </span>
              </div>
            );
          },
        },
        {
          title: '职位',
          width: 220,
          align: 'center',
          customRender: (text, item) => {
            return item.Roles.filter((i) => i.Type !== 4).map((i) => <div class={styles.subtd}>{i.Value}</div>);
          },
        },
        {
          title: '任职时间',
          width: 220,
          align: 'center',
          customRender: (text, item) => {
            return item.Roles.filter((i) => i.Type !== 4).map((i) => <div class={styles.subtd}>{i.timeDesc}</div>);
          },
        },
      ];
    },
  },

  methods: {
    async getDetail() {
      const that = this as any;
      const { title, ...detailParams } = that.detailParams;
      const params = {
        ...detailParams,
        pageIndex: that.pagination.current,
        pageSize: that.pagination.pageSize,
      };
      const res = await that.$service.group.getPersonSummaryInfo(params);
      const result = res?.Result[0] || [];
      that.pagination.total = result.Paging.TotalRecords || 0;
      (this as any).formatData(result);
    },
    formatData(data) {
      data.RelatedCoyInfos.forEach((x) => {
        const stockRole = _.find(x.Roles, (y) => y.Type === 4);
        if (stockRole) {
          x.stockRole = stockRole;
        }
        const compareIndicator = [0, -1];
        x.Roles.forEach((y) => {
          const hasStartDate = y.StartDate !== undefined && compareIndicator.indexOf(+y.StartDate) < 0;
          const hasEndDate = y.EndDate !== undefined && compareIndicator.indexOf(+y.EndDate) < 0;
          if (!hasStartDate && !hasEndDate) {
            y.timeDesc = '至今';
          } else if (!hasStartDate && hasEndDate) {
            y.timeDesc = `截止 ${moment(y.EndDate * 1000).format('YYYY.MM')}`;
          } else if (hasStartDate && !hasEndDate) {
            y.timeDesc = `${moment(y.StartDate * 1000).format('YYYY.MM')} 至今`;
          } else if (hasStartDate && hasEndDate) {
            y.timeDesc = `${moment(y.StartDate * 1000).format('YYYY.MM')} 至 ${moment(y.EndDate * 1000).format('YYYY.MM')}`;
          } else {
            y.timeDesc = '-';
          }
        });
      });
      this.tableData = data.RelatedCoyInfos || [];
    },
    pageChange(current, pageSize) {
      const that = this as any;
      that.pagination.current = current;
      that.pagination.pageSize = pageSize;
      that.getDetail();
    },
  },

  mounted() {
    const that = this as any;
    // 分页及选择每页个数回调设置
    that.pagination.onChange = that.pageChange;
    that.pagination.onShowSizeChange = that.pageChange;
    (this as any).getDetail();
  },
  render() {
    const that = this as any;
    const { tableData, columns } = that;
    return (
      <div class={styles.personjobtable}>
        <q-rich-table dataSource={tableData} columns={columns} pagination={that.pagination} rowKey={'KeyNo'} />
      </div>
    );
  },
});

export default PersonJobs;
