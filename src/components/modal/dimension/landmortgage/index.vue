<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">宗地标识</td>
        <td width="30%">{{ viewData.LandSign || '-' }}</td>
        <td class="tb" width="20%">宗地编号</td>
        <td width="30%">{{ viewData.LandNo || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">所在行政区</td>
        <td width="30%">{{ viewData.AdministrativeAreaName || '-' }}</td>
        <td class="tb" width="20%">土地面积(公顷)</td>
        <td width="30%">{{ viewData.Acreage || '-' }}</td>
      </tr>
      <tr>
        <td width="20%" class="tb">宗地座落</td>
        <td colspan="3">{{ viewData.Address || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">土地他项权利人证号</td>
        <td width="30%">{{ viewData.ObligeeNo || '-' }}</td>
        <td class="tb" width="20%">土地使用权证号</td>
        <td width="30%">{{ viewData.UsufructNo || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">土地抵押人名称</td>
        <td width="30%">
          <q-entity-link :coy-obj="viewData.MortgagorName"></q-entity-link>
        </td>
        <td class="tb" width="20%">土地抵押人性质</td>
        <td width="30%">{{ viewData.MortgagorNature || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">土地抵押权人</td>
        <td width="30%">
          <q-entity-link :coy-obj="viewData.MortgagePeople"></q-entity-link>
        </td>
        <td class="tb" width="20%">抵押土地用途</td>
        <td width="30%">{{ viewData.MortgagePurpose || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">抵押土地权属性质与使用权类型</td>
        <td width="30%">{{ viewData.NatureAndType || '-' }}</td>
        <td class="tb" width="20%">抵押面积(公顷)</td>
        <td width="30%">{{ viewData.MortgageAcreage || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">评估金额(万元)</td>
        <td width="30%">{{ numberToHuman(viewData.AssessmentPrice) || '-' }}</td>
        <td class="tb" width="20%">抵押金额(万元)</td>
        <td width="30%">{{ numberToHuman(viewData.MortgagePrice) || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">土地抵押登记起始日期</td>
        <td width="30%">{{ viewData.OnBoardStartTime | dateformat('YYYY-MM-DD') }}</td>
        <td class="tb" width="20%">土地抵押结束日期</td>
        <td width="30%">{{ viewData.OnBoardEndTime | dateformat('YYYY-MM-DD') }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
import { numberToHuman } from '@/utils/number-formatter';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    numberToHuman,
  },
};
</script>
