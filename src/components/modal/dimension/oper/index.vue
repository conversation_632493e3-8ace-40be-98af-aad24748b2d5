<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>{{ viewData.Name || '-' }}</span>
        </td>
        <td width="23%" class="tb">变更日期：</td>
        <td width="27%">
          {{ viewData.ChangeDate || '-' }}
        </td>
      </tr>
      <tr v-if="viewData.BeforeObject">
        <td class="tb" width="23%">变更前：</td>
        <td colspan="3">
          <q-entity-link
            v-if="viewData.BeforeObject.KeyNo"
            :coy-obj="{
              KeyNo: viewData.BeforeObject.KeyNo,
              Name: viewData.BeforeObject.Name,
            }"
          />
          <span v-else>{{ viewData.BeforeObject.Name || '-' }}</span>
          <span v-if="viewData.Extend">，{{ viewData.Extend }}</span>
        </td>
      </tr>
      <tr v-if="viewData.AfterObject">
        <td class="tb" width="23%">变更后：</td>
        <td colspan="3">
          <q-entity-link
            v-if="viewData.AfterObject.KeyNo"
            :coy-obj="{
              KeyNo: viewData.AfterObject.KeyNo,
              Name: viewData.AfterObject.Name,
            }"
          />
          <span v-else>{{ viewData.AfterObject.Name || '-' }}</span>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>
