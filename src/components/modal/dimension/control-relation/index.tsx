import { defineComponent } from 'vue';

import QPlainTable from '@/components/global/q-plain-table';
import CompanyLogo from '@/components/company-logo';
import AssociationPath from '@/components/association-path';

const ControlRelation = defineComponent({
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const dataSource = this.viewData?.Names?.[0] || {};
    const startCompany = {
      Name: this.viewData.CompanyName,
      KeyNo: this.viewData.KeyNo,
      Level: dataSource.Level,
      Percent: 0,
      PercentTotal: 0,
    };
    const pathData = dataSource.Paths.map((item) => {
      const fullPath = [startCompany, ...item];
      return fullPath.map((data, idx) => {
        return {
          ...data,
          underStr: fullPath[idx + 1] ? fullPath[idx + 1].Percent : data.Percent,
        };
      });
    });
    return (
      <QPlainTable>
        <tr>
          <td class="tb" width="180px">
            控制企业名称
          </td>
          <td>
            <div class="flex items-center" style={{ gap: '14px' }}>
              <CompanyLogo src={dataSource.ImageUrl} id={dataSource.KeyNo} name={dataSource.Name} />
              <q-entity-link coy-obj={{ Name: dataSource.Name, KeyNo: dataSource.KeyNo, Org: dataSource.Org }} />
            </div>
          </td>
        </tr>
        <tr>
          <td class="tb">投资比例</td>
          <td>{dataSource.PercentTotal}</td>
        </tr>
        <tr>
          <td class="tb">投资链</td>
          <td>
            <AssociationPath paths={pathData} showTitle showBackground></AssociationPath>
          </td>
        </tr>
      </QPlainTable>
    );
  },
});

export default ControlRelation;
