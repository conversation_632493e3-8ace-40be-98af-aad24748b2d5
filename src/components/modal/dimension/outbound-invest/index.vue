<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">{{ !isPerson(viewData.KeyNo) ? '变更企业' : '变更人员' }}：</td>
        <td width="27%">
          <q-entity-link v-if="viewData" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>-</span>
        </td>
        <td width="23%" class="tb">更新时间<q-glossary-info info-id="241" />：</td>
        <td width="27%">
          {{ viewData.ChangeDate || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">{{ viewData.ChangeStatus === 1 ? '新增' : '退出' }}投资企业：</td>
        <td colspan="3">
          <q-entity-link
            v-if="viewData.InvestTargetObject"
            :coy-obj="{ KeyNo: viewData.InvestTargetObject.KeyNo, Name: viewData.InvestTargetObject.Name }"
          />
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">法定代表人：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.OperObject" :coy-obj="{ KeyNo: viewData.OperObject.KeyNo, Name: viewData.OperObject.Name }" />
          <span v-else>-</span>
        </td>
        <td class="tb" width="23%">注册资本：</td>
        <td width="27%">
          {{ viewData.RegistCap || '-' }}
        </td>
      </tr>
      <tr v-if="viewData.ChangeStatus === 1">
        <td class="tb" width="23%">投资比例：</td>
        <td width="27%">
          {{ viewData.InvestPercent || viewData.AfterContent || '-' }}
        </td>
        <td class="tb" width="23%">投资数额：</td>
        <td width="27%">
          {{ viewData.InvestAmount || '-' }}
        </td>
      </tr>
      <tr v-else>
        <td class="tb" width="23%">退出前持股比例：</td>
        <td width="27%">
          {{ viewData.InvestPercent || viewData.BeforeContent || '-' }}
        </td>
        <td class="tb" width="23%">退出前持股数额：</td>
        <td width="27%">
          {{ viewData.InvestAmount || '-' }}
        </td>
      </tr>
      <tr v-if="viewData.RelateChange">
        <td class="tb" width="23%">关联变更：</td>
        <td colspan="3">{{ viewData.RelateChange }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
import _ from 'lodash';

export default {
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    isPerson(keyNo) {
      return _.startsWith(keyNo, 'p');
    },
  },
};
</script>
