import { Pagination, ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';

import { numberFormat } from '@/utils/number-formatter';

export default {
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      zhCN,
      numberFormat,
      entity: {},
      list: [],
      pageInfo: {
        pageIndex: 1,
        pageSize: 5,
        total: 0,
      },
      columns: [
        {
          title: '案件名称',
          align: 'left',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '案件类型',
          width: '8%',
          customCell: (item) => {
            if (item.CaseTypeArray?.length) {
              return {
                domProps: {
                  innerHTML: item.CaseTypeArray.join('<br>'),
                },
              };
            }
            return '-';
          },
        },
        {
          title: '案件身份',
          width: '12%',
          customCell: (item) => {
            // let roleD = '-'
            let roleRL = [];
            let showInfo = '';
            if (item.CaseRoleSearch) {
              let arr;
              try {
                arr = JSON.parse(item.CaseRoleSearch);
                // eslint-disable-next-line no-empty
              } catch {}
              if (arr?.length) {
                arr.forEach((role) => {
                  if (role.N === this.$route.params.id) {
                    // roleD = role.D
                    roleRL = role.RL;
                  }
                });
              }
              if (roleRL?.length) {
                roleRL.forEach((rl, index) => {
                  showInfo += rl.T + rl.R;
                  if (rl.LRD) {
                    let classtype;
                    if (['获得支持', '对方不被支持', '获得部分支持'].includes(rl.LRD)) {
                      classtype = 'text-success';
                    } else if (['诉讼中止', '被解除查封', '被执行完毕', '被解除查冻扣', '执行中止'].includes(rl.LRD)) {
                      classtype = 'text-warning';
                    } else if (['不被支持', '对方被支持', '被驳回', '被查封', '被查冻扣', '终结本次执行'].includes(rl.LRD)) {
                      classtype = 'text-danger';
                    } else {
                      classtype = 'text-gray';
                    }

                    showInfo += `<span class="${classtype}">[${rl.LRD}]</span>`;
                  }

                  if (index < roleRL.length - 1) {
                    showInfo += '<br>';
                  }
                });
              }
            }
            return {
              domProps: {
                innerHTML: showInfo,
              },
            };
            // return showInfo;
          },
        },
        {
          title: '案由',
          width: '10%',
          align: 'left',
          dataIndex: 'CaseReason',
        },
        {
          title: '案号',
          width: '10%',
          align: 'left',
          scopedSlots: { customRender: 'sameCaseReason' },
        },
        {
          title: '案件金额(元)',
          width: '9%',
          align: 'right',
          scopedSlots: { customRender: 'caseMoney' },
          // customRender: (item) => {
          //   if (item.AmtInfo?.Amt) {
          //     return numberFormat(item.AmtInfo.Amt, 2);
          //   }
          //   return '-';
          // },
        },
        {
          title: '法院',
          width: '10%',
          customCell: (item) => {
            if (item.CourtList?.length) {
              return {
                domProps: {
                  innerHTML: item.CourtList.join('<br>'),
                },
              };
            }
            return '-';
          },
        },
        {
          title: '最新案件进程',
          width: '9%',
          dataIndex: 'LatestTrialRound',
        },
        {
          title: '最新进程日期',
          width: 80,
          dateFormat: true,
          dataIndex: 'LastestDateNew',
          scopedSlots: {
            customRender: 'date',
          },
        },
      ],
    };
  },
  mounted() {
    this.getData();
    this.getCompanyDetail();
  },
  components: {
    ConfigProvider,
    Pagination,
  },
  computed: {
    isPerson() {
      return this.detailParams.keyNo[0] === 'p';
    },
  },
  methods: {
    getData() {
      this.$service.dimensionDetail
        .sameCaseList({
          keyNo: this.detailParams.keyNo,
          seriesGroupId: this.detailParams.groupId,
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        })
        .then((data) => {
          if (data) {
            this.pageInfo.total = data.Paging?.TotalRecords || 0;
            this.list = data.Result || [];
            console.log(this.pageInfo, 'this.pageInfo');
          }
        });
    },
    getCompanyDetail() {
      this.$service[this.isPerson ? 'person' : 'company']
        .getDetail({
          keyNo: this.isPerson ? undefined : this.detailParams.keyNo,
          personId: this.isPerson ? this.detailParams.keyNo : undefined,
        })
        .then((data) => {
          this.entity = data;
        });
    },
    pageChange(pageIndex) {
      this.pageInfo.pageIndex = pageIndex;
      this.getData();
    },
    pageLimit(total) {
      if (total > 5000) {
        return 5000;
      }
      return total;
    },
    detail(item) {
      this.$emit('detailClick', item);
    },
    showCaseReason(list) {
      if (list && list.length) {
        return list.join('<br>');
      }
      return '-';
    },
  },
};
