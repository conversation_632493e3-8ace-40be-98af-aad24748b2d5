<template>
  <div>
    <div class="mtcaption">
      关联<template v-if="detailParams.keyNo && detailParams.keyNo[0] === 'p'">人员</template><template v-else>企业</template>：
      <q-entity-link :coy-obj="{ Name: entity.Name, KeyNo: detailParams.keyNo }"></q-entity-link>
    </div>
    <q-rich-table
      v-if="list.length > 0"
      :columns="columns"
      :data-source="list"
      :page-info="pageInfo"
      :scroll-outer="'.app-risk-detail .modal-body'"
      @pageChange="pageChange"
    >
      <template slot="name" slot-scope="item">
        <q-link v-if="item.Id" :to="`/caseDetail/${item.Id}`">{{ item.CaseName }}</q-link>
        <span v-else> {{ item.CaseName || '-' }}</span>
      </template>
      <template slot="sameCaseReason" slot-scope="item">
        <q-link v-if="item.AnNoList && item.AnNoList.length" :href="`/caseDetail/${item.Id}`">
          <span v-html="showCaseReason(item.AnNoList)"></span>
        </q-link>
        <span v-else> {{ item.CaseName || '-' }}</span>
      </template>
      <template slot="caseMoney" slot-scope="item">
        <span style="white-space: nowrap" v-if="item.AmtInfo && item.AmtInfo.Amt">
          {{ numberFormat(item.AmtInfo.Amt, 2) }}
        </span>
        <span v-else>-</span>
      </template>
    </q-rich-table>
    <div class="block-paging" v-if="pageInfo.total > pageInfo.pageSize">
      <config-provider :locale="zhCN">
        <Pagination
          size="small"
          :showQuickJumper="true"
          :total="pageLimit(pageInfo.total)"
          :current-page="pageInfo.pageIndex"
          :ajax-update="true"
          :page-size="pageInfo.pageSize"
          @change="pageChange"
        />
      </config-provider>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" src="./style.less"></style>
