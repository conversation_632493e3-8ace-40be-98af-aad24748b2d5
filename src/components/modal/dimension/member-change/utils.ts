import _ from 'lodash';

export const getPartnerChangeDescMap = (partnerChangeInfo) => {
  let typeName = '股东';
  let stockDesc = '股份';
  let percentTdTitle = '持股比例';
  let majorShareHolderTypeName = '大股东';
  let majorShareHolderPercentDesc = '持股';

  const shardHolderTitleType = partnerChangeInfo?.Title || '';

  if (shardHolderTitleType === '合伙人信息') {
    typeName = '合伙人';
    stockDesc = '出资';
    percentTdTitle = '出资比例';
    majorShareHolderTypeName = '最大出资人';
    majorShareHolderPercentDesc = '出资比例';
  } else if (shardHolderTitleType === '主管部门（出资人）信息') {
    typeName = '主管部门/出资人';
    majorShareHolderTypeName = '最大出资人';
  } else if (shardHolderTitleType === '合作各方信息') {
    typeName = '合作方';
    majorShareHolderTypeName = '最大出资人';
  }

  return {
    typeName,
    stockDesc: partnerChangeInfo?.D?.length || partnerChangeInfo?.H?.length ? stockDesc : '',
    subTypeName: typeName !== '股东' ? typeName : '',
    subStockDesc: stockDesc,
    percentTdTitle,
    majorShareHolderTypeName,
    majorShareHolderPercentDesc,
  };
};

const handleItemType = (members, type) => {
  if (!members?.length) {
    return [];
  }
  return members.map((item) => ({
    keyNo: item.K,
    KeyNo: item.K,
    name: item.A,
    Name: item.A,
    position: item.B,
    type,
  }));
};

const hasFullPositions = (members) => members?.length && members.every((item) => item.position);

const getPositions = (members) => {
  const positions = members.map((item) => item.position);
  return [...new Set(positions)].sort();
};

const getGroupMembers = (members) => {
  const currMembers = _.groupBy(members, 'position');

  return Object.keys(currMembers).map((key) => ({
    key,
    reduce: currMembers[key].filter((item) => item.type === 'reduce'),
    add: currMembers[key].filter((item) => item.type === 'add'),
  }));
};

export const formatEmployeeData = (employeeData) => {
  const reduceMember = handleItemType(employeeData.E, 'reduce');
  const addMember = handleItemType(employeeData.F, 'add');
  let isSamePositionChange = false;
  let reduceAndAddMember: Array<{ key: string; reduce: any[]; add: any[] }> = [];

  // 退出和新增同时存在 并且 职位去重后完全一样的情况下，特殊展示
  if (hasFullPositions(reduceMember) && hasFullPositions(addMember)) {
    const reducePositons = getPositions(reduceMember);
    const addPositons = getPositions(addMember);

    isSamePositionChange = reducePositons.join('') === addPositons.join('');

    if (isSamePositionChange) {
      // 按照职位聚合
      reduceAndAddMember = getGroupMembers([...reduceMember, ...addMember]);
    }
  }

  return {
    reduceMember,
    addMember,
    isSamePositionChange,
    reduceAndAddMember,
  };
};
