<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr v-for="item in viewData.NewData" :key="item.key">
          <th width="18%" class="tb">{{ item.key }}</th>
          <td>
            <div v-html="format(item)" v-if="item.key !== '发证机构状态'"></div>
            <q-tag type="success" v-else>{{ item.value }}</q-tag>
          </td>
        </tr>
      </tbody>
    </q-plain-table>
  </div>
</template>

<script src="./component.js"></script>
