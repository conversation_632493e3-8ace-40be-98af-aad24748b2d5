import _ from 'lodash';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    format(item) {
      const key = item.key;
      const value = item.value;
      if (key === '排污许可证正本' || key === '排污许可证副本') {
        let str = `<span>${value.name || '-'}</span>`;
        if (value.url) {
          str = `<a target='_blank' href=${value.url}>${value.name || '-'}</a>`;
        }
        return str;
      }
      if (
        key === '企业法人营业执照或其他主体资格证明' ||
        key === '与特许经营活动相关的商标权、专利权其他经营资源的注册证书' ||
        key === '执行报告'
      ) {
        const reportArr = JSON.parse(value);
        let str = '';
        reportArr.forEach((v, i) => {
          if (i > 0) {
            str += '<br/>';
          }
          if (v.url) {
            str += `<a target='_blank' href=${v.url}>${v.name || '-'}</a>`;
          } else if (v.link) {
            str += `<a target='_blank' href=${v.link}>${v.name || '-'}</a>`;
          } else {
            str += `<span>${v.name || '-'}</span>`;
          }
        });
        return str;
      }
      if (key === '变更情况') {
        return _.isEmpty(value) ? '-' : value;
      }
      if (key === '发证机构状态') {
        return `<q-tag type="success">${value}</q-tag>`;
      }
      return value;
    },
    formatData() {
      const returnData = [];
      if (this.viewData?.Schema?.order?.length) {
        _.forEach(this.viewData.Schema.order, (key) => {
          if (this.viewData.Data[key]) {
            returnData.push({
              key,
              value: this.viewData.Data[key],
            });
          }
        });
      }
      if (this.viewData) {
        this.viewData.NewData = returnData;
      }
    },
  },
  created() {
    this.formatData();
    console.log(this.viewData);
  },
};
