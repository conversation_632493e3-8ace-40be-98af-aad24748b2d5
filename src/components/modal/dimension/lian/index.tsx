import { PropType, defineComponent } from 'vue';
import { Icon, Tooltip } from 'ant-design-vue';

import QPlainTable from '@/components/global/q-plain-table';
import QLink from '@/components/global/q-link';
import QRelateCases from '@/components/global/q-relate-cases';
import { dateFormat } from '@/utils/format';
import QRoleList from '@/components/global/q-role-list';

interface VIEWDATA {
  Reason?: string;
  CaseReasonDescription?: string;
  CaseSearchId?: string[];
  CaseNo?: string;
  RegistDate?: string;
  HoldDate?: string;
  FinishDate?: string;
  Department?: string;
  Court?: string;
  Judger?: string;
  Assistant?: string;
  CaseType?: string;
  CaseStatus?: string;
  RoleList?: Array<{ Desc: string; Items: any[] }>;
  Prosecutor?: Array<any>;
  Appellee?: Array<any>;
  ThirdPartyList?: Array<any>;
  OtherPartyList?: Array<any>;
}

/**
 * 立案信息
 */
const LianDimension = defineComponent({
  name: 'LianDimension',
  props: {
    viewData: {
      type: Object as PropType<VIEWDATA>,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    return (
      <div>
        <QPlainTable>
          <tbody>
            <tr>
              <td class="tb" width="20%">
                案号
              </td>
              {viewData.CaseSearchId && viewData.CaseSearchId.length !== 0 ? (
                <td>
                  <QLink rel="nofollow" href={`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}`}>
                    <span domPropsInnerHTML={viewData.CaseNo || '-'}></span>
                  </QLink>
                </td>
              ) : (
                <td width="30%" domPropsInnerHTML={viewData.CaseNo || '-'}></td>
              )}
              <td width="20%" class="tb">
                案由
              </td>
              <td width="30%">
                {viewData.Reason || '-'}
                {viewData.CaseReasonDescription && (
                  <span class="ml-1">
                    <Tooltip placement="bottom">
                      <div slot="title">{viewData.CaseReasonDescription}</div>
                      <Icon style="color: #d6d6d6" class="icon" type="info-circle" />
                    </Tooltip>
                  </span>
                )}
              </td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                案件类型
              </td>
              <td width="30%">{viewData.CaseType || '-'}</td>
              <td class="tb" style="width: 20%">
                当事人
              </td>
              <td width="30%">
                <QRoleList list={viewData.RoleList} />
              </td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                法院
              </td>
              <td width="30%">{viewData.Court || '-'}</td>
              <td class="tb" width="20%">
                承办部门
              </td>
              <td width="30%">{viewData.Department || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                承办法官
              </td>
              <td width="30%">{viewData.Judger || '-'}</td>
              <td class="tb" width="20%">
                法官助理
              </td>
              <td width="30%">{viewData.Assistant || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                案件状态
              </td>
              <td width="30%">{viewData.CaseStatus || '-'}</td>
              <td class="tb" width="20%">
                立案日期
              </td>
              <td width="30%">{dateFormat(viewData.RegistDate)}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                开庭时间
              </td>
              <td width="30%">{dateFormat(viewData.HoldDate)}</td>
              <td class="tb" width="20%">
                结束时间
              </td>
              <td width="30%">{dateFormat(viewData.FinishDate)}</td>
            </tr>
          </tbody>
        </QPlainTable>
        <QRelateCases searchParams={viewData} />
      </div>
    );
  },
});

export default LianDimension;
