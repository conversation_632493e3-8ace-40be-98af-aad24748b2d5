<template>
  <div>
    <table class="ntable">
      <template v-if="['食品', '化妆品'].indexOf(viewData.ProductType) > -1">
        <tr>
          <td class="tb" width="20%">HS编码：</td>
          <td width="30%">{{ viewData.HsCode || '-' }}</td>
          <td class="tb" width="20%">检验检疫编号：</td>
          <td width="30%">{{ viewData.Quarantinecode || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">产品名称：</td>
          <td>{{ viewData.ProductName || '-' }}</td>
          <td class="tb">产地：</td>
          <td>{{ viewData.Origin || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">生产企业信息：</td>
          <td colspan="3">{{ viewData.TradeMark || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">数/重量：</td>
          <td>{{ viewData.Weight || '-' }}</td>
          <td class="tb">进境口岸：</td>
          <td>{{ viewData.Entryport || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">进口商备案号：</td>
          <td colspan="3">{{ viewData.Eoriimporter || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">未准入境的事实：</td>
          <td colspan="3">{{ viewData.RefusedReason || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">报送时间：</td>
          <td>{{ viewData.SubmissionMonth || '-' }}</td>
          <td class="tb">附件：</td>
          <td>
            <template v-if="viewData.Attachment">
              <a :href="viewData.Attachment" target="_blank" class="text-primary">查看详情</a>
            </template>
            <span v-else>-</span>
          </td>
        </tr>
      </template>
      <template v-else>
        <tr>
          <td width="20%" class="tb">产品名称：</td>
          <td width="30%">{{ viewData.ProductName || '-' }}</td>
          <td width="20%" class="tb">品牌：</td>
          <td width="30%">{{ viewData.TradeMark || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">数/重量：</td>
          <td>{{ viewData.Weight || '-' }}</td>
          <td class="tb">原产地：</td>
          <td>{{ viewData.Origin || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">进口商名称：</td>
          <td colspan="3">{{ viewData.ImporterName || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">进境口岸：</td>
          <td>{{ viewData.Entryport || '-' }}</td>
          <td class="tb">处置措施：</td>
          <td>{{ viewData.Disposemeasure || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">不合格原因：</td>
          <td colspan="3">{{ viewData.RefusedReason || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">报送时间：</td>
          <td>{{ viewData.SubmissionMonth || '-' }}</td>
          <td class="tb">附件：</td>
          <td>
            <template v-if="viewData.Attachment">
              <a :href="viewData.Attachment" target="_blank" class="text-primary">查看详情</a>
            </template>
            <span v-else>-</span>
          </td>
        </tr>
      </template>
    </table>
  </div>
</template>

<script>
// eslint-disable-next-line vue/require-name-property
export default {
  props: {
    viewData: {
      type: Object,

      default() {
        return {};
      },
    },
  },
};
</script>
