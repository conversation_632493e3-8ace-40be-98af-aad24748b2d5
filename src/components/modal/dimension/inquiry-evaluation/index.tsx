import { defineComponent } from 'vue';

import dateformat from '@/filters/dateformat';

import styles from './inquiry-evaluation.module.less';

const InquiryEvaluation = defineComponent({
  name: 'InquiryEvaluation',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    const columns = [
      {
        title: '案件名称',
        dataIndex: 'CaseName',
        scopedSlots: {
          customRender: 'CaseName',
        },
      },
      {
        title: '案件类型',
        width: 100,
        dataIndex: 'CaseType',
      },
      {
        title: '案由',
        width: 150,
        dataIndex: 'CaseReason',
      },
      {
        title: '案号',
        width: 120,
        dataIndex: 'AnNoList',
        scopedSlots: {
          customRender: 'list',
        },
      },
      {
        title: '法院',
        width: 120,
        dataIndex: 'CourtList',
        scopedSlots: {
          customRender: 'list',
        },
      },
      {
        title: '最新审理程序',
        width: 120,
        dataIndex: 'LatestTrialRound',
      },
    ];
    return (
      <div class={styles.container}>
        <div class={styles.pt}>
          <q-plain-table>
            <tr>
              <td class={styles.tdColor}>当事人：</td>
              <td>
                <q-entity-link coyArr={viewData.OwnerInfo}></q-entity-link>
              </td>
              <td class={styles.tdColor}>案号：</td>
              <td>
                {viewData.CaseSearchId ? (
                  <a
                    href={`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId}&title=${viewData.CaseNo}`}
                    target="_blank"
                    domPropsInnerHTML={viewData.CaseNo}
                  />
                ) : (
                  viewData.CaseNo
                )}
              </td>
            </tr>
            <tr>
              <td class={styles.tdColor}>标的物：</td>
              <td>{viewData.SubjectName}</td>
              <td class={styles.tdColor}>财产类型：</td>
              <td>{viewData.SubjectType}</td>
            </tr>
            <tr>
              <td class={styles.tdColor}>法院名称：</td>
              <td>{viewData.CourtName}</td>
              <td class={styles.tdColor}>摇号日期：</td>
              <td>{dateformat(viewData.LotteryDate * 1000)}</td>
            </tr>
            <tr>
              <td class={styles.tdColor}>选定评估机构：</td>
              <td colspan={3}>
                <q-entity-link coyArr={viewData.AgencyInfo}></q-entity-link>
              </td>
            </tr>
          </q-plain-table>
        </div>

        <div class={styles.case}>所属司法案件</div>
        <q-rich-table
          columns={columns}
          dataSource={viewData.relatedCases}
          scopedSlots={{
            list: (data) => {
              return data.join('、') || '-';
            },
            CaseName: (name, record) => {
              if (!record.Id) return name;
              return (
                <a href={`/embed/courtCaseDetail?caseId=${record.Id}&title=${name}`} target="_blank">
                  {name}
                </a>
              );
            },
          }}
        ></q-rich-table>
      </div>
    );
  },
});

export default InquiryEvaluation;
