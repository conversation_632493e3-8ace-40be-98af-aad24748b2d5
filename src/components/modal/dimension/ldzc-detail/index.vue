<template>
  <div class="dnotice-content">
    <table class="ntable" v-if="dialogProps.type === 0">
      <tr>
        <td class="tb" width="23%">案号</td>
        <td width="27%">{{ viewData.ArbitrateCaseNo || '-' }}</td>
        <td class="tb" width="23%">发布日期</td>
        <td width="27%">{{ viewData.PublishDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
      <tr>
        <td class="tb">申请人</td>
        <td>
          <template v-if="viewData.PltfKeyNoArray && viewData.PltfKeyNoArray.length > 0">
            <q-entity-link :coy-arr="viewData.PltfKeyNoArray"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
        <td class="tb">被申请人</td>
        <td>
          <template v-if="viewData.DefdKeyNoArray && viewData.DefdKeyNoArray.length > 0">
            <q-entity-link :coy-arr="viewData.DefdKeyNoArray"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
      <tr>
        <td class="tb">原文</td>
        <td colspan="3">
          <div v-html="viewData.Content"></div>
        </td>
      </tr>
    </table>
    <table class="ntable" v-if="dialogProps.type === 1">
      <tr>
        <td class="tb" width="23%">案号</td>
        <td width="27%">{{ viewData.ArbitrateCaseNo || '-' }}</td>
        <td class="tb" width="23%">主案由</td>
        <td width="27%">{{ viewData.ArbitrateCaseReason || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">申请人</td>
        <td>
          <template v-if="viewData.PltfKeyNoArray && viewData.PltfKeyNoArray.length > 0">
            <q-entity-link :coy-arr="viewData.PltfKeyNoArray"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
        <td class="tb">被申请人</td>
        <td>
          <template v-if="viewData.DefdKeyNoArray && viewData.DefdKeyNoArray.length > 0">
            <q-entity-link :coy-arr="viewData.DefdKeyNoArray"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
      <tr>
        <td class="tb">仲裁员</td>
        <td>
          {{ viewData.PersArbitrator || '-' }}
        </td>
        <td class="tb">书记员</td>
        <td>
          {{ viewData.PersClerk || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb">地点</td>
        <td colspan="3">
          {{ viewData.SessionAdress || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb">发布机构</td>
        <td colspan="3">
          <div v-html="viewData.ArbitrateOffice"></div>
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">开庭时间</td>
        <td width="27%">{{ viewData.SessionTime | dateformat('YYYY-MM-DD') }}</td>
        <td class="tb" width="23%">发布日期</td>
        <td width="27%">{{ viewData.PublishDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogProps: {
      type: Object,
      default() {
        return {};
      },
      required: true,
    },
  },
};
</script>
