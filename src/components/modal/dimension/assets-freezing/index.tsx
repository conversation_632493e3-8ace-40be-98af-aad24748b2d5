import { computed, defineComponent } from 'vue';
import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import { dateFormat } from '@/utils/format';
import { isJSONString } from '@/utils/data-type/is-json-string';
import FileIcon from '@/components/file-icon';
import { getOssUrl } from '@/utils';

const AssetsFreezing = defineComponent({
  name: 'AssetsFreezing',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const subjectInfo = computed(() => {
      if (isJSONString(props.viewData.subjectnameandkeyno)) {
        return JSON.parse(props.viewData.subjectnameandkeyno);
      }
      return [{ KeyNo: props.viewData.subjectnames[1], Name: props.viewData.subjectnames[0] }];
    });

    const attachmentInfo = computed(() => {
      if (isJSONString(props.viewData.attachmentinfo)) {
        return JSON.parse(props.viewData.attachmentinfo);
      }
      return [];
    });

    return {
      subjectInfo,
      attachmentInfo,
    };
  },
  render() {
    return (
      <QPlainTable>
        <tr>
          <td width="20%" class="tb">
            公告标题
          </td>
          <td colspan="3">{this.viewData.Title || '-'}</td>
        </tr>
        <tr>
          <td class="tb" width="20%">
            案号
          </td>
          <td width="30%">{this.viewData.caseno || '-'}</td>
          <td class="tb" width="20%">
            被执行人
          </td>
          <td width="30%">
            <QEntityLink coy-arr={this.subjectInfo} />
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">
            法院
          </td>
          <td width="30%">{this.viewData.court || '-'}</td>
          <td class="tb" width="20%">
            查封公告日期
          </td>
          <td width="30%">{dateFormat(this.viewData.publishdate)}</td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            查封起止时间
          </td>
          <td colspan="3">
            {dateFormat(this.viewData.startdate)} 至 {dateFormat(this.viewData.enddate)}
          </td>
        </tr>
        <tr>
          <td width="20%" class="tb">
            查封资产
          </td>
          <td colspan="3">{this.viewData.seizedassets || '-'}</td>
        </tr>
        {this.attachmentInfo.length > 0 ? (
          <tr>
            <td width="20%" class="tb">
              附件
            </td>
            <td colspan="3">
              {this.attachmentInfo.map((item) => {
                return (
                  <div class="flex items-center" style={{ gap: '4px' }}>
                    <FileIcon type={item.type.replace('.', '')} />
                    <a href={getOssUrl(item.url)} target="_blank">
                      {item.name}
                    </a>
                  </div>
                );
              })}
            </td>
          </tr>
        ) : null}
      </QPlainTable>
    );
  },
});

export default AssetsFreezing;
