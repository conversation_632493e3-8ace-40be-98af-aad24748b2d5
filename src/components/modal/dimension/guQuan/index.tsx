import { defineComponent } from 'vue';

import gStructureChart from '@/components/charts/g-structure-chart/index.vue';

import styles from './gu-quan.module.less';

const GuQuan = defineComponent({
  name: 'GuQuan',

  components: {
    [gStructureChart.name]: gStructureChart,
  },

  props: {
    dialogProps: {
      type: Object,
      default() {
        return {};
      },
    },
  },

  render() {
    const { keyNo, name } = this.dialogProps;
    return (
      <div class={styles.container}>
        <g-structure-chart class={styles.chart} keyNo={keyNo} name={name} iframe></g-structure-chart>;
      </div>
    );
  },
});

export default GuQuan;
