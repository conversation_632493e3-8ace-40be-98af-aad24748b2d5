<template>
  <div>
    <table class="ntable">
      <tr v-for="(item, index) in detailParams.EventDetail" :key="index">
        <td width="23%" class="tb">抽查事项{{ index + 1 }}</td>
        <td width="27%">{{ item.eventName || '-' }}</td>
        <td width="23%" class="tb">检查结果</td>
        <td width="27%" :style="{ color: item.eventStatus === 0 ? '#F04040' : '#333333' }">
          {{ item.eventResult || '-' }}
        </td>
      </tr>
    </table>
  </div>
</template>
<script src="./component.js"></script>
