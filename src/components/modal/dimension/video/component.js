export default {
  name: 'Video',
  data() {
    return {
      title: '庭审视频',
      videoUrl: '',
    };
  },
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    viewData: {
      handler(val) {
        if (val) {
          this.show({ title: this.title, videoUrl: this.viewData.videoUrl });
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    show({ title, videoUrl }) {
      this.title = title;
      this.videoUrl = videoUrl?.replace('http', 'https');
      return new Promise((resolve, reject) => {
        this.$promise = { resolve, reject };
      });
    },
    // videoLoad() {},
    // 无法监听加载失败事件会导致组件无法销毁
    videoError() {
      this.$promise.resolve();
    },
  },
};
