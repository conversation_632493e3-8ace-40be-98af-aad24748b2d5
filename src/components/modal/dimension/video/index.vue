<template>
  <div class="modal fade app-note" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog nmodal multi-body" style="width: 960px">
      <div class="modal-content">
        <div class="modal-header">
          <!--          <button type="button" class="close" data-dismiss="modal">-->
          <!--            <span aria-hidden="true">&times;</span>-->
          <!--            <span class="sr-only">x</span>-->
          <!--          </button>-->
          <!--          <h4 class="modal-title">-->
          <!--            {{ title }}-->
          <!--          </h4>-->
        </div>
        <div class="modal-body">
          <iframe
            title="video"
            @error="videoError"
            :src="videoUrl"
            width="916"
            height="550"
            frameborder="no"
            border="0"
            scrolling="no"
            allowtransparency="yes"
          >
          </iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style lang="less" src="./style.less" scoped></style>
