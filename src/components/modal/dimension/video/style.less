.app-note {
  .modal-body {
    padding-bottom: 30px;
    max-height: 600px;
    overflow: auto;
  }

  .note-list {}



  .note-wrap {
    padding: 10px 0;

    .content-input {
      resize: none;
      border: 0;
      outline: 0;
      display: block;
      width: 100%;
      background: transparent;
    }

    .image-list {
      margin-top: 10px;
      margin-bottom: -5px;

      .image-input {
        position: absolute;
        height: 0;
        width: 0;
        visibility: hidden;
      }

      .img {
        position: relative;
        display: inline-block;
        width: 98px;
        height: 98px;
        border: solid 1px #eee;
        margin-right: 10px;
        margin-bottom: 10px;

        img {
          height: 100%;
          width: 100%;
          object-fit: contain;
        }

        .bicon {
          position: absolute;
          right: 0;
          top: 0;
          margin: 0;
        }
      }
    }

    .sim-input {
      position: relative;
      margin-top: 10px;

      .form-control {
        display: inline-block;
        padding-left: 30px;
        width: 300px;
      }

      .bicon {
        position: absolute;
        left: 10px;
        top: 11px;
      }
    }

    .note-content {
      padding-right: 80px;
      word-break: break-all;
      min-height: 22px;

      .note-content-text {
        letter-spacing: normal;
        word-spacing: normal;
        text-transform: none;
        text-indent: 0;
        text-shadow: none;
        display: inline-block;
        text-align: start;
        -webkit-rtl-ordering: logical;
        flex-direction: column;
        resize: auto;
        cursor: text;
        white-space: pre-wrap;
        overflow-wrap: break-word;
      }

      .note-oper {
        position: absolute;
        right: 0;
        top: 10px;
      }
    }

    .sim-item {
      margin-top: 10px;
      word-break: break-all;

      .sim {
        display: inline-block;
        background: #F3F9FD;
        padding: 4px 12px;
        border-radius: 2px;
        min-width: 300px;
      }
    }

  }

  .note-item {
    border-bottom: solid 1px #eee;
    position: relative;
  }

  .note-item.edit {
    border: solid 1px #eee;
    margin-bottom: 5px;

    .note-wrap {
      background: #FCFCFC;
      min-height: 88px;
      padding: 10px;


    }

    .image-list {
      margin-right: -10px;
    }
  }



  .note-foot {
    background: #F6F6F6;
    height: 32px;
    border-top: solid 1px #eee;

    &>.oper-btn {
      color: #999;
      line-height: 32px;
      margin-left: 15px;

      &:hover {
        color: #128bed;

        .bicon {
          background-position-y: 0;
        }
      }
    }

    .btn {
      float: right;
      font-size: 12px;
      min-width: 84px;
      padding: 7px 12px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      margin-right: -1px;
      margin-top: -1px;
    }
  }
}