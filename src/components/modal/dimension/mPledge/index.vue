<template>
  <div>
    <div class="mtcaption">动产抵押登记信息</div>
    <table class="ntable">
      <tbody>
        <tr>
          <td width="20%" class="tb">登记机关</td>
          <td colspan="3">{{ detailData.RegisterOffice || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="20%">登记编号</td>
          <td width="30%">{{ detailData.RegisterNo || '-' }}</td>
          <td class="tb" width="20%">登记日期</td>
          <td width="30%" v-if="detailData.RegisterDate">
            {{ detailData.RegisterDate | dateformat('YYYY-MM-DD') }}
          </td>
          <td v-else>-</td>
        </tr>
      </tbody>
    </table>
    <template v-if="detailData.MPledgeDetail && detailData.MPledgeDetail.PledgeeList && detailData.MPledgeDetail.PledgeeList.length > 0">
      <div class="mtcaption">抵押权人信息</div>
      <table class="ntable">
        <template v-for="(obj, index) in detailData.MPledgeDetail.PledgeeList">
          <tr :key="`tr-1-${index}`">
            <td class="tb" width="20%">抵押权人名称</td>
            <td width="30%" v-if="obj.KeyNo">
              <q-entity-link :coy-obj="{ Name: obj.Name, KeyNo: obj.KeyNo }"></q-entity-link>
            </td>
            <td style="width: 30%" v-else>{{ obj.Name || '-' }}</td>
            <td class="tb" width="20%">抵押权人证照类型</td>
            <td width="30%">{{ obj.IdentityType || '-' }}</td>
          </tr>
          <tr :key="`tr-2-${index}`">
            <td class="tb">证照号码</td>
            <td>{{ obj.IdentityNo || '-' }}</td>
            <td class="tb">住所地</td>
            <td>{{ obj.Address || '-' }}</td>
          </tr>
        </template>
      </table>
    </template>

    <template v-if="detailData.MPledgeDetail && detailData.MPledgeDetail.GuaranteedCredRight">
      <div class="mtcaption">被担保主债权信息</div>
      <table class="ntable">
        <tbody>
          <tr>
            <td width="20%" class="tb">抵押人</td>
            <td colspan="3"><q-entity-link :coy-obj="detailData.RelatedCompanyInfo"></q-entity-link></td>
          </tr>
          <tr>
            <td width="20%" class="tb">债务人履行债务的期限</td>
            <td colspan="3">{{ detailData.MPledgeDetail.GuaranteedCredRight.FulfillObligation || '-' }}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">种类</td>
            <td width="30%">{{ detailData.MPledgeDetail.GuaranteedCredRight.Kind || '-' }}</td>
            <td class="tb" width="20%">数额</td>
            <td width="30%">{{ numberToHumanWithUnit(detailData.MPledgeDetail.GuaranteedCredRight.Amount) || '-' }}</td>
          </tr>
          <tr>
            <td class="tb">担保的范围</td>
            <td>{{ detailData.MPledgeDetail.GuaranteedCredRight.AssuranceScope || '-' }}</td>
            <td class="tb">备注</td>
            <td>{{ detailData.MPledgeDetail.GuaranteedCredRight.Remark || '-' }}</td>
          </tr>
        </tbody>
      </table>
    </template>

    <template
      v-if="detailData.MPledgeDetail && detailData.MPledgeDetail.GuaranteeList && detailData.MPledgeDetail.GuaranteeList.length > 0"
    >
      <div class="mtcaption">抵押物信息</div>
      <table class="ntable">
        <template v-for="(obj, index) in detailData.MPledgeDetail.GuaranteeList">
          <tr :key="`tr-1-${index}`">
            <td class="tb" width="20%">抵押物名称</td>
            <td width="30%">{{ obj.Name || '-' }}</td>
            <td class="tb" width="20%">所有权或使用权归属</td>
            <td style="width: 30%" v-if="obj.KeyNoList && obj.KeyNoList.length">
              <div v-if="obj.KeyNoList.some((e) => e.position === 4 || e.Position === 4)">
                <q-entity-link :coy-arr="obj.KeyNoList" />
              </div>
              <template v-else>
                <div>
                  所有权：
                  <q-entity-link :coy-arr="obj.KeyNoList.filter((e) => e.position === 2 || e.Position === 2)" />
                </div>
                <div>
                  使用权：
                  <q-entity-link :coy-arr="obj.KeyNoList.filter((e) => e.position === 3 || e.Position === 3)" />
                </div>
              </template>
            </td>
            <td width="30%" v-else>{{ obj.Ownership || '-' }}</td>
          </tr>
          <tr :key="`tr-2-${index}`">
            <td class="tb">数量、质量、状况、所在地等情况</td>
            <td>{{ obj.Other || '-' }}</td>
            <td class="tb">备注</td>
            <td>{{ obj.Remark || '-' }}</td>
          </tr>
        </template>
      </table>
    </template>

    <template v-if="detailData.MPledgeDetail && detailData.MPledgeDetail.ChangeList && detailData.MPledgeDetail.ChangeList.length > 0">
      <div class="mtcaption">变更信息</div>
      <table class="ntable">
        <tr v-for="(obj, index) in detailData.MPledgeDetail.ChangeList" :key="index">
          <td class="tb" width="20%">变更日期</td>
          <td width="30%">{{ obj.ChangeDate || '-' }}</td>
          <td class="tb" width="20%">变更内容</td>
          <td width="30%">{{ obj.ChangeContent || '-' }}</td>
        </tr>
      </table>
    </template>

    <template v-if="detailData.MPledgeDetail && detailData.MPledgeDetail.CancelInfo">
      <div class="mtcaption">注销信息</div>
      <table class="ntable">
        <tbody>
          <tr>
            <td class="tb" width="20%">注销日期</td>
            <td width="30%">{{ detailData.MPledgeDetail.CancelInfo.CancelDate | dateformat('YYYY-MM-DD') }}</td>
            <td class="tb" width="20%">注销原因</td>
            <td width="30%">{{ detailData.MPledgeDetail.CancelInfo.CancelReason || '-' }}</td>
          </tr>
        </tbody>
      </table>
    </template>
  </div>
</template>
<script>
import { numberToHumanWithUnit } from '@/utils/number-formatter';
import { dateFormat } from '@/utils/format/index';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogProps: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    isFromZDW() {
      // 1-工商 2-中登网
      return this.dialogProps?.Detail?.sourcefrom === 2;
    },
    detailData() {
      if (this.isFromZDW) {
        const { Detail = {} } = this.dialogProps;
        const { debttermstart, debttermend } = Detail;
        const startEnd = debttermstart || debttermend ? `${dateFormat(debttermstart)} 至 ${dateFormat(debttermend)}` : '-';
        return {
          sourcefrom: Detail.sourcefrom,
          RegisterOffice: Detail.registeroffice,
          RegisterNo: Detail.registerno,
          RegisterDate: Detail.registerstartdate,
          RelatedCompanyInfo: Detail.DebtorJson?.[0],
          MPledgeDetail: {
            PledgeeList: Detail.PledgeeJson,
            GuaranteedCredRight: {
              Remark: Detail.guaranteeremark,
              Kind: Detail.guaranteedkind,
              AssuranceScope: Detail.assurancescope,
              Amount: Detail.pledgedamountdesc,
              FulfillObligation: startEnd,
            },
          },
          // 抵押物信息
          GuaranteeInfo: {
            description: Detail.description,
            amount: Detail.ratecontractamount ? `${numberToHumanWithUnit(Detail.ratecontractamount)}${Detail.contractcurrency}` : '-',
            mainno: Detail.mainno,
          },
        };
      }
      return this.viewData;
    },
  },
  methods: {
    numberToHumanWithUnit,
  },
};
</script>
