<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">决定书文号</td>
        <td width="30%">{{ viewData.CaseNo || '-' }}</td>
        <td class="tb" width="20%">行政相对人名称</td>
        <td width="30%">
          <q-entity-link :coy-arr="viewData.CompanyNameAndKeyNo" target="_blank"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb">处罚事由</td>
        <td colspan="3" v-html="viewData.PunishReason || '-'"></td>
      </tr>
      <tr>
        <td class="tb" width="20%">违法类型</td>
        <td width="30%">{{ viewData.IllegalType || '-' }}</td>
        <td class="tb" width="20%">处罚依据</td>
        <td width="30%">{{ viewData.PunishBasis || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">处罚结果</td>
        <td width="30%"><span v-html="viewData.PunishmentResult || '-'"></span></td>
        <td class="tb" width="20%">处罚单位</td>
        <td width="30%">{{ viewData.PunishGov || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">处罚日期</td>
        <td width="30%" v-if="viewData.PunishDate">{{ viewData.PunishDate | dateformat }}</td>
        <td v-else>-</td>
        <td class="tb" width="20%">执行情况</td>
        <td width="30%">{{ viewData.Implementation || '-' }}</td>
      </tr>
    </table>
    <q-relate-cases :search-params="viewData" stitle="关联"></q-relate-cases>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
