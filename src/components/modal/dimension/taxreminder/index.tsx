import { defineComponent } from 'vue';

import FileLogo from '@/components/file-logo';
import QPlainTable from '@/components/global/q-plain-table';
import formatDate from '@/utils/format/date';
import { numberToHuman } from '@/utils/number-formatter';

const TexReminder = defineComponent({
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    return (
      <QPlainTable>
        <tr>
          <td class="tb" width="190px">
            企业名称
          </td>
          <td width="295px">
            <q-entity-link coy-arr={viewData.TaxpayerKeynoArray} />
          </td>
          <td class="tb" width="190px">
            纳税人识别号
          </td>
          <td width="295px">{viewData.TaxpayerNo || '-'}</td>
        </tr>
        <tr>
          <td class="tb">税种</td>
          <td>{viewData.TaxCategory || '-'}</td>
          <td class="tb">征收品目</td>
          <td>{viewData.ItemsType || '-'}</td>
        </tr>
        <tr>
          <td class="tb">所属期起</td>
          <td>{formatDate(viewData.PeriodStartDate)}</td>
          <td class="tb">所属期止</td>
          <td>{formatDate(viewData.PeriodEndDate)}</td>
        </tr>
        {/*  税务催报 */}
        {viewData.dimension === 1 && (
          <tr>
            <td class="tb">申报期限</td>
            <td colspan="3">{formatDate(viewData.DeclareTermDate)}</td>
          </tr>
        )}
        {/*  税务催缴 */}
        {viewData.dimension === 2 && (
          <tr>
            <td class="tb">欠缴金额（元）</td>
            <td>{numberToHuman(viewData.AmountOwed)}</td>
            <td class="tb">缴款期限</td>
            <td>{formatDate(viewData.PaymentDate)}</td>
          </tr>
        )}
        <tr>
          <td class="tb">违规行为</td>
          <td colspan="3">{viewData.IllegalAct || '-'}</td>
        </tr>
        <tr>
          <td class="tb">责令限期改正内容</td>
          <td colspan="3">{viewData.ContentCorrect || '-'}</td>
        </tr>
        <tr>
          <td class="tb">责令改正期限</td>
          <td colspan="3">{formatDate(viewData.PeriodCorrectDate)}</td>
        </tr>
        <tr>
          <td class="tb">处理依据</td>
          <td colspan="3">{viewData.PunishBasis || '-'}</td>
        </tr>
        <tr>
          <td class="tb">文号</td>
          <td>{viewData.DocNo || '-'}</td>
          <td class="tb">原文</td>
          <td>
            <FileLogo
              fileData={{
                FileUrl: viewData.AttachmentUrl,
                FileType: viewData.AttachmentType,
              }}
            />
          </td>
        </tr>
        <tr>
          <td class="tb">主管税务机关</td>
          <td>
            <q-entity-link coy-arr={viewData.TaxKeynoArray} />
          </td>
          <td class="tb">发布日期</td>
          <td>{formatDate(viewData.PublishDate)}</td>
        </tr>
      </QPlainTable>
    );
  },
});

export default TexReminder;
