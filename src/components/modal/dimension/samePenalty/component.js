import { FILE_TYPES } from '@/shared/constants/file-type';

export default {
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      list: [],
      pageInfo: {
        pageIndex: 1,
        pageSize: 5,
        total: 0,
      },
      columns: [
        {
          title: '决定文书号',
          scopedSlots: { customRender: 'DocNo' },
          align: 'left',
        },
        {
          title: '处罚事由/违法行为类型',
          width: '200',
          ellipsis: 4,
          dataIndex: 'PunishReason',
          scopedSlots: { customRender: 'shrinkContent' },
        },
        {
          title: '处罚结果/内容',
          width: '200',
          dataIndex: 'PunishResult',
          placeholder: '未公示',

          scopedSlots: { customRender: 'shrinkContent' },
        },
        {
          title: '处罚金额（元）',
          align: 'right',
          width: 100,
          customRender: (item) => {
            if (item.PenaltyMoney) {
              return item.PenaltyMoney.toLocaleString();
            }
            return '-';
          },
        },
        {
          title: '处罚单位',
          width: '95',
          dataIndex: 'PunishOffice',
          align: 'left',
        },
        {
          title: '数据来源',
          width: '90',
          customText: (item) => {
            return item.SourceName || '-';
          },
        },
        {
          title: '处罚日期',
          width: '103',
          align: 'center',
          scopedSlots: { customRender: 'PunishDate' },
        },
        {
          title: '原文',
          width: 70,
          align: 'center',
          scopedSlots: { customRender: 'originalSource' },
        },
      ],
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.$service.dimension
        .adminpenaltylist({
          keyNo: this.detailParams.keyNo,
          groupId: this.detailParams.groupId,
          isValid: this.detailParams.isValid,
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        })
        .then((data) => {
          if (data) {
            this.pageInfo.total = data.Paging?.TotalRecords || 0;
            this.list = data.Result || [];
          }
        });
    },
    pageChange(pageIndex) {
      this.pageInfo.pageIndex = pageIndex;
      this.getData();
    },
    detail(item) {
      console.log(item);
      // if (item.Source === '信用中国') {
      //   this.$modal.showDimension('xzcfPunish', { id: item.sourceId, type: 2 });
      // } else if (item.Source === '其他') {
      //   this.$modal.showDimension('otherPunish', { id: item.sourceId, type: 4, title: item.SourceName });
      // } else if (item.Source === '税务局') {
      //   this.$modal.showDimension('taxPunish', { id: item.sourceId, type: 3 });
      // } else if (item.Source === '工商局') {
      //   this.$modal.showDimension('gongshangPunish', { id: item.sourceId, keyNo: this.keyNo, type: 1 });
      // } else if (
      //   item.Source === '反垄断行政处罚' &&
      //   item.SourceName !== '中国海关企业进出口信用信息公示平台'
      // ) {
      this.$modal.showDimension('antitrustPunish', { id: item.sourceId, type: 5 });
      // }
    },

    getUrl(type) {
      if (type && FILE_TYPES.indexOf(type) > -1) {
        return type;
      }
      return 'other';
    },

    goDetail(url) {
      if (url) {
        window.open(url);
      }
    },
  },
};
