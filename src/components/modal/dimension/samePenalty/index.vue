<template>
  <div>
    <div class="mtcaption">
      关联企业：<q-entity-link :coy-obj="{ Name: detailParams.relatedName, KeyNo: detailParams.relatedKeyNo }"></q-entity-link>
    </div>
    <q-rich-table
      v-if="list.length > 0"
      :columns="columns"
      :data-source="list"
      :page-info="pageInfo"
      :scroll-outer="'.app-risk-detail .modal-body'"
      @pageChange="pageChange"
    >
      <template slot="DocNo" slot-scope="item">
        <a @click="detail(item)">{{ item.DocNo }}</a>
      </template>
      <template slot="PunishDate" slot-scope="item">
        <span>{{ item.PunishDate | dateformat }}</span>
      </template>
      <template slot="originalSource" slot-scope="item">
        <template v-if="item.AttachUrl">
          <img
            @click="goDetail(item.AttachUrl)"
            style="cursor: pointer"
            :src="require(`../../../../assets/images/${getUrl(item.AttachType)}.svg`)"
            alt=""
          />
        </template>
        <template v-else>-</template>
      </template>
    </q-rich-table>
  </div>
</template>
<script src="./component.js"></script>
