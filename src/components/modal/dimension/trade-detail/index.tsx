import { defineComponent, onMounted, reactive } from 'vue';
import QRichTable from '@/components/global/q-rich-table';
import { useFetchState } from '@/hooks/use-fetch-state';
import { dimensionDetail } from '@/shared/services';
import QEntityLink from '@/components/global/q-entity-link';

const columns = [
  {
    title: '交易方',
    width: 200,
    dataIndex: 'RelatParties',
    scopedSlots: { customRender: 'parties' },
  },
  {
    title: '关联关系',
    dataIndex: 'RelationShip',
  },
  {
    title: '交易金额',
    customRender: (item) => (item.TransactionAmtAnalyze ? item.TransactionAmt + item.CurrencyName : '-'),
  },
  {
    title: '交易方式',
    dataIndex: 'TransactionMethod',
  },
  {
    title: '公告日期',
    width: 88,
    dataIndex: 'AnnounceDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '交易简介',
    width: 300,
    dataIndex: 'Stat',
    scopedSlots: { customRender: 'shrinkContent' },
  },
];

const TradeDetail = defineComponent({
  name: 'TradeDetail',
  props: {
    detailParams: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const fetchData = async () => {
      const res = await dimensionDetail.tradeDetail({
        ...props.detailParams,
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
      });
      pagination.total = res.Paging.TotalRecords;
      pagination.current = res.Paging.PageIndex;
      return res;
    };

    const { isLoading, result, execute } = useFetchState(fetchData);

    const handlePageChange = (current: number, pageSize: number) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      execute();
    };

    const pagination = reactive({
      pageSize: 5,
      current: 1,
      total: 0,
      onChange: handlePageChange,
      showSizeChanger: false,
    });

    onMounted(() => {
      execute();
    });

    return {
      result,
      pagination,
      isLoading,
      handlePageChange,
    };
  },
  render() {
    return (
      <QRichTable
        loading={this.isLoading}
        columns={columns}
        dataSource={this.result?.Result}
        pagination={this.pagination}
        onPageChange={this.handlePageChange}
        scopedSlots={{
          parties: (item) => {
            return <QEntityLink coyArr={item} />;
          },
        }}
      />
    );
  },
});

export default TradeDetail;
