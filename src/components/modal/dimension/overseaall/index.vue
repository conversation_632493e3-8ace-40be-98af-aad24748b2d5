<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">参控股企业名称</td>
        <td width="30%">
          <q-entity-link :coy-obj="{ KeyNo: detailParams.RelatedCompKeyNo, Name: detailParams.RelatedCompName }"></q-entity-link>
        </td>
        <td class="tb" width="20%">所属地区</td>
        <td width="30%">{{ detailParams.RegisterAddress || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">注册地</td>
        <td width="30%">{{ detailParams.RegisterAddressOrigin || '-' }}</td>
        <td class="tb" width="20%">注册资本</td>
        <td width="30%">{{ detailParams.RegisterCapital || '-' }}</td>
      </tr>
    </table>
    <div style="margin-bottom: 15px; color: #666; font-size: 14px">财务信息</div>
    <q-rich-table v-data="mListGetTableData('current')" :columns="columns" :pagination="pagination"> </q-rich-table>
  </div>
</template>

<script src="./component.js"></script>
