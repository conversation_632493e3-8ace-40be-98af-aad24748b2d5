import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin(['current']);

export default {
  mixins: [dimensionMixin, listMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      pagination: {
        pageIndex: 1,
        current: 1,
        pageSize: 5,
        total: 0,
        onChange: this.pageChange,
        onShowSizeChange: this.pageChange,
        pageSizeOptions: ['5'],
      },
    };
  },
  computed: {
    columns() {
      return [
        {
          title: '报告期',
          width: 102,
          dataIndex: 'EndDate',
          align: 'center',
        },
        {
          title: '参控关系',
          dataIndex: 'RelationShipDesc',
          align: 'center',
        },
        {
          title: '持股比例',
          dataIndex: 'HoldRatio',
          align: 'center',
        },
        {
          title: '投资额',
          dataIndex: 'InvestMoney',
          align: 'center',
        },
        {
          title: '营业收入',
          dataIndex: 'RelatedOperRev',
          align: 'center',
        },
        {
          title: '净利润',
          dataIndex: 'RelatedNetProfit',
          align: 'center',
        },
        {
          title: '是否合并报表',
          dataIndex: 'IfConsolidated',
          align: 'center',
        },
        {
          title: '是否上市',
          dataIndex: 'IfListed',
          align: 'center',
        },
        {
          title: '主营业务',
          dataIndex: 'RelatedMainBusiness',
          align: 'center',
        },
      ];
    },
  },

  methods: {
    fetchDataSource() {
      const params = {
        keyNo: this.detailParams.CompKeyNo,
        relatedCompName: this.detailParams.RelatedCompName,
        relatedKeyNo: this.detailParams.RelatedCompKeyNo, // 默认false
        ...this.pagination,
      };
      return this.mDimenGetList(params, 'oversea', 'dimensionDetail').then((data) => {
        this.pagination.total = data?.Paging?.TotalRecords || 0;
        return data;
      });
    },
    pageChange(current, pageSize) {
      this.pagination.current = current;
      this.pagination.pageIndex = current;
      this.pagination.pageSize = pageSize;
      this.refresh();
    },
    refresh() {
      this.mListResetModule(this.mListActiveKey);
      this.mListInit(this.mListActiveKey);
    },
  },
};
