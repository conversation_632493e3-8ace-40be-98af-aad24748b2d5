<template>
  <q-plain-table>
    <tbody>
      <tr>
        <td width="18%" class="tb">产品名称</td>
        <td width="26%">{{ viewData.ProductDetailedName || '-' }}</td>
        <td width="24%" class="tb">产品类型</td>
        <td width="32%">{{ viewData.ProductName || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">生产单位</td>
        <td><q-entity-link :coy-arr="viewData.CompKeyno"></q-entity-link></td>
        <td class="tb">所在地</td>
        <td v-html="viewData.Province || '-'"></td>
      </tr>
      <tr>
        <td class="tb">规格型号</td>
        <td>{{ viewData.Specification || '-' }}</td>
        <td class="tb">产品等级</td>
        <td>{{ viewData.ProductLevel || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">生产日期/批号</td>
        <td>{{ viewData.BatchNumber || '-' }}</td>
        <td class="tb">抽查结果</td>
        <td>
          <span
            v-if="['合格', '不合格', '存在异议', '未认定'].includes(viewData.Result)"
            class="ntag"
            :class="{
              'text-success': viewData.Result === '合格',
              'text-danger': viewData.Result === '不合格',
              'text-warning': viewData.Result === '存在异议',
              'text-gray': viewData.Result === '未认定',
            }"
            >{{ viewData.Result }}</span
          >
          <span v-else>-</span>
        </td>
      </tr>

      <tr>
        <td class="tb">检验机构</td>
        <td><q-entity-link :coy-arr="viewData.InspectionAgencyKeyno"></q-entity-link></td>
        <td class="tb">抽样机构</td>
        <td><q-entity-link :coy-arr="viewData.CheckUnitKeyno"></q-entity-link></td>
      </tr>
      <tr>
        <td class="tb">抽查时间</td>
        <td>{{ viewData.InspectDate | dateformat() }}</td>
        <td class="tb">公告时间</td>
        <td>{{ viewData.ReleaseDate | dateformat() }}</td>
      </tr>
      <tr>
        <td class="tb">抽查类型</td>
        <td v-html="viewData.InspectType || '-'"></td>
        <td class="tb">抽样来源</td>
        <td>{{ viewData.SamplingSource || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">主要不合格项目</td>
        <td v-html="viewData.UnqualifiedItem || '-'"></td>
        <td class="tb">抽查值</td>
        <td v-html="viewData.SamplingValue || '-'"></td>
      </tr>
      <tr>
        <td class="tb">标准值</td>
        <td v-html="viewData.StandardValue || '-'"></td>
        <td class="tb">经营单位</td>
        <td><q-entity-link :coy-arr="viewData.OperatorKeyno"></q-entity-link></td>
      </tr>
      <tr>
        <td class="tb">经营单位地址</td>
        <td v-html="viewData.OperatorAddress || '-'" colspan="3"></td>
      </tr>
      <tr>
        <td class="tb">生产/经营许可编号</td>
        <td v-html="viewData.MedicalInspectionLicenseNum || '-'" colspan="3"></td>
      </tr>
      <tr>
        <td class="tb">检查依据</td>
        <td v-html="viewData.MedicalInspectionBasis || '-'" colspan="3"></td>
      </tr>
      <tr>
        <td class="tb">检查结果描述</td>
        <td v-html="viewData.MedicalInspectionResult || '-'" colspan="3"></td>
      </tr>
      <tr>
        <td class="tb">缺陷和问题描述</td>
        <td v-html="viewData.MedicalInspectionDefect || '-'" colspan="3"></td>
      </tr>
      <tr>
        <td class="tb">处理措施</td>
        <td v-html="viewData.MedicalInspectionMeasure || '-'" colspan="3"></td>
      </tr>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
