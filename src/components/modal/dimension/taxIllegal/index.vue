<template>
  <div>
    <div class="mtcaption">注册信息</div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">纳税人名称</td>
        <td width="30%">
          <q-entity-link :coy-obj="{ KeyNo: viewData.TaxpayerId, Name: viewData.TaxpayerName }"></q-entity-link>
        </td>
        <td class="tb" width="20%">纳税人识别号</td>
        <td width="30%">{{ viewData.TaxpayerNumber || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">组织机构代码</td>
        <td>{{ viewData.OrgCode || '-' }}</td>
        <td class="tb">注册地址</td>
        <td>{{ viewData.Address || '-' }}</td>
      </tr>
    </table>

    <div class="mtcaption">案件信息</div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">发布日期</td>
        <td width="30%">{{ viewData.PublishTime | dateformat('YYYY-MM-DD') }}</td>
        <td class="tb" width="20%">案件性质</td>
        <td width="30%">{{ viewData.CaseNature || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">所属税务机关</td>
        <td colspan="3">{{ viewData.TaxGov || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">主要违法事实</td>
        <td colspan="3">{{ viewData.IllegalContent || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">相关法律依据及税务处理处罚情况</td>
        <td colspan="3">{{ viewData.PunishContent || '-' }}</td>
      </tr>
    </table>

    <template v-if="viewData.OperInfo">
      <div class="mtcaption">相关人员及公司</div>
      <table class="ntable">
        <tr>
          <td class="tb" width="20%">法定代表人或负责人</td>
          <td width="30%">
            <q-entity-link
              v-if="viewData.OperInfo"
              :coy-obj="{ KeyNo: viewData.OperInfo.OperId, Name: viewData.OperInfo.OperName }"
            ></q-entity-link>
          </td>
          <td class="tb" width="20%">性别</td>
          <td width="30%">{{ viewData.OperInfo.OperGender || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">证件名称</td>
          <td>{{ viewData.OperInfo.OperCerType || '-' }}</td>
          <td class="tb">证件号码</td>
          <td>{{ viewData.OperInfo.OperCerNo || '-' }}</td>
        </tr>
      </table>
    </template>

    <table class="ntable" v-if="viewData.FinanceChiefInfo">
      <tr>
        <td class="tb" width="20%">负有责任的财务负责人</td>
        <td width="30%">
          <q-entity-link
            v-if="viewData.FinanceChiefInfo"
            :coy-obj="{
              KeyNo: viewData.FinanceChiefInfo.FinanceChiefId,
              Name: viewData.FinanceChiefInfo.FinanceChiefName,
            }"
          ></q-entity-link>
        </td>
        <td class="tb" width="20%">性别</td>
        <td width="30%">{{ viewData.FinanceChiefInfo.FinanceChiefGender || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">证件名称</td>
        <td>{{ viewData.FinanceChiefInfo.FinanceChiefCerType || '-' }}</td>
        <td class="tb">证件号码</td>
        <td>{{ viewData.FinanceChiefInfo.FinanceChiefCerNo || '-' }}</td>
      </tr>
    </table>
    <table class="ntable" v-if="viewData.AgencyInfo">
      <tr>
        <td class="tb" width="20%">负有直接责任的中介机构</td>
        <td colspan="3">
          <q-entity-link :coy-obj="{ KeyNo: viewData.AgencyInfo.AgencyKeyNo, Name: viewData.AgencyInfo.AgencyCompanyName }"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb" width="20%">负有直接责任的中介机构从业人员</td>
        <td width="30%">
          <q-entity-link
            :coy-obj="{
              KeyNo: viewData.AgencyInfo.AgencyPersonId,
              Name: viewData.AgencyInfo.AgencyPersonName,
            }"
          ></q-entity-link>
        </td>
        <td class="tb" width="20%">性别</td>
        <td width="30%">{{ viewData.AgencyInfo.AgencyPersonGender || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">证件名称</td>
        <td>{{ viewData.AgencyInfo.AgencyPersonCerType || '-' }}</td>
        <td class="tb">证件号码</td>
        <td>{{ viewData.AgencyInfo.AgencyPersonCerNo || '-' }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
