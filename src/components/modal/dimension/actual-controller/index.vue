<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>-</span>
        </td>
        <td width="23%" class="tb">变更日期：</td>
        <td width="27%">
          {{ viewData.ChangeDate || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">变更前：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.BeforeObject" :coy-obj="{ KeyNo: viewData.BeforeObject.KeyNo, Name: viewData.BeforeObject.Name }" />
          <span v-else>-</span>
        </td>
        <td class="tb" width="23%">变更后：</td>
        <td width="27%">
          <q-entity-link v-if="viewData.AfterObject" :coy-obj="{ KeyNo: viewData.AfterObject.KeyNo, Name: viewData.AfterObject.Name }" />
          <span v-else>-</span>
        </td>
      </tr>
      <tr v-if="viewData.AfterControlPath">
        <td class="tb" width="23%">变更后控制链路：</td>
        <td colspan="3" class="tdPath">
          <template v-if="viewData.AfterControlPath.length > 0">
            <div v-for="(item, index) in viewData.AfterControlPath" :key="index">
              {{ index + 1 }}.
              <span v-for="(v, k) in item" :key="`${index}-${k}`">
                <q-entity-link v-if="v.KeyNo" :coy-obj="{ KeyNo: v.KeyNo, Name: v.Name || '-' }" />
                <span v-else>{{ v.Name || '-' }}</span>
                <span v-if="k < item.length - 1" class="arrow" :class="[!v.Percent && 'arrow-no-text']">{{ v.Percent }}</span>
              </span>
            </div>
          </template>
          <span v-else>-</span>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
import _ from 'lodash';

export default {
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    isPerson(keyNo) {
      return _.startsWith(keyNo, 'p');
    },
  },
};
</script>
<style lang="less" scoped src="../style.less"></style>
