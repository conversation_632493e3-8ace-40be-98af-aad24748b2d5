import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin(['current']);

export default {
  mixins: [dimensionMixin, listMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    columns() {
      return [
        {
          title: '被投资企业',
          width: 486,
          scopedSlots: { customRender: 'comp' },
        },
        {
          title: '持股比例',
          dataIndex: 'StockPercent',
          width: '90px',
          align: 'center',
        },
        {
          title: '投资数额',
          dataIndex: 'ShouldCapi',
          width: '100px',
          align: 'center',
        },
        {
          title: '投资日期',
          dataIndex: 'ShoudDate',
          width: '100px',
          align: 'center',
        },
      ];
    },
  },
  methods: {
    fetchDataSource({ pagination }) {
      const { title, ...detailParams } = this.detailParams;
      return this.mDimenGetList(
        {
          ...detailParams,
          ...pagination,
        },
        'groupInvesterDetail'
      );
    },
  },
};
