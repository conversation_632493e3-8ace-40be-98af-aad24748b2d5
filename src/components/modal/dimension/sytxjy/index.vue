<template>
  <div>
    <div class="mtcaption">特许人基本信息</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%" class="tb">备案号</th>
          <td>{{ viewData.RecordNo || '-' }}</td>
          <th width="23%" class="tb">备案公告日期</th>
          <td>{{ viewData.RecordDate | dateformat() }}</td>
        </tr>
        <tr>
          <th width="23%" class="tb">法定代表人</th>
          <td width="27%">
            <template v-if="viewData.LegalPer">
              <q-entity-link :coy-obj="{ Name: viewData.LegalPer, KeyNo: viewData.KeynoPer }"></q-entity-link>
            </template>
            <template v-else>-</template>
          </td>
          <th width="23%" class="tb">成立日期</th>
          <td>{{ viewData.EstablishDate | dateformat() }}</td>
        </tr>
        <tr>
          <th width="23%" class="tb">住所</th>
          <td>{{ viewData.Address || '-' }}</td>
          <th width="23%" class="tb">公司网站</th>
          <td>{{ viewData.Website || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <div class="mtcaption">特许人联系方式</div>
    <q-plain-table class="ntable">
      <tr>
        <th width="23%" class="tb">电话</th>
        <td width="27%">{{ viewData.Tel || '-' }}</td>
        <th width="23%" class="tb">传真</th>
        <td>{{ viewData.Fax || '-' }}</td>
      </tr>
      <tr>
        <th width="23%" class="tb">电子邮件</th>
        <td colspan="3">{{ viewData.Email || '-' }}</td>
      </tr>
    </q-plain-table>
    <div class="mtcaption">变更记录</div>
    <q-plain-table class="ntable">
      <tr>
        <th width="23%" class="tb">特许人名称变更后</th>
        <td colspan="3">
          <template v-if="viewData.FranchisorName">
            <q-entity-link :coy-obj="{ Name: viewData.FranchisorName, KeyNo: viewData.KeynoFranchisor }"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
      <tr>
        <th width="23%" class="tb">特许人名称变更前</th>
        <td colspan="3">
          <template v-if="viewData.OldFranchisorkeyno && viewData.OldFranchisorkeyno.length > 0">
            <q-entity-link
              :coy-arr="
                viewData.OldFranchisorkeyno.map((v) => {
                  return { KeyNo: v.keyNo, Name: v.name };
                })
              "
            ></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
    </q-plain-table>
    <div class="mtcaption">电子材料</div>
    <q-plain-table class="ntable">
      <colgroup>
        <col width="23%" />
        <col width="77%" />
      </colgroup>
      <tr>
        <td class="tb">
          <div>与特许经营信息活动相关的商标权、专利权及其他经营资源的注册证书</div>
        </td>
        <td>
          <template v-if="viewData.FranchiseCertificate.length > 0">
            <span v-for="item in viewData.FranchiseCertificate" :key="item.key">
              <a :href="item.OssLinkId" target="_blank">{{ item.Name || '-' }}</a>
              <br />
            </span>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
    </q-plain-table>
    <div class="mtcaption">境内加盟店</div>
    <q-plain-table class="ntable">
      <tr>
        <th width="23%" class="tb">加盟分布区域</th>
        <td colspan="3">{{ viewData.FranchiseArea || '-' }}</td>
      </tr>
    </q-plain-table>
    <div class="mtcaption">经营资源信息</div>
    <q-plain-table class="ntable info">
      <tr style="text-align: center">
        <th>序号</th>
        <th>权利号</th>
        <th>权利性质</th>
        <th>权利日期</th>
        <th>权利期限</th>
        <th>权利类型</th>
        <th>注册类别</th>
        <th>特许品牌</th>
      </tr>
      <tr v-for="(item, index) in viewData.FranchiseResource" :key="`item-${index}`" style="text-align: center">
        <td>{{ index + 1 }}</td>
        <td>{{ item.RightNumber || '-' }}</td>
        <td>{{ item.RightNature || '-' }}</td>
        <td>{{ item.RightStartDate || '-' }}</td>
        <td>{{ item.RightEndDate || '-' }}</td>
        <td>{{ item.RightType || '-' }}</td>
        <td>{{ item.RegistrationCategory || '-' }}</td>
        <td>{{ item.LicensedBrand || '-' }}</td>
        <!--
        <td>
          <a :href="item.AttachedUrl" target="_blank">{{item.Title || '-'}}</a>
          <span class="text-gray pull-right">{{item.PublishDate | dateformat('YYYY-MM-DD')}}</span>
        </td>
        -->
      </tr>
    </q-plain-table>
  </div>
</template>

<script src="./component.js"></script>
<style src="./style.less" scoped lang="less"></style>
