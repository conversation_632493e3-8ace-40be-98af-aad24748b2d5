// 关联企业
import { defineComponent } from 'vue';

const InvestBenefit = defineComponent({
  name: 'InvestBenefit',

  props: {
    dialogProps: {
      type: Object,
    },
  },

  data() {
    return {
      viewData: {},
    };
  },

  computed: {
    keyNo() {
      return (this as any).dialogProps?.keyNo;
    },
    tags(): any {
      return (this as any).dialogProps?.tags ?? [];
    },
    isInvest() {
      return (this as any).dialogProps?.isInvest;
    },
    reverse() {
      return (this as any).dialogProps?.reverse;
    },
    investName() {
      return (this as any).dialogProps.investName || '';
    },
    totalPercentLabel() {
      return (this as any).dialogProps.totalPercentLabel || '最终受益股份';
    },
    directPercentLabel() {
      return (this as any).dialogProps.directPercentLabel || '';
    },
    pathLabel() {
      return (this as any).dialogProps.pathLabel || '股权链';
    },
  },

  methods: {
    async getDetail() {
      const that = this as any;
      if ((this as any).dialogProps.viewData) {
        (this as any).viewData = (this as any).dialogProps.viewData;
      } else {
        const { keyNo, partnerId } = that.dialogProps;
        this.$service.dimensionDetail
          .getBenefitDetail({
            keyNo,
            partnerId,
          })
          .then(({ Result: data }) => {
            that.viewData = data?.Names?.[0];
            that.viewData.holdName = data.CompanyName;
            that.viewData.holdKeyNo = data.KeyNo;
          });
      }
    },
    formatData(list) {
      return list.map((v) => {
        const item = v;
        item.area = item.Area?.Province;
        if (['注销', '吊销', '停业', '撤销', '清算'].includes(item.Status)) {
          item.statusType = 'danger';
        } else if (['筹建', '迁入', '迁出'].includes(item.Status)) {
          item.statusType = 'default';
        } else {
          item.statusType = 'success';
        }
        if (item.Status === '存续（在营、开业、在册）') {
          item.Status = '存续';
        }
        if (item.Relation?.length) {
          item.Relation.forEach((r) => {
            if (r.Type === '1') {
              item.radio = r.Value;
            } else if (r.Type === '2') {
              item.postion = r.Value;
            }
          });
        }
        return item;
      });
    },
  },

  mounted() {
    (this as any).getDetail();
  },

  render() {
    const that = this as any;
    const { tags, viewData, isInvest, reverse, investName, totalPercentLabel, directPercentLabel, pathLabel } = that;
    return (
      <table class="ntable">
        <tr>
          <td class="tb" width="23%">
            股东名称
          </td>
          <td colspan="3">
            <q-td-coy
              keyNo={viewData.KeyNo}
              name={viewData.Name}
              image={viewData.ImageUrl}
              has-image={viewData.HasImage}
              company-count={viewData.RelatedCount}
              show-war-link={true}
              long-td={true}
              no-click={true}
              tags={tags}
            ></q-td-coy>
          </td>
        </tr>
        <tr v-show={investName}>
          <td class="tb" width="23%">
            {investName}
          </td>
          <td colspan="3">
            <q-td-coy
              keyNo={viewData.holdKeyNo}
              name={viewData.holdName}
              image={viewData.holdImageUrl}
              has-image={viewData.HasImage}
              company-count={viewData.RelatedCount}
              show-war-link={true}
              long-td={true}
              no-click={true}
              tags={tags}
            ></q-td-coy>
          </td>
        </tr>
        <tr>
          <td class="tb" width="23%">
            {totalPercentLabel}
          </td>
          <td colspan="3">{viewData.PercentTotal || '-'}</td>
        </tr>
        <tr v-show={directPercentLabel}>
          <td class="tb" width="23%">
            {directPercentLabel}
          </td>
          <td colspan="3">{viewData.DirectPercent || '-'}</td>
        </tr>
        <tr>
          <td class="tb" width="23%">
            {pathLabel}
          </td>
          <td colspan="3">
            {investName ? (
              <q-td-path
                name={viewData.Name}
                key-no={viewData.KeyNo}
                data-type={viewData.dataType}
                paths={viewData.Paths}
                isInvest={isInvest}
                reverse={reverse}
              ></q-td-path>
            ) : (
              <q-td-path
                name={viewData.holdName}
                key-no={viewData.holdKeyNo}
                data-type={viewData.dataType}
                paths={viewData.Paths}
                isInvest={isInvest}
                reverse={reverse}
              ></q-td-path>
            )}
          </td>
        </tr>
      </table>
    );
  },
});

export default InvestBenefit;
