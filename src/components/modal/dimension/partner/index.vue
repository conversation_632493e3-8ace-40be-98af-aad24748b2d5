<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">变更企业：</td>
        <td width="27%" v-if="viewData.KeyNo">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>{{ viewData.Name || '-' }}</span>
        </td>
        <td width="27%" v-else>
          {{ viewData.Name }}
        </td>
        <td width="23%" class="tb">发现变更日期：</td>
        <td width="27%">
          {{ viewData.ChangeDate | dateformat('YYYY-MM-DD') }}
        </td>
      </tr>
      <template v-if="viewData">
        <tr v-if="viewData.ChangeObject.length > 0 && viewData.RiseObject.length > 0">
          <td class="tb" width="23%">股份下降：</td>
          <td width="27%">
            <span v-for="(item, k) in viewData.ChangeObject" :key="k">
              <span v-if="item.KeyNo">
                <q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              </span>
              <span v-else>
                {{ item.Name || '-' }}
              </span>
              持股比例从
              <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'"> {{ item.B }}下降到 </span>
              <span v-else> 未知下降到 </span>
              <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'"> {{ item.C }}<br /> </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
          <td class="tb" width="23%">股份上升：</td>
          <td width="27%">
            <span v-for="(item, k) in viewData.RiseObject" :key="k">
              <span v-if="item.KeyNo">
                <q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              </span>
              <span v-else>
                {{ item.Name || '-' }}
              </span>
              持股比例从
              <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'"> {{ item.B }}上升到 </span>
              <span v-else> 未知上升到 </span>
              <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'"> {{ item.C }}<br /> </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
        </tr>
        <tr v-if="viewData.ChangeObject.length > 0 && viewData.RiseObject.length === 0">
          <td class="tb" width="23%">股份下降：</td>
          <td colspan="3">
            <span v-for="(item, k) in viewData.ChangeObject" :key="k">
              <span v-if="item.KeyNo">
                <q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              </span>
              <span v-else>
                {{ item.Name || '-' }}
              </span>
              持股比例从
              <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'"> {{ item.B }}下降到 </span>
              <span v-else> 未知下降到 </span>
              <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'"> {{ item.C }}<br /> </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
        </tr>
        <tr v-if="viewData.ChangeObject.length === 0 && viewData.RiseObject.length > 0">
          <td class="tb" width="23%">股份上升：</td>
          <td colspan="3">
            <span v-for="(item, k) in viewData.RiseObject" :key="k">
              <span v-if="item.KeyNo">
                <q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              </span>
              <span v-else>
                {{ item.Name || '-' }}
              </span>
              持股比例从
              <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'"> {{ item.B }}上升到 </span>
              <span v-else> 未知上升到 </span>
              <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'"> {{ item.C }}<br /> </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
        </tr>
        <tr v-if="viewData.ExitObject.length > 0">
          <td class="tb" width="23%">退出：</td>
          <td colspan="3">
            <span v-for="(item, k) in viewData.ExitObject" :key="k">
              <span v-if="item.KeyNo">
                <q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              </span>
              <span v-else>
                {{ item.Name || '-' }}
              </span>
              <span v-if="item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%'">
                ，退出前持股{{ item.StockPercent }}<br />
              </span>
              <span v-else> ，退出前持股未知<br /> </span>
            </span>
          </td>
        </tr>
        <tr v-if="viewData.AddObject.length > 0">
          <td class="tb" width="23%">新增：</td>
          <td colspan="3">
            <span v-for="(item, k) in viewData.AddObject" :key="k">
              <span v-if="item.KeyNo">
                <q-entity-link :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name || '-' }" />
              </span>
              <span v-else>
                {{ item.Name || '-' }}
              </span>
              <span v-if="item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%'">
                ，持股{{ item.StockPercent }}<br />
              </span>
              <span v-else>
                <br />
              </span>
            </span>
          </td>
        </tr>
      </template>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
