<template>
  <div v-if="formattedData.Detail">
    <table class="ntable">
      <tr>
        <td class="tb" width="18%">公告状态</td>
        <td width="103" colspan="2">
          {{ formattedData.NoticeStatus || '-' }}
        </td>
      </tr>
      <!--   清算组备案信息   -->
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td class="tb" width="20%" rowspan="10">清算组备案信息</td>
        <td width="20%">企业名称</td>
        <td>
          <q-entity-link
            :coy-obj="{
              Name: formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.CompanyName,
              KeyNo: formattedData.keyNo_p,
            }"
          ></q-entity-link>
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">统一社会信用代码/注册号</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.CreditCode) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">登记机关</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo.BelongOrg && formattedData.Detail.LiqBAInfo.BelongOrg) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">清算组备案日期</td>
        <td width="27%" v-if="formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqBADate">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqBADate) | dateformat('YYYY-MM-DD') }}
        </td>
        <td width="27%" v-else>-</td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">清算组成立日期</td>
        <td width="27%" v-if="formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqStartDate">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqStartDate) | dateformat('YYYY-MM-DD') }}
        </td>
        <td width="27%" v-else>-</td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">注销原因</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.CancelReason) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">清算组办公地址</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqAddress) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">清算组联系电话</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqTelNo) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">清算组负责人</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqLeader) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.LiqBAInfo">
        <td width="20%">清算组成员</td>
        <td width="27%">
          {{ (formattedData.Detail.LiqBAInfo && formattedData.Detail.LiqBAInfo.LiqMember) || '-' }}
        </td>
      </tr>

      <!--债权人公告信息-->
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td class="tb" width="20%" rowspan="9">债权人公告信息</td>
        <td width="20%">企业名称</td>
        <td
          width="27%"
          v-if="formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.CompanyName && formattedData.keyNo_p"
        >
          <q-entity-link
            :coy-obj="{
              Name: formattedData.Detail.CreditorNoticeInfo.CompanyName,
              KeyNo: formattedData.keyNo_p,
            }"
          >
          </q-entity-link>
        </td>
        <td width="27%" v-else>-</td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">统一社会信用代码/注册号</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.CreditCode) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">登记机关</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.BelongOrg) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">公告期</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.NoticeDate) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">公告内容</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.NoticeContent) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">债权申报联系人</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.ClaimsDeclarationMember) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">债权申报联系电话</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.ClaimsDeclarationTelNo) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.CreditorNoticeInfo">
        <td width="20%">债权申报地址</td>
        <td width="27%">
          {{ (formattedData.Detail.CreditorNoticeInfo && formattedData.Detail.CreditorNoticeInfo.ClaimsDeclarationAddress) || '-' }}
        </td>
      </tr>

      <!--修改信息-->
      <tr v-if="formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeContent">
        <td class="tb" width="20%" rowspan="8">修改信息</td>
        <td width="20%">修改内容</td>
        <td width="27%">
          {{ (formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeContent) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeContent">
        <td width="20%">修改前</td>
        <td width="27%">
          {{ (formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeBefore) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeContent">
        <td width="20%">修改后</td>
        <td width="27%">
          {{ (formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeAfter) || '-' }}
        </td>
      </tr>
      <tr v-if="formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeContent">
        <td width="20%">修改时间</td>
        <td width="27%" v-if="formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeDate">
          {{ (formattedData.Detail.ChangeInfos && formattedData.Detail.ChangeInfos.ChangeDate) | dateformat('YYYY-MM-DD') }}
        </td>
        <td width="27%" v-else>-</td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    dialogProps: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    formattedData() {
      return this.dialogProps;
    },
  },
};
</script>
