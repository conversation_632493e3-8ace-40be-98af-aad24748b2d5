<template>
  <div class="bond-detail">
    <div class="breach-history-list clearfix" v-for="(item, index) in viewData.DefaultCourse" :key="`item-${index}`">
      <div class="breach-history-list-date">{{ item.EventDate | dateformat('YYYY-MM-DD') }}</div>
      <div class="breach-history-list-point-wrap">
        <div :class="['breach-history-list-point-icon', index === 0 ? '' : 'lint-top']"></div>
        <div class="breach-history-list-point-bottom-line" v-if="index < viewData.DefaultCourse.length - 1"></div>
      </div>
      <div v-if="item.viewType" class="breach-history-list-content">
        <div class="breach-history-list-content-stage">{{ item.EventCategoryDesc || '-' }}</div>
        <div class="breach-history-list-content-text">
          <span>{{ item.EventItem ? `[${item.EventItem}]` : '' }}</span
          >{{ item.Description || '-' }}
        </div>
      </div>
      <div v-else class="breach-history-list-content">
        <div class="breach-history-list-content-stage font-bold">{{ item.EventItem || '-' }}</div>
        <div class="breach-history-list-content-text">{{ item.Description || '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style src="./style.less" lang="less" scoped></style>
