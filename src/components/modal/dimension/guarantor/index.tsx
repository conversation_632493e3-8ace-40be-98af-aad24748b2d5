import { defineComponent, ref, watch } from 'vue';

import { dateFormat, formatNumber } from '@/utils/format';

export default defineComponent({
  name: 'guarantor',
  props: {
    viewData: {
      type: [Object, Array],
      default: () => ({}),
    },
  },
  setup(props) {
    const dataSource = ref({}) as any;
    watch(
      () => props.viewData,
      (val) => {
        if (Array.isArray(val)) {
          dataSource.value = val[0];
        } else {
          dataSource.value = val;
        }
      },
      {
        immediate: true,
      }
    );
    return {
      dataSource,
    };
  },
  render() {
    const { dataSource } = this;
    return (
      <div>
        <table class="ntable">
          <tr>
            <td width="20%" class="tb">
              担保方
            </td>
            <td colspan="3">
              <q-entity-link coy-arr={dataSource.Guarantee}></q-entity-link>
            </td>
          </tr>
          <tr>
            <td width="20%" class="tb">
              被担保方
            </td>
            <td colspan="3">
              <q-entity-link coy-arr={dataSource.Vouchee}></q-entity-link>
            </td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              公告日期
            </td>
            <td width="30%">{dateFormat(dataSource.PublicDate * 1000)}</td>
            <td class="tb" width="20%">
              担保方式
            </td>
            <td width="30%">{dataSource.GuaranteeType || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              担保金额(万元)
            </td>
            <td width="30%">{dataSource.GuaranteeMoney ? formatNumber(dataSource.GuaranteeMoney) : '-'}</td>
            <td class="tb" width="20%">
              币种
            </td>
            <td width="30%">{dataSource.GuaranteeCurrency || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              担保期限
            </td>
            <td width="30%">{dataSource.GuaranteePeriod || '-'}</td>
            <td class="tb" width="20%">
              担保起始日
            </td>
            <td width="30%">{dateFormat(dataSource.GuaranteeStartDate * 1000)}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              担保终止日
            </td>
            <td width="30%">{dateFormat(dataSource.GuaranteeEndDate * 1000)}</td>
            <td class="tb" width="20%">
              是否履行完毕
            </td>
            <td width="30%">{dataSource.IfCompleteDesc || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              是否关联交易
            </td>
            <td width="30%">{dataSource.IfRelatedTradeDesc || '-'}</td>
            <td class="tb" width="20%">
              交易日期
            </td>
            <td width="30%">{dateFormat(dataSource.TradeDate * 1000)}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              报告期
            </td>
            <td width="30%">{dateFormat(dataSource.ReportDate * 1000)}</td>
            <td class="tb" width="20%">
              报告期类别
            </td>
            <td width="30%">{dataSource.ReportTypeDesc || '-'}</td>
          </tr>
          <tr>
            <td width="20%" class="tb">
              担保事件说明
            </td>
            <td colspan="3">{dataSource.GuaranteeIntro || '-'}</td>
          </tr>
        </table>
      </div>
    );
  },
});
