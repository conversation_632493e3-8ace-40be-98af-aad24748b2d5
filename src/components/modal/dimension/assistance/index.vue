<template>
  <div>
    <div class="basic">
      <div class="assistance-container">基础信息</div>
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 20%">执行通知书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.ExecutionNoticeNum" style="width: 30%">
            <a
              rel="nofollow"
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.ExecutionNoticeNum}`"
              target="_blank"
              v-html="viewData.ExecutionNoticeNum || '-'"
            />
          </td>
          <td v-else style="width: 30%" v-html="viewData.ExecutionNoticeNum || '-'" />
          <td class="tb" style="width: 20%">冻结状态</td>
          <td style="width: 30%">{{ viewData.StatuesDetail || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">被执行人</td>
          <td>
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }" />
          </td>
          <td class="tb">
            疑似申请执行人
            <q-glossary-info tooltip="该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。" />
          </td>
          <td>
            <q-entity-link v-if="viewData.SqrInfo" :coy-arr="viewData.SqrInfo" />
            <template v-else>-</template>
          </td>
        </tr>
        <tr>
          <td class="tb">被冻结标的企业</td>
          <td :colspan="viewData.JudicialPartnersChangeDetail ? '3' : '1'">
            <q-entity-link :coy-obj="viewData.RelatedCompanyInfo" />
          </td>
          <td v-if="!viewData.JudicialPartnersChangeDetail" class="tb">被冻结标的数额、其他投资权益数额</td>
          <td v-if="!viewData.JudicialPartnersChangeDetail">{{ numberToHumanWithUnit(viewData.EquityAmount) }}</td>
        </tr>
        <tr>
          <td class="tb">执行法院</td>
          <td colspan="3">{{ viewData.EnforcementCourt || '-' }}</td>
        </tr>
      </table>
    </div>

    <template v-if="viewData.EquityFreezeDetail">
      <div class="assistance-container m-top">股权冻结信息</div>
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 20%">执行裁定书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.EquityFreezeDetail.ExecutionVerdictNum" style="width: 30%">
            <a
              rel="nofollow"
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.EquityFreezeDetail.ExecutionVerdictNum}`"
              target="_blank"
              v-html="viewData.EquityFreezeDetail.ExecutionVerdictNum || '-'"
            />
          </td>
          <td v-else style="width: 30%" v-html="viewData.EquityFreezeDetail.ExecutionVerdictNum || '-'" />
          <td class="tb" style="width: 20%">公示日期</td>
          <td style="width: 30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.PublicDate) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb">被执行人</td>
          <td>
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }" />
          </td>
          <td class="tb">被冻结标的数额、其他投资权益数额</td>
          <td>{{ numberToHumanWithUnit(viewData.EquityAmount) }}</td>
        </tr>
        <tr>
          <td class="tb">被执行人证照种类</td>
          <td>{{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.ExecutedPersonDocType) || '-' }}</td>

          <td class="tb">被执行人证照号码</td>
          <td>{{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.ExecutedPersonDocNum) || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">冻结日期自</td>
          <td>{{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeStartDate) || '-' }}</td>
          <td class="tb">冻结日期至</td>
          <td>{{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeEndDate) || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">冻结期限</td>
          <td>
            <template v-if="viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeTerm">
              {{ viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeTerm }}
            </template>
            <template v-else> - </template>
          </td>
          <td class="tb" style="width: 20%">执行事项</td>
          <td>{{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.ExecutionMatters) || '-' }}</td>
        </tr>
      </table>
    </template>
    <template v-if="viewData.JudicialPartnersChangeDetail">
      <div class="assistance-container m-top">股权变更</div>
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 20%">执行裁定书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum" style="width: 30%">
            <a
              rel="nofollow"
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum}`"
              target="_blank"
              v-html="viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum || '-'"
            />
          </td>
          <td v-else style="width: 30%" v-html="viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum || '-'" />
          <td class="tb" style="width: 20%">协助执行日期</td>
          <td style="width: 30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.AssistExecDate) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb">被执行人</td>
          <td>
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }" />
          </td>
          <td class="tb">被执行人持有股权、其他投资权益数额</td>
          <td>{{ numberToHumanWithUnit(viewData.EquityAmount) }}</td>
        </tr>
        <tr>
          <td class="tb">被执行人证照种类</td>
          <td>
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutedPersonDocType) || '-' }}
          </td>
          <td class="tb">被执行人证照号码</td>
          <td>
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutedPersonDocNum) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb">受让人</td>
          <td>
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.Assignee) || '-' }}
          </td>
          <td class="tb">执行事项</td>
          <td>
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionMatters) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb">受让人证照号码</td>
          <td>
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.AssigneeRegNo) || '-' }}
          </td>
          <td class="tb">受让人证照种类</td>
          <td>
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.AssigneeDocKind) || '-' }}
          </td>
        </tr>
      </table>
    </template>
    <template v-if="viewData.EquityUnValidDetail">
      <div class="assistance-container m-top">冻结失效</div>
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 20%">冻结失效原因</td>
          <td style="width: 30%">
            {{ (viewData.EquityUnValidDetail && viewData.EquityUnValidDetail.UnValidReason) || '-' }}
          </td>
          <td class="tb" style="width: 20%">失效时间</td>
          <td style="width: 30%">
            {{ (viewData.EquityUnValidDetail && viewData.EquityUnValidDetail.UnValidDate) || '-' }}
          </td>
        </tr>
      </table>
    </template>

    <template v-if="viewData.EquityUnFreezeDetail">
      <div class="assistance-container m-top">股权解冻</div>
      <table class="ntable">
        <tr>
          <td class="tb" style="width: 20%">执行裁定书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.EquityUnFreezeDetail.ExecutionVerdictNum" style="width: 30%">
            <a
              rel="nofollow"
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.EquityUnFreezeDetail.ExecutionVerdictNum}`"
              target="_blank"
              v-html="viewData.EquityUnFreezeDetail.ExecutionVerdictNum || '-'"
            />
          </td>
          <td v-else style="width: 30%" v-html="viewData.EquityUnFreezeDetail.ExecutionVerdictNum || '-'" />
          <td class="tb" style="width: 20%">公示日期</td>
          <td style="width: 30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.PublicDate) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb">被执行人</td>
          <td>
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }" />
          </td>
          <td class="tb">被冻结标的数额、其他投资权益数额</td>
          <td>{{ numberToHumanWithUnit(viewData.EquityAmount) }}</td>
        </tr>
        <tr>
          <td class="tb">被执行人证照种类</td>
          <td>{{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ExecutedPersonDocType) || '-' }}</td>
          <td class="tb">被执行人证照号码</td>
          <td>{{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ExecutedPersonDocNum) || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">解除冻结日期</td>
          <td>{{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.UnFreezeDate) || '-' }}</td>
          <td class="tb">执行事项</td>
          <td>{{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ExecutionMatters) || '-' }}</td>
        </tr>
      </table>
    </template>
    <q-relate-cases :search-params="viewData" />
  </div>
</template>

<script src="./component.js"></script>
<style lang="less">
.assistance-container {
  margin-bottom: 10px;
}
</style>
