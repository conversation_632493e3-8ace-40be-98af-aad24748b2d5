import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin(['current'], {
  rowKey: 'SourceId',
});

export default {
  mixins: [dimensionMixin, listMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    columns() {
      return [
        { title: '客户', scopedSlots: { customRender: 'customer' } },
        {
          title: '销售占比',
          dataIndex: 'Proportion',
          align: 'center',
          customRender: (text) => {
            return text ? `${text}%` : '-';
          },
        },
        {
          title: '销售金额(万元)',
          align: 'center',
          dataIndex: 'Quota',
        },
        {
          title: '报告期/公开时间',
          align: 'center',
          customRender: (text, record) => {
            return record.ReportYear ? `${record.ReportYear}${record.Month ? `-${record.Month}` : ''}` : '-';
          },
        },
        {
          title: '数据来源',
          align: 'center',
          customRender: (text, record) => {
            if (record.SourceId && record.SourceCode === 6) {
              return <q-link to={`/tender/${record.SourceId}`}>{record.Source || '-'}</q-link>;
            }
            if (record.SourceId && record.SourceCode === 7) {
              const sourceId = record.SourceId.slice(0, record.SourceId.length - 1);
              return <q-link to={`/wenshuDetail/${sourceId}`}>{record.Source || '-'}</q-link>;
            }
            return record.Source || '-';
          },
        },
        {
          title: '关联关系',
          align: 'center',
          dataIndex: 'Relationship',
        },
      ];
    },
  },
  methods: {
    fetchDataSource({ pagination }) {
      return this.mDimenGetList(
        {
          dataType: 1,
          ...this.detailParams,
          ...pagination,
        },
        'supplierCustomer'
      );
    },
  },
};
