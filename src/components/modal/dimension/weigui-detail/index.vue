<template>
  <q-plain-table>
    <tbody>
      <tr>
        <td class="tb" width="20%">公告日期</td>
        <td width="30%" v-if="viewData.publicdate">{{ dateFormat(viewData.publicdate) }}</td>
        <td v-else>-</td>
        <td class="tb" width="20%">处罚对象</td>
        <td width="30%">
          <q-entity-link v-if="viewData.PunishObj" :coy-obj="viewData.PunishObj"></q-entity-link>
          <q-entity-link v-else-if="MarkedmanList" :coy-arr="MarkedmanList"></q-entity-link>
          <q-entity-link
            v-else-if="viewData.markedman"
            :coy-obj="{ KeyNo: viewData.markedmankey, Name: viewData.markedman, Org: viewData.markedmanorg }"
          ></q-entity-link>
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td class="tb" width="20%">违规类型</td>
        <td width="30%">{{ viewData.type || '-' }}</td>
        <td class="tb" width="20%">处分类型</td>
        <td width="30%">{{ viewData.disposition || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">处理人</td>
        <td width="30%">{{ viewData.processman || '-' }}</td>
        <td class="tb" width="20%">处罚金额（万元）</td>
        <td width="30%">{{ viewData.punishmentamount || '-' }}</td>
      </tr>
      <tr>
        <td width="20%" class="tb">违规行为</td>
        <td colspan="3">{{ viewData.violation || '-' }}</td>
      </tr>
      <tr>
        <td width="20%" class="tb">处分措施</td>
        <td colspan="3">{{ viewData.punishmentmeasure || '-' }}</td>
      </tr>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
