import { dateFormat } from '@/utils/format';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    MarkedmanList() {
      return this.viewData.markedmanarray ? JSON.parse(this.viewData.markedmanarray) : null;
    },
  },
  methods: {
    dateFormat(date, options) {
      return dateFormat(date, options);
    },
  },
};
