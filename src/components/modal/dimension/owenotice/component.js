import { dateFormat } from '@/utils/format';
import { numberToHuman } from '@/utils/number-formatter';
// import uiService from '@/ui-service';
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    numberToHuman(val) {
      return numberToHuman(Number(val), { precision: 2 });
    },
    dateFormat(date, options) {
      return dateFormat(date, options);
    },
  },
};
