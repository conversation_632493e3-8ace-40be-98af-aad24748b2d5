<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <th class="tb" width="23%">单位名称</th>
          <td colspan="3">
            <q-entity-link :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.CompanyName }" />
          </td>
        </tr>
        <tr>
          <th width="23%" class="tb">纳税人类型</th>
          <td width="27%">{{ viewData.Type || '-' }}</td>
          <th width="23%" class="tb">纳税人识别号</th>
          <td width="27%">{{ viewData.IdentifyNo || '-' }}</td>
        </tr>
        <tr>
          <th width="23%" class="tb">负责人姓名</th>
          <td width="27%">
            <q-entity-link :coy-obj="viewData.Oper" />
          </td>
          <th width="23%" class="tb">证件号码</th>
          <td width="27%">{{ viewData.IdNo || '-' }}</td>
        </tr>
        <tr>
          <th class="tb" width="23%">经营地点</th>
          <td colspan="3">
            {{ viewData.Addr || '-' }}
          </td>
        </tr>

        <tr>
          <th class="tb" width="23%">欠税税种</th>
          <td colspan="3">{{ viewData.Category || '-' }}</td>
        </tr>
        <tr>
          <th width="23%" class="tb">欠税余额(元)</th>
          <td width="27%">{{ numberToHuman(viewData.Balance) || '0' }}</td>
          <th width="23%" class="tb">当前新发生的欠税余额(元)</th>
          <td width="27%">{{ numberToHuman(viewData.NewBal) || '0' }}</td>
        </tr>

        <tr>
          <th width="23%" class="tb">发布单位</th>
          <td width="27%">{{ viewData.IssuedBy || '-' }}</td>
          <th width="23%" class="tb">发布日期</th>
          <td width="27%">{{ viewData.PublishDate | dateformat('YYYY-MM-DD') }}</td>
        </tr>
      </tbody>
    </q-plain-table>
  </div>
</template>
<script src="./component.js"></script>
