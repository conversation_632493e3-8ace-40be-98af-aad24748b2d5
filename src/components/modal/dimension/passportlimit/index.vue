<template>
  <div>
    <table class="ntable">
      <tr>
        <td style="width: 18%" class="tb">案号</td>
        <td style="width: 26%">
          <a
            v-if="viewData.CaseSearchId"
            ref="noopener"
            target="_blank"
            :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId}&title=${viewData.CaseNo}`"
            >{{ viewData.CaseNo }}</a
          >
          <span v-else>{{ viewData.CaseNo || '-' }}</span>
        </td>
        <td style="width: 24%" class="tb">限制出境对象</td>
        <td style="width: 32%">
          <span v-if="viewData.LimitedPerson && viewData.LimitedPerson.length != 0">
            <span v-for="(lim, index) in viewData.LimitedPerson" :key="index">
              <q-entity-link :coy-obj="{ Name: lim.Name, KeyNo: lim.KeyNo }"></q-entity-link>
            </span>
          </span>
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td class="tb">失信被执行人</td>
        <td>
          <span v-if="viewData.ExecutedPerson && viewData.ExecutedPerson.length != 0">
            <span v-for="(exe, index) in viewData.ExecutedPerson" :key="index">
              <q-entity-link :coy-obj="{ Name: exe.Name, KeyNo: exe.KeyNo }"></q-entity-link>
            </span>
          </span>
          <span v-else>-</span>
        </td>
        <td class="tb">被执行人地址</td>
        <td v-html="viewData.ExecutedAddress || '-'"></td>
      </tr>
      <tr>
        <td class="tb">申请执行人</td>
        <td>
          <span v-if="viewData.Applayer && viewData.Applayer.length != 0">
            <span v-for="(appl, index) in viewData.Applayer" :key="index">
              <q-entity-link :coy-obj="{ Name: appl.Name, KeyNo: appl.KeyNo }"></q-entity-link>
            </span>
          </span>
          <span v-else>-</span>
        </td>
        <td class="tb">执行标的(元)</td>
        <td>{{ viewData.ExecutedAmount }}</td>
      </tr>
      <tr>
        <td class="tb">执行法院</td>
        <td>{{ viewData.Court || '-' }}</td>
        <td class="tb">联系电话</td>
        <td>{{ viewData.CourtTel || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">发布日期</td>
        <td colspan="3">{{ viewData.PublishDate }}</td>
      </tr>
    </table>
  </div>
</template>
<script src="./component.js"></script>
