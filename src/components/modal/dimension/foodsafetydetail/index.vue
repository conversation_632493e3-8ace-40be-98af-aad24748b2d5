<template>
  <table class="ntable">
    <tr>
      <td width="23%" class="tb">被抽检企业：</td>
      <td width="27%">
        <q-entity-link :coy-arr="dataSource.SamplingCompanys" />
      </td>
      <td width="23%" class="tb">食品名称：</td>
      <td>{{ dataSource.FoodName || '-' }}</td>
    </tr>
    <tr>
      <td width="23%" class="tb">抽检结果：</td>
      <td width="27%">
        {{ dataSource.CheckResult || '-' }}
      </td>
      <td width="23%" class="tb">抽检次数：</td>
      <td width="27%">第{{ dataSource.CheckRank }}次抽检</td>
    </tr>
    <tr>
      <td width="23%" class="tb">标称生产企业：</td>
      <td width="27%">
        <q-entity-link :coy-arr="dataSource.ProdCompanys" />
      </td>
      <td width="23%" class="tb">标称生产企业地址：</td>
      <td width="27%">{{ dataSource.ProdAddress || '-' }}</td>
    </tr>
    <tr>
      <td width="23%" class="tb">商标：</td>
      <td width="27%">{{ dataSource.TrademarkInfo || '-' }}</td>
      <td width="23%" class="tb">规格型号：</td>
      <td width="27%">{{ dataSource.Specs || '-' }}</td>
    </tr>
    <tr>
      <td width="23%" class="tb">生产日期/批号：</td>
      <td colspan="3">
        {{ dataSource.Batch || '-' }}
      </td>
    </tr>
  </table>
</template>

<script>
export default {
  name: 'foodsafetydetail',
  props: ['viewData'],
  data() {
    return {
      dataSource: null,
    };
  },
  watch: {
    viewData: {
      handler(val) {
        if (Array.isArray(val)) {
          this.dataSource = val[0];
        } else {
          this.dataSource = val;
        }
      },
      immediate: true,
    },
  },
};
</script>
