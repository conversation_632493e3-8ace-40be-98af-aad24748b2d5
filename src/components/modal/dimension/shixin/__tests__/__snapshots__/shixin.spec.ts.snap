// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Shixin > should render correctly with viewData 1`] = `
<div>
  <div class="container">
    <table class="table">
      <tbody>
        <tr>
          <td width="23%" class="tb">案号</td>
          <td><a href="/embed/courtCaseDetail?caseId=case123&amp;title=北京市第一中级人民法院(2023)京01民初123号" target="_blank">(2023)京01民初123号</a></td>
          <td width="23%" class="tb">执行依据文号</td>
          <td>(2023)京01执123号</td>
        </tr>
        <tr>
          <td width="23%" class="tb">失信被执行人</td>
          <td><span><anonymous-stub to="" href="/embed/companyDetail?keyNo=key1&amp;title=失信企业1" display="inline"><span class="name">失信企业1</span></anonymous-stub><span>，</span>
            <anonymous-stub to="" href="/embed/companyDetail?keyNo=key2&amp;title=失信企业2" display="inline"><span class="name">失信企业2</span></anonymous-stub>
            </span>
            <div><span class="text-warning" style="cursor: pointer;">财产线索 5 &gt; </span></div>
          </td>
          <td width="23%" class="tb">证件号码/组织机构代码</td>
          <td>91110000123456789X</td>
        </tr>
        <tr>
          <td width="23%" class="tb">疑似申请执行人<anonymous-stub trigger="hover" placement="bottom" transitionname="zoom-big-fast" aftervisiblechange="[Function]" overlay="[object Object]" overlaystyle="[object Object]" prefixcls="ant-tooltip" mouseenterdelay="0.1" mouseleavedelay="0.1" align="[object Object]" builtinplacements="[object Object]">
              <localereceiver-stub componentname="Icon" class="icon ml-1" style="color: #d6d6d6;"><i aria-label="undefined: info-circle" class="anticon anticon-info-circle">
                  <antdicon-stub type="info-circle-o" focusable="false" class=""></antdicon-stub>
                </i></localereceiver-stub>
            </anonymous-stub>
          </td>
          <td><span><anonymous-stub to="" href="/embed/companyDetail?keyNo=sqr1&amp;title=申请执行人1" display="inline"><span class="name">申请执行人1</span></anonymous-stub></span></td>
          <td width="23%" class="tb">涉案金额(元)<anonymous-stub trigger="hover" placement="bottom" transitionname="zoom-big-fast" aftervisiblechange="[Function]" overlay="[object Object]" overlaystyle="[object Object]" prefixcls="ant-tooltip" mouseenterdelay="0.1" mouseleavedelay="0.1" align="[object Object]" builtinplacements="[object Object]">
              <localereceiver-stub componentname="Icon" class="icon ml-1" style="color: #d6d6d6;"><i aria-label="undefined: info-circle" class="anticon anticon-info-circle">
                  <antdicon-stub type="info-circle-o" focusable="false" class=""></antdicon-stub>
                </i></localereceiver-stub>
            </anonymous-stub>
          </td>
          <td>1,000,000.00</td>
        </tr>
        <tr>
          <td width="23%" class="tb">执行法院</td>
          <td>北京市第一中级人民法院</td>
          <td width="23%" class="tb">做出执行依据单位</td>
          <td>北京市第一中级人民法院</td>
        </tr>
        <tr>
          <td width="23%" class="tb">被执行人的履行情况</td>
          <td>未履行</td>
          <td width="23%" class="tb">立案日期</td>
          <td>2023-01-15</td>
        </tr>
        <tr>
          <td width="23%" class="tb">发布日期</td>
          <td>2023-02-01</td>
          <td width="23%" class="tb">失信被执行人行为具体情形</td>
          <td width="23%">有履行能力而拒不履行生效法律文书确定义务</td>
        </tr>
        <tr>
          <td width="23%" class="tb">生效法律文书确定的义务</td>
          <td colspan="3">
            <div>
              <p>支付货款100万元及利息</p>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <!---->
</div>
`;
