import { shallowMount } from '@vue/test-utils';
import Shixin from '..';

describe('Shixin', () => {
  let wrapper;

  const mockViewData = {
    NameKeyNoCollection: [
      { KeyNo: 'key1', Name: '失信企业1' },
      { KeyNo: 'key2', Name: '失信企业2' },
    ],
    Name: '失信企业名称',
    Orgno: '91110000123456789X',
    SqrInfo: [{ KeyNo: 'sqr1', Name: '申请执行人1' }],
    Amount: '1000000',
    Executegov: '北京市第一中级人民法院',
    Executestatus: '未履行',
    Province: '北京市',
    Executeno: '(2023)京01执123号',
    Liandate: '2023-01-15',
    CaseSearchId: ['case123'],
    Anno: '(2023)京01民初123号',
    Executeunite: '北京市第一中级人民法院',
    Publicdate: '2023-02-01',
    Actionremark: '有履行能力而拒不履行生效法律文书确定义务',
    Yiwu: '<p>支付货款100万元及利息</p>',
  };

  const mockDialogProps = {
    ccxsCount: 5,
    keyNo: 'test-key-no',
  };

  beforeEach(() => {
    wrapper = shallowMount(Shixin, {
      propsData: {
        viewData: mockViewData,
        dialogProps: mockDialogProps,
      },
    });
  });

  it('should render correctly with viewData', () => {
    expect(wrapper.html()).matchSnapshot();
  });

  it('should not render when viewData is null', () => {
    const emptyWrapper = shallowMount(Shixin, {
      propsData: {
        viewData: null,
      },
    });
    expect(emptyWrapper.find('div').exists()).toBe(false);
  });

  it('should render name when NameKeyNoCollection does not exist', () => {
    const wrapperWithoutCollection = shallowMount(Shixin, {
      propsData: {
        viewData: {
          ...mockViewData,
          NameKeyNoCollection: null,
        },
        dialogProps: mockDialogProps,
      },
    });

    expect(wrapperWithoutCollection.text()).toContain('失信企业名称');
  });

  it('should not render QCcxs when neither ccxsCount nor keyNo exists', () => {
    const wrapperWithoutCcxs = shallowMount(Shixin, {
      propsData: {
        viewData: mockViewData,
        dialogProps: {},
      },
    });

    expect(wrapperWithoutCcxs.html()).not.toContain('财产线索');
  });

  it('should handle empty SqrInfo gracefully', () => {
    const wrapperWithoutSqr = shallowMount(Shixin, {
      propsData: {
        viewData: {
          ...mockViewData,
          SqrInfo: [],
        },
        dialogProps: mockDialogProps,
      },
    });

    expect(wrapperWithoutSqr.text()).toContain('-');
  });
});
