import { defineComponent, PropType } from 'vue';
import { Icon, Tooltip } from 'ant-design-vue';
import { isNil } from 'lodash';

import QPlainTable from '@/components/global/q-plain-table';
import QEntityLink from '@/components/global/q-entity-link';
import QCcxs from '@/components/global/q-ccxs';
import QRelateCases from '@/components/global/q-relate-cases';

import { numberToHuman } from '@/utils/number-formatter';
import { dateFormat } from '@/utils/format';

interface ViewData {
  NameKeyNoCollection?: Array<{ KeyNo: string; Name: string }>;
  Name?: string;
  Orgno?: string;
  SqrInfo?: Array<{ KeyNo: string; Name: string }>;
  Amount?: string | number;
  Executegov?: string;
  Executestatus?: string;
  Province?: string;
  Executeno?: string;
  Liandate?: string;
  CaseSearchId?: string[];
  Anno?: string;
  Executeunite?: string;
  Publicdate?: string;
  Actionremark?: string;
  Yiwu?: string;
}

interface DialogProps {
  ccxsCount?: string | number;
  keyNo?: string;
}

/**
 * 失信被执行人
 */
const Shixin = defineComponent({
  name: 'Shixin',
  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
    dialogProps: {
      type: Object as PropType<DialogProps>,
      default: () => ({}),
    },
  },
  setup() {
    const formatAmount = (val: string | number | undefined): string => {
      if (isNil(val) || val === '') {
        return '-';
      }
      return numberToHuman(Number(val), { precision: 2 });
    };

    return {
      formatAmount,
    };
  },
  render() {
    const { viewData, dialogProps } = this;

    if (!viewData) {
      return null;
    }

    return (
      <div>
        <QPlainTable>
          <tbody>
            <tr>
              <td width="23%" class="tb">
                案号
              </td>
              <td>
                {viewData.CaseSearchId && viewData.CaseSearchId.length !== 0 ? (
                  <a
                    href={`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.Executegov}${viewData.Anno}`}
                    target="_blank"
                  >
                    {viewData.Anno || '-'}
                  </a>
                ) : (
                  viewData.Anno || '-'
                )}
              </td>
              <td width="23%" class="tb">
                执行依据文号
              </td>
              <td>{viewData.Executeno || '-'}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                失信被执行人
              </td>
              <td>
                {viewData.NameKeyNoCollection ? <QEntityLink coyArr={viewData.NameKeyNoCollection} /> : viewData.Name || '-'}
                {(dialogProps.ccxsCount || dialogProps.keyNo) && <QCcxs ccxsCount={dialogProps.ccxsCount} keyNo={dialogProps.keyNo} />}
              </td>
              <td width="23%" class="tb">
                证件号码/组织机构代码
              </td>
              <td>{viewData.Orgno || '-'}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                疑似申请执行人
                <Tooltip placement="bottom">
                  <div slot="title">该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。</div>
                  <Icon style={{ color: '#d6d6d6' }} class="icon ml-1" type="info-circle" />
                </Tooltip>
              </td>
              <td>{viewData.SqrInfo && viewData.SqrInfo.length ? <QEntityLink coyArr={viewData.SqrInfo} /> : '-'}</td>
              <td width="23%" class="tb">
                涉案金额(元)
                <Tooltip placement="bottom">
                  <div slot="title">该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。</div>
                  <Icon style={{ color: '#d6d6d6' }} class="icon ml-1" type="info-circle" />
                </Tooltip>
              </td>
              <td>{this.formatAmount(viewData.Amount)}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                执行法院
              </td>
              <td>{viewData.Executegov || '-'}</td>
              <td width="23%" class="tb">
                做出执行依据单位
              </td>
              <td>{viewData.Executeunite || '-'}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                被执行人的履行情况
              </td>
              <td>{viewData.Executestatus || '-'}</td>
              <td width="23%" class="tb">
                立案日期
              </td>
              <td>{dateFormat(viewData.Liandate)}</td>
            </tr>
            <tr>
              <td width="23%" class="tb">
                发布日期
              </td>
              <td>{dateFormat(viewData.Publicdate)}</td>
              <td class="tb" width="23%">
                失信被执行人行为具体情形
              </td>
              <td width="23%">{viewData.Actionremark || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="23%">
                生效法律文书确定的义务
              </td>
              <td colspan="3">{viewData.Yiwu ? <div domPropsInnerHTML={viewData.Yiwu} /> : '-'}</td>
            </tr>
          </tbody>
        </QPlainTable>
        <QRelateCases searchParams={viewData} />
      </div>
    );
  },
});

export default Shixin;
