import { Button } from 'ant-design-vue';
import { defineComponent } from 'vue';

import * as companyUtil from '@/utils/firm';

import styles from './company-contact.module.less';

const CompanyContact = defineComponent({
  name: 'CompanyContact',

  props: {
    dialogProps: {
      type: Object,
    },
  },

  data() {
    return {
      title: '',
      vipLimit: false,
      sameList: [],
      actingList: {},
      inType: '',
      KeyNo: '',
      vtList: [],
    };
  },

  computed: {
    info() {
      const { dialogProps } = this as any;
      return dialogProps.info;
    },
    type() {
      const { dialogProps } = this as any;
      return dialogProps.type;
    },
    list() {
      const { dialogProps } = this as any;
      const result = dialogProps?.list ?? [];
      return result.map((v) => {
        const vo = v;
        if (!vo.sourse && vo.s) {
          if (`${vo.s}` === '4') {
            vo.sourse = '互联网';
          } else if (`${vo.s}` === '5') {
            vo.sourse = '互联网';
          } else if (`${vo.s}` === '6') {
            vo.sourse = '互联网';
          } else if (vo.s) {
            vo.sourse = vo.s.indexOf('年报') > 0 ? vo.s : `${vo.s}年报`;
          } else {
            vo.sourse = '其他';
          }
        }
        return vo;
      });
    },
  },

  methods: {
    genContact() {
      const that = this as any;
      let contentNode: any = [];
      const { type } = that;
      if (type === 'tel') {
        contentNode = that.genTel();
      } else if (type === 'email') {
        contentNode.push(that.genEmail());
      } else if (type === 'address') {
        contentNode = that.genAddress();
      }
      return contentNode;
    },
    genTel() {
      const that = this as any;
      const { list, actingList, sameList, vtList, title } = that;
      return (
        <table>
          {list.map((vo, index) => {
            return (
              <tr key={`tel_${index}`}>
                {title === '更多号码-批量查询' ? (
                  <td>{vo}</td>
                ) : (
                  [
                    <td>
                      {vo.t && vtList ? <q-phone-status phone={vo.t} vtList={vtList}></q-phone-status> : null} {vo.t}
                      {actingList[vo.t] ? <span>(疑似代记账)</span> : null}
                      {sameList[vo.t] ? (
                        <a class="q-m-l-sm" onClick={() => that.onShowSameList('疑似同电话企业', sameList[vo.t])}>
                          同电话企业 {sameList[vo.t].count}
                        </a>
                      ) : null}
                    </td>,
                    <td class="q-text-right q-text-gray">{vo.sourse}</td>,
                  ]
                )}
              </tr>
            );
          })}
        </table>
      );
    },
    genEmail() {
      const that = this as any;
      const { list } = that;
      return (
        <table>
          {list.map((vo, index) => {
            return (
              <tr key={`email_${index}`}>
                <td>
                  <a href={`mailto:${vo.e}`}>{vo.e}</a>
                </td>
                <td class="q-text-right q-text-gray">{vo.sourse}</td>
              </tr>
            );
          })}
        </table>
      );
    },
    genAddress() {
      const that = this as any;
      const { list } = that;
      return (
        <table>
          {list.map((vo, index) => {
            return (
              <tr key={`address_${index}`}>
                <td>{vo.address}</td>
                <td class="q-text-right q-text-gray">{vo.year ? <a onClick={that.onJumpReportYear}>{vo.year}年报</a> : <span>-</span>}</td>
              </tr>
            );
          })}
        </table>
      );
    },
    onShowSameList(title, field) {
      const that = this as any;
      if (field.value && that.actingList[field.value]) {
        // eslint-disable-next-line no-param-reassign
        field.isActing = true;
      }
      that.$modal.showDimension('companySamePhone', {
        data: {
          title,
          ...field,
        },
      });
    },
    onJumpReportYear() {
      const that = this as any;
      that.$emit('visibleChange', false);
    },
    onCopy() {
      const that = this as any;
      const getVtDest = (tel) => {
        const findList: any = that.vtList.find((item: any) => tel === item.k);
        const vtV = findList && findList.v ? findList.v : null;
        if (vtV === '1') {
          return '正常';
        }
        if (vtV === '3') {
          return '未知';
        }
        if (['0', '2', '4', '5'].includes(vtV)) {
          return '不可用';
        }
        return '未知';
      };
      that.$refs.copyPart.onCopy(
        this.list
          .map((vo) => {
            return `${getVtDest(vo.t)}\t${vo.t}\t${vo.sourse || ''}`;
          })
          .join('\n')
      );
    },
  },

  mounted() {
    const that = this as any;
    const { type } = that;
    if (this.inType === 'search') {
      const params = {
        keyNo: this.KeyNo,
      };
      that.$service.company.getDetail(params).then((data) => {
        this.sameList = companyUtil.setSameList(data);
        this.actingList = companyUtil.setActingList(data);
      });
    } else if (type === 'tel') {
      this.sameList = companyUtil.getSameList(that.info);
      this.actingList = companyUtil.getActingList(that.info);
      this.vtList = that.info.VTList ?? [];
    }
  },

  render() {
    const that = this as any;
    const { type, title } = that;
    return (
      <div class={styles.container}>
        <div style={{ maxHeight: '300px', overflow: 'auto' }}>{that.genContact()}</div>

        {/* 复制手机号 */}
        {type === 'tel' && title !== '更多号码-批量查询' ? (
          <div class={styles.btnWrap}>
            <q-copy ref="copyPart">
              <Button type="primary" onClick={that.onCopy} slot="btn">
                全部复制
              </Button>
            </q-copy>
          </div>
        ) : null}
      </div>
    );
  },
});

export default CompanyContact;
