<template>
  <div>
    <table class="ntable" v-if="viewData">
      <tr>
        <td width="23%" class="tb">列入日期：</td>
        <td width="27%">
          {{ viewData.Date || '-' }}
        </td>
        <td width="23%" class="tb">作出决定机关：</td>
        <td width="27%">
          {{ viewData.Agency || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">列入原因：</td>
        <td colspan="3">
          {{ viewData.Reason || '-' }}
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>
<style lang="less" scoped src="../style.less"></style>
