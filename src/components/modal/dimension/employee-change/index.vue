<template>
  <q-plain-table>
    <tbody>
      <tr>
        <td width="23%" class="tb">变更企业</td>
        <td width="27%">
          <q-entity-link :coy-obj="{ Name: viewData.Name, KeyNo: viewData.KeyNo }"></q-entity-link>
        </td>
        <template>
          <td width="23%" class="tb">发现变更日期</td>
          <td width="27%">{{ viewData.ChangeDate | dateformat('YYYY-MM-DD') }}</td>
        </template>
      </tr>
      <tr v-if="viewData.ChangeExtend.D && viewData.ChangeExtend.D.length">
        <td class="tb" width="23%">职务调整</td>
        <td colspan="3">
          <template v-for="(sItem, sIndex) in viewData.ChangeExtend.D">
            <br v-if="sIndex !== 0" :key="sIndex + 'br'" />
            <q-entity-link :key="sIndex + '_company'" :coy-obj="{ Name: sItem.A, KeyNo: sItem.K }"> </q-entity-link>
            ，
            <span :key="sIndex + '_job'" v-if="sItem.B">从 {{ sItem.B }} 调整为 {{ sItem.C }}</span>
          </template>
        </td>
      </tr>
      <tr v-if="viewData.ChangeExtend.E && viewData.ChangeExtend.E.length">
        <td class="tb" width="23%">退出</td>
        <td colspan="3">
          <template v-for="(sItem, sIndex) in viewData.ChangeExtend.E">
            <q-entity-link :key="sIndex + '_company'" :coy-obj="{ Name: sItem.A, KeyNo: sItem.K }"> </q-entity-link>
            <span :key="sIndex + '_job'" v-if="sItem.B">，退出前职务：{{ sItem.B }} <br /></span>
          </template>
        </td>
      </tr>
      <tr v-if="viewData.ChangeExtend.F && viewData.ChangeExtend.F.length">
        <td class="tb" width="23%">新增</td>
        <td colspan="3">
          <template v-for="(sItem, sIndex) in viewData.ChangeExtend.F">
            <q-entity-link :key="sIndex + '_company_f'" :coy-obj="{ Name: sItem.A, KeyNo: sItem.K }"> </q-entity-link>
            <span :key="sIndex + '_job_f'" v-if="sItem.B">，职务：{{ sItem.B }} <br /></span>
            <span :key="`${sIndex}_seprator`" v-else>
              <span v-if="sIndex !== viewData.ChangeExtend.F.length - 1">，</span>
            </span>
          </template>
        </td>
      </tr>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
