<template>
  <div class="product-recall">
    <div class="product-header">
      <div class="detail-title">{{ productRecallDetail.title }}</div>
      <div class="detail-baseinfo">
        <span
          >来源：
          <a v-if="productRecallDetail.contentUrl" :href="productRecallDetail.contentUrl" target="_blank">{{
            productRecallDetail.source || '-'
          }}</a>
          <span v-else>{{ productRecallDetail.source || '-' }}</span>
        </span>
        <span>发表于 {{ productRecallDetail.publishTime | dateformat }}</span>
      </div>
    </div>
    <div class="product-content">
      <div class="detail-content" v-html="content" />
      <div class="detail-footer">
        本文新闻来源{{
          productRecallDetail.Source
        }}，版权归原作者所有，内容仅代表作者本人观点，不代表企查查的立场。如有任何疑问或需要删除，请联系
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductrecallDetail',

  props: {
    viewData: {
      type: Object,

      default() {
        return {};
      },
    },
  },

  data() {
    return {
      followInfo: [],
      content: '',
      productRecallDetail: {},
    };
  },

  watch: {
    viewData: {
      handler(val) {
        if (val) {
          this.init(val);
        }
      },

      immediate: true,
    },
  },

  methods: {
    init(val) {
      this.productRecallDetail = val;
      let content = this.productRecallDetail.content;
      content = content.replace(/<br\/>/g, '');
      content = content.replace(/\n\n/g, "<br/><div class='space'></div>");
      content = content.replace(/https:\/\/www.qcc.com\/firm_/g, 'https://i.qcc.com/embed/companyDetail?keyNo=');
      content = content.replace(/.html/g, '');
      this.content = content;
    },

    getContentLength() {
      let content = this.productRecallDetail.content;
      content = content.replace(/<style[^>]*>[^>]*<\/style>/gi, '');
      content = content.replace(/(<([^>]+)>)/gi, '').replace(/[\r\n]/g, '');
      content = content.replace(/\s+/g, '');
      return content.length;
    },
  },
};
</script>

<style lang="less" scoped>
.product-recall {
  background: #f7f7f7;
  padding: 10px 15px 15px;
  border-radius: 0 0 6px 6px;

  .product-header {
    border-radius: 4px;
    background: #fff;
    margin-bottom: 10px;
    padding: 15px;

    .detail-title {
      margin-bottom: 10px;
      color: #333;
      font-size: 22px;
      line-height: 30px;
      font-weight: bold;
    }

    .detail-baseinfo {
      > span {
        display: inline-block;
        color: #999;

        &::after {
          content: ' ';
          width: 1px;
          height: 14px;
          background-color: #eee;
          margin: 0 25px;
          position: relative;
          display: inline-block;
          left: 2px;
          top: 4px;
        }

        &:last-child {
          &::after {
            display: none;
          }
        }
      }
    }
  }

  .product-content {
    padding: 10px 15px;
    border-radius: 4px;
    background: #fff;
    margin-bottom: 10px;

    .detail-content {
      position: relative;
      margin-bottom: 20px;
      font-size: 16px;
      color: #333333;
      word-break: break-all;

      ::v-deep img {
        max-width: 100%;
      }

      .reward_qrcode_img {
        width: 200px;
      }

      ::v-deep > div {
        color: #333;
        font-size: 16px !important;
        line-height: 30px !important;

        div,
        p {
          left: unset !important;
          margin-bottom: 0;
          width: 100% !important;
          text-indent: unset !important;
          font-size: 16px !important;
          line-height: 30px !important;
        }

        p {
          img {
            display: inline-block;
            margin-top: -4px;
            margin-right: 5px !important;
            border-radius: 4px;
          }

          video {
            border-radius: 4px;
          }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 45px !important;
          font-size: 16px !important;
          line-height: 30px !important;
          color: #333333 !important;
          font-weight: bold !important;
        }

        a {
          font-size: 16px !important;
          color: #128bed !important;
        }
      }
    }

    .detail-footer {
      background: #f6f6f6;
      padding: 10px;
      color: #999;
    }
  }
}
</style>
