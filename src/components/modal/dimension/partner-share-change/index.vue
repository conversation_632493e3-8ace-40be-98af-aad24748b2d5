<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" style="width: 23%">变更企业：</td>
        <td style="width: 27%">
          <q-entity-link
            v-if="viewData.ChangeObject"
            :coy-obj="{
              KeyNo: viewData.ChangeObject.KeyNo,
              Name: viewData.ChangeObject.Name,
            }"
          />
          <span v-else>-</span>
        </td>
        <td class="tb" style="width: 23%">更新时间<q-glossary-info info-id="241" />：</td>
        <td style="width: 27%">
          {{ viewData.ChangeDate | dateformat || '-' }}
        </td>
      </tr>
      <tr>
        <td class="tb">变更前：</td>
        <td>
          {{ viewData.BeforeContent || '-' }}
        </td>
        <td class="tb">变更后：</td>
        <td>
          {{ viewData.AfterContent || '-' }}
        </td>
      </tr>
      <tr v-if="viewData.RelateChange">
        <td class="tb">关联变更：</td>
        <td colspan="3">
          {{ viewData.RelateChange }}
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
// eslint-disable-next-line vue/require-name-property
export default {
  props: {
    viewData: {
      type: Object,

      default() {
        return {};
      },
    },
  },
};
</script>
