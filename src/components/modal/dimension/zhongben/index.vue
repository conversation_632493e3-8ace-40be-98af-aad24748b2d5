<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <td width="23%" class="tb">被执行人姓名/名称</td>
          <td v-if="viewData.OwnerInfo && viewData.OwnerInfo.KeyNo">
            <q-entity-link :coy-obj="viewData.OwnerInfo"></q-entity-link>
            <q-ccxs :ccxs-count="dialogProps.ccxsCount" :key-no="dialogProps.keyNo"></q-ccxs>
          </td>
          <td v-else-if="viewData.OwnerInfo">
            {{ viewData.OwnerInfo.Name || '-' }}
          </td>
          <td width="23%" class="tb">证件号码/组织机构代码</td>
          <td>{{ viewData.CerNo || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">案号</td>
          <td colspan="3" v-if="viewData.CaseSearchId && viewData.CaseSearchId.length !== 0">
            <q-link :to="`/caseDetail/${viewData.CaseSearchId[0]}`">{{ viewData.CaseNo || '-' }}</q-link>
          </td>
          <td colspan="3" v-else>{{ viewData.CaseNo || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">执行法院</td>
          <td colspan="3">{{ viewData.Court || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">立案时间</td>
          <td v-if="viewData.JudgeDate">{{ viewData.JudgeDate | dateformat }}</td>
          <td v-else>-</td>
          <td width="23%" class="tb">终本日期</td>
          <td v-if="viewData.EndDate">{{ viewData.EndDate | dateformat }}</td>
          <td v-else>-</td>
        </tr>
        <tr>
          <td width="23%" class="tb">执行标的(元)</td>
          <td>{{ viewData.BiaoDi || '-' }}</td>
          <td width="23%" class="tb">未履行金额(元)</td>
          <td>{{ viewData.UnFinishedAmt || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
