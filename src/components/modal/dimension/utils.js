import _ from 'lodash';

const formatTitle = (that, subType) => {
  let title;
  /* eslint-disable */
  switch (subType) {
    case '1':
    case '2':
      title = that.viewDetail.Name
      break
    case '5':
      title = that.viewDetail.case_no || that.viewDetail.case_reason
      break
    case '3':
      title = that.viewDetail.Ano || that.viewDetail.CaseReason
      break
    case '10':
    case '33':
      title = that.viewDetail.CaseNo || that.viewDetail.Reason
      break
    case '11':
      title = that.viewDetail.AnNo || that.viewDetail.Title
      break
    case '16':
      title = that.viewDetail.MortgagorName?.Name
      break
    case '18':
      title = that.viewDetail.Holders?.map(i => i.Name).join('，')
      break
    case '19':
      title = that.viewDetail.PjNo
      break
    case '21':
      title = that.viewDetail.TaxpayerName + (that.viewDetail.OperInfo?.OperName && `，${that.viewDetail.OperInfo.OperName}`)
      break
    case '23':
      title = that.viewDetail.OwnerInfo?.Name
      break
    case '26':
      title = that.viewDetail.CaseNo || that.viewDetail.CaseType
      break
    case '33':
      title = that.viewDetail.ExecutionNoticeNum || that.viewDetail.ExecutedBy
      break
    case '34':
      title = that.viewDetail.ImporterName
      break
    case '37':
    case '141':
      title = that.viewDetail.CompanyName
      break
    case '91':
      title = that.viewDetail.ExecutionNoticeNum || that.viewDetail.ExecutedBy
      break
    case '151':
      title = that.viewDetail.RegisterNo || that.viewDetail.RelatedCompanyInfo?.Name
      break
    case '171':
      if (that.viewDetail.RegisterNo) { //登记编号
        title = that.viewDetail.RegisterNo
      } else if (that.viewData.PledgorList?.length) { //出质人
        title = that.viewData.PledgorList.map(i => i.Name).join('，')
      } else { //出质人
        title = that.viewDetail.PledgorInfo?.Name
      }
      break
    case '251':
      title = that.viewDetail.Detail?.LiqBAInfo?.CompanyName
      break
    case '10001':
      title = that.viewDetail.CaseNo || that.viewDetail.NameAndKeyno?.map(i => i.Name || i.name).join('，')
      break
    case '10002':
      title = that.viewDetail?.Guarantee?.length ? that.viewDetail.Guarantee?.map(i => i.Name).join('，') : ''
      break
    case '10003':
      title = JSON.parse(that.viewDetail.Details).find(i => ['企业名称', '法定代表人'].includes(i.dataKey)).dataValue
      break
    default:
      title = ''
  }
  /* eslint-enable */

  return title;
};

const handleItemType = (members, type) => {
  if (!members?.length) {
    return [];
  }
  return members.map((item) => ({
    keyNo: item.K,
    name: item.A,
    position: item.B,
    type,
  }));
};

const hasFullPositions = (members) => members?.length && members.every((item) => item.position);

const getPositions = (members) => {
  const positions = members.map((item) => item.position);
  return [...new Set(positions)].sort();
};

const getGroupMembers = (members) => {
  const currMembers = _.groupBy(members, 'position');

  return Object.keys(currMembers).map((key) => ({
    key,
    reduce: currMembers[key].filter((item) => item.type === 'reduce'),
    add: currMembers[key].filter((item) => item.type === 'add'),
  }));
};

const formatEmployeeData = (employeeData) => {
  const reduceMember = handleItemType(employeeData.E, 'reduce');
  const addMember = handleItemType(employeeData.F, 'add');
  let isSamePositionChange = false;
  let reduceAndAddMember = [];

  // 退出和新增同时存在 并且 职位去重后完全一样的情况下，特殊展示
  if (hasFullPositions(reduceMember) && hasFullPositions(addMember)) {
    const reducePositons = getPositions(reduceMember);
    const addPositons = getPositions(addMember);

    isSamePositionChange = reducePositons.join('') === addPositons.join('');

    if (isSamePositionChange) {
      // 按照职位聚合
      reduceAndAddMember = getGroupMembers([...reduceMember, ...addMember]);
    }
  }

  return {
    reduceMember,
    addMember,
    isSamePositionChange,
    reduceAndAddMember,
  };
};

export default {
  formatTitle,
  formatEmployeeData,
};
