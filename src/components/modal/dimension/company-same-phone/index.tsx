import { Tooltip } from 'ant-design-vue';
import { defineComponent } from 'vue';

import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

import styles from './company-same-phone.module.less';

const listMixin = createMixin(['current'], {
  rowKey: 'KeyNo',
  pageSizeOptions: ['5', '10', '20'],
  pagination: {
    current: 1,
    pageSize: 5,
    total: 0,
  },
});

const CompanySamePhone = defineComponent({
  name: 'CompanySamePhone',

  mixins: [dimensionMixin, listMixin],

  data() {
    return {
      title: '',
      value: '',
      isActing: false, // 是否是疑似代记账号码

      columns: [
        {
          title: '企业名称',
          width: '',
          dataIndex: 'name',
          customRender: (text, record) => {
            return (
              <Tooltip title={record.Name}>
                <q-entity-link coy-obj={record} />
              </Tooltip>
            );
          },
        },
        {
          title: '法定代表人',
          width: '180px',
          dataIndex: 'oper',
          customRender: (_, record) => {
            if (record.Oper) {
              return (
                <Tooltip title={record.OperName}>
                  <q-entity-link coy-obj={{ Name: record.OperName, KeyNo: record.Oper.k, Org: record.Oper.o }} />
                </Tooltip>
              );
            }
            return <span>{record.OperName}</span>;
          },
        },
        {
          title: '注册资本	',
          width: '170px',
          dataIndex: 'RegistCapi',
        },
        {
          title: '成立日期	',
          width: '110px',
          dateFormat: true,
          scopedSlots: {
            customRender: 'date',
          },
          dataIndex: 'StartDate',
        },
        {
          title: '登记状态',
          width: '90px',
          statusTd: true,
          dataIndex: 'ShortStatus',
          customRender: (text) => (
            <q-company-status status={text} ghost>
              {text}
            </q-company-status>
          ),
        },
      ],
      downloadTitle: '',
    };
  },

  props: {
    detailParams: {
      type: Object,
    },
  },

  methods: {
    setDownload() {
      if (this.title === '疑似同地址企业') {
        this.downloadTitle = `同地址企业（${this.value.substr(0, 10)}）`;
      } else {
        this.downloadTitle = `同电话企业（${this.value}）`;
      }
    },
    fetchDataSource({ pagination }) {
      const that = this as any;
      const dataSource = this.title === '疑似同地址企业' ? 'Address' : 'Tel';
      const initState: any = {
        ...pagination,
        searchKey: that.detailParams.data.key,
        searchType: '0,1,3,4,5,10,11,12,20',
        dataSource,
      };
      return that.$service.search.getSameContacts(initState).then((res) => {
        return {
          ...res,
          Result: res?.Result.map((item) => {
            if (item.OperInfo) {
              // eslint-disable-next-line no-param-reassign
              item.Oper = JSON.parse(item.OperInfo);
            }
            return item;
          }),
        };
      });
    },
  },

  mounted() {
    const that = this as any;
    const data = that.detailParams.data;
    this.title = data.title;
    this.value = data.value;
    this.isActing = data.isActing;
    (this as any).setDownload();
  },

  render() {
    const that = this as any;
    const { value, title, mListGetTableData, columns } = that;
    return (
      <div class={styles.container}>
        <div class={styles.titleWrap}>
          <span class="text">
            {that.isActing ? (
              <span class="text">
                {value} <span> (疑似代记账)</span>{' '}
              </span>
            ) : (
              <div class={styles.downloadWrap}>
                <span> {value} </span>
              </div>
            )}
            {/* <div class={styles.downloadWrap}>
              <span> {value} </span>
            </div> */}
          </span>
        </div>
        <q-rich-table {...mListGetTableData('current')} columns={columns}>
          <template slot="footnote">
            {title}
            是由企查查基于公开信息利用大数据分析后的结果，仅供用户参考，并不代表企查查的任何明示、暗示之观点或保证。
          </template>
        </q-rich-table>
      </div>
    );
  },
});

export default CompanySamePhone;
