import { computed, defineComponent } from 'vue';

import { numberToHuman } from '@/utils/number-formatter';
import { dateFormat } from '@/utils/format';
import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import QRichTable from '@/components/global/q-rich-table';

/**
 * 国央企采购黑名单
 */
const EndExecutionCase = defineComponent({
  name: 'EndExecutionCase',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup() {
    const numberFormat = (value: string | number) => {
      let n = value;
      if (typeof n === 'string') {
        n = Number(n);
      }
      const precision = n > 0 && n < 0.01 ? 4 : 2;
      return n !== undefined ? numberToHuman(n, { precision }) : '-';
    };

    const tableColumns = computed(() => [
      {
        title: '案件名称',
        width: 250,
        scopedSlots: { customRender: 'caseName' },
      },
      {
        title: '案件类型',
        width: 90,
        dataIndex: 'CaseTypeArray',
        scopedSlots: { customRender: 'listToString' },
      },
      { title: '案由', dataIndex: 'CaseReason' },
      {
        title: '案号',
        width: 195,
        dataIndex: 'AnNoList',
        scopedSlots: { customRender: 'listToString' },
      },
      {
        title: '法院',
        dataIndex: 'CourtList',
        scopedSlots: { customRender: 'listToString' },
      },
      { title: '最新审理程序', dataIndex: 'LatestTrialRound' },
    ]);
    return {
      numberFormat,
      tableColumns,
    };
  },
  render() {
    const caseSearchId = Array.isArray(this.viewData.CaseSearchId) ? this.viewData.CaseSearchId[0] : this.viewData.CaseSearchId;

    return (
      <div>
        <QPlainTable>
          <tbody>
            <tr>
              <th>被执行人姓名/名称</th>
              <td>
                <QEntityLink coyObj={this.viewData.OwnerInfo} />
              </td>
              <th>证件号/组织机构代码</th>
              <td>{this.viewData.CerNo || '-'}</td>
            </tr>
            <tr>
              <th>案号</th>
              <td colspan={3}>
                {this.viewData.CaseNo ? (
                  <a
                    target="_blank"
                    href={caseSearchId ? `/embed/courtCaseDetail?caseId=${caseSearchId}&title=${this.viewData.CaseNo}` : undefined}
                  >
                    {this.viewData.CaseNo}
                  </a>
                ) : (
                  '-'
                )}
              </td>
            </tr>
            <tr>
              <th>执行法院</th>
              <td colspan={3}>{this.viewData.Court || '-'}</td>
            </tr>
            <tr>
              <th>立案时间</th>
              <td>{dateFormat(this.viewData.JudgeDate)}</td>
              <th>终本日期</th>
              <td>{dateFormat(this.viewData.EndDate)}</td>
            </tr>
            <tr>
              <th>执行标的（元）</th>
              <td>{this.numberFormat(this.viewData.BiaoDi)}</td>
              <th>未履行金额（元）</th>
              <td>{this.numberFormat(this.viewData.UnFinishedAmt)}</td>
            </tr>
          </tbody>
        </QPlainTable>

        {Array.isArray(this.viewData.relatedCases) ? (
          <div>
            <div style="padding: 20px 0 10px">关联司法案件</div>
            <QRichTable
              dataSource={this.viewData.relatedCases}
              columns={this.tableColumns}
              scopedSlots={{
                caseName: (text, record) => {
                  if (!record.Id) {
                    return record.CaseName || '-';
                  }
                  return (
                    <a target="_blank" href={`/embed/courtCaseDetail?caseId=${record.Id}&title=${record.CaseName}`}>
                      {record.CaseName || '-'}
                    </a>
                  );
                },
                listToString: (list) => {
                  if (!Array.isArray(list) || !list) {
                    return '-';
                  }
                  return list.map((item, index) => {
                    return <div key={index}>{item ?? '-'}</div>;
                  });
                },
              }}
            />
          </div>
        ) : null}
      </div>
    );
  },
});

export default EndExecutionCase;
