.tdPath {
  line-height: 28px;

  .arrow {
    display: inline-block;
    width: 84px;
    height: 26px;
    background: url('./images/line_arrow.png') no-repeat right bottom;
    background-size: 75px 8px;
    padding-bottom: 10px;
    font-size: 12px;
    color: #128bed;
    text-align: center;
    position: relative;
    top: -9px;
    margin-right: 9px;
  }

  .arrow-no-text {
    top: 0;
    height: 10px;
  }
}

.nstatus {
  font-weight: normal;
  display: inline-block;
  line-height: 16px;
  font-size: 14px;
  padding: 2px 6px;
  border-radius: 2px;
  border-style: solid;
  border-width: 1px;
  background: #fff;
  user-select: none;
}

.nstatus.text-primary {
  background: #f8fbfe;
  border-color: #9ccff7;
  color: #128bed;
}

.nstatus.text-success {
  background: #f7fcfa;
  border-color: #94dcbe;
  color: #00ad65;
}

.nstatus.text-warning {
  background: #fffbf7;
  border-color: #ffce94;
  color: #ff8901;
}

.nstatus.text-danger {
  background: #fffafa;
  border-color: #ffbcbc;
  color: #f04040;
}

.nstatus.text-gray {
  background: #f9f9f9;
  border-color: #d8d8d8;
  color: #999;
}
