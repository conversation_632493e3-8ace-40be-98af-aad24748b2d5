<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">变更企业</td>
        <td width="30%" v-if="viewData.Name">
          <q-entity-link :coy-arr="[{ KeyNo: viewData.KeyNo, Name: viewData.Name }]"></q-entity-link>
        </td>
        <td v-else>-</td>
        <td class="tb" width="20%">发现变更日期</td>
        <td width="30%">{{ viewData.ChangeDate }}</td>
      </tr>
      <tr v-if="viewData.IsLess !== 0 || (viewData.RiseObject && viewData.RiseObject.length)">
        <template v-if="viewData.IsLess !== 0 && !(viewData.RiseObject && viewData.RiseObject.length)">
          <td class="tb" width="20%">股份下降</td>
          <td colspan="3">
            <span v-for="(item, k) in viewData.ChangeObject" :key="k">
              <q-entity-link :coy-arr="[item]"></q-entity-link>
              持股比例从
              <span v-if="item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%'">
                {{ item.StockPercent }} 下降到
              </span>
              <span v-else> 未知下降到 </span>
              <span v-if="item.LessStockPercent && item.LessStockPercent !== '0%' && item.LessStockPercent !== '0.00%'">
                {{ item.LessStockPercent }}<br />
              </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
        </template>
        <template v-else-if="viewData.IsLess === 0 && viewData.RiseObject && viewData.RiseObject.length">
          <td class="tb" width="20%">股份上升</td>
          <td colspan="3">
            <span v-for="(item, k) in viewData.RiseObject" :key="k">
              <q-entity-link :coy-arr="[item]"></q-entity-link>
              持股比例从
              <span v-if="item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%'">
                {{ item.StockPercent }} 上升到
              </span>
              <span v-else> 未知上升到 </span>
              <span v-if="item.LessStockPercent && item.LessStockPercent !== '0%' && item.LessStockPercent !== '0.00%'">
                {{ item.LessStockPercent }}<br />
              </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
        </template>
        <template v-else>
          <td class="tb" width="20%">股份下降</td>
          <td width="30%">
            <span v-for="(item, k) in viewData.ChangeObject" :key="k">
              <q-entity-link :coy-arr="[item]"></q-entity-link>
              持股比例从
              <span v-if="item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%'">
                {{ item.StockPercent }} 下降到
              </span>
              <span v-else> 未知下降到 </span>
              <span v-if="item.LessStockPercent && item.LessStockPercent !== '0%' && item.LessStockPercent !== '0.00%'">
                {{ item.LessStockPercent }}<br />
              </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
          <td class="tb" width="20%">股份上升</td>
          <td width="30%">
            <span v-for="(item, k) in viewData.RiseObject" :key="k">
              <q-entity-link :coy-arr="[item]"></q-entity-link>
              持股比例从
              <span v-if="item.StockPercent && item.StockPercent !== '0%' && item.StockPercent !== '0.00%'">
                {{ item.StockPercent }} 上升到
              </span>
              <span v-else> 未知上升到 </span>
              <span v-if="item.LessStockPercent && item.LessStockPercent !== '0%' && item.LessStockPercent !== '0.00%'">
                {{ item.LessStockPercent }}<br />
              </span>
              <span v-else> 未知<br /> </span>
            </span>
          </td>
        </template>
      </tr>
      <tr v-if="viewData.IsExit !== 0">
        <td width="20%" class="tb">退出</td>
        <td colspan="3">
          <div v-for="(exit, eIndex) in viewData.ExitObject" :key="`${exit.KeyNo}-${eIndex}`">
            <q-entity-link :coy-arr="[exit]"></q-entity-link>
            <span v-if="exit.StockPercent">，持股 {{ exit.StockPercent }}</span>
          </div>
        </td>
      </tr>
      <tr v-if="viewData.IsAdd !== 0">
        <td width="20%" class="tb">新增</td>
        <td colspan="3">
          <div v-for="(add, aIndex) in viewData.AddObject" :key="`${add.KeyNo}-${aIndex}`">
            <q-entity-link :coy-arr="[add]"></q-entity-link>
            <span v-if="add.StockPercent">，持股 {{ add.StockPercent }}</span>
          </div>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
