export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      businessColumns: [
        {
          title: '业务分类',
          dataIndex: 'Scope',
          width: '200',
        },
        {
          title: '业务种类',
          dataIndex: 'Scope',
        },
        {
          title: '覆盖范围',
          dataIndex: 'Scope',
          width: '250',
        },
        {
          title: '是否有效',
          dataIndex: 'IsOK',
          width: '90',
        },
      ],
      authorizeColumns: [
        {
          title: '持证公司名称',
          scopedSlots: { customRender: 'ownerCompany' },
          align: 'left',
          width: 220,
        },
        {
          title: '许可证号',
          dataIndex: 'LicenseNo',
          align: 'left',
        },
        {
          title: '授权子公司',
          scopedSlots: { customRender: 'authorizeCompany' },
          align: 'left',
          width: 220,
        },
        {
          title: '授权业务及范围',
          dataIndex: 'AuthBusiness',
          ellipsis: 3,
          width: 300,
          align: 'left',
          scopedSlots: { customRender: 'shrinkContent' },
        },
      ],
    };
  },
  mounted() {
    console.log('电信详情', this.viewData);
  },
};
