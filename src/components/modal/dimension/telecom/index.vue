<template>
  <div>
    <div class="mtcaption">基本信息</div>
    <q-plain-table>
      <tbody>
        <tr>
          <th width="23%">公司名称</th>
          <td width="27%">
            <template v-if="viewData.NameAndKeyNo">
              <q-entity-link :coy-obj="{ KeyNo: viewData.NameAndKeyNo.KeyNo, Name: viewData.NameAndKeyNo.CompanyName }"></q-entity-link>
            </template>
            <template v-else>-</template>
          </td>
          <th width="23%">许可证号</th>
          <td width="27%">{{ viewData.LicenseNo || '-' }}</td>
        </tr>
      </tbody>
    </q-plain-table>
    <!-- 业务及覆盖范围 -->
    <template v-if="viewData.TelecomInfoList && viewData.TelecomInfoList.length">
      <div class="mtcaption">业务及覆盖范围</div>
      <table class="ntable business-range-table">
        <tr>
          <th class="tx">序号</th>
          <th v-for="(column, ci) in businessColumns" :key="`ntable-th_${ci}`" :width="column.width">
            {{ column.title }}
          </th>
        </tr>
        <template v-for="(item, index) in viewData.TelecomInfoList">
          <!-- 存在业务种类 -->
          <template v-if="item.TelecomSubBeans && item.TelecomSubBeans.length">
            <tr v-for="(TItem, TIndex) in item.TelecomSubBeans" :key="'dxxk' + index + TIndex">
              <td :rowspan="item.TelecomSubBeans.length" v-if="TIndex === 0">
                {{ index + 1 }}
              </td>
              <td class="left" :rowspan="item.TelecomSubBeans.length" v-if="TIndex === 0">
                <span v-html="item.BusinessClassification || '-'"></span>
              </td>
              <td class="left">
                <span v-html="TItem.BusinessKind || '-'"></span>
              </td>
              <td class="left dxxk-area">
                <span>{{ TItem.Area || '-' }}</span>
              </td>
              <td>
                <q-tag v-if="TItem.IsOk === '有效'" type="success">
                  {{ TItem.IsOk }}
                </q-tag>
                <q-tag v-else type="danger">
                  {{ TItem.IsOk }}
                </q-tag>
              </td>
            </tr>
          </template>
          <!-- 不存在业务种类 -->
          <template v-else>
            <tr :key="'dxxk' + index">
              <td>
                {{ index + 1 }}
              </td>
              <td class="left">
                <span v-html="item.BusinessClassification || '-'"></span>
              </td>
              <td class="left">
                <span>-</span>
              </td>
              <td class="left dxxk-area">
                <span>-</span>
              </td>
              <td>
                <span>-</span>
              </td>
            </tr>
          </template>
        </template>
      </table>
    </template>
    <template v-if="viewData.AnnualReport && Object.keys(viewData.AnnualReport).length">
      <div class="mtcaption">最新年报公示</div>
      <q-plain-table>
        <tbody>
          <tr>
            <th width="23%">公司名称</th>
            <td width="27%">
              <template v-if="viewData.AnnualReport.NameAndKeyNo">
                <q-entity-link
                  :coy-obj="{
                    KeyNo: viewData.AnnualReport.NameAndKeyNo.KeyNo,
                    Name: viewData.AnnualReport.NameAndKeyNo.CompanyName,
                  }"
                ></q-entity-link>
              </template>
              <template v-else>-</template>
            </td>
            <th width="23%">统一社会信用代码</th>
            <td width="27%">{{ viewData.AnnualReport.RegNo || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">法定代表人</th>
            <td width="27%">
              <template v-if="viewData.AnnualReport.OperInfo">
                <q-entity-link
                  :coy-obj="{
                    KeyNo: viewData.AnnualReport.OperInfo.OperId,
                    Name: viewData.AnnualReport.OperInfo.OperName,
                  }"
                ></q-entity-link>
              </template>
              <template v-else>-</template>
            </td>
            <th width="23%">许可证编号</th>
            <td width="27%">{{ viewData.AnnualReport.LicenseNo || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">注册地址</th>
            <td width="27%">{{ viewData.AnnualReport.Addr || '-' }}</td>
            <th width="23%">注册属地</th>
            <td width="27%">{{ viewData.AnnualReport.Province || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">注册资本</th>
            <td width="27%">{{ viewData.AnnualReport.Assets || '-' }}</td>
            <th width="23%">许可证业务种类</th>
            <td width="27%">{{ viewData.AnnualReport.Type || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">企业性质</th>
            <td width="27%">{{ viewData.AnnualReport.EntKind || '-' }}</td>
            <th width="23%">上市情况</th>
            <td width="27%">{{ viewData.AnnualReport.IpoStatus || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">股票代码</th>
            <td width="27%">{{ viewData.AnnualReport.StockCode || '-' }}</td>
            <th width="23%">客服服务电话</th>
            <td width="27%">{{ viewData.AnnualReport.Tel || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">用户投诉量</th>
            <td width="27%">{{ viewData.AnnualReport.TsNum || '-' }}</td>
            <th width="23%">用户投诉回复率</th>
            <td width="27%">{{ viewData.AnnualReport.TsAnswerPer || '-' }}</td>
          </tr>
        </tbody>
      </q-plain-table>
    </template>
    <!-- 许可证授权信息 -->
    <template v-if="viewData.AuthorizeList && viewData.AuthorizeList.length">
      <div class="mtcaption">许可证授权信息</div>
      <q-rich-table rowKey="BaseId" :columns="authorizeColumns" :data-source="viewData.AuthorizeList">
        <template slot="ownerCompany" slot-scope="item">
          <q-entity-link :coy-obj="{ Name: item.NameAndKeyNo.CompanyName, KeyNo: item.NameAndKeyNo.KeyNo }"></q-entity-link>
        </template>
        <template slot="authorizeCompany" slot-scope="item">
          <q-entity-link
            :coy-obj="{
              Name: item.AuthorizeCompanyKeyNo.CompanyName,
              KeyNo: item.AuthorizeCompanyKeyNo.KeyNo,
            }"
          ></q-entity-link>
        </template>
      </q-rich-table>
    </template>
    <template v-if="viewData.BadCount > 0">
      <div class="mtcaption">不良名单信息</div>
      <q-plain-table v-for="(item, index) in viewData.BadList" :key="`bad-item-${index}`">
        <tbody>
          <tr>
            <th width="23%">处理日期</th>
            <td width="27%">{{ item.TreatmentDate | dateformat('YYYY-MM-DD') }}</td>
            <th width="23%">列入单位</th>
            <td width="27%">{{ item.InUnit || '-' }}</td>
          </tr>
          <tr>
            <th>列入事由</th>
            <td colspan="5">{{ item.InReason || '-' }}</td>
          </tr>
        </tbody>
      </q-plain-table>
    </template>
    <template v-if="viewData.ShixinCount > 0">
      <div class="mtcaption">失信名单信息</div>
      <q-plain-table v-for="(item, index) in viewData.ShixinList" :key="`shixin-item-${index}`">
        <tbody>
          <tr>
            <th width="23%">处理日期</th>
            <td width="27%">{{ item.TreatmentDate | dateformat('YYYY-MM-DD') }}</td>
            <th width="23%">列入单位</th>
            <td width="27%">{{ item.InUnit || '-' }}</td>
          </tr>
          <tr>
            <th width="23%">主要投资者</th>
            <td width="27%">{{ item.a || '-' }}</td>
            <th width="23%">列入单位</th>
            <td width="27%">{{ item.b || '-' }}</td>
          </tr>
          <tr>
            <th>列入事由</th>
            <td colspan="5">{{ item.InReason || '-' }}</td>
          </tr>
        </tbody>
      </q-plain-table>
    </template>
    <div class="text-gray q-m-t-sm">数据来源：电信业务市场综合管理信息系统</div>
  </div>
</template>

<script src="./component.js"></script>
<style lang="less" scoped>
.mtcaption {
  margin: 16px 0 12px 0;
  &:first-child {
    margin-top: 0;
  }
}

.business-range-table {
  th {
    padding: 6px 12px;
  }

  td {
    text-align: center;
    padding: 6px 12px;
  }
  td.left {
    text-align: left;
  }
}
</style>
