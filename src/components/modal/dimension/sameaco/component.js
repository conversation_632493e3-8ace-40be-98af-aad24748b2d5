import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin(['current']);

export default {
  mixins: [dimensionMixin, listMixin],
  props: {
    detailParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  mounted() {
    console.log(this.detailParams);
  },
  computed: {
    columns() {
      return [
        { title: '决定文书/许可编号', width: '14%', dataIndex: 'DocPermissionNo' },
        { title: '决定文书/许可证名称', width: '15%', dataIndex: 'DocCertifiName' },
        { title: '有效期自', dataIndex: 'StartDate', align: 'center', width: '105px' },
        { title: '有效期至', dataIndex: 'EndDate', align: 'center', width: '105px' },
        {
          title: '许可机关',
          dataIndex: 'PermissionGov',
          width: '10%',
          align: 'center',
        },
        {
          title: '许可内容',
          dataIndex: 'PermissionContent',
          ellipsis: 4,
          scopedSlots: { customRender: 'shrinkContent' },
        },
        {
          title: '来源',
          align: 'center',
          width: 125,
          scopedSlots: { customRender: 'source' },
        },
        {
          title: '操作',
          width: '60px',
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ];
    },
  },
  methods: {
    fetchDataSource({ pagination }) {
      return this.mDimenGetList(
        {
          keyNo: this.detailParams.keyNo,
          sameId: this.detailParams.sameId,
          isValid: this.detailParams.isValid,
          ...pagination,
        },
        'acolist'
      );
    },
    showDetail(item) {
      this.$modal.showDimension('aco', { id: item.Id });
    },
  },
};
