<template>
  <div>
    <div class="q-m-b-sm">
      关联企业：<q-entity-link :coy-obj="{ Name: detailParams.relatedName, KeyNo: detailParams.relatedKeyNo }"></q-entity-link>
    </div>
    <q-rich-table v-data="mListGetTableData('current')" :columns="columns">
      <template slot="action" slot-scope="record">
        <span v-if="['工商', '工商自主公示'].includes(record.Source)">-</span>
        <a v-else @click="showDetail(record)">详情</a>
      </template>
      <template slot="source" slot-scope="record">
        {{ record.Source || '-' }}
        <a-popover
          v-if="record.Source === '工商自主公示'"
          placement="bottom"
          :arrowPointAtCenter="true"
          content="该信息由企业自主在工商官网公示，企业对其报送信息的真实性、合法性负责"
        >
          <q-icon type="icon-a-shuomingxian" :style="{ color: '#bbb' }" />
        </a-popover>
      </template>
    </q-rich-table>
  </div>
</template>
<script src="./component.js"></script>
