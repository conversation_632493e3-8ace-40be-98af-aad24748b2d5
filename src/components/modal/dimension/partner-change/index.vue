<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" style="width: 120px">变更企业：</td>
        <td style="width: 360px">
          <q-entity-link v-if="viewData.KeyNo" :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
          <span v-else>{{ viewData.Name || '-' }}</span>
        </td>
        <td class="tb" style="width: 120px">更新时间<q-glossary-info info-id="241" />：</td>
        <td>
          {{ viewData.ChangeDate | dateformat }}
        </td>
      </tr>
    </table>
    <template v-if="getChangeExtendItems('D').length > 0">
      <div class="t-title">股份下降：{{ getChangeExtendItems('D').length }}个<q-glossary-info info-id="498" placement="bottomLeft" /></div>
      <table v-if="!getAmountShow('D')" class="ntable">
        <tr>
          <th width="60" class="tb">序号</th>
          <th width="300" class="tb">股东</th>
          <th width="300" class="tb">变更前</th>
          <th class="tb">变更后</th>
        </tr>
        <tr v-for="(item, index) in getChangeExtendItems('D')" :key="index">
          <td class="text-center">{{ index + 1 }}</td>
          <td>
            <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
          </td>
          <td class="text-center">
            <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
            <span v-else>-</span>
          </td>
        </tr>
        <tr v-if="getChangeExtendItems('D').length > 1">
          <td colspan="2">合计</td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('D')).before !== '0%' ? computeTotal(getChangeExtendItems('D')).before : '-' }}
          </td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('D')).after !== '0%' ? computeTotal(getChangeExtendItems('D')).after : '-' }}
          </td>
        </tr>
      </table>
      <table v-else class="ntable ntable-gh">
        <tr>
          <th rowspan="2" width="60" class="tb">序号</th>
          <th rowspan="2" width="300" class="tb">股东</th>
          <th colspan="2" width="300" class="tb">变更前</th>
          <th colspan="2" class="tb">变更后</th>
        </tr>
        <tr>
          <th width="150" class="tb">持股比例</th>
          <th width="150" class="tb">认缴金额</th>
          <th width="150" class="tb">持股比例</th>
          <th class="tb">认缴金额</th>
        </tr>
        <tr v-for="(item, index) in getChangeExtendItems('D')" :key="index">
          <td class="text-center">{{ index + 1 }}</td>
          <td>
            <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
          </td>
          <td class="text-center">
            <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.D">{{ item.D }}万元</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.E">{{ item.E }}万元</span>
            <span v-else>-</span>
          </td>
        </tr>
        <tr v-if="getChangeExtendItems('D').length > 1">
          <td colspan="2">合计</td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('D')).before !== '0%' ? computeTotal(getChangeExtendItems('D')).before : '-' }}
          </td>
          <td class="text-center">
            <span v-if="computeTotal(getChangeExtendItems('D')).beforeAmount"
              >{{ computeTotal(getChangeExtendItems('D')).beforeAmount }}万元</span
            >
            <span v-else>-</span>
          </td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('D')).after !== '0%' ? computeTotal(getChangeExtendItems('D')).after : '-' }}
          </td>
          <td class="text-center">
            <span v-if="computeTotal(getChangeExtendItems('D')).afterAmount"
              >{{ computeTotal(getChangeExtendItems('D')).afterAmount }}万元</span
            >
            <span v-else>-</span>
          </td>
        </tr>
      </table>
    </template>
    <template v-if="getChangeExtendItems('H').length > 0">
      <div class="t-title">
        股份上升：{{ getChangeExtendItems('H').length }}个<q-glossary-info
          v-if="!getChangeExtendItems('D').length"
          info-id="498"
          placement="bottomLeft"
        />
      </div>
      <table v-if="!getAmountShow('H')" class="ntable">
        <tr>
          <th width="60" class="tb">序号</th>
          <th width="300" class="tb">股东</th>
          <th width="300" class="tb">变更前</th>
          <th class="tb">变更后</th>
        </tr>
        <tr v-for="(item, index) in getChangeExtendItems('H')" :key="index">
          <td class="text-center">{{ index + 1 }}</td>
          <td>
            <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
          </td>
          <td class="text-center">
            <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
            <span v-else>-</span>
          </td>
        </tr>
        <tr v-if="getChangeExtendItems('H').length > 1">
          <td colspan="2">合计</td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('H')).before !== '0%' ? computeTotal(getChangeExtendItems('H')).before : '-' }}
          </td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('H')).after !== '0%' ? computeTotal(getChangeExtendItems('H')).after : '-' }}
          </td>
        </tr>
      </table>
      <table v-else class="ntable ntable-gh">
        <tr>
          <th rowspan="2" width="60" class="tb">序号</th>
          <th rowspan="2" width="300" class="tb">股东</th>
          <th colspan="2" width="300" class="tb">变更前</th>
          <th colspan="2" class="tb">变更后</th>
        </tr>
        <tr>
          <th width="150" class="tb">持股比例</th>
          <th width="150" class="tb">认缴金额</th>
          <th width="150" class="tb">持股比例</th>
          <th class="tb">认缴金额</th>
        </tr>
        <tr v-for="(item, index) in getChangeExtendItems('H')" :key="index">
          <td class="text-center">{{ index + 1 }}</td>
          <td>
            <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
          </td>
          <td class="text-center">
            <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.D">{{ item.D }}万元</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
            <span v-else>-</span>
          </td>
          <td class="text-center">
            <span v-if="item.E">{{ item.E }}万元</span>
            <span v-else>-</span>
          </td>
        </tr>
        <tr v-if="getChangeExtendItems('H').length > 1">
          <td colspan="2">合计</td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('H')).before !== '0%' ? computeTotal(getChangeExtendItems('H')).before : '-' }}
          </td>
          <td class="text-center">
            <span v-if="computeTotal(getChangeExtendItems('H')).beforeAmount"
              >{{ computeTotal(getChangeExtendItems('H')).beforeAmount }}万元</span
            >
            <span v-else>-</span>
          </td>
          <td class="text-center">
            {{ computeTotal(getChangeExtendItems('H')).after !== '0%' ? computeTotal(getChangeExtendItems('H')).after : '-' }}
          </td>
          <td class="text-center">
            <span v-if="computeTotal(getChangeExtendItems('H')).afterAmount"
              >{{ computeTotal(getChangeExtendItems('H')).afterAmount }}万元</span
            >
            <span v-else>-</span>
          </td>
        </tr>
      </table>
    </template>
    <template v-if="getChangeExtendItems('E').length > 0">
      <div class="t-title">股东退出：{{ getChangeExtendItems('E').length }}个</div>
      <table class="ntable">
        <tr>
          <th width="60" class="tb">序号</th>
          <th width="585" class="tb">股东</th>
          <th class="tb">退出前(持股比例)</th>
        </tr>
        <tr v-for="(item, index) in getChangeExtendItems('E')" :key="index">
          <td class="text-center">{{ index + 1 }}</td>
          <td>
            <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
          </td>
          <td class="text-center">
            <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
            <span v-else>-</span>
          </td>
        </tr>
      </table>
    </template>
    <template v-if="getChangeExtendItems('F').length > 0">
      <div class="t-title">股东新增：{{ getChangeExtendItems('F').length }}个</div>
      <table class="ntable">
        <tr>
          <th width="60" class="tb">序号</th>
          <th width="585" class="tb">股东</th>
          <th class="tb">持股比例</th>
        </tr>
        <tr v-for="(item, index) in getChangeExtendItems('F')" :key="index">
          <td class="text-center">{{ index + 1 }}</td>
          <td>
            <q-entity-link :coy-obj="{ KeyNo: item.K, Name: item.A }" />
          </td>
          <td class="text-center">
            <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
            <span v-else>-</span>
          </td>
        </tr>
        <tr v-if="getChangeExtendItems('F').length > 1 && computeTotal(getChangeExtendItems('F')).showFTotal">
          <td colspan="2">合计</td>
          <td class="text-center">{{ computeTotal(getChangeExtendItems('F')).before }}</td>
        </tr>
      </table>
    </template>
    <!-- 大股东变更  -->
    <template v-if="changeList.before.length || changeList.after.length">
      <div class="t-title">大股东变更</div>
      <table class="ntable">
        <template v-if="changeList.before.length && changeList.after.length">
          <tr>
            <td class="tb" style="width: 120px">变更前：</td>
            <td>
              <template v-for="(el, i) in changeList.before">
                <q-entity-link :key="`be${i}`" :coy-obj="el" />
                <template v-if="el.StockPercent"> ，变更前持股{{ el.StockPercent }} <br :key="`e1${i}`" /> </template>
              </template>
            </td>
          </tr>
          <tr>
            <td class="tb" style="width: 120px">变更后：</td>
            <td>
              <template v-for="(el, i) in changeList.after">
                <q-entity-link :key="`ae${i}`" :coy-obj="el" />
                <template v-if="el.StockPercent"> ，持股{{ el.StockPercent }} <br :key="`e1${i}`" /> </template>
              </template>
            </td>
          </tr>
        </template>
        <template v-else>
          <tr>
            <td class="tb" style="width: 120px">变更后：</td>
            <td>
              <template v-for="(el, i) in changeList.before">
                <q-entity-link :key="`b_e${i}`" :coy-obj="el" />
              </template>
              <template v-for="(el, i) in changeList.after">
                <q-entity-link :key="`a_e${i}`" :coy-obj="el" />
              </template>
              {{ changeList.before.length ? '不再是' : '成为' }}
              <q-entity-link :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.Name }" />
              的大股东
            </td>
          </tr>
        </template>
      </table>
    </template>
  </div>
</template>

<script>
import _ from 'lodash';
// eslint-disable-next-line vue/require-name-property
export default {
  props: {
    viewData: {
      type: Object,

      default() {
        return {};
      },
    },
  },

  data() {
    return {
      changeList: {
        before: [],
        after: [],
      },
    };
  },

  mounted() {
    // 大股东变更
    const t = this.viewData;
    if (t) {
      try {
        const partInfo = _.isString(t) ? JSON.parse(t) : t;

        if (partInfo?.BP?.A) {
          this.changeList.before = partInfo.BP.A;
        }
        if (partInfo?.BP?.B) {
          this.changeList.after = partInfo.BP.B;
        }
      } catch (e) {}
    }
  },

  methods: {
    getChangeExtendItems(key) {
      const changeExtend = this.viewData;
      return changeExtend[key] && changeExtend[key].length > 0 ? changeExtend[key] : [];
    },

    getAmountShow(key) {
      const arr = this.getChangeExtendItems(key);
      return arr.some((el) => el.D || el.E);
    },

    accAdd(arg1, arg2) {
      let r1 = 0;
      let r2 = 0;
      const getArr = (f) => f.toString().split('.');
      if (getArr(arg1).length > 1) {
        r1 = getArr(arg1)[1].length;
      }
      if (getArr(arg2).length > 1) {
        r2 = getArr(arg2)[1].length;
      }

      const m = Math.pow(10, Math.max(r1, r2));
      return (Math.round(arg1 * m) + Math.round(arg2 * m)) / m;
    },

    computeTotal(arr) {
      const getColumn = (list = [], key = '') => {
        if (key) {
          return list.map((el) => {
            let count = 0;
            if (el[key]) {
              count = parseFloat(el[key]);
            }
            return count;
          });
        }
        return [];
      };
      const before = getColumn(arr, 'B');
      const after = getColumn(arr, 'C');
      const beforeAmount = getColumn(arr, 'D');
      const afterAmount = getColumn(arr, 'E');

      const getNum = (num) => {
        if (num < 0) {
          return 0;
        } else if (num > 100) {
          return 100;
        }
        return num;
      };
      return {
        showFTotal: before.every((el) => el > 0),
        before: `${getNum(before.reduce((pre, cur) => this.accAdd(pre, cur), 0))}%`,
        after: `${getNum(after.reduce((pre, cur) => this.accAdd(pre, cur), 0))}%`,
        beforeAmount: beforeAmount.reduce((pre, cur) => this.accAdd(pre, cur), 0),
        afterAmount: afterAmount.reduce((pre, cur) => this.accAdd(pre, cur), 0),
      };
    },
  },
};
</script>
