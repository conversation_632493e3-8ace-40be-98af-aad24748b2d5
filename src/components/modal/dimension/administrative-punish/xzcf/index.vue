<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="15%" class="tb">处罚名称</td>
        <td width="35%">{{ viewData.name || '-' }}</td>
        <td width="15%" class="tb">行政相对人名称</td>
        <td width="35%">
          <q-entity-link :coy-arr="viewData.CompanyAndKeyNo"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb">决定文书号</td>
        <td v-html="viewData.document_no || '-'"></td>
        <td class="tb">处罚事由</td>
        <td v-html="viewData.reason || '-'"></td>
      </tr>
      <tr>
        <td width="15%" class="tb">处罚状态</td>
        <td width="35%">{{ viewData.status || '-' }}</td>
        <td width="15%" class="tb">决定日期</td>
        <td width="35%">{{ viewData.decide_date || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">处罚类别1</td>
        <td v-html="viewData.typ1 || '-'"></td>
        <td class="tb">处罚类别2</td>
        <td v-html="viewData.typ2 || '-'"></td>
      </tr>
      <tr>
        <td class="tb">处罚依据</td>
        <td colspan="3" v-html="viewData.according || '-'"></td>
      </tr>
      <tr>
        <td class="tb">处罚结果</td>
        <td v-html="viewData.content || '-'"></td>
        <td class="tb">处罚机关</td>
        <td>{{ viewData.office_no || '-' }}</td>
      </tr>
    </table>
    <q-relate-cases :search-params="viewData" stitle="关联"></q-relate-cases>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
