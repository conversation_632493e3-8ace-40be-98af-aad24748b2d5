<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="23%">行政相对人名称</td>
        <td colspan="3">
          <q-entity-link :coy-obj="{ KeyNo: viewData.KeyNo, Name: viewData.CompanyName }"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb" width="23%">行政相对人统一社会信用代码</td>
        <td width="27%">{{ viewData.CreditCode || '-' }}</td>
        <td class="tb" width="23%">法定代表人姓名</td>
        <td width="27%">
          <q-entity-link :coy-obj="viewData.Oper"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb">案件名称</td>
        <td>{{ viewData.CaseName || '-' }}</td>
        <td class="tb">决定书文号</td>
        <td>{{ viewData.CaseNo || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="23%">处罚事由</td>
        <td colspan="3">{{ viewData.Reason || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">处罚类型</td>
        <td>{{ viewData.Type || '-' }}</td>
        <td class="tb">处罚状态</td>
        <td>{{ viewData.Status || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">处罚结果</td>
        <td>{{ viewData.Result || '-' }}</td>
        <td class="tb">处罚决定日期</td>
        <td>{{ viewData.PunishDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
      <tr>
        <td class="tb">执法依据</td>
        <td colspan="3">{{ viewData.Basis || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">作出处罚决定部门</td>
        <td colspan="3">{{ viewData.IssuedBy || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">国税/地税</td>
        <td>{{ viewData.Level || '-' }}</td>
        <td class="tb">省份</td>
        <td>{{ viewData.Province || '-' }}</td>
      </tr>
    </table>
    <q-relate-cases :search-params="viewData" stitle="关联"></q-relate-cases>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
