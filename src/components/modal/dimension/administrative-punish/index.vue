<template>
  <div v-if="viewData">
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">行政处罚决定书文号</td>
        <td width="30%">{{ viewData.DocNo || '-' }}</td>
        <td class="tb" width="20%">处罚单位</td>
        <td width="30%">{{ viewData.PunishOffice || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">处罚事由</td>
        <td colspan="5" v-html="viewData.PunishReason || '-'"></td>
      </tr>
      <tr>
        <td class="tb">处罚结果</td>
        <td colspan="5" v-html="viewData.PunishResult || '-'"></td>
      </tr>
      <tr v-if="viewData.PenaltyMoney">
        <td class="tb">处罚金额（元）</td>
        <td colspan="5" v-html="getMoney(viewData.PenaltyMoney)"></td>
      </tr>
      <tr>
        <td class="tb">处罚依据</td>
        <td colspan="5" v-html="viewData.PunishBasis || '-'"></td>
      </tr>
      <tr>
        <td class="tb" width="20%">处罚日期</td>
        <td width="30%">{{ viewData.PunishDate | dateformat('YYYY-MM-DD') }}</td>
        <td class="tb" width="20%">发布日期</td>
        <td width="30%">{{ viewData.PublishDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
      <tr v-if="viewData.OssUrl && viewData.OssUrl.indexOf('.txt') === -1">
        <td class="tb">原文</td>
        <td colspan="5">
          <q-source-logo v-if="viewData.OssUrl" label="查看" :url="viewData.OssUrl"></q-source-logo>
        </td>
      </tr>
    </table>
    <!-- 补充变更信息 -->
    <div class="punishment-alt-relat" v-if="viewData.PunishmentAltList && viewData.PunishmentAltList.length > 0">
      <div class="tcaption" style="margin-bottom: 0">行政处罚变更信息</div>
      <table class="ntable ntable-odd">
        <tr>
          <th width="50px">序号</th>
          <th>变更事项</th>
          <th>变更前内容</th>
          <th>变更后内容</th>
          <th>变更日期</th>
          <th>作出变更决定机关</th>
        </tr>
        <tr v-for="(item, index) in viewData.PunishmentAltList" :key="index">
          <td width="50px" class="text-center">{{ index + 1 }}</td>
          <td width="20%" v-html="item.Alt || '-'"></td>
          <td width="20%" v-html="item.AltBefore || '-'"></td>
          <td width="20%" v-html="item.AltAfter || '-'"></td>
          <td width="15%">{{ item.AltDate || '-' }}</td>
          <td v-html="item.PenAuth || '-'"></td>
        </tr>
      </table>
    </div>
    <q-relate-cases :search-params="viewData" stitle="关联"></q-relate-cases>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    getMoney(money) {
      if (money) {
        return money.toLocaleString();
      }
      return '-';
    },
  },
};
</script>

<style lang="less" scoped>
.text-center {
  text-align: center;
}
</style>
