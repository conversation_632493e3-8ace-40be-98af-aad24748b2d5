<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <table class="ntable">
      <tr>
        <td width="23%" class="tb">当事人：</td>
        <td colspan="3">
          <q-role-list v-if="viewData.RoleList && viewData.RoleList.length" :list="viewData.RoleList" />
          <span v-else>-</span>
        </td>
      </tr>
      <tr>
        <td width="23%" class="tb">公告类型：</td>
        <td width="27%">{{ viewData.Category || '-' }}</td>
        <td width="23%" class="tb">刊登日期：</td>
        <td v-if="viewData.PublishDate">{{ viewData.PublishDate | dateformat('YYYY-MM-DD') }}</td>
        <td v-else>-</td>
      </tr>
      <tr>
        <td width="23%" class="tb">刊登版面：</td>
        <td>{{ viewData.PublishPage || '-' }}</td>
        <td width="23%" class="tb">公告人：</td>
        <td>{{ viewData.Court || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="23%">上传日期：</td>
        <td colspan="3">{{ viewData.SubmitDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
      <tr>
        <td class="tb" width="23%">内容：</td>
        <td v-if="viewData.Content" v-entity-click colspan="3" v-html="viewData.Content" />
        <td v-else>-</td>
      </tr>
    </table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script>
// eslint-disable-next-line vue/require-name-property
export default {
  props: {
    viewData: {
      type: Object,

      default() {
        return {};
      },
    },
  },
};
</script>
