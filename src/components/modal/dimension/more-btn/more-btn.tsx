import { VNode, defineComponent } from 'vue';

import styles from './more-btn.module.less';

export default defineComponent({
  functional: true,
  props: {
    label: {
      type: String,
      default: '查看更多',
    },
    showArrow: {
      type: Boolean,
      default: true,
    },
  },
  render(h, { props, data, listeners }): VNode {
    return (
      <span
        class={[styles.root, data.class]}
        style={data.style}
        role="button"
        {...{
          on: listeners,
        }}
      >
        {props.label}
        {props.showArrow && <a-icon type="right" class="ml-4" />}
      </span>
    );
  },
});
