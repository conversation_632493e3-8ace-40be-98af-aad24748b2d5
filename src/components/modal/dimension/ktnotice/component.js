import { Icon, Tooltip } from 'ant-design-vue';

export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    prosecutor() {
      return this.viewData.RoleList.find((item) => item.Value === 0)?.Items || [];
    },
    defendant() {
      return this.viewData.RoleList.find((item) => item.Value === 1)?.Items || [];
    },
    thirdParty() {
      return this.viewData.RoleList.find((item) => item.Value === 2)?.Items || [];
    },
    others() {
      return this.viewData.RoleList.find((item) => ![0, 1, 2].includes(item.Value))?.Items || [];
    },
  },
  methods: {
    showVideoModal(videoUrl) {
      window.open(videoUrl);
      // uiService.showVideoModal({
      //   title: '庭审视频',
      //   videoUrl,
      // });
      // this.$modal.showDimension('video', { videoUrl });
    },
    visibleChange() {
      this.$emit('visibleChange');
    },
  },
  components: { Icon, Tooltip },
};
