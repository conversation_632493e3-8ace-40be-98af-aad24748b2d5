<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <td class="tb" width="21%">案由：</td>
          <td colspan="3">
            {{ viewData.CaseReason || '-' }}
            <span v-if="viewData.CaseReasonDescription">
              <Tooltip placement="bottom" :title="viewData.CaseReasonDescription">
                <Icon style="color: #d6d6d6" class="icon" type="info-circle" />
              </Tooltip>
            </span>
          </td>
        </tr>
        <tr>
          <td width="21%" class="tb">案号：</td>
          <td v-if="viewData.CaseSearchId && viewData.CaseSearchId.length !== 0">
            <q-link :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId?.[0]}`">{{ viewData.CaseNo || '-' }}</q-link>
          </td>
          <td v-else>{{ viewData.CaseNo || '-' }}</td>
          <td width="21%" class="tb">开庭时间：</td>
          <td width="29%">{{ viewData.OpenTime | dateformat('YYYY-MM-DD HH:mm') }}</td>
        </tr>
        <tr>
          <td class="tb">地区：</td>
          <td>{{ viewData.Province || '-' }}</td>
          <td class="tb">排期日期：</td>
          <td>{{ viewData.ScheduleTime | dateformat('YYYY-MM-DD') }}</td>
        </tr>
        <tr>
          <td class="tb">承办部门：</td>
          <td>{{ viewData.UndertakeDepartment || '-' }}</td>
          <td class="tb">审判长/主审人：</td>
          <td>{{ viewData.ChiefJudge || '-' }}</td>
        </tr>

        <tr>
          <td class="tb" style="width: 15%">当事人：</td>
          <td colspan="3">
            <q-role-list :list="viewData.RoleList" />
          </td>
        </tr>

        <tr>
          <td class="tb">法院：</td>
          <td>{{ viewData.ExecuteGov || '-' }}</td>
          <td class="tb">法庭：</td>
          <td>{{ viewData.ExecuteUnite || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="21%">公告内容：</td>
          <td colspan="3" v-html="viewData.Content || '-'" v-entity-click></td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
