import { defineComponent } from 'vue';
import { isNil } from 'lodash';

const Invest = defineComponent({
  name: 'Invest',

  props: {
    detailParams: {
      type: Object,
    },
  },

  computed: {
    keyNo() {
      return (this as any).detailParams.keyNo;
    },
    name() {
      return (this as any).detailParams.name;
    },
    viewData() {
      return (this as any).detailParams.viewData;
    },
  },

  render() {
    const { viewData, name, keyNo } = this as any;
    if (isNil(viewData)) {
      return <div></div>;
    }

    return (
      <table class="ntable">
        <tr>
          <td class="tb" width="23%">
            间接持股企业名称
          </td>
          <td colspan="3">
            <q-td-coy keyNo={viewData.KeyNo} name={viewData.Name} image={viewData.ImageUrl}></q-td-coy>
          </td>
        </tr>
        <tr>
          <td class="tb" width="23%">
            间接持股比例
          </td>
          <td colspan="3">{viewData.PercentTotal || '-'}</td>
        </tr>
        <tr>
          <td class="tb" width="23%">
            投资链
          </td>
          <td colspan="3">
            <q-td-path name={name} keyNo={keyNo} isInvest={true} reverse={false} paths={viewData.Paths} />
          </td>
        </tr>
      </table>
    );
  },
});

export default Invest;
