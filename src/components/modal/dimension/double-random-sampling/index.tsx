import { dateFormat } from '@/utils/format';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'DoubleRandomSampling',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    return (
      <table class="ntable">
        <tr>
          <td width="23%" class="tb">
            任务编号：
          </td>
          <td width="27%">{viewData.CheckTaskNo || '-'}</td>
          <td width="23%" class="tb">
            任务名称：
          </td>
          <td width="27%">{viewData.CheckTaskName || '-'}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">
            抽查机关：
          </td>
          <td width="27%">{viewData.CheckBelongOrg || '-'}</td>
          <td width="23%" class="tb">
            完成日期：
          </td>
          <td width="27%">
            <span>{dateFormat(viewData.CheckDoneDate * 1000)}</span>
          </td>
        </tr>
        {viewData.Detail && viewData.Detail.length
          ? viewData.Detail.map((item, index) => {
              return (
                <tbody key="`checkItem-${index}`">
                  <tr>
                    <td width="23%" class="tb">
                      事项{index + 1}：
                    </td>
                    <td colspan="3">{item.CheckItem}</td>
                  </tr>
                  <tr>
                    <td width="23%" class="tb">
                      结果：
                    </td>
                    <td colspan="3">{item.CheckResult}</td>
                  </tr>
                </tbody>
              );
            })
          : null}
      </table>
    );
  },
});
