import { defineComponent, ref } from 'vue';
import { Button } from 'ant-design-vue';
import { cloneDeep, isFunction } from 'lodash';

import QModal from '@/components/global/q-modal/q-modal';
import QTabs from '@/components/global/q-tabs';
import { createPromiseDialog } from '@/components/promise-dialogs';

import styles from './styles.module.less';
import AddFromPaste from './add-from-paste';

const PasteDialog = defineComponent({
  name: 'PasteDialog',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const selection: any = ref([]);
    /**
     * 处理包装 Modal 的通用事件
     */

    const {
      tabs = [
        {
          label: '批量粘贴',
          key: 'paste',
        },
      ],
    } = props.params;
    const handleSubmit = async () => {
      try {
        if (isFunction(props.params.validate)) {
          const valid = await props.params?.validate?.(selection.value);
          if (!valid) return;
        }
        emit('resolve', cloneDeep(selection.value));
      } catch (error) {
        console.error(error);
      }
    };

    const handleSelect = (value) => {
      selection.value = value;
    };

    const handleCancel = () => {
      emit('reject', 'Cancel');
    };

    return {
      selection,
      handleSubmit,
      handleCancel,
      handleSelect,
      tabs,
    };
  },
  render() {
    return (
      <QModal
        wrapClassName={styles.dialogWrapper}
        visible={true}
        bodyStyle={{ padding: 0 }}
        onCancel={this.handleCancel}
        title={this.params.title || '批量粘贴'}
        footer={false}
      >
        <QTabs slot="title" value={'paste'} tabs={this.tabs}></QTabs>
        <div>
          <AddFromPaste onChange={this.handleSelect} placeholder={this.params.placeholder} max={this.params.max} />
        </div>
        <div style={{ textAlign: 'right', padding: '10px 15px', borderTop: '1px solid #eee' }}>
          <Button
            // FIXME: this.isLoading 未声明
            // loading={this.isLoading}
            onClick={this.handleSubmit}
            type="primary"
            disabled={!this.selection.length}
          >
            批量查询
          </Button>
        </div>
      </QModal>
    );
  },
});

export default PasteDialog;

export const openPasteDialog: any = createPromiseDialog(PasteDialog);
