import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { Button } from 'ant-design-vue';

import PasteDialog from '../index';

describe('PasteDialog', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(PasteDialog, {
      propsData: {
        params: {
          validate: vi.fn(),
          title: 'Test Title',
          placeholder: 'Paste here',
          max: 10,
        },
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('render with no props', async () => {
    const wrapper2 = mount(PasteDialog);
    await nextTick();
    expect(wrapper2.html()).toContain('批量粘贴');
  });

  test('should call handleCancel when cancel button is clicked', async () => {
    await wrapper.vm.handleCancel();
    expect(wrapper.emitted('reject')[0]).toEqual(['Cancel']);
  });

  test('should call handleSubmit and emit resolve when validation passes', async () => {
    const selectedValue = ['value1', 'value2'];
    wrapper.vm.selection = selectedValue;
    wrapper.vm.params.validate.mockResolvedValue(true);

    await wrapper.vm.handleSubmit();
    expect(wrapper.emitted('resolve')[0]).toEqual([selectedValue]);
  });

  test('should not emit resolve if validation fails', async () => {
    const selectedValue = ['value1', 'value2'];
    wrapper.vm.selection = selectedValue;
    wrapper.vm.params.validate.mockResolvedValue(false);

    await wrapper.vm.handleSubmit();
    expect(wrapper.emitted()).toEqual({});
  });

  test('should disable the button if selection is empty', async () => {
    await nextTick();
    expect(wrapper.findComponent(Button).attributes('disabled')).toBe('disabled');

    wrapper.vm.selection = ['value1'];
    await nextTick();
    expect(wrapper.findComponent(Button).attributes('disabled')).toBe(undefined);
  });
});
