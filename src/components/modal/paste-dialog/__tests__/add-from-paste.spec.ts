import { mount } from '@vue/test-utils';
import { Input } from 'ant-design-vue';

import AddFromPaste from '../add-from-paste';

describe('AddFromPaste.vue', () => {
  test('renders with default props', () => {
    const wrapper = mount(AddFromPaste);
    expect(wrapper.text()).toContain('编辑或粘贴文本');
    expect(wrapper.findComponent(Input.TextArea).attributes('placeholder')).toBe('您可以输入员工编号或者姓名 \n例\n张三\n1234');
  });

  test('normalizes text on change', async () => {
    const wrapper = mount(AddFromPaste);
    await wrapper.setData({ matchText: '张三,李四 ' });
    const textarea = wrapper.findComponent(Input.TextArea) as any;
    expect(textarea.vm.value).toBe('张三,李四 ');
    expect(wrapper.emitted().change[1][0]).toEqual(['张三', '李四']);

    const delBtn = wrapper.find('[data-testid="clear-btn"]');
    await delBtn.trigger('click');
    expect(wrapper.vm.matchText).toBe('');
  });

  test('does not emit change when matchText is empty', async () => {
    const wrapper = mount(AddFromPaste);
    await wrapper.setData({ matchText: '' });
    expect(wrapper.emitted().change[0][0]).toEqual([]);
  });
});
