import { Input } from 'ant-design-vue';
import { defineComponent, ref, ComponentInstance, watch, onMounted, nextTick } from 'vue';

import Icon from '@/shared/components/icon';

import styles from '@/shared/components/text-match-modal/text-match-modal.module.less';

const AddFromPaste = defineComponent({
  name: 'AddFromPaste',
  props: {
    visible: {
      type: Boolean,
      required: false,
    },
    placeholder: {
      type: String,
      default: '您可以输入员工编号或者姓名 \n例\n张三\n1234',
    },
    /**
     * 最大输入数量
     */
    max: {
      type: Number,
      default: 20000,
    },
  },
  setup(props, { emit }) {
    // 待匹配文本
    const matchText = ref('');
    const handleClear = () => {
      matchText.value = '';
    };

    const textarea = ref<ComponentInstance | null>(null);

    onMounted(async () => {
      await nextTick();
      (textarea.value?.$el as HTMLTextAreaElement)?.focus?.();
    });

    /**
     * 统一输入内容，将支持的符号统一替换为换行符（接口只支持\n）
     */
    const matchTextNormalize = (text: string) => {
      return text.replace(/[,，]/g, '\n');
    };

    watch(
      () => matchText.value,
      (val) => {
        const text = matchTextNormalize(val);
        emit(
          'change',
          text
            .split('\n')
            .filter((t) => !!t)
            .map((t) => t.trim())
        );
      },
      {
        immediate: true,
        deep: true,
      }
    );

    return {
      matchText,
      handleClear,
      textarea, // ref
    };
  },
  render() {
    return (
      <div class={styles.container2} style={{ padding: '15px' }}>
        <div class={styles.column}>
          <div class={styles.result}>
            <header class={styles.header}>
              <div class={styles.title}>编辑或粘贴文本</div>
              <div class={styles.extra} v-show={this.matchText?.length}>
                <div class={styles.clear} onClick={this.handleClear} data-testid="clear-btn">
                  <Icon class={styles.icon} type="icon-a-shanchuxian1x" />
                  <span>清空</span>
                </div>
              </div>
            </header>

            <div style={{ overflow: 'hidden', height: 'inherit' }}>
              <Input.TextArea
                ref="textarea"
                v-model={this.matchText}
                class={styles.text}
                size="large"
                placeholder={this.placeholder}
                maxLength={this.max}
              />
              <div class={styles.count}>
                <span>{this.matchText?.length}</span>/<span>{this.max}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default AddFromPaste;
