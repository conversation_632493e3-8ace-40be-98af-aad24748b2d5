import Vue from 'vue';
import _ from 'lodash';
import dimensionModal from './dimension';

export const showDimension = (store, router) => {
  return _.debounce(
    function (field, params, ...rest) {
      const Constructor = Vue.extend(dimensionModal);
      const modal = new Constructor({
        parent: this instanceof Vue ? this : _.get(window.__VUE_ROOT__, '$refs.entry.$refs.layout'),
        router,
        store,
      });
      const instance = modal.$mount();
      window.__DIMENSION_INSTANCE__ = instance;
      document.body.appendChild(instance.$el);
      return instance.show(field, params, ...rest).then(() => {
        instance.$destroy();
        if (instance.$el.parentNode) {
          instance.$el.parentNode.removeChild(instance.$el);
        }
      });
    },
    300,
    { leading: true, trailing: false }
  );
};
