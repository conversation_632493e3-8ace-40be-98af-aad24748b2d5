import { defineComponent, ref } from 'vue';
import { But<PERSON> } from 'ant-design-vue';
import { cloneDeep } from 'lodash';
import { useRoute } from 'vue-router/composables';

import QModal from '@/components/global/q-modal/q-modal';
import QTabs from '@/components/global/q-tabs';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { createPromiseDialog } from '@/components/promise-dialogs';

import styles from './styles.module.less';
import AddFromPaste from './add-from-paste';

const BatchPasteDialog = defineComponent({
  name: 'BatchPasteDialog',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const selection: any = ref([]);
    /**
     * 处理包装 Modal 的通用事件
     */

    const {
      tabs = [
        {
          label: '批量粘贴',
          key: 'paste',
        },
        {
          label: '从第三方列表选择',
          key: 'third',
        },
      ],
    } = props.params;
    const handleSubmit = async () => {
      try {
        if (props.params.validate) {
          const valid = await props.params?.validate?.(selection.value);
          if (!valid) return;
        }
        emit('resolve', cloneDeep(selection.value));
      } catch (error) {
        console.error(error);
      }
    };

    const handleSelect = (value) => {
      selection.value = value.map((item) => {
        return {
          ...item,
          companyId: item.KeyNo,
          companyName: item.ActualName || item.Name,
          Name: item.ActualName || item.Name,
        };
      });
    };

    const handleCancel = () => {
      emit('reject', 'Cancel');
    };

    const track = useTrack();
    const route = useRoute();
    const handleTrack = (btnName: string) => {
      track(createTrackEvent(6953, route.meta?.title, btnName, '批量粘贴弹框'));
    };

    return {
      selection,
      handleSubmit,
      handleCancel,
      handleSelect,
      handleTrack,
      tabs,
    };
  },
  render() {
    return (
      <QModal
        wrapClassName={styles.dialogWrapper}
        visible={true}
        size="extra-large"
        bodyStyle={{ padding: 0 }}
        onCancel={this.handleCancel}
        title={this.params.title || '批量粘贴'}
        footer={false}
      >
        <QTabs slot="title" value={'paste'} tabs={this.tabs}></QTabs>
        <div>
          <AddFromPaste onChange={this.handleSelect} onTrack={this.handleTrack} />
        </div>
        <div style={{ textAlign: 'right', padding: '10px 15px', borderTop: '1px solid #eee' }} v-show={this.selection?.length}>
          <Button
            onClick={() => {
              this.handleTrack('批量查询');
              this.handleSubmit();
            }}
            type="primary"
          >
            批量查询
          </Button>
        </div>
      </QModal>
    );
  },
});

export default BatchPasteDialog;

export const openBatchPasteDialog: any = createPromiseDialog(BatchPasteDialog);
