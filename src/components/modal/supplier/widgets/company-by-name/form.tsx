import { computed, defineComponent, PropType, unref } from 'vue';
import { Button, Form, Input, message, Select } from 'ant-design-vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import { company as companyService, monitor } from '@/shared/services';
import { getHTMLText } from '@/utils';
import { openGroupModal } from '@/shared/components/add-group';
import WarningInfo from '@/shared/components/warning-info';
import { getGroupList } from '@/shared/composables/use-group-search';
import { Permission } from '@/config/permissions.config';

import CompanySelect from '../../company-select';
import styles from './company-by-name.module.less';
import BatchUploadFrame from '@/shared/components/batch-upload-frame';
import FileUpload from '@/shared/components/file-upload';
import DownloadLink from '@/shared/components/download-link';
import { IMPORT_MONITOR_TEMPLATE_URL } from '@/config';
import { useRouter } from 'vue-router/composables';

const CompanyByNameForm = defineComponent({
  name: 'CompanyByNameForm',
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
    },
    // 增加 flex 布局支持
    formLayoutType: {
      type: String as PropType<'ant-row' | 'ant-row-flex'>,
      default: 'ant-row-flex',
    },
    itemLayout: {
      type: Object,
      default: () => ({
        labelCol: { flex: '64px' },
        wrapperCol: { flex: 'auto' },
      }),
    },
    groups: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    originData: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    addType: {
      type: String as PropType<'single' | 'batch'>,
      default: 'single',
    },
  },
  setup(props, { emit }) {
    const { groupList, monitorGroups } = getGroupList();

    const isBatch = computed(() => props.addType === 'batch');

    const suggestionRequest = (keywords: string) => {
      return companyService
        .searchLite({
          searchKey: keywords,
          filter: {
            st: ['0', '1', '4', '10', '11', '12'],
            ot: [
              '*********',
              '*********',
              '001006',
              '*********',
              '*********',
              '*********',
              '*********',
              '*********',
              '*********',
              '001008',
              '001004',
              '001005',
              '001011',
              '001016',
              '001015',
            ],
          },
        })
        .then((Result = []) => {
          return Result.map(({ Name, ...other }) => ({
            value: getHTMLText(Name),
            label: Name,
            ...other,
          }));
        });
    };
    const handleAddGroup = async () => {
      const form: any = await openGroupModal({
        title: '新建分组',
        data: {},
        isEdit: false,
      });
      if (!form) {
        return;
      }
      const res = await monitor.addGroup({
        ...unref(form),
        groupName: unref(form).name.trim(),
      });
      message.success('添加成功！');
      await monitorGroups.execute<any>({ pageIndex: 1, pageSize: 100 });
      props.form?.setFieldsValue({
        monitorGroupId: res.monitorGroupId,
      });
      // emit更新分组
      emit('freshGroup');
    };

    const defaultGroupId = computed(() => {
      const defaultGroup = groupList.value?.find((v) => v.groupType === 1)?.monitorGroupId;
      const res = props.originData.monitorGroupId ?? defaultGroup ?? groupList.value?.[0]?.monitorGroupId;
      return res && Number(res);
    });

    const handleUploadSuccess = ({ batchId }) => {
      if (!batchId) return;
      const groupId = props.form?.getFieldValue('monitorGroupId');
      emit('uploadSuccess', { batchId, groupId });
    };

    const handleBeforeUpload = async () => {
      props.form?.validateFields(['monitorGroupId']);
      return !!props.form?.getFieldValue('monitorGroupId');
    };

    return {
      groupList,
      handleAddGroup,
      suggestionRequest,
      defaultGroupId,
      isBatch,
      handleUploadSuccess,
      handleBeforeUpload,
    };
  },
  render() {
    return (
      <Form
        class={styles.container}
        hideRequiredMark={true}
        form={this.form}
        colon={false}
        layout="horizontal"
        labelCol={this.itemLayout.labelCol}
        wrapperCol={this.itemLayout.wrapperCol}
      >
        {this.isBatch ? null : (
          <Form.Item class={this.formLayoutType} label="企业名称">
            <CompanySelect
              disabled={this.originData.companyId}
              v-decorator={[
                'companyName',
                {
                  initialValue: this.originData.companyName,
                  rules: [
                    {
                      required: true,
                      message: '请输入企业名称或统一社会信用代码',
                    },
                  ],
                },
              ]}
              placeholder="请输入企业名称或统一社会信用代码"
              size="large"
              remote={this.suggestionRequest}
              onChange={(value, option) => {
                // 设置 form 字段
                this.form?.setFields({
                  companyName: { value: getHTMLText(option?.label) },
                  companyId: { value: option?.KeyNo },
                });
              }}
            />
          </Form.Item>
        )}

        {this.isBatch ? null : (
          <Form.Item label={'企业ID'} v-show={false} class={this.formLayoutType}>
            <Input
              v-decorator={[
                'companyId',
                {
                  initialValue: this.originData.companyId,
                  rules: [
                    {
                      required: true,
                    },
                  ],
                },
              ]}
            />
          </Form.Item>
        )}

        <Form.Item label="企业分组" class={this.formLayoutType}>
          <div class="flex">
            <Select
              placeholder="请选择企业分组"
              class={styles.groupSelect}
              v-decorator={[
                'monitorGroupId',
                {
                  initialValue: this.defaultGroupId,
                  rules: [{ required: true, message: '请选择企业分组' }],
                },
              ]}
              options={this.groupList.map((item) => {
                return {
                  ...item,
                  label: item.name,
                  value: item.monitorGroupId,
                };
              })}
            ></Select>
            <Button
              v-permission={[Permission.MONITOR_ENTERPRISE_GROUP_MANAGE]}
              class={styles.addBtn}
              type="primary"
              ghost
              v-disabletip={`当前分组数量已达上限50个，无法继续添加`}
              onClick={this.handleAddGroup}
              disabled={this.groupList.length >= 50}
            >
              新增分组
            </Button>
          </div>
        </Form.Item>

        {this.isBatch ? (
          <Form.Item label="上传文件" class={this.formLayoutType}>
            <div>
              <BatchUploadFrame innerClass={styles.uploadWrapper}>
                <FileUpload
                  ref="fileUpload"
                  action={(file) => `/insights/batch/import/monitor/excel?fileName=${file.name}`}
                  height="120px"
                  placeholder="点击或拖拽文件到此上传"
                  showUploadList={false}
                  onSuccess={this.handleUploadSuccess}
                  beforeFileUpload={this.handleBeforeUpload}
                  theme="lighter"
                />
                <ul slot="description">
                  <li>
                    <DownloadLink href={IMPORT_MONITOR_TEMPLATE_URL}>下载模板</DownloadLink>
                    <span>并按照样式编辑好数据，切勿增减列</span>
                  </li>
                  <li>上传文件不超过2M，仅支持Excel</li>
                  <li>
                    批量上传一次支持最多不超过<em>&nbsp;5000&nbsp;</em>家企业
                  </li>
                </ul>
              </BatchUploadFrame>
            </div>
          </Form.Item>
        ) : null}

        <div class={this.isBatch ? 'mt-[16px]' : 'mt-[33px]'}>
          <WarningInfo text="暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位、个体工商户发起监控" />
        </div>
      </Form>
    );
  },
});

const CompanyByNameFormWrapper = Form.create({})(CompanyByNameForm);

export default CompanyByNameFormWrapper;
