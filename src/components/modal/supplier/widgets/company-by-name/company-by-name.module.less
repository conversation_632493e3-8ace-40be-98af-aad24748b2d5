.container {
  .groupSelect {
    flex: 1; 

    :global {
      .ant-select-selection__rendered,
      .ant-select-selection--single {
        height: 40px;
        line-height: 38px;
      }
    }
  }

  .addBtn{
    margin-left: 9px;
    height: 40px;
    line-height: 40px;
    padding: 0 16px;

    &:global(.ant-btn-background-ghost.ant-btn-primary) {
      background-color: #F8FBFE !important;
    }

    &:global(.ant-btn-background-ghost.ant-btn-primary:hover) {
      background-color: #EBF6FF !important;
      color: #128BED;
    }

    &:global(.ant-btn-background-ghost.ant-btn-primary[disabled]:hover) {
      background-color: #f5f5f5 !important;
      color: #999;
    }
  }

  .uploadWrapper {
    background: transparent;
    padding: 0;
    border-radius: 0;

    :global {
      .ant-upload.ant-upload-drag {
        background: #FAFAFA !important;

        &:hover {
          background: #F2F8FE !important;
          border-color: #128BED;
        }
      }
    }
  }

  :global {
    .ant-form-item-control-wrapper {
      //overflow: hidden;
      max-width: 490px;
    }

    .ant-select-disabled {
      .ant-select-selection-selected-value,
      .ant-select-selection:hover {
        border-color: #d8d8d8;
        color: #999;

        .ant-select-arrow {
          color: #999;
        }
      }
    }
  }
}