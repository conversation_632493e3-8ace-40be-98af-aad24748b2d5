import { Wrapper, mount } from '@vue/test-utils';
import AddCompanyModal from '@/components/modal/supplier/add-company-modal/add-company-modal';
import QModal from '@/components/global/q-modal/q-modal';
import QTabs from '@/components/global/q-tabs';

describe('AddCompanyModal', () => {
  let wrapper: Wrapper<any>;

  beforeEach(() => {
    wrapper = mount(AddCompanyModal, {
      propsData: {},
    });
  });

  test('renders correctly with default props', async () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findComponent(QModal).exists()).toBe(true);
    expect(wrapper.findComponent(QTabs).exists()).toBe(true);
  });

  test('handles tab change', async () => {
    const tabComponent = wrapper.findComponent(QTabs);

    wrapper.setData({ selectedValues: [{ companyId: '123', companyName: 'Test Company' }] });
    await tabComponent.vm.$emit('change', 'partner');

    expect(wrapper.vm.selectedValues).toEqual([]);
  });

  test('adds values correctly when selecting from partners', () => {
    const values = [{ companyId: '123', companyName: 'Test Company' }];

    wrapper.vm.handleSelectFromPartners(values);
    expect(wrapper.vm.selectedValues).toEqual(values);
  });

  test('handles onCancel action', async () => {
    wrapper.vm.onCancel();
    expect(wrapper.vm.visible).toBe(false);
  });

  test('handles the currentTab change', async () => {
    wrapper.setData({ currentTab: 'company' });
    await wrapper.vm.handleTabChange();
    expect(wrapper.vm.selectedValues).toEqual([]);
  });
});
