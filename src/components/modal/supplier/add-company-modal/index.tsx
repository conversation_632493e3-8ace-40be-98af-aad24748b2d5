import { defineComponent } from 'vue';
import { Button, message } from 'ant-design-vue';

import { monitor } from '@/shared/services';

import { openAddCompanyModal } from './hooks';
import { useRouter } from 'vue-router/composables';

const AddCompany = defineComponent({
  name: 'AddCompany',
  props: {
    monitorGroupId: {
      type: [String, Number],
      default: undefined,
    },
  },
  setup(props, { emit }) {
    const router = useRouter();
    /** 添加公司 */
    const handleAddCompanies = async (newCompanies: { companyId: string; companyName: string; monitorGroupId: number }[]) => {
      const { failList } = await monitor.addCompanyToGroup({
        monitorGroupId: newCompanies[0].monitorGroupId,
        items: newCompanies,
      });
      if (failList?.length) {
        const companyNames = newCompanies.map((item) => item.companyName).join('、');
        return message.error(`添加失败，${companyNames}已经存在列表中`);
      }
      message.success('添加成功');
      return emit('refresh');
    };
    /**
     * 添加企业
     */
    const handleAddCompany = async () => {
      // 导航有id先选Id，没有再取默认分组
      const defaultGroupId = props.monitorGroupId;
      const params = {
        originData: {
          monitorGroupId: defaultGroupId,
        },
      };
      const res = await openAddCompanyModal(params);
      const { groupsChange, newCompanies, batchId, groupId } = res as any;

      if (batchId) {
        router.push({
          name: 'risk-monitor-upload-confirm',
          query: { batchId, groupId },
        });
        return;
      }

      if (newCompanies?.length) {
        return handleAddCompanies(newCompanies);
      }
      if (groupsChange) {
        emit('refresh');
      }
      return null;
    };
    return {
      handleAddCompany,
    };
  },
  render() {
    return (
      <div onClick={this.handleAddCompany}>
        {this.$slots.default ? (
          this.$slots.default
        ) : (
          <Button icon="plus-circle" type="primary">
            添加企业
          </Button>
        )}
      </div>
    );
  },
});

export default AddCompany;
