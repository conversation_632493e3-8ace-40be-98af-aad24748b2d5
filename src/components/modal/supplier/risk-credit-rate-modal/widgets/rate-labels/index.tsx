import { defineComponent, PropType } from 'vue';

import styles from './rate-labels.module.less';

const CreditRateLabels = defineComponent({
  functional: true,
  props: {
    type: {
      type: String as PropType<'good' | 'bad' | 'prompt'>,
      required: true,
    },
    values: {
      type: Array as PropType<string[]>,
      required: true,
    },
  },
  render(h, { props }) {
    return props.values.map((tag, index) => (
      <span
        key={`${index}-${tag}`}
        class={{
          [styles.label]: true,
          [styles[props.type]]: true,
        }}
      >
        {tag}
      </span>
    ));
  },
});

export default CreditRateLabels;
