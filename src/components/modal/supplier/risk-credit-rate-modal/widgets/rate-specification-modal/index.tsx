import { computed, defineComponent } from 'vue';

import QModal from '@/components/global/q-modal';
import { dateFormat } from '@/utils/format';

import styles from './rate-specification-modal.module.less';
import RateDetailSummary from '../rate-detail-summary';
import RateDetailSpecification from '../rate-detail-specification';
import { getScoreLevelStyle } from '../rate-gauge/chart/utils';

const RateSpecificationModal = defineComponent({
  name: 'RateSpecificationModal',
  props: {
    /**
     * 分数值
     */
    scoreInfo: {
      type: Object,
      default: () => ({}),
    },
    /**
     * 标题
     */
    title: {
      type: String,
      default: '分数解读',
    },
    /**
     * 是否显示
     */
    visible: {
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗宽度
     */
    width: {
      type: Number,
      default: 648,
    },
    /**
     * 描述
     */
    description: {
      type: String,
      default: `企查分是基于公开信息，通过机器学习算法及大数据模型综合计算得出的分值，随主体公开信息变化而相应变化；具体分值仅供参考，仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。保持良好的信用行为有助于分数提升。主要评估方法和参考标准如下。`,
    },
  },
  model: {
    prop: 'visible',
    event: 'change',
  },
  emits: ['change'],
  setup(props, { emit }) {
    const currentScoreLevelStyle = computed(() => {
      return getScoreLevelStyle(props.scoreInfo?.Score);
    });
    const handleCancel = () => {
      emit('change', false);
    };
    return {
      currentScoreLevelStyle,
      handleCancel,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            title: this.title,
            visible: this.visible,
            destroyOnClose: true,
            size: this.width,
            footer: null,
            viewportDistance: 50 + 53 + 63, // top 50 + header 53 + bottom 63
          },
          on: {
            cancel: this.handleCancel,
          },
        }}
      >
        <div class={styles.container}>
          <div class={styles.rate}>
            {/* Score */}
            <div class={styles.score}>
              <i>{this.scoreInfo?.Score}</i>
              <sub>分</sub>
            </div>
            {/* Level */}
            <div class={styles.info}>
              <div
                class={styles.level}
                style={{
                  color: this.currentScoreLevelStyle.color,
                }}
              >
                {this.scoreInfo?.ScoreLevel}
              </div>
              <div class={styles.date}>{dateFormat(this.scoreInfo?.UpdateDate, { pattern: 'YYYY年MM月DD日 更新' })}</div>
            </div>
          </div>

          {/* Summary */}
          <div class={[styles.section, styles.summary]}>
            <div class={styles.description}>{this.description}</div>
            <RateDetailSummary />
          </div>

          <div class={styles.divider} />

          {/* Specification */}
          <div class={[styles.section, styles.specification]}>
            <RateDetailSpecification />
          </div>
        </div>
      </QModal>
    );
  },
});

export default RateSpecificationModal;
