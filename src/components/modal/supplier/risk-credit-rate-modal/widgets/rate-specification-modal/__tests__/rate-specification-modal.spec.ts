import { shallowMount } from '@vue/test-utils';

import RateSpecificationModal from '..';

describe('RateSpecificationModal', () => {
  it('渲染正常的分数信息', async () => {
    const scoreInfo = {
      Score: 750,
      ScoreLevel: 'L-9',
      UpdateDate: '2023-10-01',
    };
    const wrapper = shallowMount(RateSpecificationModal, {
      propsData: {
        scoreInfo,
        visible: true,
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.html()).toMatchSnapshot();
  });
});
