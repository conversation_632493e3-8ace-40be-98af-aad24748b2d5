@import '@/styles/token.less';

.container {
  .summary {
    padding-top: 10px;
    padding-bottom: 15px;
  }

  .specification {
    padding-top: 15px;
  }

  .description {
    margin-bottom: 10px;
  }

  .divider {
    height: 8px;
    background-color: #f3f3f3;
    margin: 0 -15px;
  }

  // 分数
  .rate {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    .score {
      display: flex;
      align-items: center;

      i {
        margin-right: 4px;
        font-family: D-Din, sans-serif;
        font-size: 36px;
        line-height: 39px;
      }

      sub {
        font-size: 14px;
        bottom: unset;
        line-height: unset;
      }
    }

    .info {
      text-align: right;

      .level {
        font-size: 16px;
        line-height: 24px;
        font-weight: bold;
      }

      .date {
        font-size: 13px;
        color: @qcc-color-black-300;
      }
    }
  }
}
