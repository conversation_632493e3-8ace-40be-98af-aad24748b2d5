@import '@/styles/token.less';

.container {
  .tabs {
    border-bottom: 1px solid @qcc-color-gray-500;
  }

  .dimension {
    margin-top: 15px;
  }

  .title {
    line-height: 22px;
    font-weight: 700;
  }

  .list {
    margin-top: 5px;
    display: flex;
    gap: 10px;
  }

  .block {
    flex: 1;
    min-width: 10px;
    padding: 10px;
    background-color: #f7f7f7;
    border-radius: 4px;
    font-size: 14px;
    position: relative;
  }

  .name {
    font-weight: 700;
    margin-bottom: 2px;
  }

  .description {
    color: #808080;
  }

  .tag {
    position: absolute;
    top: 0;
    right: 0;
    width: 28px;
    font-size: 12px;
    text-align: center;
    line-height: 14px;
    color: #fff;
    background-color: #999;
    border-radius: 0 4px;

    i {
      transform: scale(0.83);
      display: block;
    }

    &.positive {
      background: @qcc-color-blue-500;
    }

    &.negative {
      background: #ff6060;
    }
  }
}
