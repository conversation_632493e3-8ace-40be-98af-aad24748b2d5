import 'echarts/lib/chart/radar';
import 'echarts/lib/component/title';
import { sortBy } from 'lodash';
import { computed, defineComponent, PropType } from 'vue';
import ECharts from 'vue-echarts';

import { getStarsByScore } from '../../utils/get-stars-by-score';
import IconStartEmpty from './assets/images/icon-star-empty.png';
import IconStarFull from './assets/images/icon-star-full.png';
import IconStartHalf from './assets/images/icon-star-half.png';
import styles from './rate-radar.module.less';

function getRadarChartOption(indicator, data) {
  const chartOption = {
    /** 数据 */
    series: [
      {
        type: 'radar',
        /** 数据 */
        data: [{ value: data }],
        /** 区域样式 */
        areaStyle: {
          // 径向渐变
          color: {
            x: 0.5,
            y: 0.5,
            r: 0.5,
            colorStops: [
              { offset: 0, color: 'rgba(18, 139, 237, 0.1)' },
              { offset: 1, color: 'rgba(18, 139, 237, 0.2)' },
            ],
          },
        },
        /** 区域连线样式 */
        lineStyle: {
          type: 'solid',
          width: 2,
          color: '#88C5F6',
        },
        /** 区域连接点 */
        symbol: 'none',
      },
    ],

    /** 雷达图 */
    radar: {
      /** 雷达指标 */
      indicator,

      /** 静默无交互 */
      silent: true,
      triggerEvent: false,

      name: {
        show: true,
        formatter(name: string, { score }) {
          const stars = getStarsByScore(score)
            .map((n) => {
              const map = {
                '0': 'iconEmpty',
                '0.5': 'iconHalf',
                '1': 'iconFull',
              };
              return `{${map[n]}|}`;
            })
            .join('');
          return `{text|${name}}\n${stars}`;
        },
        // textStyle
        rich: {
          text: {
            fontSize: 14,
            lineHeight: 22,
            color: '#333',
          },
          // 星标
          iconFull: {
            width: 10,
            height: 10,
            align: 'center',
            backgroundColor: {
              image: IconStarFull,
            },
          },
          // 空星标
          iconEmpty: {
            width: 10,
            height: 10,
            align: 'center',
            backgroundColor: {
              image: IconStartEmpty,
            },
          },
          // 半星标
          iconHalf: {
            width: 10,
            height: 10,
            align: 'center',
            backgroundColor: {
              image: IconStartHalf,
            },
          },
        },
      },

      /** 设置刻度文本 label */
      nameGap: 15,

      center: ['50%', '55%'],
      radius: '50%',
      /** 形状 */
      shape: 'polygon',
      /** 刻度分割线（中轴线） */
      axisLine: {
        show: true,
        symbol: ['none', 'circle'],
        symbolSize: [10, 10],
        lineStyle: {
          color: '#88C5F6',
          type: 'dashed',
        },
      },
      /** 背景切分样式 */
      splitArea: {
        show: true,
        areaStyle: {
          color: undefined, // 透明背景
        },
      },
      /** 背景切分线样式 */
      splitLine: {
        show: true,
        lineStyle: {
          width: 2,
          color: '#e2f1fc',
        },
      },
    },

    /** Theme */
    color: [
      '#5b8ff9',
      '#61ddaa',
      '#65789b',
      '#f6bd16',
      '#7262fd',
      '#78d3f8',
      '#9661bc',
      '#f6903d',
      '#008685',
      '#f08bb4',
      '#cdddfd',
      '#cdf3e4',
      '#65789b',
      '#fcebb9',
      '#d3cefd',
      '#d3eef9',
      '#decfea',
      '#ffe0c7',
      '#bbdede',
      '#ffe0ed',
    ],
  };
  return Object.freeze(chartOption);
}

/**
 * 信用分雷达图
 */
const CreditRateRadar = defineComponent({
  name: 'CreditRateRadar',
  props: {
    dimensions: {
      type: Array as PropType<
        {
          TypeDesc: string;
          Type: string;
          Score: number;
          ScoreDesc: string;
        }[]
      >,
      default: () => [],
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  setup(props) {
    const chartOptions = computed(() => {
      const minScore = 0; // 最小分值
      const maxScore = 100; // 最大分值
      const indicatorOrder = ['QYBJ', 'LYLS', 'ZZPJ', 'JYZK', 'GLSP']; // 排序

      let indicator = props.dimensions.map(({ TypeDesc, Type, Score, ScoreDesc }) => {
        return {
          min: minScore,
          max: maxScore,
          name: TypeDesc,
          // Extra
          code: Type, // order
          score: Score, // score
          scoreDesc: ScoreDesc,
        };
      });
      indicator = sortBy(indicator, ({ code }) => indicatorOrder.indexOf(code));
      const data = indicator.map(({ score }) => score);
      return getRadarChartOption(indicator, data);
    });
    return {
      chartOptions,
    };
  },
  render() {
    const { width, height, chartOptions } = this;
    return (
      <div
        class={styles.container}
        style={{
          width,
          height,
        }}
      >
        <ECharts options={chartOptions} />
      </div>
    );
  },
});

export default CreditRateRadar;
