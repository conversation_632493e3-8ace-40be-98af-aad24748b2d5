import { defineComponent } from 'vue';

import RatingStarIcon from '@/components/rating-start-icon';

import { getStarsByScore } from '../../utils/get-stars-by-score';

/**
 * 获取颜色
 */
function getColorscheme(value: number) {
  return {
    half: value === 0.5,
    color: value === 0 ? '#e3e3e3' : '#128bed',
    background: '#e3e3e3',
  };
}

const CreditRateStars = defineComponent({
  functional: true,
  props: {
    score: {
      type: Number,
      default: 0,
    },
    color: {
      type: String,
      default: '#128bed',
    },
    background: {
      type: String,
      default: '#e3e3e3',
    },
  },
  render(h, { props }) {
    const stars = getStarsByScore(props.score);
    return stars.map((value, index) => {
      const { half, color, background } = getColorscheme(value);
      return <RatingStarIcon half={half} color={color} background={background} key={index} />;
    });
  },
});

export default CreditRateStars;
