import moment from 'moment';
import { defineComponent, onMounted, ref } from 'vue';

import { CreditScoreChart } from './chart';
import { getScoreLevelStyle } from './chart/utils';

/**
 * 分数
 */
const CreditRateGauge = defineComponent({
  name: 'CreditRateGauge',
  props: {
    /**
     * 分数值
     */
    scoreInfo: {
      type: Object,
      default: () => ({}),
    },
    /**
     * width
     */
    width: {
      type: String,
      default: '100%',
    },
    /**
     * height
     */
    height: {
      type: String,
      default: '100%',
    },
  },
  setup({ scoreInfo }) {
    const container = ref<HTMLElement>();

    onMounted(() => {
      /** 更新日期 */
      const updateDate = scoreInfo.UpdateDate ? moment.unix(scoreInfo.UpdateDate).format('YYYY年MM月DD日 更新') : '-';
      const chart = new CreditScoreChart(container.value as HTMLElement, {
        actualScore: scoreInfo.Score,
        /** 圆环内文字：从上往下排列 */
        ringTextList: [
          /** 第一行 */
          {
            type: 'multiSize',
            offset: {
              y: -55,
            },
            textList: [
              {
                text: scoreInfo.Score,
                fontWeight: 'normal',
                fontSize: 28,
                fontFamily: 'D-DIN',
                color: '#333333',
                hoverColor: '#128bed',
                lineHeight: 1.2857,
                offsetX: 2,
              },
              {
                text: '分',
                fontWeight: 'normal',
                fontSize: 16,
                fontFamily: 'PingFang SC',
                color: '#333333',
                hoverColor: '#128bed',
                lineHeight: 1.375,
                offsetX: 2,
                offsetY: 3,
              },
            ],
          },
          /** 第二行 */
          {
            offset: {
              y: -27,
            },
            text: `${scoreInfo.ScoreLevel} ${scoreInfo.ScoreDesc}`,
            fontSize: 20,
            color: getScoreLevelStyle(scoreInfo.Score).color,
            lineHeight: 1.4,
          },
          /** 第三行 */
          {
            offset: {
              y: 0,
            },
            text: updateDate,
            fontSize: 14,
            lineHeight: 1.5714,
            color: '#999999',
            fontWeight: 'normal',
          },
        ],
      });
      chart.drawBaseMap();
    });

    return {
      container,
    };
  },
  render() {
    return (
      <div
        style={{
          width: this.width,
          height: this.height,
        }}
        ref="container"
      ></div>
    );
  },
});

export default CreditRateGauge;
