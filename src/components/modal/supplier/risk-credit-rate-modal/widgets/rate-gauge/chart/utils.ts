/**
 * 获取弧度
 *
 * @param   {number}  degrees  [传入角度]
 *
 * @return  {number}           [返回弧长]
 */
export function getRadian(degrees: number) {
  return (Math.PI / 180) * degrees;
}

/**
 * 获取度数
 *
 * @param   {number}  radian  [传入弧长]
 *
 * @return  {number}          [返回度数]
 */
export function getDegrees(radian: number) {
  return (180 / Math.PI) * radian;
}

// 获取圆边上点的坐标
export function getRadiusPoint(x, y, radius, degrees) {
  return {
    x1: x + radius * Math.cos(degrees),
    y1: y + radius * Math.sin(degrees),
  };
}

/**
 * [isObject 判断是否为一个对象]
 *
 * @param   {[type]}  item  [传入的值]
 *
 * @return  {[type]}        [返回布尔值]
 */
export function isObject(item) {
  if (!item) {
    return false;
  }
  return typeof item === 'object' && !Array.isArray(item);
}

/**
 * [mergeDeep description]
 *
 * @param   {[type]}  target:      [target: 被合并的对象]
 * @param   {[type]}  ...sources:  [...sources: 合并的对象]
 *
 * @return  {any}                  [return 深合并后的对象]
 */
export function mergeDeep(target, ...sources) {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach((key) => {
      if (isObject(source[key])) {
        if (!target[key]) {
          Object.assign(target, { [key]: {} });
        }
        mergeDeep(target[key], source[key]);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    });
  }

  return mergeDeep(target, ...sources);
}

/**
 * 根据分数自动定义信用等级
 */
export function getScoreLevelStyle(actualScore): {
  color: string;
  rgbColorArr: number[];
  rgbColor: string;
} {
  const score = actualScore;
  const styleObj = {} as {
    color: string;
    rgbColorArr: number[];
    rgbColor: string;
  };
  if (score < 500) {
    styleObj.color = '#ff8900';
    styleObj.rgbColorArr = [255, 137, 0];
    styleObj.rgbColor = styleObj.rgbColorArr.join(', ');
  } else if (score < 750) {
    styleObj.color = '#128bed';
    styleObj.rgbColorArr = [18, 139, 237];
    styleObj.rgbColor = styleObj.rgbColorArr.join(', ');
  } else {
    styleObj.color = '#00ad65';
    styleObj.rgbColorArr = [0, 173, 101];
    styleObj.rgbColor = styleObj.rgbColorArr.join(', ');
  }

  return styleObj;
}
