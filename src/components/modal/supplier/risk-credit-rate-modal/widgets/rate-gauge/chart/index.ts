import { cloneDeep, get, isArray, isEmpty, noop, sum } from 'lodash';

import { getRadian, getRadiusPoint, getScoreLevelStyle, mergeDeep } from './utils';
import creditScoreArrowImg from '../images/score-chart-arrow.png';
import creditScorePointerImgBlue from '../images/score-chart-move-pointer-blue.png';
import creditScorePointerImgGreen from '../images/score-chart-move-pointer-green.png';
import creditScorePointerImgOrange from '../images/score-chart-move-pointer-orange.png';

export class CreditScoreChart {
  canvas: HTMLCanvasElement;

  ctx: CanvasRenderingContext2D;

  ratio: number;

  pointImgOrange: HTMLImageElement;

  pointImgBlue: HTMLImageElement;

  pointImgGreen: HTMLImageElement;

  arrowImg: HTMLImageElement;

  isDrawComplete: boolean;

  el: HTMLElement;

  cacheParams: Record<string, any>;

  options: any;

  constructor(el: HTMLElement, options = {}) {
    /** 创建canvas */
    this.canvas = document.createElement('canvas');
    /** 生成上下文 */
    this.ctx = this.canvas.getContext('2d') as CanvasRenderingContext2D;
    /** 像素比 */
    this.ratio = window.devicePixelRatio;
    /** 新建图片实例 */
    this.pointImgOrange = new Image();
    this.pointImgOrange.src = creditScorePointerImgOrange;
    this.pointImgBlue = new Image();
    this.pointImgBlue.src = creditScorePointerImgBlue;
    this.pointImgGreen = new Image();
    this.pointImgGreen.src = creditScorePointerImgGreen;
    this.arrowImg = new Image();
    this.arrowImg.src = creditScoreArrowImg;
    this.isDrawComplete = false;
    /** 获取canvas父元素节点 */
    this.el = el;
    /** 初始化缓存配置项 */
    this.cacheParams = {};
    /** 合并配置项 */
    this.options = mergeDeep(
      {
        x: 0,
        y: 0,
        radius: 112.5,
        /** 画布的起点 */
        startAngle: 180,
        /** 画布的结束点 */
        endAngle: 360,
        /** 当前的角度 */
        currentAngle: 180,
        /** 起步分 */
        scoreStart: 0,
        /** 展示得分 */
        scoreTarget: 0,
        /** 实际得分 */
        actualScore: 0,
        /** 最低分 */
        scoreMin: 0,
        /** 最高分 */
        scoreMax: 2000,
        /** 信用等级文字 */
        scoreLevelText: '',
        /** 总角度分成几等分, */
        segAngle: 20,
        style: {
          bgColor: 'transparent',
          ring: {
            initColor: '#E3E3E3',
            activeColor: [
              [0, '#E2F1FC'],
              [0.55, '#128BED'],
              [1, '#1660D4'],
            ],
            width: 20,
          },
        },
      },
      options
    );
    this.initElm();
  }

  initElm() {
    if (this.el) {
      this.el.appendChild(this.canvas);
      /** 获取canvas绘制区域宽高 */
      const { width, height } = this.el.getBoundingClientRect();
      this.options = {
        ...this.options,
        /** 水平方向剧中 */
        x: width / 2,
        /** 设置圆点到底部距离 */
        y: height - 9,
        scoreTarget: this.calculateScore(this.options.actualScore || 0),
      };
      this.fixRangeValue(this.options);
      // 缓存实例初始化的参数
      this.cacheParams = this.options;

      // eslint-disable-next-line no-console
      console.log('options computed ==>', this.options);
      this.canvasSize(width, height);
      this.init();
    } else {
      throw new Error('请传入一个渲染区域的ID');
    }
  }

  /**
   * 由于刻度是均等分，产品要求不需要均等分，着重突出200-1000分段，通过公式去计算目标分
   *
   * @param score @type number
   * @returns number
   */
  calculateScore(score) {
    if (score >= 0 && score <= 200) {
      return (score / (200 * 8)) * 2000;
    }
    if (score <= 1000) {
      return 2000 / 8 + ((score - 200) / 800) * (3 / 4) * 2000;
    }
    if (score < 2000) {
      return (2000 * 7) / 8 + ((score - 1000) / 1000) * (1 / 8) * 2000;
    }
    return 2000;
  }

  /**
   * 自动修正范围，比如传入的最小值大于最大值，起始分和起始角度同步最小分和最小起始角度
   */
  fixRangeValue(options) {
    this.options = {
      ...options,
      /** 重置开始计算的分很为最小分 */
      scoreStart: this.options.scoreMin,
      scoreTarget: this.options.scoreTarget > this.options.scoreMax ? this.options.scoreMax : this.options.scoreTarget,
      currentAngle: this.options.startAngle,
    };
  }

  /** 初始化canvas的一些参数 */
  init(options = {}) {
    this.options = mergeDeep(this.options, options);
    this.fixRangeValue(this.options);
  }

  /** 设置画布大小：根据父元素宽高绘制 */
  canvasSize(width = 375, height = 375) {
    this.canvas.width = width * this.ratio;
    this.canvas.height = height * this.ratio;
    this.canvas.style.width = `${width}px`;
    this.canvas.style.height = `${height}px`;
    this.canvas.style.display = 'block';
    this.canvas.style.margin = '0 auto';
    this.canvas.style.backgroundColor = this.options.style.bgColor;
    this.canvas.style.borderRadius = '4px';
    this.ctx.scale(this.ratio, this.ratio);
    /** 设置默认样式 */
    this.ctx.font = 'normal 14px/1.4 PingFang SC';
    this.ctx.fillStyle = '#E3E3E3';
  }

  /**
   * 角度计算
   *
   * @param endAngle 结束的角度
   * @param startAngle 开始的角度
   * @param segAngle 拆成多少等分
   */
  stepAngleCalc(endAngle, startAngle, segAngle) {
    return (endAngle - startAngle) / segAngle;
  }

  /**
   * 清空画布
   *
   * @param   {number}  width   [清除的宽度]
   * @param   {number}  height  [清除的高度]
   *
   * @return  {[type]}          [没有返回]
   */
  clearCanvas(width = 0, height = 0) {
    this.ctx.clearRect(0, 0, width, height);
  }

  /**
   *
   * @param 线条旋转这些
   * @param 圆的三要素,x,y,半径
   */
  drawCircleLine(
    { color = '#E3E3E3' },
    {
      x = 0,
      y = 0,
      radius = 0,
      startAngle,
      endAngle,
    }: {
      x: number;
      y: number;
      radius: number;
      startAngle?: number;
      endAngle?: number;
    }
  ) {
    /** 开始创建 */
    this.ctx.beginPath();
    /** 画圆弧 */
    this.ctx.arc(x, y, radius, getRadian(startAngle || this.options.startAngle), getRadian(endAngle || this.options.endAngle));
    let gradient;
    if (isArray(color)) {
      gradient = this.ctx.createLinearGradient(
        getRadiusPoint(x, y, radius - 10, this.options.endAngle).x1,
        getRadiusPoint(x, y, radius - 10, this.options.endAngle).y1,
        getRadiusPoint(x, y, radius - 10, 0).x1,
        getRadiusPoint(x, y, radius - 10, 0).y1
      );
      color.forEach((el) => {
        if (isArray(el)) {
          gradient.addColorStop(el[0], el[1]);
        }
      });
    }
    /** 设置画笔路径颜色 */
    this.ctx.strokeStyle = gradient || color;
    /** 设置画笔风格 */
    this.ctx.lineCap = 'butt';
    /** 设置画笔宽度 */
    this.ctx.lineWidth = 20;
    /** 结束绘制 */
    this.ctx.stroke();
  }

  setTextFont(target) {
    const setLineHeight = (t) => (get(t, 'lineHeight') ? `/${get(t, 'lineHeight')}` : '');
    return `${get(target, 'fontWeight', 'bold')} ${get(target, 'fontSize', 14)}px${setLineHeight(target)} ${get(
      target,
      'fontFamily',
      'PingFang SC'
    )}`;
  }

  /**
   *
   * @param x 坐标的x
   * @param y 坐标的y
   * @param text 展示的文本
   * @param fontSize 文本的大小
   * @param color 文本的颜色
   * @param fontWeight 文本的粗细
   */
  drawInnerText(x, y, text, fontSize = 30, color = '#333333', fontWeight = 'bold', lineHeight = 1.4) {
    this.ctx.save();
    this.ctx.fillStyle = color;
    this.ctx.font = this.setTextFont({
      fontSize,
      fontWeight,
      lineHeight,
    });
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${text}`, x, y);
    this.ctx.textBaseline = 'bottom';
    this.ctx.restore();
  }

  /**
   * 适用于同一行两种不同大小字体展示
   * 通过计算宽度来实现居中
   */
  drawMultiSizeText(x, y, textList) {
    this.ctx.save();
    this.ctx.textAlign = 'start';
    const innerText = cloneDeep(textList);

    /** 获取每一个原色宽度 */
    innerText.forEach((el) => {
      this.ctx.fillStyle = el.color;
      this.ctx.font = this.setTextFont(el);
      el.width = this.ctx.measureText(`${el.text}`).width;
    });

    /** 箭头宽度 */
    const arrowWidth = 10;
    /** 所有字体和箭头的宽度集合 */
    const widthArr = [...innerText.map((el) => el.width + (el.offsetX || 0)), arrowWidth];
    const half = sum(widthArr) / 2;
    /** 动态规划不同字体和箭头的位置 */
    widthArr.forEach((el, index) => {
      let prev = 0;
      for (let i = 0; i < index; i++) {
        /** 累加前面元素的宽度 */
        prev += innerText[i].width + (innerText[i].offsetX || 0);
      }
      if (index < widthArr.length - 1) {
        innerText[index].posiX = prev - half;
      }
    });
    /** 计算文本位置绘制 */
    innerText.forEach((el) => {
      this.ctx.fillStyle = el.color;
      this.ctx.font = this.setTextFont(el);
      this.ctx.fillText(`${el.text}`, x + el.posiX, y - (el.offsetY || 0));
    });
    this.ctx.textBaseline = 'ideographic';

    // FIXME: 临时隐藏分数箭头，允许点击时再放开
    // this.ctx.drawImage(this.arrowImg, x + arrowPosiX, y - 14, arrowWidth, 10);
    this.ctx.restore();
  }

  /**
   * 画移动指针
   *
   * @param x 坐标x
   * @param y 坐标y
   * @param rotate 偏移的角度
   */
  drawPosiPointer(x, y, rotate) {
    let pointerImage;
    if (this.options.actualScore < 500) {
      pointerImage = this.pointImgOrange;
    } else if (this.options.actualScore < 750) {
      pointerImage = this.pointImgBlue;
    } else {
      pointerImage = this.pointImgGreen;
    }
    this.ctx.save();
    this.ctx.translate(x, y);
    this.ctx.rotate(getRadian(rotate));
    this.ctx.drawImage(pointerImage, -5, 3, 10, 14);
    this.ctx.restore();
  }

  /**
   * 移动追踪指针
   *
   * @param x 坐标x
   * @param y 坐标y
   * @param radius 半径
   * @param angle 移动的角度
   */
  movePosiPointer(x, y, radius, angle) {
    const { x1, y1 } = getRadiusPoint(x, y, radius, getRadian(angle));
    /**
     * Q: 为什么要加90°?
     * A: 因为图片默认是垂直的，我们要扭正他，从坐标系的初始值开始
     */
    this.drawPosiPointer(x1, y1, angle + 90);
  }

  // 获取范围
  /**
   * @param type 'score'|'angel'
   */
  getNumberRange(type) {
    if (type === 'score') {
      return this.options.scoreMax - this.options.scoreMin;
    }
    if (type === 'angel') {
      return this.options.endAngle - this.options.startAngle;
    }
    return 0;
  }

  /** 绘制画布 */
  // eslint-disable-next-line consistent-return
  drawBaseMap() {
    /** 清空画布 */
    this.clearCanvas(this.canvas.width, this.canvas.height);

    /** 画背景半圆环 */
    this.drawCircleLine(
      {
        color: this.options.style.ring.initColor,
      },
      {
        x: this.options.x,
        y: this.options.y,
        radius: this.options.radius,
      }
    );

    /**
     * 圆环内部文字
     */
    if (this.options.ringTextList?.length) {
      this.options.ringTextList.forEach((textObj) => {
        const x = this.options.x + (textObj?.offset?.x || 0);
        const y = this.options.y + (textObj?.offset?.y || 0);
        if (textObj.type === 'multiSize') {
          this.drawMultiSizeText(x, y, textObj.textList);
        } else {
          this.drawInnerText(x, y, textObj.text, textObj.fontSize, textObj.color, textObj.fontWeight);
        }
      });
    }

    /** 画高亮半圆环 */
    /** 获取不同分数的颜色配置 */
    const styleObj = getScoreLevelStyle(this.options.actualScore);
    /** 获取展示位置所占比例 */
    const endPoint = this.calculateScore(this.options.actualScore) / 2000;
    /** 重新赋值高亮颜色 */
    this.options.style.ring.activeColor = [
      [0, `rgb(${styleObj.rgbColorArr.join(' ')} / 30%)`],
      [0.55 * endPoint, `rgb(${styleObj.rgbColorArr.join(' ')} / 50%)`],
      [endPoint, `rgb(${styleObj.rgbColorArr.join(' ')} / 100%)`],
    ];
    this.drawCircleLine(
      {
        color: this.options.style.ring.activeColor,
      },
      {
        x: this.options.x,
        y: this.options.y,
        radius: this.options.radius,
        startAngle: this.options.startAngle,
        endAngle: this.options.currentAngle,
      }
    );

    /** 移动标记 */
    this.movePosiPointer(this.options.x, this.options.y, this.options.radius - 10, this.options.currentAngle);

    /** 范围内无限render */
    if (this.options.scoreStart < this.options.scoreTarget) {
      const stepMoveAngle = this.stepAngleCalc(this.options.endAngle, this.options.startAngle, this.options.segAngle);
      // 每次移动角度的度数范围
      this.options.currentAngle += stepMoveAngle;

      // 文字变化
      // 求出每次移动角度的度数范围要走多少分数
      const stepScore = (this.getNumberRange('score') * stepMoveAngle) / this.getNumberRange('angel');
      this.options.scoreStart += Math.round(stepScore);
      if (this.options.scoreStart >= this.options.scoreTarget) {
        this.options.scoreStart = this.options.scoreTarget;
      }
      // 求当前角度与分数角度的比较，当前累计的角度小于需要移动到目的地的角度就继续渲染
      const stepAngle =
        this.options.startAngle +
        (this.getNumberRange('score') * (this.options.scoreTarget - this.options.scoreMin)) / this.getNumberRange('score');

      if (this.options.currentAngle >= stepAngle) {
        this.isDrawComplete = true;
        this.ctx.save();
        return false;
      }
      window.requestAnimationFrame(this.drawBaseMap.bind(this));
    }
  }

  /**
   * 重新渲染画布
   * @param   {[type]}  delay      [delay 推迟几秒重新渲染]
   * @param   {[type]}  callback?  [callback? 传入的回调函数]
   */
  refresh(delay = 1000, callback = noop) {
    const st = setTimeout(() => {
      clearTimeout(st);
      if (this.isDrawComplete) {
        this.isDrawComplete = false;
      }
      this.init(this.cacheParams);
      this.drawBaseMap();
      if (!isEmpty(callback)) callback();
    }, delay);
  }
}
