import { getDegrees, getRadian, getRadiusPoint, getScoreLevelStyle, isObject, mergeDeep } from '../utils';

describe('utils', () => {
  describe('getRadian', () => {
    test('should convert degrees to radians', () => {
      expect(getRadian(0)).toBeCloseTo(0);
      expect(getRadian(90)).toBeCloseTo(Math.PI / 2);
      expect(getRadian(180)).toBeCloseTo(Math.PI);
      expect(getRadian(270)).toBeCloseTo((3 * Math.PI) / 2);
      expect(getRadian(360)).toBeCloseTo(2 * Math.PI);
    });
  });

  describe('getDegrees', () => {
    test('should convert radians to degrees', () => {
      expect(getDegrees(0)).toBe(0);
      expect(getDegrees(Math.PI / 2)).toBeCloseTo(90);
      expect(getDegrees(Math.PI)).toBeCloseTo(180);
      expect(getDegrees((3 * Math.PI) / 2)).toBeCloseTo(270);
      expect(getDegrees(2 * Math.PI)).toBeCloseTo(360);
    });
  });

  describe('getRadiusPoint', () => {
    test('should calculate the correct radius point', () => {
      const x = 0;
      const y = 0;
      const radius = 5;
      const degrees = 45;

      const result = getRadiusPoint(x, y, radius, degrees);

      expect(result).toEqual({
        x1: 2.626609944088649,
        y1: 4.254517622670592,
      });
    });
  });

  describe('isObject', () => {
    test('should return true for objects', () => {
      const object = { key: 'value' };
      expect(isObject(object)).toBe(true);
    });

    test('should return false for non-objects', () => {
      expect(isObject(null)).toBe(false);
      expect(isObject(undefined)).toBe(false);
      expect(isObject(123)).toBe(false);
      expect(isObject('')).toBe(false);
      expect(isObject(true)).toBe(false);
      expect(isObject([])).toBe(false);
    });

    test('should return false for arrays', () => {
      const array = [1, 2, 3];
      expect(isObject(array)).toBe(false);
    });
  });

  describe('mergeDeep', () => {
    test('should merge two objects deeply', () => {
      const obj1 = { a: 1, b: { c: 2 } };
      const obj2 = { b: { d: 3 }, e: 4 };
      const expected = { a: 1, b: { c: 2, d: 3 }, e: 4 };
      expect(mergeDeep(obj1, obj2)).toEqual(expected);
    });

    test('should merge multiple objects deeply', () => {
      const obj1 = { a: 1, b: { c: 2 } };
      const obj2 = { b: { d: 3 }, e: 4 };
      const obj3 = { f: 5 };
      const expected = { a: 1, b: { c: 2, d: 3 }, e: 4, f: 5 };
      expect(mergeDeep(obj1, obj2, obj3)).toEqual(expected);
    });

    test('should not modify the original objects', () => {
      const obj1 = { a: 1, b: { c: 2 } };
      const obj2 = { b: { d: 3 }, e: 4 };
      const expected = { a: 1, b: { c: 2, d: 3 }, e: 4 };
      mergeDeep(obj1, obj2);
      expect(obj1).not.toBe(expected);
      expect(obj2).not.toBe(expected);
    });

    test('should override nested objects with arrays', () => {
      const obj1 = { a: { b: [1, 2] } };
      const obj2 = { a: { b: [3, 4] } };
      const expected = { a: { b: [3, 4] } };
      expect(mergeDeep(obj1, obj2)).toEqual(expected);
    });

    test('should ignore non-object values', () => {
      const obj1 = { a: 1 };
      const obj2 = { b: '2' };
      const expected = { a: 1, b: '2' };
      expect(mergeDeep(obj1, obj2)).toEqual(expected);
    });

    test('should return the target if no sources are provided', () => {
      const obj = { a: 1 };
      expect(mergeDeep(obj)).toBe(obj);
    });
  });

  describe('getScoreLevelStyle', () => {
    test('should return orange color style for score less than 500', () => {
      const actualScore = 400;
      const expected = {
        color: '#ff8900',
        rgbColorArr: [255, 137, 0],
        rgbColor: '255, 137, 0',
      };
      expect(getScoreLevelStyle(actualScore)).toEqual(expected);
    });

    test('should return blue color style for score between 500 and 750', () => {
      const actualScore = 600;
      const expected = {
        color: '#128bed',
        rgbColorArr: [18, 139, 237],
        rgbColor: '18, 139, 237',
      };
      expect(getScoreLevelStyle(actualScore)).toEqual(expected);
    });

    test('should return green color style for score greater than 750', () => {
      const actualScore = 800;
      const expected = {
        color: '#00ad65',
        rgbColorArr: [0, 173, 101],
        rgbColor: '0, 173, 101',
      };
      expect(getScoreLevelStyle(actualScore)).toEqual(expected);
    });
  });
});
