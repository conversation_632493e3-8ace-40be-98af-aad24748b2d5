import _ from 'lodash';
import { computed, defineComponent, PropType, ref } from 'vue';

import QIcon from '@/components/global/q-icon';
import QPlainTable from '@/components/global/q-plain-table';

import RateSpecChart from './images/rate-specification-chart.png';
import styles from './rate-detail-summary.module.less';
import { getScoreLevelStyle } from '../rate-gauge/chart/utils';

type RateLevelDetail = {
  level: string;
  min: number;
  max: number | null;
};

const RateDetailSummary = defineComponent({
  name: 'RateDetailSummary',
  props: {
    /**
     * 风险等级映射文案
     */
    rateLevelDetails: {
      type: Array as PropType<RateLevelDetail[]>,
      default: () =>
        [
          {
            level: 'L-1',
            min: 0,
            max: 299,
          },
          {
            level: 'L-2',
            min: 300,
            max: 399,
          },
          {
            level: 'L-3',
            min: 400,
            max: 499,
          },
          {
            level: 'L-4',
            min: 500,
            max: 549,
          },
          {
            level: 'L-5',
            min: 550,
            max: 599,
          },
          {
            level: 'L-6',
            min: 600,
            max: 649,
          },
          {
            level: 'L-7',
            min: 650,
            max: 699,
          },
          {
            level: 'L-8',
            min: 700,
            max: 749,
          },
          {
            level: 'L-9',
            min: 750,
            max: 799,
          },
          {
            level: 'L-10',
            min: 800,
            max: 899,
          },
          {
            level: 'L-11',
            min: 900,
            max: 999,
          },
          {
            level: 'L-12',
            min: 1000,
            max: null,
          },
        ] satisfies RateLevelDetail[],
    },
  },
  setup(props) {
    const rateLevelChunks = computed(() => _.chunk<RateLevelDetail>(props.rateLevelDetails, 6));

    const isExpanded = ref(false);
    const handleToggleExpand = () => {
      isExpanded.value = !isExpanded.value;
    };
    return {
      isExpanded,
      handleToggleExpand,
      rateLevelChunks,
    };
  },
  render() {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.expanded]: this.isExpanded,
        }}
        onClick={this.handleToggleExpand}
      >
        {/* Chart */}
        <div class={styles.chart}>
          <img src={RateSpecChart} width="100%" />
        </div>

        <div class={styles.tables}>
          {/* Tables */}
          {this.rateLevelChunks.map((rows, tableIndex) => {
            return (
              <QPlainTable key={`table-${tableIndex}`}>
                <thead>
                  <tr>
                    <th>信用等级</th>
                    <th>分值范围</th>
                  </tr>
                </thead>
                <tbody>
                  {rows.map((row, rowIndex) => {
                    const backgroundColor = getScoreLevelStyle(row.max || row.min).color;
                    const range = row.max === null ? `${row.min}+` : `${row.min} - ${row.max}`;
                    return (
                      <tr key={`table-row-${rowIndex}`}>
                        <td>
                          <i
                            class={styles.dot}
                            style={{
                              backgroundColor,
                            }}
                          />
                          <span>{row.level}</span>
                        </td>
                        <td>{range}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </QPlainTable>
            );
          })}
        </div>

        <div class={styles.handler}>
          <QIcon type="icon-zhankai" />
        </div>
      </div>
    );
  },
});

export default RateDetailSummary;
