@import '@/styles/token.less';

.container {
  position: relative;
  background-color: #f7f7f7;
  padding: 10px 10px 0;
  cursor: pointer;
  height: 217px;
  overflow: hidden;

  .chart {
    width: 100%;
    margin-bottom: 6px;
  }

  .tables {
    display: flex;
    gap: 10px;

    .dot {
      transform: translateY(-50%);
      width: 5px;
      height: 5px;
      border-radius: 50%;
      display: inline-block;
      background: #666;
      margin-right: 5px;
    }


    table {
      background: @qcc-color-white;

      th,
      td {
        text-align: center;
      }

      th {
        padding-top: 12px;
        padding-bottom: 12px;
      }

      td {
        padding-top: 11px;
        padding-bottom: 11px;
      }
    }
  }

  &.expanded {
    height: auto;

    .handler {
      height: 22px;
      margin-top: 5px;
      background: unset;
      position: unset;

      >i {
        transform: rotate(180deg);
      }
    }
  }

  .handler {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 33px;
    background: linear-gradient(180deg, rgba(247, 247, 247, 0.5) 12%, #F7F7F7 61%);

    >i {
      font-size: 10px;
      color: #bbb;
    }
  }
}
