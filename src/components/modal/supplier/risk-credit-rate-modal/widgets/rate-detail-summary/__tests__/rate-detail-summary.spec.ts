import { mount } from '@vue/test-utils';

import RateDetailSummary from '..';

describe('RateDetailSummary', () => {
  it('渲染默认风险等级详情', () => {
    const wrapper = mount(RateDetailSummary);
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('点击后应该切换扩展状态', async () => {
    const wrapper = mount(RateDetailSummary);
    expect(wrapper.vm.isExpanded).toBe(false);
    await wrapper.trigger('click');
    expect(wrapper.vm.isExpanded).toBe(true);
  });
});
