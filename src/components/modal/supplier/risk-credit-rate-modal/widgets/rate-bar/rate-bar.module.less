@import '@/styles/token.less';

.container {
  width: 100%;

  .tick {
    display: flex;
    padding: 0 8px;
    line-height: 22px;
    margin-bottom: 5px;

    .range {
      display: flex;
      justify-content: space-between;

      &:first-child .bar:first-child,
      &:last-child .bar:last-child {
        span {
          transform: none;
        }
      }
    }

    .bar {
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      color: @qcc-color-black-400;

      span {
        transform: translateX(-50%);
      }
    }
  }

  .scale {
    background: #f3f3f3;
    border: 1px solid #eee;
    border-radius: 12px;
    padding: 0 8px;

    .inner {
      position: relative;
      display: flex;
      align-items: center;
      padding: 8px 0;
    }

    .range {
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;

      &.first {
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
      }

      &.last {
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
      }
    }

    .bar {
      height: 8px;
    }

    /** 指示器 */
    .indicator {
      position: absolute;
      width: 10px;
      height: 100%;
      border: 1px solid @qcc-color-white;
      transform: translateX(-50%);
    }

    /** 信用等级描述 */
    .description {
      color: #fff;
      position: absolute;
      font-size: 12px;
      padding: 5px 8px;
      line-height: 18px;
      font-weight: 700;
      top: calc(100% + 14px);
      background: currentcolor;
      opacity: 0.8;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
      border-radius: 2px;

      &.center {
        transform: translateX(-50%);

        &::after {
          left: 50%;
        }
      }

      &.left {
        transform: translateX(-14px);

        &::after {
          left: 14px;
        }
      }

      &.right {
        transform: translateX(calc(-100% + 14px + 10px));

        &::after {
          right: 14px;
        }
      }

      p {
        color: @qcc-color-white;
        white-space: nowrap;
      }

      // 箭头
      &::after {
        position: absolute;
        content: '';
        width: 9px;
        height: 9px;
        background: currentcolor;
        display: block;
        top: 0;
        transform: translateY(-50%) translateX(-50%) rotate(45deg);
      }
    }
  }
}
