import { mount } from '@vue/test-utils';

import CreditRateBar from '..';

describe('CreditRateBar', () => {
  it('分数为750', () => {
    const wrapper = mount(CreditRateBar, {
      propsData: {
        value: 750,
      },
    });
    expect(wrapper.vm.indicator.levelName).toBe('L-9');
    expect(wrapper.vm.indicator.color).toBe('rgba(0, 173, 101, 1)');
  });

  it('分数为负值', () => {
    const wrapper = mount(CreditRateBar, {
      propsData: {
        value: -50,
      },
    });
    expect(wrapper.vm.indicator.levelName).toBeUndefined();
  });

  it('分数超过2000', () => {
    const wrapper = mount(CreditRateBar, {
      propsData: {
        value: 2500,
      },
    });
    expect(wrapper.vm.indicator.levelName).toBeUndefined();
  });
});
