import { computed, defineComponent, type PropType } from 'vue';

import styles from './rate-bar.module.less';

const LEVEL_DIRECTION_MAP = {
  low: 'left',
  middle: 'center',
  high: 'right',
};

/**
 * 分数
 */
const CreditRateBar = defineComponent({
  name: 'CreditRateBar',
  props: {
    /**
     * 分数值
     */
    value: {
      type: Number,
      default: 0, //
    },
    /**
     * 主题颜色
     */
    theme: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({
        low: 'rgba(255, 137, 0, 1)',
        middle: 'rgba(18, 139, 237, 1)',
        high: 'rgba(0, 173, 101, 1)',
      }),
    },
    /**
     * 等级对应文案
     */
    locale: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({
        'L-1': '信用表现较差，企业内外部存在较多不确定因素，违约风险高',
        'L-2': '信用表现较差，受内外部不确定因素的影响大，违约风险较高',
        'L-3': '信用表现较差，受内外部不确定因素的影响较大，有一定的违约风险',
        'L-4': '信用表现一般，较易受不利经济环境的影响，违约风险中等',
        'L-5': '信用表现一般，受不利经济环境的影响偏大，违约风险中等',
        'L-6': '信用表现中等，受不利经济环境的影响适中，违约风险较低',
        'L-7': '信用表现中等，经营和风险管理能力尚可，违约风险较低',
        'L-8': '信用表现中等，经营和风险管理能力相对较好，违约风险较低',
        'L-9': '信用表现良好，经营和风险管理能力较好，违约风险低',
        'L-10': '信用表现良好，经营和风险管理能力优秀，违约风险低',
        'L-11': '信用表现优秀，当前处于良性循环状态，违约风险很低',
        'L-12': '信用表现卓越，当前处于良性循环状态，违约风险很低',
      }),
    },
    scale: {
      type: Array as PropType<any[]>,
      default: () => [
        {
          level: 'low',
          percentage: 25,
          min: 0,
          max: 499,
          range: [
            { levelName: 'L-1', min: 0, max: 299, opacity: 0.5, tickLabel: 'min' },
            { levelName: 'L-2', min: 300, max: 399, opacity: 0.4 },
            { levelName: 'L-3', min: 400, max: 499, opacity: 0.3 },
          ],
        },
        {
          level: 'middle',
          percentage: 42,
          min: 500,
          max: 749,
          range: [
            { levelName: 'L-4', min: 500, max: 549, opacity: 0.2, tickLabel: 'min' },
            { levelName: 'L-5', min: 550, max: 599, opacity: 0.3 },
            { levelName: 'L-6', min: 600, max: 649, opacity: 0.4, tickLabel: 'min' },
            { levelName: 'L-7', min: 650, max: 699, opacity: 0.5 },
            { levelName: 'L-8', min: 700, max: 749, opacity: 0.6 },
          ],
        },
        {
          level: 'high',
          percentage: 33,
          min: 750,
          max: 2000,
          range: [
            { levelName: 'L-9', min: 750, max: 799, opacity: 0.2, tickLabel: 'min' },
            { levelName: 'L-10', min: 800, max: 899, opacity: 0.3 },
            { levelName: 'L-11', min: 900, max: 999, opacity: 0.4, tickLabel: 'min' },
            { levelName: 'L-12', min: 1000, max: 2000, opacity: 0.5, tickLabel: 'max' },
          ],
        },
      ],
    },
  },
  setup(props) {
    const indicator = computed(() => {
      let level; // high
      let levelName; // L-1
      let color; // #ff8900
      let direction; // left | right | middle
      let percentage;
      let residualPercentage = 0;

      for (let i = 0; i < props.scale.length; i++) {
        const scale = props.scale[i];
        const rangeList = scale.range; // 将范围列表提取到循环外部

        for (let j = 0; j < rangeList.length; j++) {
          const range = rangeList[j];

          if (props.value >= range.min && props.value <= range.max) {
            level = scale.level;
            levelName = range.levelName;
            color = props.theme[scale.level];
            direction = LEVEL_DIRECTION_MAP[scale.level];

            const unitPercentage = scale.percentage / rangeList.length;
            const initPercentage = unitPercentage * j;
            const scorePercentage = unitPercentage * ((props.value - range.min) / (range.max - range.min));
            percentage = residualPercentage + initPercentage + scorePercentage;

            // 提前终止内部循环
            break;
          }
        }
        residualPercentage += scale.percentage;
      }

      return {
        level,
        levelName,
        color,
        direction,
        percentage,
      };
    });

    return {
      indicator,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        {/* Label */}
        <div class={styles.tick}>
          {this.scale.map((item) => {
            return (
              <div key={item.level} class={styles.range} style={{ width: `${item.percentage}%` }}>
                {/* Bar */}
                {item.range.map((step) => {
                  return (
                    <div
                      key={step.levelName}
                      class={styles.bar}
                      style={{
                        width: `${100 / item.range.length}%`,
                      }}
                    >
                      <span
                        style={{
                          opacity: step.tickLabel === 'min' ? 1 : 0,
                        }}
                      >
                        {step.min}
                      </span>
                      <span
                        style={{
                          opacity: step.tickLabel === 'max' ? 1 : 0,
                        }}
                      >
                        {step.max}
                      </span>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>

        {/* Scale */}
        <div class={styles.scale}>
          <div class={styles.inner}>
            {this.scale.map((item, index, scaleList) => {
              const color = this.theme[item.level];
              return (
                <div
                  key={item.level}
                  class={{
                    [styles.range]: true,
                    [styles.first]: index === 0,
                    [styles.last]: index === scaleList.length - 1,
                  }}
                  style={{ width: `${item.percentage}%` }}
                >
                  {/* Bar */}
                  {item.range.map((step) => {
                    return (
                      <div
                        key={step.levelName}
                        class={styles.bar}
                        style={{
                          width: `${100 / item.range.length}%`,
                          background: color,
                          opacity: step.opacity,
                        }}
                        data-index={step.name}
                      />
                    );
                  })}
                </div>
              );
            })}

            {/* Indicator */}
            <div
              class={styles.indicator}
              style={{
                background: this.indicator.color,
                left: `${this.indicator.percentage}%`,
              }}
            />

            {/* Description */}
            <div
              class={{
                [styles.description]: true,
                [styles[this.indicator.direction]]: true,
              }}
              style={{
                color: this.indicator.color,
                left: `${this.indicator.percentage}%`,
              }}
            >
              <p>
                {this.indicator.levelName} {this.locale[this.indicator.levelName]}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default CreditRateBar;
