@import '@/styles/token.less';

.container {
  .score,
  .info {
    display: flex;
  }

  .score + .info {
    margin-top: 15px;
  }

  // 分数图
  .score {
    min-height: 150px;
    background: #f8fbfe;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
    position: relative;

    &:hover {
      background: #f2f8fe;

      .decorator {
        background: #cae6fc;
      }
    }

    .chart {
      align-items: center;
      justify-content: center;
    }

    .detail {
      padding-top: 36px;
      padding-right: 28px;
    }

    .decorator {
      position: absolute;
      top: 0;
      right: 0;
      background: #e2f1fd;
      padding: 2px 6px;
      font-size: 14px;
      color: @qcc-color-blue-500;
    }
  }

  // 雷达图
  .info {
    background: #f7f7f7;
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;

    .chart {
      background: #fff;
      box-shadow: inset -1px 0 0 0 #eee;
      padding: 0 15px 15px;
      display: flex;
      flex-direction: column;

      .tip {
        padding: 10px;
        color: #999;
        background-color: #f7f7f7;
        font-size: 12px;
        line-height: 17px;
        border-radius: 4px;
      }
    }

    .detail {
      display: flex;
      flex-wrap: wrap;
      padding: 4px;
    }
  }

  // 维度
  .dimension {
    border-radius: 4px;
    background: #fff;
    margin: 4px;
    padding: 10px;
    width: calc(50% - 8px);

    &:last-child {
      width: 100%;
    }

    // 标题
    .title {
      font-weight: 700;
      color: #333;
      line-height: 22px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .stars {
      font-size: 10px;
      display: flex;
      align-items: center;
    }

    // 标签列表
    .summary {
      display: flex;
      flex-wrap: wrap;
      padding: 2.5px 0;
    }

    // 标签
    .label {
      margin: 2.5px 0;
      font-size: 12px;
      height: 22px;
      line-height: 14px;
      padding: 4px 8px;
      white-space: nowrap;

      &:not(:last-child) {
        margin-right: 5px;
      }

      &.good {
        color: #128bed;
        background: #e2f1fc;
      }

      &.bad {
        color: #ff6060;
        background: #ffecec;
      }

      &.prompt {
        color: #ff8900;
        background: #ffeed5;
      }
    }
  }

  .chart {
    width: 390px;
    display: flex;
  }

  .detail {
    flex: 1;
  }

  // 脚注
  .footnote {
    margin-top: 10px;
    color: #999;
    line-height: 22px;
  }
}
