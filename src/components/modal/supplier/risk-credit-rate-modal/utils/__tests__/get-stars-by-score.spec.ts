import { getStarsByScore } from '../get-stars-by-score';

describe('getStarsByScore', () => {
  test('should return an array of 5 stars for a score of 50', () => {
    const score = 100;
    const expected = [1, 1, 1, 1, 1];
    const result = getStarsByScore(score);
    expect(result).toEqual(expected);
  });

  test('should return an array of 2.5 stars for a score of 50', () => {
    const score = 50;
    const expected = [1, 1, 1, 0, 0];
    const result = getStarsByScore(score);
    expect(result).toEqual(expected);
  });

  test('should return an array of 0 stars for a score of 0', () => {
    const score = 0;
    const expected = [0, 0, 0, 0, 0];
    const result = getStarsByScore(score);
    expect(result).toEqual(expected);
  });

  test('should return an array of 0 stars for a score of negative score', () => {
    const score = -100;
    const expected = [0, 0, 0, 0, 0];
    const result = getStarsByScore(score);
    expect(result).toEqual(expected);
  });
});
