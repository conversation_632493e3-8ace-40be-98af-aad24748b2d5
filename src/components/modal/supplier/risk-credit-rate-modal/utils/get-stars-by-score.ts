/**
 * 计算给定值生成星算法(共5颗星)
 */
export function getStarsByScore(score: number): number[] {
  if (score <= 0) {
    return [0, 0, 0, 0, 0];
  }

  const stars: number[] = [];
  const allHalfStars = Math.floor(score / 10) + 1;
  const fullStars = Math.floor(allHalfStars / 2);
  const halfStar = allHalfStars % 2;

  for (let i = 0; i < 5; i++) {
    if (i + 1 <= fullStars) {
      stars.push(1);
    } else if (i === fullStars && halfStar) {
      stars.push(0.5);
    } else {
      stars.push(0);
    }
  }
  return stars;
}
