import { computed, defineComponent, ref } from 'vue';

import QIcon from '@/components/global/q-icon';
import QModal from '@/components/global/q-modal';

import styles from './risk-credit-rate-modal.module.less';
import CreditRateBar from './widgets/rate-bar';
import CreditRateGauge from './widgets/rate-gauge';
import CreditRateLabels from './widgets/rate-labels';
import CreditRateRadar from './widgets/rate-radar';
import CreditRateStars from './widgets/rate-stars';
import RateSpecificationModal from './widgets/rate-specification-modal';

/**
 * 企查分弹窗
 */
const RiskCreditRateModal = defineComponent({
  name: 'RiskCreditRateModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const visible = ref(true);
    const handleCancel = () => {
      visible.value = false;
    };

    const rateSpecificationVisible = ref(false);
    const handleOpenRateSpecificationModal = () => {
      rateSpecificationVisible.value = true;
    };

    const modalTitle = computed(() => {
      const companyName = props.params?.scoreInfo?.CompanyInfo?.Name;
      return companyName ? `${companyName} 企查分` : null;
    });

    return {
      visible,
      handleCancel,
      modalTitle,
      rateSpecificationVisible,
      handleOpenRateSpecificationModal,
    };
  },
  render() {
    const { scoreInfo } = this.params;

    return (
      <QModal
        {...{
          props: {
            title: this.modalTitle,
            visible: this.visible,
            destroyOnClose: true,
            size: 'huge-large',
            footer: null,
            viewportDistance: 50 + 53 + 63, // top 50 + header 53 + bottom 63
          },
          on: {
            cancel: this.handleCancel,
          },
        }}
      >
        <div class={styles.container}>
          <div class={styles.score} onClick={this.handleOpenRateSpecificationModal}>
            {/* 仪表盘 */}
            <div class={styles.chart}>
              <CreditRateGauge scoreInfo={scoreInfo} width="300px" height="140px" />
            </div>
            {/* 条形图 */}
            <div class={styles.detail}>
              <CreditRateBar value={scoreInfo?.Score} />
            </div>
            {/* 分数解读 */}
            <div class={styles.decorator}>
              <span>分数解读</span>
              <QIcon type="icon-wenzilianjiantou" />
            </div>
          </div>

          {/* 分数解读详情 */}
          <RateSpecificationModal v-model={this.rateSpecificationVisible} scoreInfo={scoreInfo} />

          <div class={styles.info}>
            {/* 雷达图 */}
            <div class={styles.chart}>
              <CreditRateRadar width="360px" height="425px" dimensions={scoreInfo?.DimensionList} />
              <div class={styles.tip}>
                标签基于企业数据及特定规则加工得出，仅用以展示企业在特定情形下的某些特征，可结合具体场景对企业进行综合判断。
              </div>
            </div>
            {/* 模型详情 */}
            <div class={styles.detail}>
              {scoreInfo.DimensionList.map(({ Type, TypeDesc, Score, GoodLabels, BadLabels, PromptLabels, SpecialText }) => {
                return (
                  <div class={styles.dimension} key={Type}>
                    <div class={styles.title}>
                      <span>{TypeDesc}</span>
                      <span class={styles.stars}>
                        <CreditRateStars score={Score} />
                      </span>
                    </div>
                    <div class={styles.summary}>
                      <CreditRateLabels values={GoodLabels} type="good" />
                      <CreditRateLabels values={BadLabels} type="bad" />
                      <CreditRateLabels values={PromptLabels} type="prompt" />
                    </div>
                    <div class={styles.description}>{SpecialText}</div>
                  </div>
                );
              })}
            </div>
          </div>

          <div class={styles.footnote}>
            企查分是基于公开信息，通过机器学习算法及大数据模型综合计算得出的分值，随主体公开信息变化而相应变化；具体分值仅供参考，仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。
          </div>
        </div>
      </QModal>
    );
  },
});

export default RiskCreditRateModal;
