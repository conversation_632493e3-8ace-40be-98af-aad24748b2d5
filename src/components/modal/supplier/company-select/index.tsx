import { debounce, find, isNil } from 'lodash';
import { defineComponent, onMounted, PropType, ref } from 'vue';
import { Empty, Select, Spin, Tooltip } from 'ant-design-vue';

import CompanyLogo from '@/components/company-logo';
import { company } from '@/shared/services';
import { getHTMLText } from '@/utils';
import Icon from '@/shared/components/icon';
import QIcon from '@/components/global/q-icon';

import styles from './style.module.less';
// 确定精确录入
const isExact = (options, currentValue) => {
  return options.map(({ value }) => value).includes(currentValue);
};

const CompanySelect = defineComponent({
  name: 'CompanySelect',
  props: {
    value: {
      type: String,
      required: false,
    },
    placeholder: {
      type: String,
      required: false,
    },
    disabled: {
      type: Boolean,
      required: false,
    },
    // 是否自定义nodata
    coustomNodata: {
      type: Boolean,
      default: true,
    },
    stafllName: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: 'default',
    },
    enterButton: {
      type: String,
      default: '',
      required: false,
    },
    remote: {
      type: Function as PropType<(keywords: string) => Promise<any>>,
    },
    allowEmpty: {
      type: Boolean,
      default: false,
    },
    immediate: {
      type: Boolean,
      default: false,
    },
    autoFocus: {
      type: Boolean,
      default: false,
    },
    defaultOpen: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    showArrow: {
      type: Boolean,
      default: true,
    },
    showWarning: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['change', 'input', 'blur'],
  setup(props, { emit }) {
    const isLoading = ref(false);

    // 搜索选项
    const options = ref([]);
    const searchWord = ref('');

    const handleClear = () => {
      options.value = [];
      searchWord.value = '';
      emit('change', undefined, null);
    };

    const handleSelect = (option: any) => {
      const item = find(options.value, { KeyNo: option.KeyNo });
      emit('change', option.value, item || {});
    };
    const handleChange = (value: string) => {
      if (isNil(value)) {
        emit('change', undefined, null);
      } else if (!isExact(options.value, value)) {
        // FIXME: 是否仅支持从原
        // console.error('请从结果中选择对应的值');
        emit('input', value);
      }
    };

    const request = props.remote
      ? props.remote
      : async (keywords) => {
          const { Result = [] } = await company.getCompanyListQCC({
            searchKey: keywords,
            pageSize: 5,
            // 过滤港澳台企业
            // 0: '大陆企业', 1: '社会组织', 11: '事业单位', 12: '律师事务所'
            filter: { st: ['0,4,10,12', '1', '11', '12'] },
          });
          return Result.map(({ Name, ...other }) => ({
            // option shape
            value: getHTMLText(Name),
            label: Name,
            id: other.KeyNo,
            ...other,
          }));
        };

    const handleSearch = debounce(async (keywords: string) => {
      // 搜索词改变，清空搜索结果
      if (!searchWord.value || !keywords.startsWith(searchWord.value)) {
        options.value = [];
      }
      searchWord.value = keywords;
      // 可以不输入内容查询，但是一旦输入内容，就得超过2个字符，C端接口要求
      if (request && (keywords.length >= 2 || (props.allowEmpty && keywords.length !== 1))) {
        try {
          isLoading.value = true;
          const result = await request(keywords);
          options.value = result;
        } catch (err) {
          options.value = [];
        } finally {
          isLoading.value = false;
        }
      } else {
        options.value = [];
      }
    }, 300);

    onMounted(() => {
      if (props.immediate) {
        handleSearch(searchWord.value);
      }
    });

    return {
      isLoading,
      options,
      searchWord,
      handleSearch,
      handleSelect,
      handleChange,
      handleClear,
    };
  },
  render() {
    const props = {
      showSearch: true,
      placeholder: this.placeholder,
      value: this.value || undefined,
      size: this.size,
      enterButton: this.enterButton,
      dropdownClassName: styles.dropdown,
      // optionLabelProp: 'label',
      filterOption: false,
      autoFocus: this.autoFocus,
      defaultOpen: this.defaultOpen,
      allowClear: this.allowClear,
      showArrow: this.showArrow,
    };
    const on = {
      change: this.handleChange,
      // select: this.handleSelect,
      search: this.handleSearch,
      focus: () => {
        // 允许空值的情况下，查询数据
        if (!(this.value || this.allowEmpty)) {
          this.options = [];
          return;
        }
        if (this.options?.length) return;
        this.handleSearch(this.value || '');
      },
      blur: () => this.$emit('blur'),
    };

    const renderEmpty = () => {
      if (this.isLoading) {
        return (
          <div>
            <div class="flex justify-center items-center" style={{ height: '92px' }}>
              <Spin spinning size="small" />
            </div>
          </div>
        );
      }
      const emptyWord = this.stafllName ? `没有匹配到与“${this.stafllName}”同姓名的关联企业` : '暂无数据';
      return (
        <div class={styles.empty}>
          <Empty image={(Empty as any).PRESENTED_IMAGE_SIMPLE} imageStyle={{ marginBottom: '10px' }} description={emptyWord} />
        </div>
      );
    };
    const renderWarning = () => {
      return (
        <div class={styles.warning}>
          <span class={styles.icon}>
            <Icon type="icon-a-shuomingxian" />
          </span>
          <span class={styles.content}>仅支持核验中国大陆企业（不包含基金会和港股企业）</span>
        </div>
      );
    };

    const scopedSlots = {
      dropdownRender: (menu) => {
        return (
          <div>
            {menu}
            {this.showWarning ? <div style={{ padding: '6px 10px 10px 10px' }}>{renderWarning()}</div> : null}
          </div>
        );
      },
    };

    return (
      <Select {...{ props, on, scopedSlots }} optionLabelProp="value" class={styles.select} disabled={this.disabled}>
        {this.options?.map((option: any) => (
          <Select.Option
            key={option.KeyNo}
            value={option.value}
            onClick={() => {
              this.handleSelect(option);
            }}
          >
            <Tooltip mouseEnterDelay={getHTMLText(option.label)?.length > 32 ? 0 : 9999} title={getHTMLText(option.label)}>
              <div class={styles.companyItem}>
                <CompanyLogo
                  src={option.ImageUrl}
                  id={option.companyId || option.KeyNo}
                  name={option.ShortName || option.companyName || option.value}
                  hasimage={option.HasImage}
                  class="shrink-0"
                />
                <div class={styles.companyName} domPropsInnerHTML={option.labelNew || option.label}></div>
              </div>
            </Tooltip>
          </Select.Option>
        ))}
        <QIcon slot="clearIcon" type="icon-shanchu3" onClick={this.handleClear}></QIcon>

        {this.coustomNodata ? <div slot="notFoundContent">{renderEmpty()}</div> : null}
      </Select>
    );
  },
});

export default CompanySelect;
