@import '@/styles/token.less';

.select {
  :global {
    .ant-select-search__field {
      color: #333;
      padding-left: 0;
      padding-right: 0;
    }

    .ant-select-selection__clear {
      color: #D8D8D8;
    }

    .ant-select-selection__clear:hover {
      color: #808080;
    }
  }
}

.dropdown {
  :global {
    .ant-select-dropdown {
      &-menu {
        &-item {
          &-active {
            background: #f2f8fe;
            color: #128bed;
          }

          &-disabled:hover {
            background: none;
          }
        }
      }
    }
  }

  .companyItem {
    width: 100%;
    display: flex;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
    font-weight: normal;
    gap: 5px;
  }

  .companyName {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.empty :global {
  .ant-empty {
    margin: 10px 0;
  }
}

.warning {
  pointer-events: none;
  color: #d97716;
  font-size: 12px;
  line-height: 18px;
  border-radius: 4px;
  padding: 4px 10px;
  background: rgba(255, 240, 224, 0.3);
  display: flex;
  align-items: flex-start;
  gap: 4px;

  .content {
    flex: 1;
    white-space: normal;
  }

  .icon :global {
    .anticon {
      color: currentcolor !important;
    }
  }

}
