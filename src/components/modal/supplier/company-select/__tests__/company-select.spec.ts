import { mount, shallowMount } from '@vue/test-utils';

import { company } from '@/shared/services';

import CompanySelect from '../index';

vi.mock('@/shared/services', () => {
  return {
    company: {
      getCompanyListQCC: vi.fn(),
    },
  };
});

describe('CompanySelect', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  // 测试组件挂载是否成功
  it('renders correctly', () => {
    vi.mocked<any>(company.getCompanyListQCC).mockResolvedValue({
      Result: [
        { value: '1', label: '公司1' },
        { value: '2', label: '公司2' },
      ],
    });
    const wrapper = mount(CompanySelect, {
      propsData: {
        placeholder: '请选择公司',
      },
    });
    expect(wrapper.html()).toBeTruthy();
  });
  // 测试远程搜索功能
  it('fetches data from remote when search word changes', async () => {
    const mockRequest = vi.fn().mockResolvedValue([{ value: 'test', label: '测试公司' }]);
    const wrapper = mount(CompanySelect, {
      propsData: {
        remote: mockRequest,
      },
    });
    wrapper.vm.handleSearch('测试');
    await vi.advanceTimersByTimeAsync(300);
    expect(mockRequest).toHaveBeenCalledWith('测试');
    expect(wrapper.vm.options.length).toBe(1);
  });

  // 测试默认搜索功能
  it('searches data when search word changes', async () => {
    vi.mocked<any>(company.getCompanyListQCC).mockResolvedValue({});
    const wrapper = mount(CompanySelect, {
      propsData: {},
    });
    wrapper.vm.handleSearch('公司');
    await vi.advanceTimersByTimeAsync(300);
    expect(wrapper.vm.options.length).toBe(0);
  });

  it('searches data when search word changes', async () => {
    vi.mocked<any>(company.getCompanyListQCC).mockResolvedValue({
      Result: [
        { value: '1', label: '公司1', KeyNo: '1' },
        { value: '2', label: '公司2', keyNo: '2' },
      ],
    });
    const wrapper = mount(CompanySelect, {
      propsData: {},
    });
    wrapper.vm.handleSearch('公司');
    await vi.advanceTimersByTimeAsync(300);
    expect(wrapper).toBeTruthy();
  });

  it('should render correctly when request is failed', () => {
    const mockRemote = vi.fn().mockRejectedValue(new Error('error'));
    const wrapper = mount(CompanySelect, {
      propsData: {
        allowEmpty: true,
        remote: mockRemote,
      },
    });
    wrapper.vm.handleSearch('测试');
    expect(wrapper.vm.options.length).toBe(0);
  });

  it('should not request data if search word is empty and allowEmpty is false', () => {
    const mockRemote = vi.fn();
    const wrapper = mount(CompanySelect, {
      propsData: {
        remote: mockRemote,
      },
    });
    wrapper.vm.handleSearch('');
    expect(mockRemote).not.toHaveBeenCalled();
  });

  // 测试组件是否正确处理空值
  it('handles empty value correctly', async () => {
    const wrapper = mount(CompanySelect, {
      propsData: {
        allowEmpty: true,
        placeholder: '请选择公司',
      },
    });
    wrapper.vm.handleSearch('');
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.options.length).toBe(0);
  });

  it('should request data when mounted', async () => {
    const mockRemote = vi.fn().mockResolvedValue([{ value: '1', label: '公司1' }]);
    const wrapper = mount(CompanySelect, {
      propsData: {
        allowEmpty: true,
        immediate: true,
        remote: mockRemote,
      },
    });
    await vi.advanceTimersByTimeAsync(300);
    expect(wrapper.vm.options.length).toBe(1);
  });

  it('should emit value when value is changed', () => {
    const wrapper = mount(CompanySelect, {
      propsData: {},
    });
    wrapper.setData({ options: [{ value: '1', label: '公司1' }] });
    wrapper.vm.handleChange('2');
    expect(wrapper.emitted('input')?.[0]).toEqual(['2']);
  });

  it('should emit value when select option', () => {
    const wrapper = mount(CompanySelect, {
      propsData: {},
    });
    wrapper.setData({ options: [{ value: '1', label: '公司1', KeyNo: '1' }] });
    wrapper.vm.handleSelect({ value: '1', label: '公司1', KeyNo: '1' });
    expect(wrapper.emitted('change')?.[0]).toEqual(['1', { value: '1', label: '公司1', KeyNo: '1' }]);
  });

  it('should search data when focus on input if allowEmpty is true', () => {
    const wrapper = shallowMount(CompanySelect, {
      propsData: {
        allowEmpty: true,
      },
    });
    const spy = vi.spyOn(wrapper.vm, 'handleSearch');
    const selectWrapper = wrapper.findComponent({ name: 'ASelect' });
    selectWrapper.vm.$emit('focus');
    expect(spy).toHaveBeenCalled();
  });
  it('should search data when focus on input if value is not empty', () => {
    const wrapper = shallowMount(CompanySelect, {
      propsData: {
        value: '1',
      },
    });
    const spy = vi.spyOn(wrapper.vm, 'handleSearch');
    const selectWrapper = wrapper.findComponent({ name: 'ASelect' });
    selectWrapper.vm.$emit('focus');
    expect(spy).toHaveBeenCalled();
  });

  it('should not search data when focus on input if value is empty and allowEmpty is false', () => {
    const wrapper = shallowMount(CompanySelect, {
      propsData: {},
    });
    const spy = vi.spyOn(wrapper.vm, 'handleSearch');
    const selectWrapper = wrapper.findComponent({ name: 'ASelect' });
    selectWrapper.vm.$emit('focus');
    expect(spy).not.toHaveBeenCalled();
  });
  it('should not search data when focus on input if options is not empty', () => {
    const wrapper = mount(CompanySelect, {
      propsData: {
        allowEmpty: true,
      },
    });
    wrapper.setData({ options: [{ value: '1', label: '公司1' }] });
    const spy = vi.spyOn(wrapper.vm, 'handleSearch');
    const selectWrapper = wrapper.findComponent({ name: 'ASelect' });
    selectWrapper.vm.$emit('focus');
    expect(spy).not.toHaveBeenCalled();
  });
});
