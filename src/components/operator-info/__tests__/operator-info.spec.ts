import { shallowMount } from '@vue/test-utils';

import OperatorInfo from '..';

describe('OperatorInfo 组件', () => {
  it('正常显示操作人信息', () => {
    const wrapper = shallowMount(OperatorInfo, {
      propsData: {
        operatorInfo: {
          name: '张三',
          phone: '12345678901',
          time: '2023-04-30T12:34:56Z',
        },
      },
    });
    expect(wrapper.find('.operatorLabel').text()).toContain('操作人：张三');
    expect(wrapper.find('.operatorInfo span').text()).toContain('（12345678901）');
    expect(wrapper.find('.operatorLabel:last-of-type').text()).toContain('操作时间：2023-04-30 20:34:56');
  });

  it('姓名和手机号相同时不显示手机号', () => {
    const wrapper = shallowMount(OperatorInfo, {
      propsData: {
        operatorInfo: {
          name: '张三',
          phone: '张三',
          time: '2023-04-30T12:34:56Z',
        },
      },
    });
    expect(wrapper.find('.operatorLabel').text()).toContain('操作人：张三');
    expect(wrapper.find('.operatorInfo span').exists()).toBe(false);
  });

  it('缺少操作人信息时不显示操作人信息', () => {
    const wrapper = shallowMount(OperatorInfo, {
      propsData: {
        operatorInfo: {
          time: '2023-04-30T12:34:56Z',
        },
      },
    });
    expect(wrapper.find('.operatorLabel').text()).not.toContain('操作人');
    expect(wrapper.find('.operatorLabel:last-of-type').text()).toContain('操作时间：2023-04-30 20:34:56');
  });

  it('缺少操作时间时不显示操作时间', () => {
    const wrapper = shallowMount(OperatorInfo, {
      propsData: {
        operatorInfo: {
          name: '张三',
          phone: '12345678901',
        },
      },
    });
    expect(wrapper.find('.operatorLabel').text()).toContain('操作人：张三');
    expect(wrapper.find('.operatorInfo span').text()).toContain('（12345678901）');
    expect(wrapper.find('.operatorLabel').text()).not.toContain('操作时间');
  });

  it('gap属性的默认值为15px', () => {
    const wrapper = shallowMount(OperatorInfo, {
      propsData: {
        operatorInfo: {
          name: '张三',
          phone: '12345678901',
          time: '2023-04-30T12:34:56Z',
        },
      },
    });
    expect(wrapper.find('div').attributes('style')).toContain('gap: 15px');
  });

  it('自定义gap属性', () => {
    const wrapper = shallowMount(OperatorInfo, {
      propsData: {
        operatorInfo: {
          name: '张三',
          phone: '12345678901',
          time: '2023-04-30T12:34:56Z',
        },
        gap: '20px',
      },
    });
    expect(wrapper.find('div').attributes('style')).toContain('gap: 20px');
  });
});
