import { PropType, defineComponent } from 'vue';
import moment from 'moment';

import styles from './operator-info.module.less';

type OPRATORINFO = {
  name: string;
  phone: string;
  time: string;
};
const OperatorInfo = defineComponent({
  functional: true,
  props: {
    operatorInfo: {
      type: Object as PropType<OPRATORINFO>,
      required: false,
    },
    gap: {
      type: String,
      default: '15px',
    },
  },
  render(h, { props }) {
    return (
      <div class={styles.time} style={{ gap: props.gap }}>
        {props.operatorInfo?.name ? (
          <span class="operatorLabel">
            操作人：
            <span class="operatorInfo">
              {props.operatorInfo?.name}
              {/* 姓名和手机号相同的话不显示手机号 */}
              {props.operatorInfo?.phone && props.operatorInfo?.name !== props.operatorInfo?.phone ? (
                <span>（{props.operatorInfo.phone}）</span>
              ) : null}
            </span>
          </span>
        ) : null}
        {props.operatorInfo?.time ? (
          <span class="operatorLabel">
            操作时间：
            <span class="operatorInfo">{moment(props.operatorInfo?.time).format('YYYY-MM-DD HH:mm:ss')}</span>
          </span>
        ) : null}
      </div>
    );
  },
});

export default OperatorInfo;
