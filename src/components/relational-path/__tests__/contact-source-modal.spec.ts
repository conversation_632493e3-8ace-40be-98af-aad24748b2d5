import { shallowMount } from '@vue/test-utils';

import QModal from '@/components/global/q-modal/q-modal';

import ContactSourceModal from '../contact-source-modal';

describe('ContactSourceModal', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof ContactSourceModal>>(ContactSourceModal, {
      propsData: {
        params: {
          modalTitle: 'MODAL_TITLE',
          dataSource: [],
          columns: [],
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: table', () => {
    const wrapper = shallowMount<InstanceType<typeof ContactSourceModal>>(ContactSourceModal, {
      propsData: {
        params: {
          modalTitle: 'MODAL_TITLE',
          dataSource: [
            {
              // sameAddress
              address: 'ADDRESS_1',
              sameAddress: 'SAME_ADDRESS_1',
              // contactSource
              id: 'ID_1',
              title: 'TITLE_1',
              // registTime
              startDate: '2023-01-01',
              endDate: undefined,
            },
            {
              // sameAddress
              address: 'ADDRESS_2',
              sameAddress: 'SAME_ADDRESS_2',
              // contactSource
              id: undefined,
              title: undefined,
              // registTime
              startDate: '2023-01-01',
              endDate: '2024-01-01',
            },
          ],
          columns: [
            {
              title: 'SAME_ADDRESS',
              dataIndex: 'address',
              scopedSlots: {
                customRender: 'sameAddress',
              },
            },
            {
              title: 'REGIST_TIME',
              width: '180px',
              scopedSlots: {
                customRender: 'registTime',
              },
            },
            {
              title: 'REGIST_TIME',
              width: '180px',
              scopedSlots: {
                customRender: 'ContactSource',
              },
            },
          ],
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('events: reject', async () => {
    vi.useFakeTimers({
      now: new Date('2024-01-01'),
    });

    const wrapper = shallowMount<InstanceType<typeof ContactSourceModal>>(ContactSourceModal, {
      propsData: {
        params: {
          modalTitle: 'MODAL_TITLE',
          dataSource: [],
          columns: [],
        },
      },
    });
    wrapper.findComponent(QModal).vm.$emit('cancel');
    await vi.advanceTimersByTimeAsync(1000);
    expect(wrapper.emitted('reject')).toEqual([['Cancel']]);

    vi.useRealTimers();
  });
});
