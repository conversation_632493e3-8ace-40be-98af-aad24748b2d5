import { shallowMount } from '@vue/test-utils';

import RelationalPath from '../index';

describe('RelationalPath', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof RelationalPath>>(RelationalPath, {
      propsData: {
        namespace: 'NAMESPACE',
        elements: [
          {
            type: 'node',
            id: 'NODE_ID',
            name: 'NODE_NAME',
            label: 'NODE_LABEL',
          },
          {
            type: 'edge',
            direction: 'left',
            roles: ['ROLE_A', 'ROLE_B'],
            startid: 'START_ID',
            endid: 'END_ID',
          },
          {
            type: 'none',
          },
        ],
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
