import { openContactSourceModal } from '../contact-source-modal';
import { preDelDetailData } from '../use-data-source-hook';

vi.mock('../contact-source-modal');

describe('useDataSourceHook', () => {
  describe('preDelDetailData', () => {
    test('address - should merge same companyName', () => {
      const mockOpenContactSourceModal = vi.mocked(openContactSourceModal);

      const source = {
        dataSource: [
          {
            address: '上海市青浦区北青公路9138号1幢',
            relationA: {
              companyName: 'COMPANY_NAME_1',
              keyNo: 'KEY_NO_1',
              address: '上海市青浦区北青公路9138号1幢3层Y区324室',
              startDate: '2019-03-12',
            },
            relationB: {
              companyName: 'COMPANY_NAME_2',
              keyNo: 'KEY_NO_2',
              address: '上海市青浦区北青公路9138号1幢3层B区3300室',
              startDate: '2023-05-10',
            },
          },
          {
            address: '上海市青浦区北青公路7199号',
            relationA: {
              companyName: 'COMPANY_NAME_1',
              keyNo: 'KEY_NO_1',
              address: '上海市青浦区北青公路7199号',
              startDate: '2019-03-12',
              reportyear: '2023',
            },
            relationB: {
              companyName: 'COMPANY_NAME_2',
              keyNo: 'KEY_NO_2',
              address: '上海市青浦区北青公路7199号',
              startDate: '2023-05-10',
              reportyear: '2023',
            },
          },
        ],
        relations: [
          {
            type: 'node',
            entityType: 'company',
            id: 'KEY_NO_1',
            name: 'COMPANY_NAME_1',
            label: 'COMPANY_NAME_1',
          },
          {
            startid: 'KEY_NO_1',
            endid: 'KEY_NO_2',
            direction: 'null',
            type: 'edge',
            role: '相同经营地址',
            data: [
              {
                data: [
                  {
                    address: '上海市青浦区北青公路9138号1幢',
                    relationA: {
                      companyName: 'COMPANY_NAME_1',
                      keyNo: 'KEY_NO_1',
                      address: '上海市青浦区北青公路9138号1幢3层Y区324室',
                      startDate: '2019-03-12',
                    },
                    relationB: {
                      companyName: 'COMPANY_NAME_2',
                      keyNo: 'KEY_NO_2',
                      address: '上海市青浦区北青公路9138号1幢3层B区3300室',
                      startDate: '2023-05-10',
                    },
                  },
                  {
                    address: '上海市青浦区北青公路7199号',
                    relationA: {
                      companyName: 'COMPANY_NAME_1',
                      keyNo: 'KEY_NO_1',
                      address: '上海市青浦区北青公路7199号',
                      startDate: '2019-03-12',
                      reportyear: '2023',
                    },
                    relationB: {
                      companyName: 'COMPANY_NAME_2',
                      keyNo: 'KEY_NO_2',
                      address: '上海市青浦区北青公路7199号',
                      startDate: '2023-05-10',
                      reportyear: '2023',
                    },
                  },
                ],
                role: '相同经营地址',
                type: 'Address',
              },
            ],
            roles: ['相同经营地址'],
            roleType: 'Address',
          },
          {
            type: 'node',
            entityType: 'company',
            id: 'KEY_NO_2',
            name: 'COMPANY_NAME_2',
            label: 'COMPANY_NAME_2',
          },
        ],
      };
      const expected = {
        dataList: [
          {
            companyName: 'COMPANY_NAME_1',
            keyNo: 'KEY_NO_1',
            address: '上海市青浦区北青公路9138号1幢3层Y区324室',
            startDate: '2019-03-12',
            sameAddress: '上海市青浦区北青公路9138号1幢3层',
            attrs: { rowSpan: 2 },
          },
          {
            companyName: 'COMPANY_NAME_1',
            keyNo: 'KEY_NO_1',
            address: '上海市青浦区北青公路7199号',
            startDate: '2019-03-12',
            reportyear: '2023',
            sameAddress: '上海市青浦区北青公路7199号',
            attrs: { rowSpan: 0 },
          },
          {
            companyName: 'COMPANY_NAME_2',
            keyNo: 'KEY_NO_2',
            address: '上海市青浦区北青公路9138号1幢3层B区3300室',
            startDate: '2023-05-10',
            sameAddress: '上海市青浦区北青公路9138号1幢3层',
            attrs: { rowSpan: 2 },
          },
          {
            companyName: 'COMPANY_NAME_2',
            keyNo: 'KEY_NO_2',
            address: '上海市青浦区北青公路7199号',
            startDate: '2023-05-10',
            reportyear: '2023',
            sameAddress: '上海市青浦区北青公路7199号',
            attrs: { rowSpan: 0 },
          },
        ],
        showMore: true,
      };

      const { getDataList, isShowMore, handleShowMore, columnsMap } = preDelDetailData(source.dataSource, source.relations);
      expect(getDataList('Address')).toEqual(expected.dataList);
      expect(isShowMore('Address')).toBe(expected.showMore);

      handleShowMore('Address');
      expect(mockOpenContactSourceModal).toHaveBeenLastCalledWith({
        dataSource: expected.dataList,
        ...columnsMap.Address,
      });
    });
  });
});
