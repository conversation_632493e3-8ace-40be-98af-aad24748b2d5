// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ContactSourceModal > render 1`] = `
<anonymous-stub afterclose="[Function]" visible="true" destroyonclose="true" title="[object Object]" transitionname="zoom" masktransitionname="fade" prefixcls="ant-modal" wrapclassname="" width="900" dialogstyle="[object Object]" dialogclass="" closeicon="[object Object]" class="">
  <table-stub size="default" datasource="" columns="" rowkey="key" locale="[object Object]" indentsize="20" bordered="true" showheader="true" childrencolumnname="children" sortdirections="ascend,descend"></table-stub>
</anonymous-stub>
`;

exports[`ContactSourceModal > render: table 1`] = `
<anonymous-stub afterclose="[Function]" visible="true" destroyonclose="true" title="[object Object]" transitionname="zoom" masktransitionname="fade" prefixcls="ant-modal" wrapclassname="" width="900" dialogstyle="[object Object]" dialogclass="" closeicon="[object Object]" class="">
  <table-stub size="default" datasource="[object Object],[object Object]" columns="[object Object],[object Object],[object Object]" rowkey="key" locale="[object Object]" indentsize="20" bordered="true" showheader="true" childrencolumnname="children" sortdirections="ascend,descend"></table-stub>
</anonymous-stub>
`;
