import { sortBy } from 'lodash';

import { longestSubstring } from '@/utils/string/longest-substring';
import { getSource } from '@/utils/helper/data-parser/data-node';

import { openContactSourceModal } from './contact-source-modal';

/**
 * 获取命中数量的统计数据
 * @param data 数据集
 * @param key 统计字段
 * @returns {Map}
 */
const getHitStatisticsByKey = <T extends Record<string, any>[]>(data: T, key: string) => {
  const hitsMap = new Map<string, { count: number; index: number }>();
  data.forEach((item, index) => {
    const hits = { count: 1, index };
    const target = hitsMap.get(item[key]);
    if (target) {
      hits.count = target.count + 1;
      hits.index = target.index;
    }
    hitsMap.set(item[key], hits);
  });
  return hitsMap;
};

/**
 * 基于命中数量统计，基于命中数量对相同名称的列进行合并
 * @param data 数据集
 * @param hitsMap 命中数量统计
 * @returns
 */
const mergeColumnsByHits = <T extends Record<string, any>>(data: T[], hitsMap: Map<string, { count: number; index: number }>) => {
  const result = data.map((item, index) => {
    const column = { ...item, attrs: {} };
    const target = hitsMap.get(item.companyName);
    if (target) {
      const rowSpan = target.index === index ? target.count : 0;
      column.attrs = { rowSpan };
    }
    return column;
  });
  return result;
};

/**
 * FIXME: 性能优化 - `getDataList` 重复执行多次，可转为纯函数 或者 缓存 getDataList 的结果（memorize）
 * @description 计算相同维度的数据
 * totalData：命中的数据
 * relations： 图谱节点数据，只有相同电话用到了
 */
export const preDelDetailData = (totalData, relations) => {
  const getDataList = (type) => {
    let dataSource: any[] = [];
    if (type === 'ContactNumber') {
      /**
       * 招投标相同联系人的代码 由于数据屏蔽，暂时不展示
        if (!totalData?.some((item) => item.tenders?.length)) {
          return [];
        }
        const map = groupBy(totalData, 't');
        const result = Object.entries(map).map(([key, value]) => {
          return {
            phone: key,
            value: value.map((item: any) => ({
              ...item,
              companyName: relations.find((element: any) => element.id === item.companyId)?.label,
            })),
          };
        });
        dataSource = result.flatMap((item) => {
          const max = Math.max(...item.value.map((v) => v.tenders?.length));
          if (!max) {
            return [];
          }
          return new Array(max).fill('').map((_, index) => {
            return {
              ...omit(item, 'value'),
              source: item.value.map((v) => ({
                ...v.tenders[index],
                from: 'tenders',
              })),
              attrs: {
                rowSpan: index === 0 ? max : 0,
              },
            };
          });
        });
       */

      // 根据电话号码进行排序，然后统计电话号码出现的次数，并设置是否已经rowspan过
      const sortDataByNumber = sortBy(totalData, 't');
      const hitsMap = getHitStatisticsByKey(sortDataByNumber, 't');
      dataSource = sortDataByNumber.map((item, index) => {
        const hits = hitsMap.get(item.t);
        const companyName = relations.find((element: any) => element.id === item.companyId)?.label;
        return {
          ...item,
          companyName,
          source: getSource(item.s?.split(',')),
          rowspan: hits?.index === index ? hits?.count : 0,
        };
      });
      return dataSource;
    }
    if (type === 'Address') {
      // 地址取data即可
      dataSource = sortBy(
        totalData?.reduce((total, cur) => {
          if (!cur?.relationA) {
            return [];
          }
          const sameAddress = longestSubstring(cur.relationA.address, cur.relationB.address);
          total = [...total, { ...cur.relationA, sameAddress }, { ...cur.relationB, sameAddress }];
          return total;
        }, []) || [],
        ['companyName', 'startDate']
      );
    }
    // 获取命中数量统计
    const companyNameHits = getHitStatisticsByKey<typeof dataSource>(dataSource, 'companyName');
    // 基于命中数量统计，基于命中数量对相同名称的列进行合并
    dataSource = mergeColumnsByHits(dataSource, companyNameHits);
    return dataSource;
  };

  /**
   * 是否展示更多的标识符
   * @param type 命中维度type
   * @returns boolean
   */
  const isShowMore = (type) => {
    return getDataList(type)?.length > 0;
  };

  const columnsMap = {
    ContactNumber: {
      modalTitle: '相同电话号码',
      columns: [
        {
          title: '企业名称',
          scopedSlots: {
            customRender: 'company',
          },
        },
        {
          title: '电话号码',
          dataIndex: 't',
          width: '180px',
          customRender: (value: any, row: any) => {
            return {
              attrs: {
                rowSpan: row.rowspan,
              },
              children: value,
            };
          },
        },
        {
          title: '数据来源',
          dataIndex: 'source',
        },
      ],
    },
    Address: {
      modalTitle: '相同经营地址',
      columns: [
        {
          title: '企业名称',
          dataIndex: 'companyName',
          customRender: (value: string, row: { attrs: Record<string, any> }) => {
            const renderObj = {
              children: value,
              attrs: row.attrs,
            };
            return renderObj;
          },
        },
        {
          title: '地址信息',
          dataIndex: 'address',
          width: '360px',
          scopedSlots: {
            customRender: 'sameAddress',
          },
        },
        {
          title: '登记时间',
          width: '180px',
          scopedSlots: {
            customRender: 'registTime',
          },
        },
      ],
    },
  };
  /**
   * 展示联系方式详细来源
   * @param type 命中维度type
   * @returns
   */
  const handleShowMore = (type = 'ContactNumber') => {
    const dataSource = getDataList(type);
    openContactSourceModal({
      dataSource,
      ...columnsMap[type],
    });
  };

  return { getDataList, isShowMore, handleShowMore, columnsMap };
};
