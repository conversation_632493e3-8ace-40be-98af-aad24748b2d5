import { defineComponent, ref } from 'vue';
import { Table } from 'ant-design-vue';

import QModal from '@/components/global/q-modal';
import { createPromiseDialog } from '@/components/promise-dialogs';

const ContactSourceModal = defineComponent({
  name: 'ContactSourceModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const visible = ref(true);
    const onCancel = async () => {
      visible.value = false;
      setTimeout(() => {
        emit('reject', 'Cancel');
      }, 1000);
    };

    return {
      onCancel,
      visible,
    };
  },

  render() {
    return (
      <QModal
        {...{
          props: {
            visible: this.visible,
            destroyOnClose: true,
            footer: false,
            size: 'x-large',
          },
          on: {
            cancel: () => this.onCancel(),
          },
        }}
      >
        <div slot="title">{this.params.modalTitle}</div>
        <Table
          bordered
          dataSource={this.params.dataSource || []}
          columns={this.params.columns || []}
          scopedSlots={{
            company: (record) => {
              return <q-entity-link coyObj={{ KeyNo: record.companyId, Name: record.companyName }} />;
            },
            sameAddress: (text, record) => {
              // 高亮显示匹配部分
              let formatStr = text.replace(record.sameAddress, `<span style="color: #f04040;">${record.sameAddress}</span>`);
              // 显示来源：年报
              if (record?.reportyear) {
                formatStr += `（${record.reportyear}年报）`;
              }
              return <div domPropsInnerHTML={formatStr}></div>;
            },
            ContactSource: (record) => {
              if (!record?.title) {
                return '-';
              }
              return (
                <a href={`/embed/tenderDetail?id=${record.id}&title=${record.title}`} target="_blank">
                  {record.title}
                </a>
              );
            },
            registTime: (record) => {
              const start = record.startDate || '未知';
              const end = record.endDate ? `至 ${record.endDate}` : '至 至今';
              return `${start} ${end}`;
            },
          }}
          pagination={false}
        />
      </QModal>
    );
  },
});

export default ContactSourceModal;

export const openContactSourceModal = createPromiseDialog(ContactSourceModal);
