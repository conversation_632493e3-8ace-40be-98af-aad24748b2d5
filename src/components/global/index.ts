import { VueConstructor } from 'vue';

const filterSubComponent = (fileName: string) => {
  if (fileName.includes('/components') || fileName.includes('/interfaces')) {
    return false;
  }
  return true;
};

/**
 * 自动注册 Global 组件
 */
export const install = async (Vue: VueConstructor) => {
  const modules: Record<string, any> = import.meta.glob('./*/index.(ts|tsx|js|jsx|vue)', { eager: true });
  for (const path in modules) {
    if (filterSubComponent(path) && modules[path]) {
      const moduleName = path.replace(/^\.\/(.*)\/index\.(ts|tsx|js|jsx|vue)$/, '$1');
      // console.log(moduleName, path, modules[path]);
      // if (['q-icon', 'q-select', 'q-button-select'].includes(moduleName)) {
      //   Vue.component(moduleName, modules[path].default);
      // }
      const component = modules[path].default;
      Vue.component(moduleName, component);
    }
  }
};
