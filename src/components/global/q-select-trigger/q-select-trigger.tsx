import { Icon } from 'ant-design-vue';
import _ from 'lodash';
import { defineComponent } from 'vue';

import styles from './q-select-trigger.module.less';

export default defineComponent({
  name: 'QSelectTrigger',
  props: {
    mode: {
      type: String, // input dropdown
      default: 'input',
    },
    label: {
      type: [String, Number],
    },
    placeholder: {
      type: String,
    },
    visible: {
      type: Boolean,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
    },
  },
  data() {
    return {
      hovering: false,
    };
  },
  computed: {
    showClearBtn() {
      if (this.label) {
        if (this.hovering) {
          return true;
        }
      }

      return false;
    },
  },
  methods: {
    handleClickClearBtn(e) {
      e.stopPropagation();
      this.$emit('clear');
    },
    handleMouseenter() {
      this.hovering = true;
    },
    handleMouseleave() {
      this.hovering = false;
    },
  },
  render() {
    const { width, mode } = this;
    const showClear = this.allowClear && this.showClearBtn;
    const events = {};

    if (this.allowClear) {
      Object.assign(events, {
        mouseenter: (this as any).handleMouseenter,
        mouseleave: (this as any).handleMouseleave,
      });
    }
    return (
      <div
        class={{
          [styles.disabled]: this.disabled,
          'q-select-trigger': true,
          [styles.trigger]: true,
          [styles.input]: mode === 'input',
          [styles.dropdown]: mode === 'dropdown',
          [styles.active]: Boolean(this.label),
        }}
        style={_.isNumber(width) ? { width: `${width}px` } : undefined}
        {...{ on: events }}
      >
        {this.label && <span class={styles.text}>{this.label}</span>}
        {!this.label && this.placeholder && <span class={[styles.text, styles.placeholder]}>{this.placeholder}</span>}
        <Icon
          class={styles.clear}
          v-show={showClear}
          type="close-circle"
          theme="filled"
          role="button"
          onClick={(this as any).handleClickClearBtn}
        />
        <q-icon
          type={'icon-a-shixinxia1x1'}
          style={{ transform: this.visible ? 'rotate(-180deg)' : 'rotate(0deg)' }}
          class={[styles.arrow, 'arrow']}
          v-show={!showClear}
        />
      </div>
    );
  },
});
