@import '../../../styles/token.less';

.trigger {
  width: auto;
  position: relative;
  cursor: pointer;
  height: 32px;
  line-height: 1.5;
  display: inline-flex;
  color: #333;
  align-items: center;
  transition: 0.2s;
  user-select: none;

  &.disabled {
    cursor: not-allowed;
  }

  // :global {
  //   .anticon {
  //     top: auto;
  //   }
  // }
}

.input {
  padding: 0 26px 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  &:not(.disabled) {
    &.active,
    &:focus,
    &:hover {
      border-color: @primary-color;
      color: @primary-color;

      .arrow {
        color: @primary-color;
      }
    }
  }

  &.disabled {
    background-color: #f5f5f5;
    color: rgba(0, 0, 0, 0.25);
    border-color: #d9d9d9;

    .arrow {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.dropdown {
  padding-right: 16px;

  & + & {
    margin-left: 20px;
  }

  &.disabled {
    color: rgba(0, 0, 0, 0.25);
  }
}

.text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.arrow,
.clear {
  font-size: 12px;
  position: absolute;
  right: 8px;
  top: 50%;
}

.arrow {
  font-size: 16px;
  color: #666;
  transition: color 0.2s;
  transform: translateY(-50%);
  top: auto;
}

.clear {
  color: rgba(0, 0, 0, 0.25);
  transform: translateY(-50%);
}
