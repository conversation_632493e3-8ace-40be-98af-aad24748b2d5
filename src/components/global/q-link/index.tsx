import { CreateElement, VNodeData, defineComponent } from 'vue';
import { isObject, isString } from 'lodash';

import { EventHandler } from '../interfaces';
import styles from './q-link.module.less';
import QIcon from '../q-icon';

const iconRender = (h, needRender: boolean, icon = 'icon-a-xian<PERSON><PERSON><PERSON>') => (needRender ? <QIcon type={icon} /> : null);

const QLink = defineComponent({
  functional: true,

  props: {
    /**
     * 是否显示箭头图标
     */
    arrow: {
      type: Boolean,
      default: false,
    },
    /**
     * 指定链接
     */
    to: {
      type: [String, Object],
      required: false,
      default: '',
    },
    // 指定链接，和 to 一样，做兼容
    href: {
      type: String,
      required: false,
      default: '',
    },
    /**
     * 是否为外链（行为同 a 链接）
     */
    external: {
      type: Boolean,
      default: false,
    },
    /**
     * 自定义链接颜色
     */
    color: {
      type: String,
      // default: '#128bed',
    },
    /**
     * 图标
     */
    icon: {
      type: String,
      required: false,
    },
    /**
     * 行内
     */
    display: {
      type: String,
      default: 'inline',
    },
    /**
     * 行内
     */
    wordBreak: {
      type: Boolean,
      default: false,
    },
    /**
     * ellipsis
     */
    ellipsis: {
      type: Boolean,
      default: false,
    },
  },

  render(h: CreateElement, { props, children, listeners, data }) {
    const { external, color, display, wordBreak, arrow, icon, ellipsis } = props;
    const to = props.to;
    const { attrs } = data;
    const hasClickListener = !!listeners.click && !to;
    const renderArrow = iconRender(h, arrow);
    const renderIcon = iconRender(h, !!icon, icon);
    // 是否为应用内跳转 (router-link)
    // 有 click 事件，忽略链接属性，发送事件
    const tag = external || props.href || hasClickListener ? 'a' : 'router-link';
    // 自定义样式
    const customClazz = isObject(data.class) ? data.class : { [data.class]: true };

    // 通用属性
    const linkData: VNodeData = {
      ...data,
      class: {
        [styles.container]: true,
        [styles.break]: wordBreak,
        [styles.ellipsis]: display !== 'inline' && ellipsis,
        ...customClazz,
      },
      attrs: {
        style: color ? `color: ${color}; display: ${display}` : `display: ${display}`,
        ...attrs,
      },
      on: {
        click: (ev: Event) => {
          if (hasClickListener) {
            ev.preventDefault();
            (listeners.click as EventHandler)(ev);
          }
        },
      },
    };

    switch (tag) {
      case 'a':
        if (isString(to)) {
          Object.assign(linkData.attrs ?? {}, {
            href: props.href,
            target: '_blank',
          });
        }
        break;
      case 'router-link':
      default:
        Object.assign(linkData, {
          props: {
            to,
          },
        });
        break;
    }

    return h(tag, linkData, [renderIcon, children, renderArrow]);
  },
});

export default QLink;
