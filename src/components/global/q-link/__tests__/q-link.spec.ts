import { shallowMount } from '@vue/test-utils';
import { defineComponent } from 'vue';

import QLink from '..';
import QIcon from '../../q-icon';

describe('QLink', () => {
  let ChildComponent;
  beforeEach(() => {
    ChildComponent = defineComponent({
      functional: true,
      render: (h) => {
        return h('span', {}, 'TEST');
      },
    });
  });

  test('render: `router-link` component', () => {
    // Act
    const wrapper = shallowMount(QLink, {
      slots: {
        default: [ChildComponent],
      },
    });
    // Assert
    expect(wrapper).toMatchSnapshot();
  });

  test('render: `a` tag', () => {
    // Act
    const wrapper = shallowMount(QLink, {
      listeners: {
        click: vi.fn(),
      },
      slots: {
        default: [ChildComponent],
      },
      propsData: {
        href: 'link',
      },
      attrs: {
        target: '_self',
      },
    });
    // Assert
    expect(wrapper).toMatchSnapshot();
  });

  test('render: `icon` component', () => {
    // Act
    const wrapper = shallowMount(QLink, {
      slots: {
        default: [ChildComponent],
      },
      propsData: {
        arrow: true,
      },
    });
    // Assert
    expect(wrapper.findComponent(QIcon).exists()).toBe(true);
  });

  test('events: click', async () => {
    // Arrange
    const clickFn = vi.fn();
    // Act
    const wrapper = shallowMount(QLink, {
      listeners: {
        click: clickFn,
      },
      slots: {
        default: [ChildComponent],
      },
    });
    await wrapper.trigger('click');
    // Assert
    expect(clickFn).toHaveBeenCalled();
  });
});
