@import '@/styles/token.less';

// @input-width: 220px;
@input-width: 100%;
@enter-btn-width: 32px;

.input-style() {
  line-height: 22px;
  padding: 4px 8px;
}

.container {
  .default {
    .input {
      display: flex;
      align-items: center;
      width: @input-width - @enter-btn-width;
      cursor: pointer;
      border-radius: @border-radius-base;
      color: @qcc-color-black-600;
      border: 1px solid transparent;
      .input-style();

      &:hover {
        border-color: @qcc-color-gray-500;
      }

      >span{
        width: 60px;
      }
    }

    .icon {
      color: @qcc-color-blue-500;

      & + span {
        margin-left: 5px;
      }
    }
  }

  .search {
    width: 258px;
    display: flex;
    align-items: center;

    .input {
      width: @input-width;
    }

    .clear {
      display: inline-block;
      margin-left: 10px;
      white-space: nowrap;
    }

    :global {
      .ant-input {
        border-color: @qcc-color-blue-500;
        box-shadow: none;
        .input-style();
      }

      .ant-input-search-button {
        padding: 0;
        width: @enter-btn-width;
        min-width: auto;

        &:hover {
          background: #0069bf;
        }
      }
    }
  }

  .inputLeft{
    display: flex;
    align-items: center;
    padding: 0 9px;
    height: 32px;
    font-size: 14px;
    color: #bbb;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    cursor: pointer;

    &:hover{
      border: 1px solid #128BED;
    }
  }

}
