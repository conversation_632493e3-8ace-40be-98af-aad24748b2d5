import { mount } from '@vue/test-utils';

import TinySearch from '..';

describe('TinySearch', () => {
  let wrapper;
  test('RenderBaisc', () => {
    wrapper = mount(TinySearch, {
      propsData: {
        placeholder: 'unit-test',
      },
    });

    expect(wrapper.findComponent({ ref: 'tiny-search_ref' }).exists()).toBe(true);
  });

  let inputWrapper;
  test('ModeSwitch', () => {
    wrapper.trigger('click');
    inputWrapper = wrapper.find('input');
    expect(inputWrapper.exists()).toBe(true);
    expect(inputWrapper.attributes('placeholder')).toBe('unit-test');
  });

  const changeValue = 'unitValue';
  test('EmitEventChange', async () => {
    inputWrapper.setValue(changeValue);
    expect(wrapper.emitted('change')).toBeTruthy();
    expect(inputWrapper.element.value).toBe(changeValue);
    expect(wrapper.emitted('change')).toEqual([[changeValue]]);
    // inputWrapper.element.value = changeValue
    // await wrapper.find('.tiny-search__search').trigger('click');
    // expect(wrapper.emitted('search')).toEqual([[changeValue]]);
  });

  test('handles cancel correctly', async () => {
    inputWrapper.setValue();
    inputWrapper.trigger('click');
    expect(wrapper.vm.mode).toBe(0);
  });
});
