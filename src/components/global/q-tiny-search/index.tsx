import { defineComponent, Ref, ref, watch, watchPostEffect } from 'vue';
import { Button, Input } from 'ant-design-vue';

import Icon from '@/shared/components/icon';

import styles from './tiny-search.module.less';

enum DisplayMode {
  Default,
  Search,
}

const TinySearch = defineComponent({
  name: 'TinySearch',
  props: {
    hint: {
      type: String,
      default: '点击进行搜索',
    },
    placeholder: {
      type: String,
      default: '请输入要搜索的内容',
    },
    // defaultValue: {
    //   type: String,
    //   required: false,
    // },
    searchPosition: {
      type: String,
      default: 'right',
    },
    value: {
      type: String,
      required: false,
    },
    modeType: {
      type: String,
      default: 'Default',
    },
    inputWidth: {
      type: String,
      default: '',
    },
  },
  emits: ['change', 'search', 'cancel'],
  model: {
    prop: 'value',
    event: 'change',
  },
  setup(props, { emit }) {
    const handleChange = (ev: Event) => {
      if (ev.target !== null) {
        emit('change', (ev.target as HTMLInputElement).value);
      }
    };

    /**
     * 显示模式切换
     */
    const mode = ref(DisplayMode[props.modeType]);
    const isSearchMode = () => {
      return mode.value === DisplayMode.Search;
    };
    const toggleMode = () => {
      mode.value = isSearchMode() ? DisplayMode.Default : DisplayMode.Search;
    };

    /**
     * 在外部重置 `value` 为 `undefined` 时, 重置显示模式
     */
    watch(
      () => props.value,
      (value, oldValue) => {
        if (value !== oldValue && value === undefined) {
          mode.value = DisplayMode.Default;
        } else if (value) {
          mode.value = DisplayMode.Search;
        }
      },
      {
        immediate: true,
      }
    );

    /**
     * 自动聚焦
     */
    const input: Ref<HTMLInputElement | null> = ref(null);
    watchPostEffect(() => {
      if (props.modeType === 'Search') {
        mode.value = DisplayMode.Search;
      }
      if (isSearchMode() && input.value !== null) {
        input.value.focus();
      }
    });

    /**
     * 搜索
     */
    const handleSearch = (rawValue: string) => {
      const value = typeof rawValue === 'string' ? rawValue.trim() : '';
      if (value === '') {
        toggleMode();
      }
      emit('search', value);
    };

    /**
     * 取消
     */
    const handleCancel = () => {
      toggleMode();
      emit('cancel');
    };

    return {
      mode,
      toggleMode,
      handleChange,
      handleSearch,
      handleCancel,
      input,
    };
  },
  render() {
    return (
      <div ref="tiny-search_ref" class={[styles.container, 'tiny-search__container']}>
        {/* Default mode */}
        <div class={[styles.default, 'tiny-search__default']} v-show={this.mode === DisplayMode.Default}>
          {this.searchPosition === 'right' ? (
            <div class={[styles.input]} onClick={this.toggleMode}>
              <Icon class={styles.icon} type="icon-sousuo" />
              <span>搜索</span>
            </div>
          ) : (
            <div class={styles.inputLeft} onClick={this.toggleMode}>
              <Icon class={styles.icon} type="icon-sousuo" style="color: #bbb" />
              <span>{this.hint}</span>
            </div>
          )}
        </div>

        {/* Search mode */}
        <div class={styles.search} v-show={this.mode === DisplayMode.Search} style={this.inputWidth ? { width: this.inputWidth } : {}}>
          <Input.Search
            ref="input"
            // defaultValue={this.defaultValue}
            value={this.value}
            class={[styles.input, 'tiny-search__input']}
            placeholder={this.placeholder}
            allowClear
            onChange={this.handleChange}
            onSearch={this.handleSearch}
          >
            <Button slot="enterButton" type="primary" class="tiny-search__search">
              <Icon type="icon-sousuo" />
            </Button>
          </Input.Search>
          <div class={styles.clear} v-show={this.mode === DisplayMode.Search} onClick={this.handleCancel}>
            <a class="tiny-search__cancel">取消</a>
          </div>
        </div>
      </div>
    );
  },
});

export default TinySearch;
