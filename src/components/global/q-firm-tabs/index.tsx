import { Dropdown, Menu, Tooltip } from 'ant-design-vue';
import { find } from 'lodash';
import { defineComponent, PropType } from 'vue';

import styles from './q-firm-tabs.module.less';

type IQFirmTabKey = string | number;

export interface IQFirmTab {
  key: IQFirmTabKey;
  label: string;
  count?: number | string;
  tooltip?: number;
}

const ExtraTabs = defineComponent({
  functional: true,
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
    max: {
      type: Number,
      required: false,
    },
    value: {
      type: [String, Number] as PropType<string | number>,
      required: false,
    },
    defaultLabel: {
      type: String,
      required: false,
    },
    highlight: {
      type: Boolean,
      required: false,
    },
  },
  render(h, { props, listeners }) {
    const { tabs, max, value, defaultLabel } = props;
    const availableTabs = tabs.slice(max);
    const defaultNode: any = find(availableTabs, { key: value }) || {
      key: 'defaultLabel',
      label: defaultLabel,
    };
    const defaultClasses = {
      [styles.tab]: true,
      [styles.highlight]: props.highlight,
    };

    return (
      <Dropdown>
        <div
          class={{
            ...defaultClasses,
            [styles.extra]: true,
            [styles.active]: defaultNode.key === value,
          }}
        >
          <span class={styles.label}>{defaultNode.label}</span>
          <i class={styles.count} v-show={!!defaultNode.count}>
            {defaultNode.count}
          </i>
          <q-icon class={styles.arrow} type="icon-a-shixinxia1x" />
        </div>

        <Menu slot="overlay">
          {availableTabs.map(({ key, label, count, tooltip }: any) => {
            return (
              <Menu.Item
                key={key}
                onClick={() => {
                  if (value !== key && typeof listeners.change === 'function') {
                    listeners.change(key);
                  }
                }}
              >
                <div class={defaultClasses}>
                  <span class={styles.label}>{label}</span>
                  <i class={styles.count} v-show={!!count}>
                    {count}
                  </i>
                  <Tooltip v-show={tooltip} title={tooltip}>
                    <q-icon class={styles.tooltip} type="icon-a-shuomingxian" />
                  </Tooltip>
                </div>
              </Menu.Item>
            );
          })}
        </Menu>
      </Dropdown>
    );
  },
});

const QFirmTabs = defineComponent({
  name: 'QFirmTabs',
  props: {
    value: {
      type: [String, Number] as PropType<string | number>,
      required: false,
    },
    tabs: {
      type: Array as PropType<IQFirmTab[]>,
      required: true,
    },
    tabStyle: {
      type: Object,
      default: () => ({}),
    },
    highlight: {
      type: Boolean,
      default: false,
    },
    max: {
      type: Number,
      required: false,
    },
    maxLabel: {
      type: String,
      default: '更多选项',
    },
  },
  emits: ['change'],
  model: {
    prop: 'value',
    event: 'change',
  },
  setup(props, { emit }) {
    const handleChange = (value) => {
      emit('change', value);
    };
    return {
      handleChange,
    };
  },
  render() {
    const { tabs, value, max, tabStyle, highlight, maxLabel } = this;
    const availableTabs = max ? tabs.slice(0, max) : tabs;
    return (
      <div class={styles.container}>
        {availableTabs.map(({ key, label, count, tooltip }) => {
          return (
            <div
              key={key}
              class={{
                [styles.tab]: true,
                [styles.notAllowed]: count === 0,
                [styles.active]: value === key,
                [styles.highlight]: highlight,
              }}
              style={tabStyle}
              onClick={() => {
                if (count === 0) {
                  return;
                }
                this.handleChange(key);
              }}
            >
              <span class={styles.label}>{label}</span>
              <i class={styles.count} v-show={!!count}>
                {count}
              </i>
              <Tooltip v-show={tooltip} title={tooltip}>
                <q-icon class={styles.tooltip} type="icon-a-shuomingxian" />
              </Tooltip>
            </div>
          );
        })}
        {/* 扩展导航为下拉菜单 */}
        {max && max > 0 && (
          <ExtraTabs value={value} max={max} defaultLabel={maxLabel} tabs={tabs} highlight={highlight} onChange={this.handleChange} />
        )}
      </div>
    );
  },
});

export default QFirmTabs;
