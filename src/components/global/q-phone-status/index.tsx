import { Popover } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import styles from './q-phone-status.module.less';

const setPhoneStatus = (phone?: string, vtList?: Record<string, any>[], isValid?: number) => {
  const defaults = {
    icon: 'icon-a-dianhuaxian',
    statusDesc: '固定电话或非大陆号码',
    color: '#128bed',
  };

  if (!phone && !vtList) {
    return defaults;
  }

  const obj: any = vtList?.find((vo: any) => vo.k === (phone as string).replace(/<[^>]+>/g, ''));

  if (obj?.v === '1' || isValid === 1) {
    defaults.icon = 'icon-a-dianhuarenzheng';
    defaults.color = '#00ad65';
    defaults.statusDesc = '正常：号码正常，可联系';
  } else if (obj?.v === '3') {
    defaults.icon = 'icon-a-dianhuaweizhi';
    defaults.statusDesc = '未知：暂未获取到监测状态，我们将持续更新';
  } else if (obj?.v === '0' || obj?.v === '2' || obj?.v === '4' || obj?.v === '5' || isValid === -1) {
    defaults.icon = 'icon-a-dianhuabukeyong';
    defaults.color = '#F04040';
    defaults.statusDesc = '不可用：空号、停机、沉默号、风险号，建议跳过';
  }
  // `isValid === 2` 保持原有状态
  return defaults;
};

const QPhoneStatus = defineComponent({
  functional: true,

  props: {
    /**
     * 电话号码
     */
    phone: {
      type: String,
      required: false,
    },
    /**
     * 电话号码状态
     */
    vtList: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    /**
     * 默认电话号码状态，如果vtList中未匹配到，则使用这个状态：1-正常，-1-不可用，2-未知
     */
    isValid: {
      type: Number,
    },
  },

  render(h, { props }) {
    const { statusDesc, icon, color } = setPhoneStatus(props.phone, props.vtList, props.isValid);

    return (
      <span class={styles.phoneStatus}>
        <Popover placement="bottom" content={statusDesc} overlayClassName={styles.popover}>
          <q-icon class={styles.phoneStatusIcon} type={icon} style={{ color }} data-description={statusDesc} />
        </Popover>
      </span>
    );
  },
});

export default QPhoneStatus;
