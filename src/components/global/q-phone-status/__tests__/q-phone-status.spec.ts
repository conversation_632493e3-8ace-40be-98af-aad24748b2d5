import { mount } from '@vue/test-utils';

import QPhoneStatus from '..';

describe('QPhoneStatus', () => {
  test.each([
    [1, '正常：号码正常，可联系'],
    [-1, '不可用：空号、停机、沉默号、风险号，建议跳过'],
    [2, '固定电话或非大陆号码'],
  ])('props: isValid %s', (isValid, expected: string) => {
    const wrapper = mount(QPhoneStatus, {
      context: {
        props: {
          isValid,
          phone: '0516-66697899',
        },
      },
    });
    expect(wrapper.html()).toContain(expected);
  });

  test.each([
    ['1', '正常：号码正常，可联系'],
    ['3', '未知：暂未获取到监测状态，我们将持续更新'],
    ['0', '不可用：空号、停机、沉默号、风险号，建议跳过'],
    ['2', '不可用：空号、停机、沉默号、风险号，建议跳过'],
    ['4', '不可用：空号、停机、沉默号、风险号，建议跳过'],
    ['5', '不可用：空号、停机、沉默号、风险号，建议跳过'],
  ])('props: vtList %s', (v: string, expected: string) => {
    const wrapper = mount(QPhoneStatus, {
      context: {
        props: {
          phone: '13605217722',
          vtList: [{ k: '13605217722', v }],
        },
      },
    });
    expect(wrapper.html()).toContain(expected);
  });
});
