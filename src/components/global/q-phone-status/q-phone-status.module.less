.phoneStatus {
  cursor: pointer;
  display: inline-block;
  width: 16px;
  position: relative;
}

.phoneStatusIcon {
  font-size: 12px;
  padding-right: 4px;
  // background-image: url(./images/phone_status_icon.png);
  // background-size: 88px 44px;
  // position: absolute;
  // left: -2px;
  // top: -16px;
}

.phoneStatusIcon.normal {
  background-position-x: -22px;
}

.phoneStatusIcon.danger {
  background-position-x: -44px;
}

.phoneStatusIcon.unknown {
  background-position-x: -66px;
}

.popover {
  :global {
    .ant-popover-inner {
      background-color: rgba(0, 0, 0, 0.8);
    }

    .ant-popover-inner-content {
      color: #fff;
      padding: 5px 12px;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
