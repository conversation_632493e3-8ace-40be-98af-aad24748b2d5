<template>
  <div class="tchart-table clearfix" :style="{ 'padding-left': titleWidth }">
    <slot name="table">
      <table class="ntable" :style="{ width: titleWidth, 'margin-left': `-${titleWidth}`, height: height || 'auto' }">
        <tr v-for="(item, index) in list" :key="`tchart-table_${index}`" @mouseenter="onHover(item)" :class="{ active: item === current }">
          <td class="ch">{{ item.name }}</td>
        </tr>
      </table>
    </slot>
    <div
      v-if="current"
      :class="[titleWidth == 0 && 'no-margin']"
      class="chart-contain"
      :style="{ height: height || `${list.length * 42 + 1}px` }"
    >
      <a-popover placement="bottom" v-if="current.tips">
        <template slot="content">
          <div v-html="current.tips"></div>
        </template>
        <div v-if="current.title" class="name" v-html="current.title"></div>
        <div v-else class="name">
          {{ current.name
          }}<a>
            <!--          <app-icon type="zhushi"></app-icon>-->
          </a>
        </div>
      </a-popover>
      <template v-else>
        <div v-if="current.title" class="name" v-html="current.title"></div>
        <div v-else class="name">{{ current.name }}</div>
      </template>
      <slot v-if="current.customView" :name="current.customView" :option="current.option"></slot>
      <q-echarts
        v-else-if="current.option"
        :height="current.option.height || height || `${list.length * 42}px`"
        :option="mergeOption(current.option)"
      >
      </q-echarts>
      <q-no-data old absolute v-else />
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" src="./style.less"></style>
