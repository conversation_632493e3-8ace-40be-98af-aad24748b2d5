/**
 * list 配置
 *   title:图表标题
 *   id:echarts初始化的domid，不能重复
 *   customView:slot
 *   options:图表配置项
 */
import _ from 'lodash';
// import appEcharts from '../app-echarts'
import * as echarts from 'echarts';

export default {
  name: 'q-tchart-table',
  props: {
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: '',
    },
    titleWidth: {
      type: String,
      default: '180px',
    },
    height: {
      type: String,
    },
    oDefault: {
      // 默认的defualtOption
      type: Object,
    },
  },
  data() {
    return {
      current: null,
    };
  },
  watch: {
    list: {
      // eslint-disable-next-line
      handler: function (val) {
        this.current = val[0];
      },
      immediate: true,
    },
  },
  mounted() {
    this.current = this.list[0];
  },
  methods: {
    onHover(item) {
      if (this.current !== item) {
        this.$emit('hoverChanged', item);
      }
      this.current = item;
    },
    mergeOption(option) {
      let defaultOption = this.oDefault
        ? _.cloneDeep(this.oDefault)
        : {
            tooltip: {
              trigger: 'item',
            },
            toolbox: {
              right: 28,
              top: 5,
              feature: {
                saveAsImage: {},
              },
            },
          };
      const chartType = option.series[0].type;
      const notMerge = option.notMerge;
      if (!notMerge) {
        if (chartType === 'map') {
          defaultOption = _.extend(defaultOption, {
            visualMap: {
              min: 0,
              left: 40,
              top: 90,
              text: ['高', '低'], // 文本，默认为数值文本
              calculable: true,
              inRange: {
                color: ['#BFEFFF', '#128BED'],
              },
            },
            series: [
              {
                type: 'map',
                mapType: 'china',
                itemStyle: {
                  emphasis: {
                    areaColor: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: '#00EEEE', // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#00FF7F', // 100% 处的颜色
                        },
                      ],
                      false
                    ),
                  },
                },
                label: {
                  normal: {
                    show: false,
                  },
                  emphasis: {
                    show: true,
                  },
                },
              },
            ],
          });
        } else if (chartType === 'pie') {
          defaultOption = _.extend(defaultOption, {
            color: ['#4DD8E5', '#78ADEF', '#98D869', '#F4D576', '#E49E6C'],
            series: [
              {
                type: 'pie',
                selectedMode: 'single',
                selectedOffset: 0,
                center: ['50%', '50%'],
                radius: [0, '40%'],
                labelLine: {
                  length: 10,
                },
                label: {
                  formatter: '{b}({c})：{d}%',
                  color: '#666',
                },
              },
            ],
          });
        } else if (chartType === 'bar' || chartType === 'line') {
          defaultOption = _.extend(defaultOption, {
            color: ['#67aef5'],
            xAxis: {
              type: 'category',
              boundaryGap: true,
              axisLine: {
                show: true,
                lineStyle: {
                  width: 0.5,
                  color: '#eee',
                  type: 'solid',
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0, // 可以设置成 0 强制显示所有标签。
                textStyle: {
                  color: '#666',
                },
              },
              splitLine: {
                show: false,
              },
            },
            yAxis: {
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              splitLine: {
                show: false,
              },
            },
            series: [
              {
                barWidth: 15,
                label: {
                  show: true,
                  position: 'top',
                  color: '#333333',
                },
              },
            ],
          });
        }
      }
      return _.merge(defaultOption, option);
    },
  },
};
