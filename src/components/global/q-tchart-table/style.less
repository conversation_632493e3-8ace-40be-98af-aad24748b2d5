@import '@/styles/token.less';

.ntable {
  width: 100%;
  margin: 0 auto;
  margin-bottom: 20px;
}

.ntable th {
  text-align: center;
  background: @rover-table-header-bg;
  border: @rover-table-border-color 1px solid;
  border-collapse: collapse;
  padding: 12px 2px;
  font-weight: normal;
  color: #333;
  line-height: 20px;
  white-space: nowrap;
}

.ntable th.text-left {
  text-align: left;
  padding-left: 10px;
}

.ntable th.text-right {
  text-align: right;
  padding-right: 10px;
}


.ntable th:not(:last-child) {
  border-right: @rover-table-header-bg 1px solid;
  border-color: #e4eef6;
}

.ntable th.tl {
  text-align: left;
}

.ntable th.tr {
  text-align: right;
}

.ntable th .aicon {
  position: relative;
}

.ntable td {
  padding: 10px 10px 11px;
  border: @rover-table-border-color 1px solid;
  word-break: break-all;
  font-size: 13px;
  line-height: 1.6;
  color: #222;
  word-wrap: break-word;
}

.ntable td.hlh {
  line-height: 1.8;
}

.ntable td.vip-blur {
  filter: blur(4px);
  user-select: none;
}

.ntable td.keep-all {
  word-break: keep-all;
}

.ntable.npth th {
  padding-left: 0;
  padding-right: 0;
}

.ntable.nptd td {
  padding-left: 6px;
  padding-right: 6px;
}

.ntable.nptd td .line-clamp-btn {
  right: 6px;
}

.ntable .filter-blur {
  filter: blur(5px);
  user-select: none;
  cursor: pointer;
}

.ntable td.npv {
  padding-top: 0;
  padding-bottom: 0;
}

.ntable td .split-line {
  &:not(:last-child) {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 10px;

    &::after {
      content: ' ';
      position: absolute;
      height: 1px;
      background: @rover-table-border-color;
      left: -10px;
      right: -10px;
      bottom: 0;
    }
  }
}

.ntable td {
  .aicon-zhushi {
    position: relative;
    margin-left: 3px;
    top: 1px;
    font-size: 16px;
  }
}

.ntable .tb {
  background: @rover-table-header-bg;
  padding-right: 6px;
}

.ntable .tx {
  text-align: center;
  width: 56px;
  padding-left: 0;
  padding-right: 0;
}

.ntable .ti {
  text-align: left;
  padding-left: 32px;
}

.ntable .ti i {
  position: absolute;
  margin-left: -26px;
  margin-top: -3px;
}

.ntable em {
  color: #e33244;
  font-style: normal;
}

.ntable-ne em {
  color: #222;
}

.ntable a {
  color: #128bed;
}

.ntable a:hover {
  color: #007add;
}

.ntable a.text-gray {
  color: #999;
}

.ntable a.text-gray:hover {
  color: #007add;
}

.ntable a.text-danger {
  color: #F04040;
}

.ntable a.text-danger:hover {
  color: #e33244;
}

.ntable a.text-dk {
  color: #333;
}

.ntable .tblank {
  margin-top: 15px;
}

.ntable .tblank > th {
  border: none;
  background: none;
}

.ntable .tsub > th {
  border: @rover-table-border-color 1px solid;
}

.ntable tr .ch {
  cursor: pointer;
  font-size: 13px;
}

.ntable tr:hover .ch,
.ntable tr.active .ch {
  color: #128bed;
  background: @rover-table-header-bg;
}

.ntable .btn-touzi {
  color: #ff722d !important;
  float: right;
  font-size: 14px;
}

.ntable .blank {
  border: none;
  padding: 5px;
}

.ntable .explain-row {
  background: #fff9ed;
}

.ntable .explain-row.own {
  display: none;
}

.ntable .explain-row .text-gray {
  color: #999 !important;
}

.ntable .explain-row .simg {
  width: 20px;
  height: 20px;
  object-fit: contain;
  margin-left: 5px;
}

.ntable .explain-row .limg {
  max-width: 300px;
  object-fit: contain;
}

.ntable .tips {
  color: #999;
}

.ntable .sec-partner {
  display: inline-block;
  position: relative;
  width: 100%;
  height: 50px;
  margin-top: 10px;
  z-index: 1;

  .sec-partner-L-line {
    position: absolute;
    left: 20px;
    bottom: 25px;
    width: 32px;
    height: 65px;
    border-left: 1px solid #d6d6d6;
    border-bottom: 1px solid #d6d6d6;
    float: left;
    z-index: -1;
  }

  .sec-partner-content {
    float: right;
    width: calc(100% - 50px);
    height: 50px;
    background: #fafafa;
    border-left: 5px solid #128bed;
    padding: 3px 0 5px 10px;

    .sec-partner-name {
      font-size: 12px;
    }

    .sec-partner-info {
      cursor: pointer;
      // margin-bottom: 5px;
      width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .sec-partner-lable {
        font-size: 12px;
        color: #666;

        .sec-partner-value {
          color: #333;
        }
      }

      .sec-partner-lable:first-child {
        margin-right: 5px;
      }
    }
  }
}

.ntable .sec-partner::after {
  display: inline-table;
  content: ' ';
  clear: both;
}

.ntable .td-coy {
  width: 100%;
  display: table;

  .tags {
    margin-top: 5px;

    .ntag {
      margin-bottom: 2px;
    }

    .ntag.click {
      cursor: pointer;
    }

    .tags-hover {
      max-width: 300px;
      font-size: 12px;
    }
  }

  .headimg,
  .cont,
  .foot {
    display: table-cell;
    vertical-align: middle;
  }

  .name {
    word-break: break-word;
  }

  .headimg {
    width: 50px;
  }

  .cont .war-link {
    display: block;
    margin-top: 3px;
  }

  .foot {
    min-width: 125px;
    text-align: right;
  }
}

.ntable .war-link {
  color: #ff722d !important;
}

.ntable .war-link .aicon {
  color: #ff722d !important;
}

.ntable .tsort {
  cursor: pointer;
  user-select: none;

  .tsort-icon {
    //background-image: url(./images/icon/icon_sort.png);
    background-size: 18px 10px;
    display: inline-block;
    width: 6px;
    height: 10px;
  }

  .tsort-icon.asc {
    background-position: -12px 0;
  }

  .tsort-icon.desc {
    background-position: -6px 0;
  }
}

.ntable .htag {
  .ntag.sm {
    font-size: 12px;
    padding: 4px 5px;
    margin-right: 6px;
  }

  .line:not(:last-child) {
    margin-bottom: 5px;
  }
}

.nosort .tsort {
  display: none;
}

.ntable .line-clamp {
  overflow-y: hidden;
}

.ntable .ntag {
  white-space: nowrap;
}

.ntable .line-clamp-btn {
  right: 10px;
  background: #fff;
  margin-top: -22px;
  width: 45px;
  text-align: right;
  position: absolute;
  cursor: pointer;
}

.tchart-table {
  margin-bottom: 25px;

  .ntable {
    float: left;
    margin-bottom: 0;
  }

  .chart-contain {
    margin-left: 10px;
    border: solid 1px rgb(228, 238, 246);
    position: relative;
    overflow: hidden;

    .name {
      color: #333;
      font-size: 13px;
      position: absolute;
      top: 11px;
      left: 15px;

      i {
        top: 4px;
      }
    }

    &.no-margin {
      margin-left: 0;
    }
  }
}

.ntable .td-path {
  .path-line {
    line-height: 28px;

    &:not(:last-child) {
      margin-bottom: 6px;
    }

    .name {
      color: #222;
      font-weight: bold;
    }

    a {
      color: #333;

      &:hover {
        color: #128bed;
      }
    }

    .path-item {
      .percent {
        display: inline-block;
        width: 84px;
        height: 26px;
        //background: url(./images/line_arrow.png) no-repeat right bottom;
        background-size: 75px 8px;
        padding-bottom: 10px;
        font-size: 12px;
        color: #128bed;
        text-align: center;
        position: relative;
        top: -9px;
        margin-right: 6px;
      }

      .percent.t5 {
        width: 105px;
        //background: url(./images/line_arrowt5.png) no-repeat right bottom;
        background-size: 100px 34px;
        font-size: 12px;
        top: -9px;
        margin-right: 6px;
        padding-bottom: 40px;
      }
    }
  }
}

.tchart-list {
  margin-bottom: 15px;
  margin-left: -5px;
  margin-right: -5px;

  .col {
    float: left;
    padding-left: 5px;
    padding-right: 5px;
    margin-bottom: 10px;
  }

  .col-6 {
    width: 100%;
  }

  .col-3 {
    width: 50%;
  }

  .col-2 {
    width: 33.3%;
  }

  .item {
    position: relative;
    border: solid 1px #eee;

    .name {
      color: #333;
      font-size: 14px;
      position: absolute;
      top: 11px;
      left: 15px;
    }

    .tchart-btns {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 2;

      .tchart-btn {
        cursor: pointer;
        margin-left: 5px;

        &:first-child {
          margin-left: 0;
        }

        &.btn-active {
          color: #128bed;
        }
      }
    }
  }
}

.tcaption {
  height: 28px;
  margin-bottom: 15px;

  .title {
    font-size: 16px;
    color: #000;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 0;
    display: inline;
    line-height: 1.8;

    img {
      height: 20px;
      position: relative;
      top: -3px;
    }
  }

  .tbadge {
    font-size: 16px;
    font-weight: bold;
    color: #128bed;

    &.nig {
      color: #F04040;
    }
  }

  .title-tab {
    position: relative;

    .item {
      color: #666;
      padding: 5px 0;

      &:not(:last-child) {
        margin-right: 15px;
      }

      .tbadge {
        font-size: 14px;
        font-weight: normal;
      }

      &.active {
        color: #222;
        font-weight: bold;
        border-bottom: solid 2px #128bed;

        .tbadge {
          font-weight: bold;
        }
      }
    }
  }

  .aicon-zhushi {
    position: relative;
    margin-left: 4px;
    top: 1px;
    font-size: 16px;
  }

  .notice-tip {
    cursor: pointer;
    background: #fef3dc;
    color: #f9ad14;
    padding: 0 4px;
    line-height: 22px;
    display: inline-block;
    position: relative;
    font-size: 12px;
    border-radius: 2px;
    margin-left: 10px;
    top: -1px;

    .icon-notice {
      //background-image: url(./images/icon/icon_notice.png);
      background-size: 14px 14px;
      display: inline-block;
      width: 14px;
      height: 14px;
      position: relative;
      top: 2px;
    }

    .aicon {
      font-size: 12px;
      color: #128bed;
    }

    &.notice-tip-blue {
      background: #e7f4ff;
      color: #128bed;
      font-weight: normal;

      .icon-notice {
        //background-image: url(./images/icon/icon_notice_blue.png);
      }
    }
  }

  .ntag.tcap {
    position: relative;
    top: 3px;

    .bicon {
      width: 20px;
      height: 20px;
      background-size: 20px 20px;
      margin-top: -10px;
    }
  }

  .right .ntag.tcap {
    top: 6px;
  }

  .strick-text {
    display: inline-block;
    margin-left: 20px;
    animation: ripple 2s infinite;
    text-decoration: none;
    transform-origin: left;
    margin-right: 10px;
  }

  .ant-popover .ant-popover-inner {
    max-width: 400px;
  }

  .tdrop {
    display: inline-block;
    position: relative;

    .btn {
      border: 1px solid #d6d6d6 !important;
      color: #333 !important;
      background-color: #fff !important;
      box-shadow: none;

      &.active {
        color: #128bed !important;
        border-color: #128bed !important;
      }

      .desc {
        display: inline-block;
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: bottom;
      }
    }

    .dropdown-menu {
      background: #fff;
      max-height: 400px;
      overflow-y: auto;

      & > li > a {
        padding: 6px 15px;
        font-size: 13px;

        &:hover {
          color: #128bed;
          background-color: @rover-table-header-bg;
        }
      }
    }

    .tdrop-content {
      width: 500px;
      padding: 15px;
    }

    .tdrop-content-line:not(:first-child) {
      border-top: solid 1px #eee;
      padding-top: 10px;
      margin-top: 10px;
    }

    .tdrop-head {
      color: #999;
      margin-bottom: 5px;
    }
  }

  .right {
    float: right;
    margin-top: -3px;

    & > .bicon {
      margin-right: 2px;
    }

    .btn {
      margin-left: 6px;

      .picon-exporting {
        background-size: 18px 18px;
        width: 18px;
        height: 18px;
      }
    }

    .line-text {
      line-height: 32px;
    }
  }

  .tinput {
    display: inline-block;
    margin-left: 5px;

    .search-inner {
      position: relative;
      border: solid 1px #d6d6d6;
      display: inline-block;
      height: 34px;
      vertical-align: middle;
      color: #999;
      padding: 6px 8px 6px 26px;
      border-radius: 2px;

      .aicon {
        position: absolute;
        left: 7px;
        top: 8px;
        font-size: 15px;
      }
    }
  }

  .tinput.open {
    width: 305px;
    position: relative;

    .open-wrap {
      width: 255px;
      display: inline-block;
      vertical-align: middle;
    }

    input {
      width: 205px;
      border-color: #128bed;
      padding-left: 8px;
      padding-right: 25px;
    }

    .btn {
      margin-left: 0;
    }

    .clear-searchkey {
      top: 9px;
      right: 108px;
    }
  }

  .ntag.vip {
    position: relative;
    top: -1px;
    margin-right: 6px;
  }

  .ntag.vip + .notice-tip {
    margin-left: 2px;
  }

  .trarelat {
    margin-left: 3px;

    a {
      color: #999;
      font-size: 14px;
      margin-left: 10px;

      .count {
        color: #f5a623;
      }

      &:hover {
        color: #f5a623;
      }
    }
  }

  .watermark {
    width: 92px;
    height: 26px;
    float: right;
    //background-image: url(./images/watermark.png);
    margin-left: 10px;
    background-size: 92px 26px;
    margin-top: 1px;
  }

  .expand-btn {
    float: right;
    color: #666;
    cursor: pointer;
  }

  &.sub {
    .title {
      font-size: 14px;
      color: #666;
      font-weight: bold;
      margin-top: 0;
      margin-bottom: 0;
      display: inline;
    }

    .tbadge {
      font-size: 14px;
    }
  }
}

.tcaption + .tcaption {
  margin-top: -5px;
}

.view-section .view-panel {
  border: solid 1px @rover-table-border-color;
  background: #f6faff;
  width: 100%;
  margin-bottom: 30px;

  .icon-img {
    width: 18px;
    position: relative;
    top: -1px;
    margin-right: 6px;
  }

  .ntag {
    width: 62px;
    text-align: center;
  }

  .title {
    font-weight: bold;
  }

  td {
    padding: 10px 15px;
  }

  .btn-primary {
    width: 82px;
    font-size: 12px;
  }
}

.risk-section .risk-panel {
  border: solid 1px #ffdadf;
  background: rgba(253, 237, 237, 0.2);
  width: 100%;
  margin-bottom: 30px;

  .text-right {
    padding-right: 15px;
  }

  .btn-danger {
    font-size: 12px;
    width: 82px;
  }

  .content-td {
    padding: 15px;

    .rline:not(:last-child) {
      margin-bottom: 15px;
    }

    .rdot {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #F04040;
      border-radius: 4px;
      vertical-align: middle;
    }

    em {
      color: #F04040;
      font-style: normal;
    }
  }
}

.graph-section .ntable th {
  height: 45px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.graph-section .tupu-tool-save {
  background-position-y: -24px;
}

.graph-section .btn {
  padding: 6px 8px;
  margin-left: 6px;
}

.graph-section .btn > i {
  margin-top: -12px;
  top: 7px;
}

.graph-section .btn:hover > i {
  background-position-x: -24px;
}

.graph-section .app-loading {
  position: absolute !important;
  left: 0;
  top: 0;
  width: 100%;
  height: 417px !important;
}

.ntable-list {
  width: 100%;
}

.ntable-list td {
  padding: 15px;
  border-right: none;
  border-left: none;
  border-color: #eee;
}

.ntable-list .title {
  display: block;
  color: #222;
  font-size: 18px;
  margin-bottom: 6px;
  font-weight: bold;
}

.ntable-list .title a {
  color: #222;
}

.ntable-list a.title {
  cursor: pointer;
}

.ntable-list tr:hover {
  background: #f5f9ff;
}

.ntable-list tr:hover {
  a.title,
  .title a {
    color: #128bed;
  }
}

.ntable-list td {
  border-top: solid 1px #eee;
  border-bottom: solid 1px #eee;
}

.ntable-list td:not(:last-child) {
  padding-right: 0;
}

.ntable-list tr:last-child td {
  border-bottom: none;
}

.ntable-list .title {
  display: block;
  color: #222;
  font-size: 18px;
  margin-bottom: 3px;
  font-weight: bold;
}

.ntable-list .tags {
  margin-top: 5px;
  margin-bottom: 5px;

  .ntag {
    margin-bottom: 3px;
  }
}

.ntable-list .statustd {
  width: 140px;
  padding-left: 0;
  text-align: right;
}

.ntable-list .statustd .status {
  padding: 6px 10px;
  border: solid 1px #eee;
  border-radius: 2px;
  word-break: keep-all;
  white-space: nowrap;
}

.ntable-list .content {
  margin-bottom: 6px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.ntable-list .content.clampContent {
  position: relative;
  max-height: 44px;
  padding-right: 0;
}

.ntable-list .content.clampContent .more {
  position: absolute;
  right: 0;
  bottom: 0;
  background: #fff;
}

.ntable-list tr:hover .content.clampContent .more {
  background: #f5f9ff;
}

.ntable-list .statustd .status.text-primary {
  border-color: #128bed;
  color: #128bed;
  background: #fff;
}

.ntable-list .statustd .status.text-success {
  border-color: #094;
  color: #094;
  background: #fff;
}

.ntable-list .statustd .status.text-danger {
  border-color: #F04040;
  color: #F04040;
  background: #fff;
}

.ntable-list .rline:not(:last-child) {
  margin-bottom: 5px;
}

.ntable-list .imgtd {
  width: 115px;
  vertical-align: middle;
  padding-left: 25px;
  padding-right: 5px;
}

.ntable-list .number {
  display: inline-block;
  min-width: 20px;
  height: 20px;
  border-radius: 6px;
  background: #eee;
  color: #666;
  font-size: 13px;
  text-align: center;
  line-height: 20px;
  padding: 0 5px;
  position: relative;
  left: 5px;
}

.ntable-list .number.top {
  background: #128bed;
  color: #fff;
}

.relate-info {
  margin-top: 6px;
  color: #999;
  font-size: 13px;
}

.ntable-list .relate-info .val {
  color: #666;
}

.ntable-list .relate-info a.val {
  color: #128bed;
}

.ntable-list .relate-info .val.long-text {
  display: inline-block;
  vertical-align: top;
  max-width: 920px;
}

.ntable-list .relate-info .val.long-text > a {
  display: inline-block;
}

.ntable-list .relate-info .f {
  margin-right: 25px;
}

.ntable-list .relate-info .sf:not(:last-child)::after {
  content: ' ';
  display: inline-block;
  width: 1px;
  height: 14px;
  background: #eee;
  vertical-align: middle;
  position: relative;
  margin-left: 10px;
  margin-right: 8px;
}

.ntable-content {
  position: relative;
}

.ntable-scroll {
  overflow-x: auto;
  border-right: @rover-table-border-color 1px solid;
  border-left: @rover-table-border-color 1px solid;
  margin-bottom: 20px;

  .ntable {
    margin-bottom: 0;

    th,
    td {
      &:first-child {
        border-left: none;
      }

      &:last-child {
        border-right: none;
      }
    }
  }

  &.dragable .ntable {
    //cursor: url(./images/cur/openhand2.cur) 8 8, default;
  }

  &.dragable.move .ntable {
    //cursor: url(./images/cur/closedhand2.cur) 8 8, move;
    user-select: none;
  }

  &.dragable {
    td > * {
      cursor: text;
    }

    td > a,
    th > a {
      cursor: pointer;
    }
  }

  &.fixed-over .fixed-in-body {
    visibility: hidden;
  }
}

.ntable-fixed {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
  position: absolute;
  left: 1px;
  top: 0;
  background: #fff;
  display: none;

  & > .ntable {
    table-layout: fixed;
  }
}

.mtcaption {
  margin-bottom: 15px;
  color: #666;
}

.dtable {
  width: 100%;

  td {
    padding: 5px 0;
  }
}

.ntable > .ant-table-wrapper {
  // border-right: @rover-table-border-color 1px solid;

  table {
    border-collapse: collapse;
  }

  .ant-table-scroll > div {
    margin-right: -5px;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-width: 0;
    }
  }

  .ant-table-content colgroup > col {
    min-width: 70px !important;
  }

  .ant-table-content > .ant-table-body {
    overflow: hidden;
    margin-right: -1px;
  }

  &.dragable .ant-table-tbody {
    //cursor: url(./images/cur/openhand2.cur) 8 8, default;
  }

  &.dragable .move .ant-table-tbody {
    //cursor: url(./images/cur/closedhand2.cur) 8 8, move;
    user-select: none;
  }

  &.dragable {
    td > * {
      cursor: text;
    }

    td > a,
    th > a {
      cursor: pointer;
    }
  }

  .ant-table-body.drag {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .ant-table-thead {
    background: @rover-table-header-bg;
  }

  .ant-table-thead > tr > th {
    background: none;
    color: #333;
    border-bottom: @rover-table-border-color 1px solid;
    border-right: @rover-table-border-color 1px solid;
    transition: none;
    font-weight: normal;
    padding: 10px 8px 10px 10px;
    white-space: nowrap;
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid @rover-table-border-color;
    transition: none;
    padding: 10px 8px 10px 10px;
  }

  .ant-table-tbody > tr:hover > td,
  .ant-table-tbody > tr.ant-table-row-hover > td {
    background: #f3f9fe;
  }

  .ant-table-header-column {
    color: #333;
  }

  .resize-table-th {
    position: relative;

    .table-draggable-handle {
      height: 100% !important;
      bottom: 0;
      left: auto !important;
      right: -5px;
      cursor: col-resize;
      touch-action: none;

      &:hover::after,
      &.dragging::after {
        content: ' ';
        background: #128bed;
        width: 2px;
        top: 0;
        bottom: 0;
        right: 4px;
        position: absolute;
      }
    }
  }

  // 火狐
  @-moz-document url-prefix() {
    .ant-table-scroll .ant-table-fixed {
      width: 100% !important;
    }
  }

  // 谷歌
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    .ant-table-scroll .ant-table-fixed {
      width: 100% !important;
    }
  }

  .hide-row {
    td:not(:first-child) {
      visibility: hidden;
    }
  }

  .ant-table-column-sorters {
    cursor: pointer;

    .ant-table-column-sorter-inner {
      margin-top: -0.4em;
      margin-left: 0.2em;
    }
  }
}
