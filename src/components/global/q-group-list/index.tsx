import { VNode } from 'vue';
import { isNil } from 'lodash';
import mixins from 'vue-typed-mixins';

import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';
import QRichTable from '@/components/global/q-rich-table';
import QFirmSection from '@/components/global/q-firm-section';
import QFirmTabs from '@/components/global/q-firm-tabs';
import QLink from '@/components/global/q-link';

const listMixin = createMixin(['current', 'history'], undefined, { autoInit: false });

const QGroupList = mixins(dimensionMixin, listMixin).extend({
  name: 'QGroupList',
  // mixins: [dimensionMixin, listMixin],
  props: {
    keyNo: {
      type: String,
      required: true,
    },
    info: {
      type: Object,
      required: true,
    },
    params: {
      type: Object,
    },
  },
  data() {
    return {};
  },
  computed: {
    initTable(): any[] {
      return [
        {
          title: '公司名称',
          align: 'center',
          dataIndex: 'Name',
          customRender: (text, item) => <QLink to={`/firm/${item.KeyNo || item.keyNo}`}>{text}</QLink>,
        },
        {
          title: `${this.info.name}数量`,
          align: 'center',
          dataIndex: 'Count',
          customRender: (text, item) => <a onClick={() => this.showDetail(item)}>{text}</a>,
        },
      ];
    },
    tabs(): { key: string; label: string; count: number }[] {
      const tabsArr = !isNil(this.info.historyCount)
        ? [
            { key: 'current', label: this.info.name, count: this.info.count },
            { key: 'history', label: `历史${this.info.name}`, count: this.info.historyCount },
          ]
        : [];
      if (tabsArr.length) {
        this.mListInit('current');
        this.mListInit('history');
      } else {
        this.mListInit();
      }
      return tabsArr;
    },
  },
  methods: {
    showDetail(item) {
      const { Count, ImageUrl, KeyNo, keyNo, Name, Result } = item;
      (this as any).$modal.showDimension(
        'groupDialog',
        {
          currentKey: this.mListActiveKey,
          Count,
          ImageUrl,
          KeyNo: KeyNo || keyNo,
          Name,
          Result,
        },
        { info: this.info }
      );
    },
    fetchDataSource({ key, pagination }) {
      const params = {
        ...this.params,
        isValid: key === 'history' ? 0 : 1,
        type: this.info.type,
        ...pagination,
      };

      return this.mDimenGetList(params, this.info.api, 'group');
    },
  },
  render(): VNode {
    // 针对第一个tab是0的情况做特殊处理
    if (this.tabs.length > 1 && this.tabs[0].count === 0) {
      this.mListActiveKey = this.tabs[1].key;
    }
    return (
      <div>
        {this.tabs.length ? (
          <QFirmSection title={this.info.name} withLogo={false} type={'root'}>
            <QFirmSection>
              <QFirmTabs slot="title" vModel={this.mListActiveKey} tabs={this.tabs} />
              {this.tabs.map((tab) => {
                return (
                  <QRichTable
                    key={tab.key}
                    v-show={this.mListActiveKey === tab.key}
                    {...this.mListGetTableData(tab.key)}
                    columns={this.initTable}
                  />
                );
              })}
            </QFirmSection>
          </QFirmSection>
        ) : (
          <QFirmSection title={this.info.name} count={this.info.count || null} type={'root'}>
            <QRichTable key={this.info.key} {...this.mListGetTableData()} columns={this.initTable} />
          </QFirmSection>
        )}
      </div>
    );
  },
});

export default QGroupList;
