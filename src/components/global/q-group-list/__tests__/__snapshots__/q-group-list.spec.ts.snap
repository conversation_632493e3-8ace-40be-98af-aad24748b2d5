// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QGroupList > render 1`] = `
<div>
  <div class="q-section container root">
    <div class="q-section-header header" style="display: none;">
      <div class="toolbar"></div>
    </div>
    <div class="body q-firm-body">
      <div class="q-section container sub">
        <div class="q-section-header header">
          <div class="title">
            <div class="container">
              <div class="tab active"><span class="label"></span><i class="count" style="display: none;"></i>
                <anonymous-stub trigger="hover" placement="top" transitionname="zoom-big-fast" aftervisiblechange="[Function]" overlay="" overlaystyle="[object Object]" prefixcls="ant-tooltip" mouseenterdelay="0.1" mouseleavedelay="0.1" align="[object Object]" builtinplacements="[object Object]" style="display: none;">
                  <q-icon-stub type="icon-a-shuomingxian" class="tooltip"></q-icon-stub>
                </anonymous-stub>
              </div>
              <div class="tab"><span class="label">历史undefined</span><i class="count">1</i>
                <anonymous-stub trigger="hover" placement="top" transitionname="zoom-big-fast" aftervisiblechange="[Function]" overlay="" overlaystyle="[object Object]" prefixcls="ant-tooltip" mouseenterdelay="0.1" mouseleavedelay="0.1" align="[object Object]" builtinplacements="[object Object]" style="display: none;">
                  <q-icon-stub type="icon-a-shuomingxian" class="tooltip"></q-icon-stub>
                </anonymous-stub>
              </div>
            </div>
          </div>
          <div class="toolbar"></div>
        </div>
        <div class="body q-firm-body">
          <div class="container">
            <div class="ant-spin-nested-loading">
              <div>
                <div class="ant-spin ant-spin-spinning"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>
              </div>
              <div class="ant-spin-container ant-spin-blur">
                <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
                  <div class="ant-spin-nested-loading">
                    <div class="ant-spin-container">
                      <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered ant-table-empty">
                        <div class="ant-table-content">
                          <!---->
                          <div class="ant-table-body">
                            <table class="">
                              <colgroup>
                                <col style="width: 58px; min-width: 58px;">
                                <col>
                                <col>
                              </colgroup>
                              <thead class="ant-table-thead">
                                <tr>
                                  <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                          </div></span></th>
                          <th key="Name" align="left" class="ant-table-align-center" style="text-align: center;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">公司名称</span><span class="ant-table-column-sorter"></span>
                        </div></span></th>
                        <th key="Count" align="left" class="ant-table-align-center ant-table-row-cell-last" style="text-align: center;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">undefined数量</span><span class="ant-table-column-sorter"></span>
                      </div></span></th>
                      </tr>
                      </thead>
                      <tbody class="ant-table-tbody"></tbody>
                      </table>
                    </div>
                    <div class="ant-table-placeholder">
                      <div class="empty"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="container" style="display: none;">
      <div class="ant-spin-nested-loading">
        <div>
          <div class="ant-spin ant-spin-spinning"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>
        </div>
        <div class="ant-spin-container ant-spin-blur">
          <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
            <div class="ant-spin-nested-loading">
              <div class="ant-spin-container">
                <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered ant-table-empty">
                  <div class="ant-table-content">
                    <!---->
                    <div class="ant-table-body">
                      <table class="">
                        <colgroup>
                          <col style="width: 58px; min-width: 58px;">
                          <col>
                          <col>
                        </colgroup>
                        <thead class="ant-table-thead">
                          <tr>
                            <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="Name" align="left" class="ant-table-align-center" style="text-align: center;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">公司名称</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="Count" align="left" class="ant-table-align-center ant-table-row-cell-last" style="text-align: center;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">undefined数量</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody"></tbody>
                </table>
              </div>
              <div class="ant-table-placeholder">
                <div class="empty"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
`;
