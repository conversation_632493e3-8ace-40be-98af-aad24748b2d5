import { mount } from '@vue/test-utils';

import QRichTable from '..';
import QTag from '../../q-tag';

// QTag 是函数式组件，不能使用一些特定断言方法，例如：expect(wrapper.find(QTag).props('type')).toBe('success');
vi.mock('../../q-tag', () => ({
  default: {
    name: 'QTag',
    props: ['type'],
    template: '<div data-stubbed="1" :type="type"><slot></slot></div>',
  },
}));

describe('QRichTable', () => {
  test('render: empty', () => {
    const wrapper = mount(QRichTable, {
      propsData: {
        columns: [],
      },
    });
    expect(wrapper.text()).toContain('暂时没有找到相关数据');
    expect(wrapper).toMatchSnapshot();
  });

  test('render: dataSource', () => {
    const wrapper = mount(QRichTable, {
      propsData: {
        rowKey: 'id',
        dataSource: [
          {
            id: '1',
            title: 'AAA',
          },
        ],
        columns: [
          {
            dataIndex: 'title',
          },
        ],
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test.each([
    [
      'index',
      {
        dataSource: [
          {
            id: 1,
          },
        ],
        columns: [
          {
            title: 'index',
            dataIndex: 'index',
            scopedSlots: { customRender: 'index' },
          },
        ],
      },
      {
        thead: 'index',
        tbody: '1',
      },
    ],
    [
      'money',
      {
        dataSource: [
          {
            id: 1,
            money: 100000,
          },
        ],
        columns: [
          {
            title: 'money',
            dataIndex: 'money',
            scopedSlots: { customRender: 'money' },
          },
        ],
      },
      {
        thead: 'money',
        tbody: '100,000.00',
      },
    ],
    [
      'date',
      {
        dataSource: [
          {
            id: 1,
            date: '2023-01-01',
          },
        ],
        columns: [
          {
            title: 'date',
            dataIndex: 'date',
            scopedSlots: { customRender: 'date' },
          },
        ],
      },
      {
        thead: 'date',
        tbody: '2023-01-01',
      },
    ],
    [
      'readableAmount',
      {
        dataSource: [
          {
            id: 1,
            readableAmount: 100000,
          },
        ],
        columns: [
          {
            title: 'readableAmount',
            dataIndex: 'readableAmount',
            scopedSlots: { customRender: 'readableAmount' },
          },
        ],
      },
      {
        thead: 'readableAmount',
        tbody: '100,000',
      },
    ],
  ])('render: customRender %s', (slotName, context, expected) => {
    const wrapper = mount(QRichTable, {
      propsData: {
        showIndex: false,
        rowKey: 'id',
        dataSource: context.dataSource,
        columns: context.columns,
      },
    });
    expect(wrapper.find('thead').text()).toBe(expected.thead);
    expect(wrapper.find('tbody').text()).toBe(expected.tbody);
  });

  test.each([
    [
      '',
      {
        type: 'success',
        text: '-',
      },
    ],
    [
      '存续（在营、开业、在册）',
      {
        type: 'success',
        text: '存续',
      },
    ],
    [
      '注销',
      {
        type: 'danger',
        text: '注销',
      },
    ],
    [
      '筹建',
      {
        type: 'warning',
        text: '筹建',
      },
    ],
  ])('render: customRender status - %s', (condition, expected) => {
    const wrapper = mount(QRichTable, {
      propsData: {
        showIndex: false,
        rowKey: 'id',
        dataSource: [
          {
            id: 1,
            status: condition,
          },
        ],
        columns: [
          {
            title: 'status',
            dataIndex: 'status',
            scopedSlots: { customRender: 'status' },
          },
        ],
      },
      stubs: {
        QTag,
      },
    });
    if (condition !== '') {
      expect(wrapper.findComponent(QTag).exists()).toBe(true);
      expect(wrapper.findComponent(QTag).props('type')).toBe(expected.type);
    }
    expect(wrapper.find('thead').text()).toBe('status');
    expect(wrapper.find('tbody').text()).toBe(expected.text);
  });

  test.each([
    [
      'html',
      {
        dataSource: [
          {
            id: 1,
            html: '<div><strong data-test="1">AAA</strong></div>',
          },
        ],
        columns: [
          {
            title: 'html',
            dataIndex: 'html',
            scopedSlots: { customRender: 'html' },
          },
        ],
      },
    ],
    [
      'richText',
      {
        dataSource: [
          {
            id: 1,
            richText: ['A', 'B', 'C'],
          },
        ],
        columns: [
          {
            title: 'richText',
            dataIndex: 'richText',
            scopedSlots: { customRender: 'richText' },
          },
        ],
      },
    ],
    [
      'caseReason',
      {
        dataSource: [
          {
            id: 1,
            caseReason: 'AAA',
          },
        ],
        columns: [
          {
            title: 'caseReason',
            dataIndex: 'caseReason',
            scopedSlots: { customRender: 'caseReason' },
          },
        ],
      },
    ],

    [
      'image',
      {
        dataSource: [
          {
            id: 1,
            image: 'image.jpg',
          },
        ],
        columns: [
          {
            title: 'image',
            dataIndex: 'image',
            scopedSlots: { customRender: 'image' },
          },
        ],
      },
    ],
    [
      'shrinkContent',
      {
        dataSource: [
          {
            id: 1,
            shrinkContent: 'AAA',
          },
        ],
        columns: [
          {
            title: 'shrinkContent',
            dataIndex: 'shrinkContent',
            scopedSlots: { customRender: 'shrinkContent' },
          },
        ],
      },
    ],
    [
      'expanded',
      {
        dataSource: [
          {
            id: 1,
            expanded: 'AAA',
            title: 'company title aaa',
          },
        ],
        columns: [
          {
            title: 'expanded',
            dataIndex: 'expanded',
            // NOTE: 特殊处理：`expanded` 为封装在组件内部的操作按钮，inject 对应外部渲染的 scopedSlot 名称
            scopedSlots: { customRender: 'expanded' },
          },
          {
            title: 'expandedTitleRender',
            dataIndex: 'title',
            scopedSlots: { customRender: 'expandedTitleRender' },
          },
        ],
      },
    ],
  ])('render: customRender %s', (slotName, context) => {
    const wrapper = mount(QRichTable, {
      propsData: {
        showIndex: false,
        rowKey: 'id',
        dataSource: context.dataSource,
        columns: context.columns,
      },
      scopedSlots: {
        expandedTitleRender: (data) => data ?? '-',
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  test.each([
    [
      'empty',
      {
        dataSource: [
          {
            id: 1,
            CaseRoleSearch: '-',
          },
        ],
        columns: [
          {
            title: 'caseIdentity',
            scopedSlots: { customRender: 'caseIdentity' },
          },
        ],
      },
    ],
    [
      'has data',
      {
        dataSource: [
          {
            id: 1,
            CaseRoleSearch: `[{"N":"COMPANY_ID","RL":[{"T":"T1","R":"R1","LRD":"获得支持"},{"T":"T2","R":"R2","LRD":"诉讼中止"},{"T":"T3","R":"R3","LRD":"不被支持"},{"T":"T4","R":"R4","LRD":"X"}]}]`,
          },
        ],
        columns: [
          {
            title: 'caseIdentity',
            scopedSlots: { customRender: 'caseIdentity' },
          },
        ],
      },
    ],
  ])('render: customRender caseIdentity %s', (condition, { dataSource, columns }) => {
    const wrapper = mount(QRichTable, {
      propsData: {
        showIndex: false,
        rowKey: 'id',
        companyId: 'COMPANY_ID',
        dataSource,
        columns,
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
