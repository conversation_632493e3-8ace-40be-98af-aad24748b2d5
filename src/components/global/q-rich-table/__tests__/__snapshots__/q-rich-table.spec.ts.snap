// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QRichTable > render: customRender caseIdentity empty 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="0" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">caseIdentity</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                    <td class="">-</td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: customRender caseIdentity has data 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="0" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">caseIdentity</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                    <td class="">
                      <div class="htag">
                        <div class="line">T1R1<span class="text-success"> [获得支持]</span></div>
                        <div class="line">T2R2<span class="text-warning"> [诉讼中止]</span></div>
                        <div class="line">T3R3<span class="text-danger"> [不被支持]</span></div>
                        <div class="line">T4R4<span class="text-gray"> [X]</span></div>
                      </div>
                    </td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: customRender caseReason 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="caseReason" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">caseReason</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                    <td class="">
                      <div class="container inherit">-</div>
                    </td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: customRender expanded 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="expanded" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">expanded</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="title" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">expandedTitleRender</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              </tr>
              </thead>
              <tbody class="ant-table-tbody">
                <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                  <td class="">
                    <div class="ant-row-flex ant-row-flex-space-between ant-row-flex-middle expanded">
                      <div class="ant-col" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">undefined</div>
                    </div>
                  </td>
                  <td class="">company title aaa</td>
                </tr>
              </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
`;

exports[`QRichTable > render: customRender html 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="html" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">html</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                    <td class="">
                      <div>
                        <div><strong>AAA</strong></div>
                      </div>
                    </td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: customRender image 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="image" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">image</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                    <td class=""><img src="image.jpg" class="avatar"></td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: customRender richText 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="richText" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">richText</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                    <td class="">
                      <div><br><br></div>
                    </td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: customRender shrinkContent 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="shrinkContent" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">shrinkContent</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody">
                  <tr class="ant-table-row ant-table-row-level-0" data-row-key="1" style="vertical-align: middle;">
                    <td class="">
                      <div class="root" style="position: relative; overflow: hidden; height: auto;">
                        <div class=""><span>AAA</span></div><a class="btn clamp-btn" style="display: none;">...展开</a>
                      </div>
                    </td>
                  </tr>
                </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`QRichTable > render: dataSource 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col style="width: 58px; min-width: 58px;">
                      <col>
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="title" align="left" class="ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title"></span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              </tr>
              </thead>
              <tbody class="ant-table-tbody">
                <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                  <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                  <td class="">AAA</td>
                </tr>
              </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
`;

exports[`QRichTable > render: empty 1`] = `
<div class="container">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered ant-table-empty">
              <div class="ant-table-content">
                <!---->
                <div class="ant-table-body">
                  <table class="">
                    <colgroup>
                      <col style="width: 58px; min-width: 58px;">
                    </colgroup>
                    <thead class="ant-table-thead">
                      <tr>
                        <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                </tr>
                </thead>
                <tbody class="ant-table-tbody"></tbody>
                </table>
              </div>
              <div class="ant-table-placeholder">
                <div class="container"><img src="/src/shared/components/assets/images/icon_empty_text.svg" width="50px" height="50px">
                  <div class="description">暂时没有找到相关数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;
