import Draggable from 'vuedraggable';
import { defineComponent } from 'vue';

const DraggableWrapper = defineComponent({
  inject: ['data'],
  setup() {
    return {};
  },
  render() {
    const parent = (this as any).data;
    return (
      <Draggable
        tag="tbody"
        animation="300"
        handle=".drag-handle"
        list={parent.dataSource}
        onChange={(data) => {
          parent.$emit('drag', parent.dataSource);
        }}
      >
        {this.$slots.default}
      </Draggable>
    );
  },
});

export default DraggableWrapper;
