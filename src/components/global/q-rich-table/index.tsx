import { isObject, isBoolean, get, isNil, isFunction, min, toNumber, isEmpty, isString, isNaN } from 'lodash';
import { Table, Pagination, Spin, Row, Tooltip, Col, ConfigProvider, Popover } from 'ant-design-vue';
import Vue, { PropType, VNode, VNodeData } from 'vue';

import { dateFormat } from '@/utils/format';
import QPartyInfo from '@/components/global/q-party-info';
import { EPartyInfo } from '@/components/global/q-rich-table/utils';
import { numberToHuman } from '@/utils/number-formatter';
import ClampContent from '@/components/clamp-content';
import ICONSHOUQI from '@/assets/images/icon-shouqi.svg';
import ICONEXPAND from '@/assets/images/icon-expand.svg';

import QGlossaryInfo from '../q-glossary-info';
import QRichTableEmpty from './components/empty';
import styles from './q-rich-table.module.less';
import DraggableWrapper from './draggable-wrapper';
import QTag from '../q-tag';
// import VueDraggableResizable from 'vue-draggable-resizable';
// import 'vue-draggable-resizable/dist/VueDraggableResizable.css';

type NodeType = VNode | string | number;
type renderFunction = (text: any, record: any, index: number) => VNodeData | NodeType | NodeType[];
type customRenderFunction = (column: any) => VNode;

export type IQRichTableColumn = Partial<{
  title: string | VNode;
  key: string;
  dataIndex: string;
  customRender: renderFunction;
  customHeaderCell: customRenderFunction;
  scopedSlots: Record<string, string>;
  width: number | string;
  align: 'left' | 'right' | 'center';
  fixed: boolean | string;
  sorter: boolean;
  [key: string]: any;
}>;

export interface IQRichTablePagination {
  size?: string;
  total: number;
  current: number;
  pageSize: number;
  defaultCurrent?: number;
  defaultPageSize?: number;
  showQuickJumper?: boolean | object;
  showSizeChanger?: boolean;
  pageSizeOptions?: string[];
  onChange?(current: number, pageSize: number): void;
  onShowSizeChange?(current: number, pageSize: number): void;
}

export interface IQRichTableScroll {
  x: number | boolean;
  y: number;
  scrollToFirstRowOnChange: boolean;
}

const DEFAULT_SCROLL_CONFIG: Partial<IQRichTableScroll> = {
  x: 1068,
};

export const DEFAULT_PAGINATION_CONFIG: IQRichTablePagination = {
  // size: 'small',
  total: 0,
  current: 1,
  defaultCurrent: 1,
  pageSize: 10,
  defaultPageSize: 10,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '30', '50', '100'],
};

const QRichTable = Vue.extend({
  name: 'QRichTable',

  props: {
    tableLayout: {
      type: String as PropType<'fixed' | 'auto'>,
      default: 'auto',
    },
    columns: {
      type: Array as PropType<IQRichTableColumn[]>,
      required: true,
      default: () => [],
    },
    dataSource: {
      type: Array as PropType<any[]>,
      required: false,
    },
    pagination: {
      type: [Object, Boolean] as PropType<Partial<IQRichTablePagination> | boolean>,
      required: false,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: [String, Function],
    },
    /**
     * TODO 页面公司名称
     * 取全局公司id 这边先写死一个
     */
    companyId: {
      type: String,
      default: '84c17a005a759a5e0d875c1ebb6c9846',
    },
    partyInfoType: {
      type: String,
      default: 'LI_AN',
    },
    scroll: {
      type: [Object, Boolean] as PropType<Partial<IQRichTableScroll> | boolean>,
      required: false,
    },
    customScroll: {
      type: [Object, Boolean] as PropType<Partial<IQRichTableScroll> | boolean>,
      required: false,
    },
    /**
     * 是否显示表头
     */
    showHeader: {
      type: Boolean,
      default: true,
    },
    /**
     * 表头位置
     */
    headerAlign: {
      type: String,
      default: 'left',
    },
    /**
     * 是否显示序号
     */
    showIndex: {
      type: Boolean,
      default: true,
    },
    // 此表格是否固定列
    isFixed: {
      type: Boolean,
      default: false,
    },
    expandedDesc: {
      type: String,
      default: '展开查看二级股东',
    },
    showExpanded: {
      type: Function as PropType<(record: any) => boolean>,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      default: (record: any) => false,
      validator: (value: any) => isFunction(value),
    },
    // 空数据 Icon 大小
    emptySize: {
      type: String,
      default: '50px',
    },
    truncated: {
      type: Boolean,
      default: false,
    },
    // 支持批量操作
    rowSelection: Object,
    // 一直显示pagination
    alwaysShowPagination: {
      type: Boolean,
      default: false,
    },
    emptyMinHeight: {
      type: String,
    },
    // 页面是否有面包屑
    pageHasLink: {
      type: Boolean,
      default: false,
    },
    // 特殊情况下的边距
    paddingDistance: {
      type: [Number, String],
      default: 0,
    },
    customRow: {
      type: Function,
      default: () => ({}),
    },
    draggable: {
      type: Boolean,
      default: false,
    },
    emptyText: {
      type: String,
      default: '暂时没有找到相关数据',
    },
    /**
     * 获取相对的滚动容器
     * TODO: 顶级滚动容器不要写死
     */
    getScrollContainer: {
      type: Function,
      default: () => document.getElementById('workbench-layout-main') || window,
    },
    /**
     * 是否显示边框
     */
    bordered: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      expandedRowKeys: [] as Array<string | number>,
      sorter: { columnKey: '', order: '' },

      isTableScrollSynced: false,
      tableElements: [],
      tableLastScrollTop: null,
      tableLastScrollLeft: null,
    };
  },

  provide() {
    return {
      data: this,
    };
  },

  computed: {
    paginationConfig(): IQRichTablePagination {
      if (isBoolean(this.pagination)) {
        return DEFAULT_PAGINATION_CONFIG;
      }

      return {
        ...DEFAULT_PAGINATION_CONFIG,
        ...this.pagination,
      };
    },
    tableConfig(): IQRichTableColumn[] {
      const { columns, showIndex, customRenderIndex, isFixed } = this as any;
      // 遍历columns设置一些默认值
      columns.map((column) => {
        switch (column?.scopedSlots?.customRender) {
          case 'date':
            // column.align = 'center';
            column.width = column?.width ?? 103;
            return column;
          case 'action':
            // column.align = 'center';
            column.width = column?.width ?? 50;
            return column;
          default:
            return column;
        }
      });
      if (!showIndex) {
        return columns;
      }
      const alignValue: any = this.headerAlign;
      const preset: IQRichTableColumn[] = [
        {
          title: '序号',
          width: 58,
          fixed: isFixed ? true : '',
          align: alignValue,
          customRender: customRenderIndex,
        },
      ];
      return preset.concat(columns);
    },
  },

  mounted() {
    (this as any).$nextTick(() => {
      (this as any).addScrollWheelToTable();
    });
  },

  beforeDestroy() {
    (this as any).removeScrollWheelFromTable();
  },

  methods: {
    hasExpanded(itemId: number) {
      return (this as any).expandedRowKeys.indexOf(itemId) > -1;
    },
    /**
     * FIXME: ID 不同在 story 与 项目中表现不同（BUG）
     * @description 根据页码及当前顺序生成当前表格中的唯一ID
     */
    getItemUniqId(itemIndex: number) {
      const { pageSize, current } = (this as any).paginationConfig;
      return (pageSize as number) * ((current as number) - 1) + itemIndex;
    },
    /**
     * 渲染序号
     */
    customRenderIndex(text: any, record: any, index: number) {
      const id = (this as any).getItemUniqId(index);
      // 折叠状态
      const expanded = (this as any).hasExpanded(id);
      return {
        attrs: {
          rowSpan: expanded ? 2 : 1,
          // 有新增标签时候的特殊处理，增加行高
          style: record.needNewTag ? 'position: relative;padding: 20px 10px' : 'position: relative;',
          dataIndex: index,
        },
        children: [
          <span>{id + 1}</span>,
          record.needNewTag ? (
            <QTag type="warning" size="small" style={{ position: 'absolute', top: 0, left: 0, borderRadius: '0 0 2px 0' }}>
              新增
            </QTag>
          ) : null,
        ],
      };
    },
    handleTableChange(pagination, filters, sorter) {
      (this as any).sorter = sorter;
      this.$emit('change', {
        pagination,
        filters,
        sorter,
      });
    },
    handleScrollToTop() {
      const wrapper = this.$refs.tbWrapper as HTMLElement;
      const bodyTable = wrapper.querySelector('.ant-table-scroll .ant-table-body');
      if (!bodyTable) {
        return;
      }
      // 判断当前容器是否有滚动条
      const hasScrollbar = bodyTable.scrollHeight > bodyTable.clientHeight;
      // 如果没有滚动条则退出,使用默认行为
      if (!hasScrollbar) {
        return;
      }
      bodyTable.scrollTop = 0;
    },
    handlePageChange(current: number, pageSize: number) {
      const onChange = get((this as any).pagination, 'onChange');
      if (onChange) {
        onChange(current, pageSize);
      }
      (this as any).handleScrollToTop();
    },
    handlePageSizeChange(current: number, pageSize: number) {
      const onShowSizeChange = get((this as any).pagination, 'onShowSizeChange');
      if (onShowSizeChange) {
        onShowSizeChange(1, pageSize);
      }
    },
    handleExpand(record: Record<string, any>, index: number) {
      const id = record[(this as any).rowKey as string];
      let isExpand: boolean;
      if ((this as any).hasExpanded(id)) {
        (this as any).expandedRowKeys = (this as any).expandedRowKeys.filter((key) => key !== id);
        isExpand = false;
      } else {
        (this as any).expandedRowKeys.push(id);
        isExpand = true;
      }
      this.$emit('expand', {
        isExpand,
        record,
        index,
      });
    },
    scrollConfig() {
      if (!isEmpty((this as any).customScroll)) {
        return (this as any).customScroll;
      }
      if (isObject((this as any).scroll)) {
        // 滚动的表格滚动高度设置为全屏
        return {
          ...DEFAULT_SCROLL_CONFIG,
          ...(this as any).scroll,
          ...((this as any).scroll.y
            ? {
                y: (this as any).pageHasLink
                  ? `calc(100vh - 290px - ${(this as any).paddingDistance}px)`
                  : `calc(100vh - 240px - ${(this as any).paddingDistance}px)`,
              }
            : {}),
        };
      }

      if (!(this as any).scroll) {
        return null;
      }

      return DEFAULT_SCROLL_CONFIG;
    },

    /**
     * 同步表格滚动
     */
    addScrollWheelToTable() {
      if ((this as any).isTableScrollSynced) {
        return;
      }
      // 找到 fixed 元素下在相关元素（左右fixed的列） .ant-table-fixed-left/.ant-table-fixed-right 元素下的 .ant-table-body-inner
      const innerFixed = document.querySelectorAll('.ant-table-body-inner');
      // 找到原有的 table 固定区域
      const innerBody = document.querySelector('.ant-table-body');
      // 给所有相关的元素再加一次
      if (innerFixed.length) {
        (this as any).tableElements = [...innerFixed];
        if (innerBody) {
          (this as any).tableElements.push(innerBody);
        }
        (this as any).tableElements.forEach((el) => {
          el.addEventListener('scroll', (this as any).handleScrollEvent); // 绑定滚动事件
          el.addEventListener('wheel', (this as any).handleWheelEvent); // 绑定滚轮事件
        });
        (this as any).isTableScrollSynced = true;
      }
    },

    // 移除相关元素的相关事件和清空全局数据
    removeScrollWheelFromTable() {
      if (Array.isArray((this as any).tableElements)) {
        (this as any).tableElements.forEach((el) => {
          el.removeEventListener('scroll', (this as any).handleScrollEvent);
          el.removeEventListener('wheel', (this as any).handleWheelEvent);
        });
      }
      (this as any).tableElements = [];
      (this as any).tableLastScrollTop = null;
      (this as any).tableLastScrollLeft = null;
      (this as any).isTableScrollSynced = false;
    },

    /**
     * 滚动事件
     */
    handleScrollEvent(ev) {
      ev.preventDefault();
      const { scrollTop, scrollLeft } = ev.target;
      // 标记最后滚动位置
      (this as any).tableLastScrollTop = scrollTop;
      (this as any).tableLastScrollLeft = scrollLeft;
    },

    /**
     * 滚轮事件
     */
    handleWheelEvent(ev) {
      const bodyTable = document.querySelector('.ant-table-scroll .ant-table-body') as HTMLElement;
      const fixedColumnsBodyLeft = document.querySelector('.ant-table-fixed-left .ant-table-body-inner');
      const fixedColumnsBodyRight = document.querySelector('.ant-table-fixed-right .ant-table-body-inner');
      const deltaX = ev.deltaX;
      const deltaY = ev.deltaY;
      const target = ev.target;
      const scrollTop = (this as any).tableLastScrollTop ? (this as any).tableLastScrollTop + deltaY : deltaY;
      const scrollLeft = (this as any).tableLastScrollLeft ? (this as any).tableLastScrollLeft + deltaX : deltaX;

      // 判断当前容器是否有滚动条
      const hasScrollbar = bodyTable.scrollHeight > bodyTable.clientHeight;
      // 如果没有滚动条则退出,使用默认行为
      if (!hasScrollbar) {
        return;
      }

      ev.preventDefault();
      // 判断是否滚动到顶部或底部，使用外部容器的滚动条持续滚动
      const hasReachTop = bodyTable.scrollTop === 0 && deltaY < 0;
      const hasReachBottom = bodyTable.scrollHeight - bodyTable.scrollTop === bodyTable.clientHeight && deltaY > 0;
      if (hasReachTop || hasReachBottom) {
        const rootScrollContainer = (this as any).getScrollContainer();
        rootScrollContainer.scrollBy(0, ev.deltaY);
        return;
      }

      // 记录滚动位置
      if (fixedColumnsBodyLeft && target !== fixedColumnsBodyLeft) {
        fixedColumnsBodyLeft.scrollTop = scrollTop;
      }
      if (fixedColumnsBodyRight && target !== fixedColumnsBodyRight) {
        fixedColumnsBodyRight.scrollTop = scrollTop;
      }
      if (bodyTable && target !== bodyTable) {
        bodyTable.scrollTop = scrollTop;
        bodyTable.scrollLeft = scrollLeft;
      }
    },
  },

  render() {
    const {
      loading,
      tableConfig,
      paginationConfig,
      rowKey,
      handlePageChange,
      handlePageSizeChange,
      showHeader,
      headerAlign,
      alwaysShowPagination,
      emptyText,
      bordered,
      tableLayout,
    } = this as any;
    const { current, pageSize, total, pageSizeOptions } = paginationConfig;
    // 内置封装通用配置
    const tableScopedSlots = {
      index: (text, record, index) => {
        return paginationConfig.pageSize * (paginationConfig.current - 1) + index + 1;
      },
      date: (text, record, index, meta?) => {
        return dateFormat(text, meta && meta.dateProps);
      },
      html: (text) => {
        return <div domPropsInnerHTML={text || '-'} />;
      },
      status: (status) => {
        if (!status) {
          return '-';
        }
        let formatStatus = status;
        if (status === '存续（在营、开业、在册）') {
          formatStatus = '存续';
        }
        let type = 'success';
        if (['注销', '吊销', '停业', '撤销', '清算', '无效', '责令关闭'].includes(status)) {
          type = 'danger';
        } else if (['筹建', '迁入', '迁出', '歇业'].includes(status)) {
          type = 'warning';
        }
        return <QTag type={type}>{formatStatus}</QTag>;
      },
      richText: (text) => {
        const domNode = text.map((item) => item.Content).join('<br />');
        return <div domPropsInnerHTML={domNode} />;
      },
      // 案件身份slot
      caseIdentity: (text, record) => {
        let roleRL = [];
        const showInfo: VNode[] = [];
        if (record.CaseRoleSearch) {
          let arr;
          try {
            arr = JSON.parse(record.CaseRoleSearch);
          } catch {
            arr = [];
          }
          if (arr?.length) {
            arr.forEach((role) => {
              // TODO parentID
              if (role.N === (this as any).companyId) {
                roleRL = role.RL;
              }
            });
          }
          if (roleRL?.length) {
            roleRL.forEach((rl: any) => {
              let tagHtml: VNode | string = '';
              if (rl.LRD) {
                let classType;
                if (['获得支持', '对方不被支持', '获得部分支持'].includes(rl.LRD)) {
                  classType = 'text-success';
                } else if (['诉讼中止', '被解除查封', '被执行完毕', '被解除查冻扣'].includes(rl.LRD)) {
                  classType = 'text-warning';
                } else if (['不被支持', '对方被支持', '被驳回', '被查封', '被查冻扣'].includes(rl.LRD)) {
                  classType = 'text-danger';
                } else {
                  classType = 'text-gray';
                }
                tagHtml = <span class={`${classType}`}> [{rl.LRD}]</span>;
              }
              showInfo.push(
                <div class="line">
                  {rl.T}
                  {rl.R}
                  {tagHtml}
                </div>
              );
            });
          }
        }
        if (showInfo?.length) {
          return <div class="htag">{showInfo}</div>;
        }
        return '-';
      },
      // 案由
      caseReason: (text, record) => {
        return (
          <QGlossaryInfo tooltip={record.CaseReasonDescription} layout={'inherit'}>
            {record.CaseReason || record.casereason || '-'}
          </QGlossaryInfo>
        );
      },
      image: (text) => {
        return (
          <img
            src={text || '//www.qcc.com/material/theme/chacha/cms/v2/images/no_image.png'}
            onError={(e: any) => {
              e.target.onerror = null;
              e.target.classList.add(styles.error);
              e.target.src = '//www.qcc.com/material/theme/chacha/cms/v2/images/no_image.png';
            }}
            class={styles.avatar}
          />
        );
      },
      shrinkContent: (text, record, index, meta) => {
        const line = meta.ellipsis || 5;
        return (
          <ClampContent clampKey={record.id} line={line}>
            <span domPropsInnerHTML={text || meta.placeholder || '-'}></span>
          </ClampContent>
        );
      },
      partyInfo: (text) => {
        return <QPartyInfo item={text} type={EPartyInfo[(this as any).partyInfoType]} />;
      },
      /**
       * 展开按钮
       */
      expanded: (text, record, index, meta) => {
        const id = record[(this as any).rowKey as string];
        const getExpandObj = () => {
          const hasExpanded = (this as any).hasExpanded(id);
          const tooltip = hasExpanded ? '点击收起' : (this as any).expandedDesc;
          const icon = hasExpanded ? ICONSHOUQI : ICONEXPAND;
          return {
            icon,
            tooltip,
          };
        };
        const showExpandIcon = (this as any).showExpanded(record);
        let expandObj = getExpandObj();
        return (
          <Row type="flex" align="middle" justify="space-between" class={styles.expanded}>
            {showExpandIcon ? (
              <Col>
                <Tooltip title={expandObj.tooltip} placement="bottom" mouseEnterDelay={0.3} mouseLeaveDelay={0.3}>
                  <div class={styles.trigger}>
                    <img
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        (this as any).handleExpand(record, index);
                        (this as any).$nextTick(() => {
                          expandObj = getExpandObj();
                        });
                      }}
                      src={expandObj.icon}
                      // src={hasExpanded ? ICONSHOUQI : ICONEXPAND}
                    />
                  </div>
                </Tooltip>
              </Col>
            ) : null}
            <Col flex="1">{(this as any).$scopedSlots.expandedTitleRender(record)}</Col>
          </Row>
        );
      },
      // 格式化金额
      money: (value?: number | string) => {
        if ((isString(value) && value.trim() === '') || isNil(value)) {
          return '-';
        }
        if (isNaN(Number(value))) {
          return '-';
        }
        const n = typeof value === 'string' ? Number(value) : value;
        const precision = n > 0 && n < 0.01 ? 4 : 2;
        return numberToHuman(n, { precision });
      },
      // 数字千分位，无小数点
      readableAmount: (value?: number | string) => {
        if (
          (typeof value === 'string' && value.trim() === '') ||
          (typeof value === 'string' && Number.isNaN(Number(value))) ||
          isNil(value)
        ) {
          return value || '-';
        }
        const n = typeof value === 'string' ? Number(value) : value;
        return numberToHuman(n);
      },
      ...(this as any).$scopedSlots,
    };
    // 是否展示分页
    const forceShowPagination = alwaysShowPagination && total > 0;
    // 一页能显示全的不展示分页
    const hidePagination = total <= (min(pageSizeOptions.map(toNumber)) as number);
    let dataSource = (this as any).dataSource || [];

    if ((this as any).truncated) {
      dataSource = dataSource.slice((current - 1) * pageSize, min([current * pageSize, total]));
    }

    const scrollConfig = (this as any).scrollConfig() || {};
    return (
      <div class={styles.container} ref="tbWrapper">
        <Spin spinning={loading}>
          <ConfigProvider>
            {loading ? (
              <div class={styles.empty} slot="renderEmpty" />
            ) : (
              <QRichTableEmpty size={(this as any).emptySize} minHeight={(this as any).emptyMinHeight} slot="renderEmpty">
                {emptyText}
              </QRichTableEmpty>
            )}
            <Table
              tableLayout={tableLayout}
              emptyText={emptyText}
              showHeader={showHeader}
              scroll={scrollConfig}
              scrollToFirstRowOnChange={true}
              class={{
                [styles.table]: true,
                [styles.scrollable]: !(this as any).scroll,
                [styles.scrollContentSet]: isBoolean(scrollConfig.x),
              }}
              onChange={(this as any).handleTableChange}
              columns={tableConfig}
              dataSource={dataSource || []}
              bordered={bordered}
              pagination={false}
              scopedSlots={tableScopedSlots}
              rowKey={rowKey}
              transformCellText={({ text }) => (isNil(text) ? '-' : text)}
              // 嵌套表格
              expandIconAsCell={false}
              expandIconColumnIndex={-1}
              expandedRowKeys={(this as any).expandedRowKeys}
              // 自定义嵌套渲染函数
              // expandedRowRender={expandedRowRender}
              indentSize={0}
              onExpand={(this as any).handleExpand}
              customRow={(this as any).customRow}
              components={{
                header: {
                  // 外部通过 `column.customHeaderCell` 覆盖
                  cell: (h, data, children) => {
                    const isSorter = data.class.split(' ').includes('ant-table-column-has-sorters');
                    let title = '点击升序';
                    if ((this as any).sorter?.order === 'ascend' && (this as any).sorter?.columnKey === data.key) {
                      title = '点击降序';
                    } else if ((this as any).sorter?.order === 'descend' && (this as any).sorter?.columnKey === data.key) {
                      title = '取消排序';
                    } else {
                      title = '点击升序';
                    }
                    return isSorter ? (
                      <th {...data} align={headerAlign}>
                        <Popover placement="top" overlayClassName={'table-sorter-menu'}>
                          <template slot="content">
                            <span style={{ color: '#fff' }}>{title}</span>
                          </template>
                          {children}
                        </Popover>
                      </th>
                    ) : (
                      <th {...data} align={headerAlign}>
                        {children}
                      </th>
                    );
                  },
                },
                body: (this as any).draggable
                  ? {
                      wrapper: DraggableWrapper,
                    }
                  : {},
              }}
              rowSelection={(this as any).rowSelection}
            ></Table>
          </ConfigProvider>
          {/* 保证有分页器或者有自定义footer时展示此模块 */}
          {((this as any).pagination && !hidePagination) || forceShowPagination || (this as any).$slots.footnote ? (
            <div class={styles.footer}>
              {(this as any).$slots.footnote && <div class={styles.footnote}>{(this as any).$slots.footnote}</div>}
              <div class={styles.pagination} v-show={((this as any).pagination && !hidePagination) || forceShowPagination}>
                <Pagination
                  onChange={handlePageChange}
                  onShowSizeChange={handlePageSizeChange}
                  {...{ props: (this as any).paginationConfig }}
                />
              </div>
            </div>
          ) : null}
        </Spin>
      </div>
    );
  },
});

export default QRichTable;
