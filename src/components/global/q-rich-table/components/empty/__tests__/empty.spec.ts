import { shallowMount } from '@vue/test-utils';

import QRichTableEmpty from '..';

describe('QRichTableEmpty', () => {
  test('render', () => {
    const wrapper = shallowMount(QRichTableEmpty);
    expect(wrapper).toMatchSnapshot();
  });

  test('props: size', () => {
    const expected = {
      size: '100px',
    };
    const wrapper = shallowMount(QRichTableEmpty, {
      propsData: { size: expected.size },
    });
    expect(wrapper.html()).toMatch(expected.size);
  });

  test('props: minHeight', () => {
    const expected = {
      minHeight: '100px',
    };
    const wrapper = shallowMount(QRichTableEmpty, {
      propsData: { minHeight: expected.minHeight },
    });
    expect(wrapper.html()).toMatch(expected.minHeight);
  });
});
