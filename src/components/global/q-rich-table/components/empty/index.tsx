import { defineComponent } from 'vue';

import EmptyImg from '@/shared/components/assets/images/icon_empty_text.svg';

import styles from './empty.module.less';

const QRichTableEmpty = defineComponent({
  functional: true,
  props: {
    size: {
      type: String,
      default: '50px',
    },
    minHeight: {
      type: String,
      // default: '350px',
    },
  },
  render(h, { props, children, data }) {
    return (
      <div class={[styles.container, data.class]} style={{ ...((data.style as any) || {}), minHeight: props.minHeight }}>
        <img src={EmptyImg} width={props.size} height={props.size} />
        <div class={styles.description}>{children}</div>
      </div>
    );
  },
});

export default QRichTableEmpty;
