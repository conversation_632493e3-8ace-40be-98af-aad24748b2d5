@import '@/styles/token.less';

.container {
  position: relative;

  .footer {
    // display: flex;
    // justify-content: space-between;
    // padding-top: 16px;
    .pagination {
      margin: 15px 0 0;
      display: flex;
      justify-content: flex-end;
      white-space: nowrap;
    }

    :global {
      .ant-pagination {
        display: flex;
        align-items: center;

        &.mini {
          .ant-pagination-item + .ant-pagination-item {
            margin-left: 4px;
          }

          .ant-pagination-options {
            margin-left: 10px;
          }
        }

        .ant-select-arrow {
          margin-top: -5px;

          svg {
            color: #999;
          }
        }

        .ant-pagination-options-size-changer.ant-select {
          margin-right: 0;
        }

        .ant-pagination-options-quick-jumper {
          margin-left: 8px;
        }

        .ant-select-selection,
        .ant-pagination-options-quick-jumper input {
          border-color: #eee;
        }

        .ant-pagination-options-quick-jumper input {
          margin: 0 4px;
        }

        .ant-pagination-prev {
          margin-right: 6px;
        }

        .ant-pagination-next {
          margin-left: 6px;
        }

        .ant-pagination-jump-prev:focus .ant-pagination-item-link-icon,
        .ant-pagination-jump-next:focus .ant-pagination-item-link-icon {
          opacity: 0;
        }

        .ant-pagination-jump-prev:hover .ant-pagination-item-link-icon,
        .ant-pagination-jump-next:hover .ant-pagination-item-link-icon {
          opacity: 1;
        }

        .ant-pagination-jump-prev:focus .ant-pagination-item-ellipsis,
        .ant-pagination-jump-next:focus .ant-pagination-item-ellipsis {
          opacity: 1;
        }

        .ant-pagination-jump-prev:hover .ant-pagination-item-ellipsis,
        .ant-pagination-jump-next:hover .ant-pagination-item-ellipsis {
          opacity: 0;
        }
      }

      .ant-pagination-options-quick-jumper input {
        height: 31px;
      }
    }
  }

  .footnote {
    // flex: 1;
    color: #999;
    line-height: 20px;
    margin-right: 10px;
    margin-top: 16px;
    display: flex;
    align-items: center;
  }

  .avatar {
    width: 40px;
    max-height: 40px;
    border-radius: 2px;

    &:not(.error) {
      border: 1px solid #eee;
    }
  }

  .empty {
    min-height: 220px;
  }

  :global {
    .table-draggable-handle {
      /* width: 10px !important; */
      height: 100% !important;
      left: auto !important;
      width: 2px !important;
      right: -2px;
      cursor: col-resize;
      touch-action: none;
      border: none;
      position: absolute;
      transform: none !important;
      bottom: 0;

      &:hover {
        background-color: #128bed;
      }
    }

    td {
      height: 1px;
    }

    .resize-table-th {
      position: relative;
    }
  }

  td:empty::before {
    content: '-';
  }

  // th[align='center'] {
  //   text-align: center !important;
  // }

  // th[align='left'] {
  //   text-align: left !important;
  // }

  // th[align='right'] {
  //   text-align: right !important;
  // }

  th {
    white-space: nowrap;
  }

  // NOTE: 嵌套表格样式
  .expanded {
    flex-wrap: nowrap;
    gap: 5px;

    .trigger {
      margin-left: 3px;
      color: @primary-color;
      font-size: 16px;
      line-height: 1;
    }
  }

  .table.scrollable {
    :global(.ant-table-body) {
      overflow-x: auto;
    }
  }

  :global {
    .ant-table {
      .ant-table {
        border: 0;

        .ant-table-content {
          border-right: 0;
        }

        .ant-table-tbody {
          background-color: #f2f9fc;
        }
      }
    }

    .ant-table-body {
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    // 隐藏多余滚动条
    // -------------------------------
    .ant-table-fixed-header {
      .ant-table-scroll{
        .ant-table-header  {
          width: calc(100% - 4px);
          overflow-y: auto !important;
          // 用来解决偶现的表头被吞的问题
          margin-bottom: 0;
          padding-bottom: 0;
        }

        .ant-table-body{
          min-height: 4px;
        }
      }
    }

    .ant-table-content .ant-table-body {
      // overflow: scroll auto !important; // 改为 `auto` 可以隐藏多余滚动条背景，但是 Safari 下会导致滚动条消失
    }

    .ant-table-fixed-right {
      .ant-table-body-inner {
        // overflow-y: auto !important;
      }
    }

    // 表体不要覆盖底部滚动条
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      bottom: 7px;
    }
    // -------------------------------

    .ant-table-thead > tr > th .ant-table-column-sorter {
      .ant-table-column-sorter-inner-full {
        margin-top: -5px;
        margin-left: 4px;
        color: @qcc-color-black-300;
      }
    }
    // Mini size
    // .ant-select-selection__rendered {
    //   line-height: 20px !important;
    // }

    .ant-table-bordered .ant-table-thead > tr > th:last-child {
      border-right-color: #e4eef6 !important;
    }
  }
}

.scrollContentSet {
  :global {
    .ant-table-scroll .ant-table-fixed {
      width: max-content !important;
    }
  }
}
