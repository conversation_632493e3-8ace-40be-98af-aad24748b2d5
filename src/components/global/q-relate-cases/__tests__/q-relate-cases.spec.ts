import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import QRelateCases from '../index.tsx';
import { flushPromises } from '@/test-utils/flush-promises';

// Mock service
vi.mock('@/shared/services', () => ({
  dimension: {
    getCaseListByIds: vi.fn().mockResolvedValue({
      Result: [
        {
          Id: '1',
          CaseName: '测试案件1',
          CloseStatus: 1,
          CaseTypeArray: ['民事案件'],
          CaseReason: '合同纠纷',
          AnNoList: ['(2023)京01民初123号'],
          CourtList: ['北京市第一中级人民法院'],
          LatestTrialRound: '一审',
        },
      ],
    }),
  },
}));

// Mock router
const mockRoute = {
  name: 'other',
};

describe('QRelateCases', () => {
  const defaultProps = {
    searchParams: {
      CaseSearchId: ['1'],
    },
    stitle: '所属',
    hideTitle: false,
  };

  it('should render correctly with default props', async () => {
    const wrapper = mount(QRelateCases, {
      propsData: defaultProps,
      mocks: {
        $route: mockRoute,
      },
    });
    await flushPromises();
    // 检查标题是否正确渲染
    expect(wrapper.find('.tcaption').text()).toBe('所属司法案件');
    // 检查表格头部是否正确渲染
    const headers = wrapper.find('thead');
    expect(headers.text()).toBe('序号案件名称案件类型案由案号法院最新审理程序');
  });

  it('should not show title when hideTitle is true', () => {
    const wrapper = mount(QRelateCases, {
      propsData: {
        ...defaultProps,
        hideTitle: true,
      },
      mocks: {
        $route: mockRoute,
      },
    });
    expect(wrapper.find('.tcaption').exists()).toBe(false);
  });

  it('should handle case click event', async () => {
    vi.spyOn(window.location, 'href', 'set');

    const wrapper = mount(QRelateCases, {
      propsData: {
        ...defaultProps,
      },
      mocks: {
        $route: mockRoute,
      },
    });

    // 等待案件数据加载
    await wrapper.vm.$nextTick();
    const caseLink = wrapper.find('a > span');
    await caseLink.trigger('click');
    await flushPromises();
    expect(wrapper.emitted('visibleChange')).toEqual([[]]);
  });

  it('should show closed case status', async () => {
    const wrapper = mount(QRelateCases, {
      propsData: defaultProps,
      mocks: {
        $route: mockRoute,
      },
    });
    await flushPromises();
    const closedStatus = wrapper.find('.textSuccess');
    expect(closedStatus.exists()).toBe(true);
    expect(closedStatus.text()).toBe('【已结案】');
  });

  it('should not render when in case detail page', () => {
    const wrapper = mount(QRelateCases, {
      propsData: defaultProps,
      mocks: {
        $route: {
          name: 'caseDetail',
        },
      },
    });
    expect(wrapper.html()).toBe('');
  });
});
