const FILE_TYPE = ['bmp', 'excel', 'ie', 'jpg', 'other', 'pdf', 'png', 'txt', 'word', 'doc', 'docx', 'xls', 'xlsx', 'picture'];

export default {
  name: 'app-source-logo',
  props: {
    fileType: {
      type: String,
      default: '',
    },
    url: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
  },
  computed: {
    logo() {
      if (this.fileType) {
        if (FILE_TYPE.indexOf(this.fileType) > -1) {
          return this.fileType;
        }
        return 'other';
      }
      return this.getFileType(this.url);
    },
  },

  methods: {
    getFileType(url) {
      const type = url && url.split('.')[url.split('.').length - 1];
      if (FILE_TYPE.includes(type)) {
        if (['doc', 'docx'].includes(type)) {
          return 'word';
        }
        if (['xls', 'xlsx'].includes(type)) {
          return 'excel';
        }
        if (['bmp', 'png', 'jpg', 'jpeg'].includes(type)) {
          return 'png';
        }
        return type;
      }
      return 'other';
    },
    showFile() {
      window.open(this.url);
    },
  },
};
