import { shallowMount } from '@vue/test-utils';

import QPartyInfo from '..';

describe('QPartyInfo', () => {
  test('render: empty', () => {
    const wrapper = shallowMount<InstanceType<typeof QPartyInfo>>(QPartyInfo);
    expect(wrapper).toMatchSnapshot();
  });

  const mockList = [{ name: 'NAME', keyno: 'KEYNO', Org: 'ORG', TD: 'TD' }];

  test.each([
    ['上诉人 / 原告', { ProsecutorList: mockList }],
    ['被上诉人 / 被告', { DefendantList: mockList }],
    ['第三人', { ThirdPartyList: mockList }],
    [
      '其他当事人',
      {
        ProsecutorList: mockList,
        OtherPartyList: mockList,
      },
    ],
  ])('props: item - %s', (description, item) => {
    const wrapper = shallowMount<InstanceType<typeof QPartyInfo>>(QPartyInfo, {
      propsData: {
        item,
      },
    });

    expect(wrapper).toMatchSnapshot();
  });
});
