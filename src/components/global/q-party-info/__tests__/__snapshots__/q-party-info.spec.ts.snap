// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QPartyInfo > props: item - 上诉人 / 原告 1`] = `
<div>
  <div>上诉人 / 原告 -<span><span class="container"><span class="content ellipsis"><span>-</span><span class="extra" style="display: none;"></span></span></span><span class="inlineBlock" style="color: #999999;">[TD]</span></span></div>
</div>
`;

exports[`QPartyInfo > props: item - 其他当事人 1`] = `
<div>
  <div>上诉人 / 原告 -<span><span class="container"><span class="content ellipsis"><span>-</span><span class="extra" style="display: none;"></span></span></span><span class="inlineBlock" style="color: #999999;">[TD]</span></span></div>
  <div>其他当事人-<span><span class="container"><span class="content ellipsis"><span>-</span><span class="extra" style="display: none;"></span></span></span><span class="inlineBlock" style="color: #999999;">[TD]</span></span></div>
</div>
`;

exports[`QPartyInfo > props: item - 第三人 1`] = `
<div>
  <div>第三人 -<span><span class="container"><span class="content ellipsis"><span>-</span><span class="extra" style="display: none;"></span></span></span><span class="inlineBlock" style="color: #999999;">[TD]</span></span></div>
</div>
`;

exports[`QPartyInfo > props: item - 被上诉人 / 被告 1`] = `
<div>
  <div>被上诉人 / 被告 -<span><span class="container"><span class="content ellipsis"><span>-</span><span class="extra" style="display: none;"></span></span></span><span class="inlineBlock" style="color: #999999;">[TD]</span></span></div>
</div>
`;

exports[`QPartyInfo > render: empty 1`] = `<div></div>`;
