import { VNode, defineComponent, PropType } from 'vue';

import QEntityLink from '../q-entity-link';
import QRoleText from '../q-role-text';
import { EPartyInfo } from '../q-rich-table/utils';

export default defineComponent({
  functional: true,
  props: {
    item: {
      type: Object as PropType<any>,
      required: true,
    },
    type: {
      type: String as PropType<string>,
      default: 'announcement',
      validator(value: EPartyInfo) {
        return Object.values(EPartyInfo).includes(value);
      },
    },
  },
  render(h, context): VNode {
    const item = context?.props?.item ?? {};
    const itemType = context?.props?.type;
    const firstList = itemType === EPartyInfo.LI_AN ? item.Prosecutor : item.ProsecutorList;
    const secondList = itemType === EPartyInfo.LI_AN ? item.Appellee : item.DefendantList;
    const thirdList = itemType === EPartyInfo.LI_AN ? item.OtherPartyList : item.ThirdPartyList;
    const renderArray: VNode[] = [];
    if (firstList?.length > 0) {
      renderArray.push(
        <div>
          {'上诉人 / 原告 -'}
          {firstList.map((pro: any, pIndex: number) => {
            return (
              <span key={`pro_${pIndex}`}>
                {itemType === EPartyInfo.ANNOUNCEMENT || itemType === EPartyInfo.LI_AN ? <QEntityLink coyObj={pro} /> : null}
                {itemType === EPartyInfo.COURT_NOTICE ? <QEntityLink coyObj={{ Name: pro.name, KeyNo: pro.keyno, Org: pro.Org }} /> : null}
                {pro.TD ? <QRoleText roleD={pro.TD} /> : null}
                {pIndex + 1 !== firstList.length ? <span>，</span> : null}
              </span>
            );
          })}
        </div>
      );
    }
    if (secondList?.length > 0) {
      renderArray.push(
        <div>
          {'被上诉人 / 被告 -'}
          {secondList.map((def: any, pIndex: number) => {
            return (
              <span key={`def_${pIndex}`}>
                {itemType === EPartyInfo.ANNOUNCEMENT || itemType === EPartyInfo.LI_AN ? <QEntityLink coyObj={def} /> : null}
                {itemType === EPartyInfo.COURT_NOTICE ? <QEntityLink coyObj={{ Name: def.name, KeyNo: def.keyno, Org: def.Org }} /> : null}
                {def.TD ? <QRoleText roleD={def.TD} /> : null}
                {pIndex + 1 !== secondList.length ? <span>，</span> : null}
              </span>
            );
          })}
        </div>
      );
    }
    if (thirdList?.length > 0 && itemType !== EPartyInfo.LI_AN) {
      renderArray.push(
        <div>
          {'第三人 -'}
          {thirdList.map((third: any, pIndex: number) => {
            return (
              <span key={`third_${pIndex}`}>
                {itemType === EPartyInfo.ANNOUNCEMENT ? <QEntityLink coyObj={third} /> : null}
                {itemType === EPartyInfo.COURT_NOTICE ? (
                  <QEntityLink coyObj={{ Name: third.name, KeyNo: third.keyno, Org: third.Org }} />
                ) : null}
                {third.TD ? <QRoleText roleD={third.TD} /> : null}
                {pIndex + 1 !== thirdList.length ? <span>，</span> : null}
              </span>
            );
          })}
        </div>
      );
    }
    if (item.OtherPartyList && item.OtherPartyList.length > 0) {
      renderArray.push(
        <div>
          {(firstList && firstList.length > 0) || (secondList && secondList.length > 0) || (thirdList && thirdList.length > 0)
            ? '其他当事人-'
            : null}
          {item.OtherPartyList.map((other: any, pIndex: number) => {
            return (
              <span key={`other_${pIndex}`}>
                <QEntityLink coyObj={other} />
                {other.TD ? <QRoleText roleD={other.TD} /> : null}
                {pIndex + 1 !== item.OtherPartyList.length ? <span>，</span> : null}
              </span>
            );
          })}
        </div>
      );
    }
    return h('div', renderArray);
  },
});
