@import '@/styles/token.less';

.shrinkContentInner {
  position: relative;
  word-break: break-all;
  line-height: 1.5em;

  &.briefEllipsis {
    overflow: hidden;
    // text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .shrinkContentLabel {
    font-size: 14px;
    color: #666;
  }
}

.openBtn {
  position: absolute;
  right: 0;
  bottom: 0;
  text-align: right;
  color: @primary-color;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), #fff 25%);

  &:hover {
    color: @qcc-color-blue-600;
  }
}

.packup {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 2px;
  color: @primary-color;

  &:hover {
    color: @qcc-color-blue-600;
  }
}

.shrinkPre {
  display: inline-block;
  vertical-align: top;
}
