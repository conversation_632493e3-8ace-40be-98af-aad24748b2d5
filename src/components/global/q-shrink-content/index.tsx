import { defineComponent, ref, unref } from 'vue';
import { useResizeObserver } from '@vueuse/core';

import { formatChar2HtmlEntity } from '@/utils/string';

import styles from './index.module.less';

const ShrinkContent = defineComponent({
  name: 'ShrinkContent',
  props: {
    /**
     * 文字内容
     */
    value: {
      type: [String],
      default: '',
    },
    /**
     * 是否是html格式的文本
     */
    htmlType: {
      type: Boolean,
      default: true,
    },
    /**
     * 自定义文本颜色
     */
    color: {
      type: String,
      default: '#333',
    },
    /**
     * 最大换行数
     */
    line: {
      type: Number,
      default: 4,
    },
    /**
     * 额外的label说明
     */
    label: {
      type: String,
      default: '',
    },
    /**
     * 展开收起按钮的样式
     */
    indicatorStyle: {
      type: String,
      default: '',
    },

    /**
     * 字体大小 会影响
     */
    fontSize: {
      type: Number,
      default: 14,
    },
    ellipsisStyle: {
      type: Object,
      default: () => ({}),
    },
    // label样式
    labelStyle: {
      type: Object,
      default: () => ({}),
    },
    toggleText: {
      type: String,
      default: '...展开',
    },
  },

  methods: {
    genValue() {
      const styleObj = {
        color: this.color,
        whiteSpace: 'initial',
        // marginRight: '20px',
      } as any;
      if (this.$slots.htmlElement) {
        return <div style={styleObj}>{this.$slots.htmlElement}</div>;
      }
      return this.htmlType ? (
        <span style={styleObj} domPropsInnerHTML={formatChar2HtmlEntity(this.value) || '-'} />
      ) : (
        <span style={styleObj}>{this.value || '-'}</span>
      );
    },
  },
  setup(props) {
    const toggle = ref(true);
    const scopeDom = ref<HTMLElement>();
    const showEclipse = ref(false);
    const getShowEllipsis = (elementRef) => {
      if (unref(elementRef)) {
        const maxHeight = props.line * parseInt(window.getComputedStyle(unref(elementRef)).lineHeight, 10);
        const { scrollHeight } = elementRef.value;
        return scrollHeight > maxHeight;
      }
      return false;
    };

    useResizeObserver(scopeDom, () => {
      setTimeout(() => {
        showEclipse.value = getShowEllipsis(scopeDom);
      });
    });

    return { toggle, showEclipse, scopeDom };
  },
  render() {
    const ellipsisStyle = this.toggle ? this.ellipsisStyle : {};
    return (
      <div
        class={{
          [styles.shrinkContentInner]: true,
          [styles.briefEllipsis]: this.toggle,
        }}
        style={{
          fontSize: `${this.fontSize}px`,
          '-webkit-line-clamp': this.line,
          height: this.showEclipse && this.toggle ? `calc(1em * 1.5 * ${this.line})` : 'auto',
          ...ellipsisStyle,
        }}
        ref="scopeDom"
      >
        {this.label ? (
          <span
            class={styles.shrinkContentLabel}
            style={{
              fontSize: `${this.fontSize}px`,
              ...this.labelStyle,
            }}
          >
            {this.label}：
          </span>
        ) : null}

        {this.genValue()}
        {this.showEclipse ? (
          <span
            class={[styles.openBtn, 'btn-expand']}
            style={`${this.indicatorStyle}; width: ${this.toggle ? '50px' : '30px'}; `}
            onClick={(event) => {
              this.toggle = !this.toggle;
              event.preventDefault();
              event.stopPropagation();
            }}
          >
            {this.toggle ? <span>{this.toggleText}</span> : <span class={styles.packup}>收起</span>}
          </span>
        ) : null}
      </div>
    );
  },
});

export default ShrinkContent;
