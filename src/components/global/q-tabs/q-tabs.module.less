@import '@/styles/token.less';

.container {
  &.middle {
    line-height: 32px;
    height: 32px;
  }

  &.default {
    line-height: 44px;
    height: 44px;
  }

  &.large {
    line-height: 50px;
    height: 50px;
  }

  .extra {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: auto;
  }
}

.item {
  font-size: 14px;
  font-weight: normal;
  color: #808080;
  padding: 0 10px;
  height: inherit;
  line-height: inherit;
  box-sizing: border-box;
  display: inline-block;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  &.active {
    color: #666;
    font-weight: bold;

    .text:not(.single)::after {
      visibility: visible;
    }
  }

  transition: all ease 0.1s;
}

.isHead{
  font-size: 15px;
  color: #999;
  padding: 0 15px;

  &.active{
    color: #333;
  }
}

.item.button {
  font-size: 13px;
  padding: 0 8px;
  color: #333;
  height: 26px;
  line-height: 26px;
  background: #f3f3f3;

  &.active {
    background: #e5f2fd;
    color: #128bed;

    .text::after {
      visibility: hidden;
    }
  }

  & + & {
    margin-left: 16px;
  }
}

.text {
  position: relative;
  height: inherit;
  line-height: inherit;
  cursor: pointer;

  &::after {
    content: '';
    background-color: #128bed;
    position: absolute;
    z-index: 1;
    bottom: 0;
    height: 2px;
    width: 100%;
    display: block;
    visibility: hidden;
  }

  > span i {
    color: @primary-color;
    margin-left: 4px;
  }
}
