import _ from 'lodash';
import { CreateElement, VNode, defineComponent, PropType } from 'vue';

import styles from './q-tabs.module.less';

type Tab = {
  key: string;
  label: string | ((h: CreateElement) => string | VNode);
  count?: number;
};

const QTabs = defineComponent({
  name: 'QTabs',
  props: {
    size: {
      type: String as PropType<'default' | 'middle' | 'large'>,
      default: 'default',
    },
    value: {
      type: [String, Number],
      required: false,
    },
    tabs: {
      type: Array as PropType<Tab[] | any[]>,
      required: true,
      validator(tabs: []) {
        return tabs.every(({ key, label }) => [key, label].every((v) => !_.isNil(v)));
      },
    },
    type: {
      type: String as PropType<'tab' | 'button'>,
      default: 'tab',
    },
    // 是否是头部一级标题
    isHead: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['change'],

  model: {
    prop: 'value',
    event: 'change',
  },

  setup(props, { emit }) {
    const handleChange = (value) => {
      emit('change', value);
    };
    return {
      handleChange,
    };
  },

  render() {
    return (
      <div class={[styles.container, 'q-tabs-wrap', styles[(this as any).size]]}>
        {(this as any).tabs.map(({ label, key, count }) => (
          <div
            key={key}
            class={{
              [styles.button]: (this as any).type === 'button',
              [styles.item]: true,
              [styles.isHead]: (this as any).isHead,
              [styles.active]: key === (this as any).value,
            }}
          >
            <div
              class={{
                [styles.text]: true,
                [styles.single]: (this as any).tabs?.length === 1,
              }}
              onClick={() => {
                this.handleChange(key);
              }}
            >
              {typeof label === 'function' ? (
                label()
              ) : (
                <span>
                  <span domPropsInnerHTML={label}></span>
                  <i v-show={!_.isNil(count)} style={{ color: count ? '#128bed' : '#808080' }}>
                    {count}
                  </i>
                </span>
              )}
            </div>
          </div>
        ))}
        <div class={styles.extra}>{this.$slots.extra}</div>
      </div>
    );
  },
});

export default QTabs;
