import { shallowMount } from '@vue/test-utils';

import QTabs from '..';

describe('QTabs', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof QTabs>>(QTabs, {
      propsData: {
        tabs: [
          {
            key: '1',
            label: '标签一',
          },
          {
            key: '2',
            label: '标签二',
          },
        ],
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
