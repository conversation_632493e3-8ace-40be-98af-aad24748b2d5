import { shallowMount } from '@vue/test-utils';

import QRoleText from '..';

describe('QRoleText', () => {
  test.each(['获得支持', '对方不被支持', '获得部分支持'])('should render correct role: %s', (role: string) => {
    const wrapper = shallowMount(QRoleText, {
      propsData: {
        roleD: role,
      },
    });
    const html = wrapper.html();
    expect(html).toContain('color: #00ad65');
    expect(html).toContain(`[${role}]`);
  });

  test.each(['诉讼中止', '被解除查封', '被执行完毕', '被解除查冻扣'])('should render correct role: %s', (role: string) => {
    const wrapper = shallowMount(QRoleText, {
      propsData: {
        roleD: role,
      },
    });
    const html = wrapper.html();
    expect(html).toContain('color: #F5A623');
    expect(html).toContain(`[${role}]`);
  });

  test.each(['不被支持', '对方被支持', '被驳回', '被查封', '被查冻扣', '终结本次执行'])(
    'should render correct role: %s',
    (role: string) => {
      const wrapper = shallowMount(QRoleText, {
        propsData: {
          roleD: role,
        },
      });
      const html = wrapper.html();
      expect(html).toContain('#F04040;');
      expect(html).toContain(`[${role}]`);
    }
  );

  test.each(['Unknown role text'])('should render correct role: %s', (role: string) => {
    const wrapper = shallowMount(QRoleText, {
      propsData: {
        roleD: role,
      },
    });
    const html = wrapper.html();
    expect(html).toContain('#999999');
    expect(html).toContain(`[${role}]`);
  });
});
