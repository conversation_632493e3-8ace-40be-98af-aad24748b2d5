import { defineComponent } from 'vue';

import { RoleTagColorMap, RoleTagMap } from '@/components/global/q-role-text/config';

import styles from './role-text.module.less';

const QRoleText = defineComponent({
  functional: true,
  props: {
    roleD: {
      type: String,
      required: false,
    },
  },
  render(h, context) {
    const roleD = context?.props?.roleD ?? '';
    if (!roleD) return null;
    let roleType = 'default';
    Object.keys(RoleTagMap).forEach((type) => {
      if (RoleTagMap[type].includes(roleD)) {
        roleType = type;
      }
    });
    return (
      <span class={[styles.inlineBlock]} style={{ color: RoleTagColorMap[roleType] }}>
        [{roleD}]
      </span>
    );
  },
});

export default QRoleText;
