@import '@/styles/token.less';

@table-column-gap: 9px 10px;

.container {
  width: 100%;
  margin: 0 auto;
  // margin-bottom: 20px;
  .table {
    width: 100%;
    color: #333;
    border-spacing: 0;
    line-height: 1.5;
    border-collapse: collapse;

    th,
    td {
      border: 1px solid @rover-table-border-color;
      padding: @table-column-gap;
    }

    td {
      word-break: break-word;
      word-wrap: break-word;

      &:not([data-blank]) {
        &:empty::before {
          content: '-';
        }
      }
    }

    th {
      background: @rover-table-header-bg;
      font-weight: normal;
      line-height: 20px;
      white-space: nowrap;
    }

    :global {
      .tb {
        background: @rover-table-header-bg !important;
      }

      [align='center'] {
        text-align: center;
        vertical-align: middle;
      }
    }
  }
}
