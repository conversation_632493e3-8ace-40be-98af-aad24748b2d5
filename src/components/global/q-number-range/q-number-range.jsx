import { InputNumber } from 'ant-design-vue';
import { isNil, isNaN, toNumber } from 'lodash';

import styles from './q-number-range.module.less';

const isValidNumberValue = (numbers) => {
  return numbers.filter((v) => !isNil(v) && !(v.length === 1 && v[0] === '-')).every((v) => !isNaN(toNumber(v)));
};
export default {
  name: 'QNumberRange',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    size: {
      type: String,
      default: 'default',
    },
    placeholder: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
    },
    defaultValue: {
      type: Array,
    },
    max: {
      type: Array,
      default: () => [Infinity, Infinity],
    },
    min: {
      type: Array,
      default: () => [0, 0],
    },
    step: {
      type: Array,
      default: () => [1, 1],
    },
    unit: {
      type: String,
    },
  },
  data() {
    return {
      internalValue: this.value || this.defaultValue || [],
    };
  },
  methods: {
    handleChange(i, value) {
      const nextValue = this.internalValue.slice();
      nextValue[i] = isNil(value) || value === '' ? undefined : value;
      if (!isValidNumberValue(nextValue)) {
        return;
      }

      this.internalValue = nextValue;
      this.$emit('change', nextValue);
    },
    handleKeydown(i, e) {
      if (e.keyCode !== 13) {
        return;
      }

      const ref = this.$refs.input[i];

      if (!ref) {
        return;
      }

      ref.blur();
    },
    search() {
      this.$nextTick(() => {
        this.$emit('search', this.internalValue);
      });
    },
  },
  watch: {
    value(v) {
      this.internalValue = Array.isArray(v) ? v : [];
    },
  },
  render() {
    const { size, unit } = this;
    const inputProps = [0, 1].map((i) => {
      const data = ['placeholder', 'defaultValue', 'max', 'min', 'step'].reduce(
        (p, k) => {
          if (this[k]) {
            // eslint-disable-next-line no-param-reassign
            p.props[k] = this[k][i];
          }
          return p;
        },
        {
          props: { size },
          on: {
            change: (v) => this.handleChange(i, v),
            keydown: (e) => this.handleKeydown(i, e),
            blur: this.search,
          },
        }
      );

      data.props.value = this.internalValue[i];

      return data;
    });

    const x = <InputNumber {...inputProps[0]} refInFor ref="input" />;
    const y = <InputNumber {...inputProps[1]} refInFor ref="input" />;

    if (this.$scopedSlots.default) {
      return this.$scopedSlots.default(x, y);
    }

    return (
      <div>
        <div class={styles.row}>
          <span>从</span>
          {x}
          {unit && <span>{unit}</span>}
        </div>
        <div class={styles.row}>
          <span>至</span>
          {y}
          {unit && <span>{unit}</span>}
        </div>
      </div>
    );
  },
};
