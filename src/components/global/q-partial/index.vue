<template>
  <div class="ui-partial-container">
    <div class="inner-container" ref="container">
      <div class="inner-wrapper" ref="wrapper">
        <slot></slot>
      </div>
    </div>
    <div class="expander-box" @click.stop="toggle" v-if="!hideExpander">
      <template v-if="!expand"> <span class="ellips-text">...</span><span class="expand-text">更多</span> </template>
      <span v-else class="expand-text">收起</span>
    </div>
  </div>
</template>

<style lang="less" src="./style.less"></style>
<script src="./component.js"></script>
