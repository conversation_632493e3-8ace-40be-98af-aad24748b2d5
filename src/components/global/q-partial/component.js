export default {
  name: 'q-partial',

  props: {
    visualHeight: {
      type: String,
      default() {
        return '220px';
      },
    },

    duration: {
      type: Number,
      default: 200,
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.$container = $(this.$refs.container);
      this.$wrapper = $(this.$refs.wrapper);
      const height = this.$wrapper.height();
      if (height <= parseInt(this.visualHeight, 10)) {
        this.hideExpander = true;
        this.$container.css({ height });
      } else {
        this.$container.css({ height: this.visualHeight });
      }
      this.calc();
    });
  },

  data() {
    return {
      expand: false,
      hideExpander: false,
    };
  },

  watch: {
    visualHeight(newVal, oldVal) {
      this.calc();
    },
  },

  updated() {
    this.calc(false);
  },

  methods: {
    calc(animate = true) {
      const height = this.$wrapper.height();
      if (this.expand) {
        if (animate) {
          this.$container.animate({ height }, this.duration);
        } else {
          this.$container.css({ height });
        }
      } else {
        if (height > parseInt(this.visualHeight, 10)) {
          this.hideExpander = false;
          if (animate) {
            this.$container.animate({ height: this.visualHeight }, this.duration);
          } else {
            this.$container.css({ height: this.visualHeight });
          }
        } else {
          this.$container.css({ height });
          this.hideExpander = true;
        }
        this.$container.css({ overflow: 'hidden' });
      }
    },

    toggle() {
      this.expand = !this.expand;
      this.calc();
    },
  },
};
