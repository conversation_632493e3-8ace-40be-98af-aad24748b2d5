import { defineComponent, PropType } from 'vue';

import QEntityLink from '../q-entity-link';
import QRoleText from '../q-role-text';

interface RoleItem {
  Name: string;
  KeyNo: string;
  Org: number;
  TD?: string;
  Td?: string;
  SupNameAndKeyNo?: {
    Name: string;
    KeyNo: string;
    Org: number;
  };
}

interface RoleListItem {
  Desc: string;
  Items: RoleItem[];
}

const QRoleList = defineComponent({
  name: 'QRoleList',
  props: {
    list: {
      type: Array as PropType<RoleListItem[]>,
      default: () => [],
    },
    // 仅有一个身份时，是否展示当事人标识
    hiddenPartyRole: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    // 仅有一个身份类别-"当事人"时，不展示"当事人"标题
    showDesc(): boolean {
      if (this.hiddenPartyRole) {
        return !(this.list.length === 1 && this.list[0].Desc === '当事人');
      }
      return true;
    },
  },

  render() {
    if (!this.list.length) {
      return <span>-</span>;
    }

    return (
      <section>
        {this.list.map((item, index) => {
          const itemKey = `${item.Desc}_${index}`;

          if (!item.Items || !item.Items.length) {
            return null;
          }

          if (item.Items.length <= 2) {
            return (
              <div key={itemKey}>
                <span>
                  {this.showDesc ? <span>{item.Desc}：</span> : null}
                  {item.Items.map((el, k) => (
                    <span key={`${el.KeyNo || el.Name}_${k}`}>
                      <QEntityLink coyObj={{ Name: el.Name, KeyNo: el.KeyNo, Org: el.Org }} />
                      {el.TD || el.Td ? <QRoleText roleD={(el.TD || el.Td)!} /> : null}
                      {k !== item.Items.length - 1 ? <span>，</span> : null}
                    </span>
                  ))}
                </span>
              </div>
            );
          }

          return (
            <div key={itemKey}>
              <div>
                {this.showDesc ? <div>{item.Desc}：</div> : null}
                {item.Items.map((el, k) => (
                  <div key={`${el.KeyNo || el.Name}_${k}`}>
                    <span>{k + 1}. </span>
                    <QEntityLink coyObj={{ Name: el.Name, KeyNo: el.KeyNo, Org: el.Org }} />
                    {el.TD || el.Td ? <QRoleText roleD={(el.TD || el.Td)!} /> : null}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </section>
    );
  },
});

export default QRoleList;
