import { defineComponent } from 'vue';

import part1 from './images/part-1.svg';
import part2 from './images/part-2.svg';
import part3 from './images/part-3.svg';
import qcc from './images/qcc.svg';
import styles from './q-loading.module.less';

const QLoading = defineComponent({
  functional: true,
  props: {
    /**
     * 'small','default','large','fullsize'
     */
    size: {
      type: String,
      default: 'default',
    },
    bgColor: {
      type: String,
      default: 'unset',
    },
  },
  render(h, { props }) {
    const { size, bgColor } = props;
    const node = (
      <span
        class={{
          [styles.root]: true,
          [styles.sm]: size === 'small',
          [styles.lg]: size === 'large',
        }}
        style={{
          backgroundColor: bgColor,
        }}
      >
        <div class={styles.logo}>
          <img src={part1} class={styles.p1} />
          <img src={part2} class={styles.p2} />
          <img src={part3} class={styles.p3} />
        </div>
        <div class={styles.name}>
          <img src={qcc} alt="企查查" />
        </div>
      </span>
    );

    if (size === 'fullsize') {
      return (
        <div
          class={styles.fullsize}
          style={{
            backgroundColor: bgColor,
          }}
        >
          {node}
        </div>
      );
    }
    return node;
  },
});

export default QLoading;
