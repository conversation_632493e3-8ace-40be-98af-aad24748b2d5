import { VNode, defineComponent } from 'vue';

import styles from './q-list.module.less';

export default defineComponent({
  name: 'QList',
  inheritAttrs: false,
  props: {
    renderItem: {
      type: Function,
      required: true,
    },
  },
  methods: {
    renderListItem(...args) {
      return <div class={[styles.item, 'q-list-item']}>{(this.renderItem as any)(...args)}</div>;
    },
  },
  render(): VNode {
    return (
      <div class={styles.root}>
        <a-list
          {...{
            props: {
              ...this.$attrs,
              renderItem: this.renderListItem,
            },
          }}
        />
      </div>
    );
  },
});
