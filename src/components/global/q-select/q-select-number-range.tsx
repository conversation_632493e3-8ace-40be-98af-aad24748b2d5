import _ from 'lodash';
import { defineComponent } from 'vue';

import { createNumberRangeValidator } from '@/utils/validator';

import styles from './q-select.module.less';

export default defineComponent({
  name: 'CustomNumberRange',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Array,
    },
    validator: {
      type: Function,
      default: createNumberRangeValidator(),
    },
  },
  render() {
    return (
      <div class={styles.numberRange}>
        <q-number-range
          {...{
            props: {
              max: [9999999999, 9999999999],
              ...this.$attrs,
            },
          }}
          size="small"
          style={{
            width: '150px',
          }}
          value={this.value}
          onChange={this.$listeners.change}
        />
        <div class={styles.btns}>
          <a-button size="small" type="link" onClick={this.$listeners.reset}>
            清空
          </a-button>
          <a-button
            size="small"
            type="link"
            onClick={(e) => {
              if (_.isFunction(this.validator) && (this?.validator as any)?.(this.value)) {
                (this.$listeners as any).submit(e);
              }
            }}
          >
            确定
          </a-button>
        </div>
      </div>
    );
  },
});
