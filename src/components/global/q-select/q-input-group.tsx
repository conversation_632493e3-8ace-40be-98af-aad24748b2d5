import { Button, message, Input } from 'ant-design-vue';
import _ from 'lodash';
import { defineComponent, PropType } from 'vue';

import styles from './select.module.less';

const CustomInput = defineComponent({
  name: 'CustomInput',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Object as PropType<{
        value: string[];
      }>,
    },
    max: {
      type: Number,
      default: 5,
    },
    placeholder: {
      type: String,
      default: '请输入关键词',
    },
  },
  methods: {
    handleReset() {
      this.$emit('reset');
    },
    handleSubmit() {
      const values = _.compact(this.parseValue());

      if (!values.length) {
        this.handleReset();
        return;
      }

      if (
        values.some((v) => {
          return v.length <= 1;
        })
      ) {
        message.error('关键词至少包含2个字符');

        return;
      }

      const checked: string[] = [];

      // eslint-disable-next-line no-restricted-syntax
      for (const keyword of values) {
        if (checked.includes(keyword)) {
          message.error(`关键词“${keyword}”已存在`);
          return;
        }

        checked.push(keyword);
      }

      this.$emit('submit');
    },
    parseValue() {
      return Array.from({
        length: this.max,
      }).map((v, i) => {
        return _.get(this.value, ['value', i]);
      });
    },
  },
  render() {
    const values = this.parseValue();
    return (
      <div class={styles.inputGroup}>
        <div class={styles.content}>
          {values.map((v, i) => {
            return (
              <div key={i} class={styles.row}>
                <div>{i === 0 ? '包含' : '或'}</div>
                <div>
                  <Input
                    value={v}
                    placeholder={this.placeholder}
                    maxLength={100}
                    onChange={(e: Event) => {
                      const nextValue = values.slice();
                      nextValue.splice(i, 1, (e.target as HTMLInputElement)?.value || '');
                      this.$emit('change', {
                        _custom: true,
                        value: nextValue,
                      });
                    }}
                  />
                </div>
              </div>
            );
          })}
        </div>
        <div class={styles.btns}>
          <Button size="small" type="link" onClick={this.handleReset}>
            清空
          </Button>
          <Button size="small" type="link" onClick={this.handleSubmit}>
            确定
          </Button>
        </div>
      </div>
    );
  },
});

export default CustomInput;
