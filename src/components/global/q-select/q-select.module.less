@import '@/styles/token.less';

.overlay {
  user-select: none;
  min-width: 160px;

  .count {
    color: #999;
  }
}

.customOverlay {
  z-index: 1050;
  padding: 0;

  :global {
    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-calendar-range {
      box-shadow: none;
      border: none;
    }
  }
}

.numberRange {
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;

  .btns {
    margin-top: 10px;
    text-align: right;

    :global {
      .ant-btn {
        padding: 0;

        & + .ant-btn {
          margin-left: 6px;
        }
      }
    }
  }
}

.dateRange {
  .resetBtn {
    position: absolute;
    right: 2px;
    top: 5px;
  }
}

.menu {
  border-radius: 4px;
  padding: 2px 0;
  max-width: 600px;
  min-width: 200px;
  border: 1px solid #eee;
  background: #fff;

  .menuList{
    max-height: 308px;
    overflow: auto;
    border-right: none;

  }

  :global{
    .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-vertical > .ant-menu-item{
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
      color: #333;
    }

    .ant-menu-submenu > .ant-menu-submenu-title{
      display: flex;
      align-items: center;
      padding: 0 0 0 12px;

      .ant-menu-submenu-arrow{
        display: none;
      }

      .anticon{
        margin-right: 0;
        color: inherit;
      }
    }

    .ant-menu-submenu,
    .ant-menu-item {
      background: #fff !important;

      &:hover {
        background: #f2f8fe !important;
        color: #128bed !important;
      }
    }

    .ant-menu-item-selected{
      color: #666;
    }

    .ant-dropdown-menu-item-active {
      color: #128bed !important;
      background: #fff !important;

      .ant-checkbox-inner {
        border: 1px solid #128bed !important;
      }
    }

    .ant-dropdown-menu-submenu-title {
      .ant-dropdown-menu-submenu-arrow-icon {
        display: none;
      }
    }
  }

  .searchInput {
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .footerBtn {
    text-align: right;
    border-top: 1px solid #eee;
    height: 40px;
    line-height: 40px;

    span{
      color: #666;
      line-height: 30px;
      display: inline-block;
      padding: 0 5px;
      margin-right: 10px;
      border-radius: 2px;
      cursor: pointer;

      &:hover{
        color: #1890ff;
        background-color: #F2F8FE;
      }
    }

    .submit{
      color: #1890ff;
    }

    .disableBtn{
      color: #999;
      cursor: not-allowed;

      &:hover{
        color: #999;
        cursor: not-allowed;
        background-color: #fff;
      }
    }
  }
}

.tipOverlay {
  padding: 0;

  :global(.ant-tooltip-arrow) {
    display: none;
  }
}

.tip {
  white-space: pre-line;
}

.multiColumnMenu {
  display: flex;
  flex-wrap: wrap;
  width: 264px;

  :global {
    .ant-dropdown-menu-item {
      flex: 1 0 50%;

      .ant-checkbox + span {
        padding-left: 0;
        padding-right: 0;
        margin-left: 8px;
      }
    }

    .ant-dropdown-menu-submenu-title {
      .ant-dropdown-menu-submenu-arrow-icon {
        display: none;
      }
    }
  }
}


.withFilter {
  padding-top: 0;
}
