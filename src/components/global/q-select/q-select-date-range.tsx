import RangePicker from 'ant-design-vue/lib/vc-calendar/src/RangeCalendar';
import _ from 'lodash';
import moment from 'moment';
import { defineComponent, PropType } from 'vue';

import styles from './q-select.module.less';

export default defineComponent({
  name: 'CustomDateRange',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Array as PropType<string[]>,
    },
  },
  data() {
    return {
      cachedValue: [] as moment.Moment[],
    };
  },
  watch: {
    value: 'syncValue',
  },
  methods: {
    syncValue() {
      const nextValue = _.cloneDeep(this.value || []);

      if (!_.isEqual(nextValue, this.cachedValue)) {
        this.cachedValue = nextValue.map((s) => moment(s));
      }
    },
    handleReset() {
      this.$emit('reset');
    },
    handleChange(value: moment.Moment[]) {
      this.cachedValue = value;

      if (value.length === 2 && value.every((d) => moment.isMoment(d))) {
        const sValue = value.map((d) => d.toISOString());
        this.$emit('change', sValue);
        this.$emit('submit');
      }
    },
  },
  render() {
    return (
      <div class={styles.dateRange}>
        <RangePicker
          {...{
            props: this.$attrs,
          }}
          prefixCls="ant-calendar"
          format="YYYY-MM-DD"
          selectedValue={this.cachedValue}
          showToday={false}
          showDateInput
          onChange={(this as any).handleChange}
        />
        <a-button size="small" type="link" onClick={(this as any).handleReset} class={styles.resetBtn}>
          清空
        </a-button>
      </div>
    );
  },
  mounted() {
    (this as any).syncValue();
  },
});
