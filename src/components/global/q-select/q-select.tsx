import Vue, { PropType, VNode } from 'vue';
import _ from 'lodash';
import moment from 'moment';
import { Empty, Input, message, Spin } from 'ant-design-vue';

import { dateRangeParser, inputGroupParser, numberRangeParser } from '@/utils/parser';
import { Parser } from '@/utils/parser/interface';

import { CustomConfig, Option } from './interface';
import CustomDateRange from './q-select-date-range';
import CustomNumberRange from './q-select-number-range';
import SelectTrigger from '../q-select-trigger';
import { CLEAR_VALUE } from '../constants';
import styles from './q-select.module.less';
import CustomInput from './q-input-group';

import shareStyles from '@/styles/share/select.module.less';

const stopPropagation = (e) => e.stopPropagation();

const defaultParsers = {
  'date-range': dateRangeParser,
  'number-range': numberRangeParser,
  'input-group': inputGroupParser,
};

type NodeType = VNode | string | number;

const QSelect = Vue.extend({
  name: 'QSelect',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    mode: {
      type: String,
    },
    placeholder: {
      type: String,
    },
    options: {
      type: Array as PropType<Option[]>,
      required: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [String, Number, Object, Array] as PropType<any | Array<any>>,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    showValues: {
      type: Boolean,
      default: false,
    },
    checkbox: {
      type: String,
      default: 'start', // | 'end
    },
    getPopupContainer: {
      type: Function,
    },
    custom: {
      // 自定义组件
      type: Object as PropType<CustomConfig>,
    },
    width: {
      type: Number,
    },
    renderReferenceLabel: {
      type: Function,
    },
    tips: {
      type: Object as PropType<Record<string, string>>,
    },
    multiColumn: {
      type: Boolean,
      default: false,
    },
    // 是否展示对options的筛选框
    showFilter: {
      type: Boolean,
      default: false,
    },
    // 是否展示对options的默认文案
    searchPlaceHolder: {
      type: String,
      default: '请输入关键字',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    placement: {
      type: String,
      default: 'bottomLeft',
    },
    // 是否展示底部 重置、确定按钮
    showFooterButton: {
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
      default: '重置',
    },
    confirmText: {
      type: String,
      default: '确定',
    },
  },

  data() {
    return {
      visible: false,
      customVisible: false,
      cacheCustomValue: undefined as any,
      inputValue: undefined, // 搜索输入框的值
      selectValue: _.cloneDeep(this.value),
    };
  },
  computed: {
    customSource(): any {
      if (!this.custom) {
        return undefined;
      }

      const { multiple, selectValue, options } = this;

      if (_.isNil(selectValue) || !options.length || (multiple && _.isEmpty(selectValue))) {
        return undefined;
      }

      if (multiple) {
        return selectValue.find((v) => !options.find((o) => _.isEqual(v, o.value)));
      }

      if (!options.find((o) => _.isEqual(selectValue, o.value))) {
        return selectValue;
      }

      return undefined;
    },
    customValue(): any {
      if (!this.custom || _.isNil(this.customSource)) {
        return undefined;
      }

      return this.customParser.toValue(this.customSource);
    },
    customParser(): Parser<any, any> {
      const { parser, type } = this.custom || { type: 'date-range' };

      return parser || defaultParsers[type];
    },
    customOptions(): Option[] {
      if (this.inputValue) {
        return this.options.filter((o: Option) => (this.inputValue ? o.label.includes(this.inputValue) : false));
      }
      return this.options;
    },
  },
  methods: {
    clearSearchInput() {
      if (this.inputValue) {
        this.inputValue = undefined;
      }
    },
    callChange(value, option: Record<string, any> = {}) {
      if (value === CLEAR_VALUE) {
        this.$emit('change', undefined);
        return;
      }

      if (this.multiple) {
        let nextValue = this.selectValue ? this.selectValue.slice() : undefined;
        const exclusiveValues = this.customOptions.filter((o: any) => o.exclusive).map((o) => o.value);
        if (_.isNil(value)) {
          nextValue = undefined;
        } else if (!nextValue || !Array.isArray(nextValue)) {
          nextValue = [value];
        } else {
          const index = _.findIndex(nextValue, (v) => _.isEqual(v, value));

          if (index === -1) {
            nextValue.push(value);
          } else {
            nextValue.splice(index, 1);
          }
        }
        // exclusive为排他选项，选择后，会取消其他已选的选项
        if (option.exclusive) {
          nextValue = [value];
        } else {
          nextValue = nextValue?.filter((v) => !exclusiveValues.includes(v));
        }
        if (nextValue && !nextValue.length) {
          nextValue = undefined;
        }
        nextValue = nextValue?.filter((v) => !v.exclusive);
        this.selectValue = nextValue;
        if (!this.showFooterButton) {
          if (!_.isEqual(this.selectValue, this.value)) {
            this.$emit('change', this.selectValue);
          }
        }
      } else {
        this.selectValue = value;
      }
    },
    toggleCustom() {
      // 当前自定义为未选中状态
      if (!this.customSource) {
        // 自定义有值，直接勾选
        if (!_.isNil(this.cacheCustomValue)) {
          this.handleCustomValueSubmit();
        }
        // 自定义为选中状态的，直接取消勾选
      } else if (!this.multiple) {
        this.$emit('change', undefined);
      } else {
        const index = this.selectValue.indexOf(this.customSource);

        if (index > -1) {
          const nextValue = this.selectValue.slice();
          nextValue.splice(index, 1);
          this.selectValue = nextValue;
          if (!this.showFooterButton) {
            this.$emit('change', nextValue);
          }
        }
      }
    },
    handleMenuClick({ key }) {
      if (key === -1) {
        this.toggleCustom();
        return;
      }
      const option = this.customOptions[key];

      if (option) {
        this.callChange(option.value, option);
      }

      if (!this.multiple && !this.showFooterButton) {
        this.handelSubmit();
      }
    },
    handelSubmit() {
      if (!_.isEqual(this.selectValue, this.value)) {
        this.$emit('change', this.selectValue);
      }
      if (this.showFooterButton || !this.multiple || (this.multiple && !this.showFooterButton)) {
        this.visible = false;
      }
    },
    handleCustomValueChange(value) {
      this.cacheCustomValue = value;
      this.customVisible = false;
    },
    handleCustomValueReset() {
      this.cacheCustomValue = undefined;
      this.handleCustomValueSubmit();
    },

    handleCustomValueSubmit() {
      this.visible = false;
      this.customVisible = false;
      const toSource = _.get(this.customParser, 'toSource');

      if (!toSource) {
        return;
      }
      const source = _.isNil(this.cacheCustomValue) ? undefined : toSource(this.cacheCustomValue);
      if (!this.multiple) {
        this.selectValue = source;
        this.$emit('change', source);
        return;
      }
      const prevSource = this.customSource;
      if (prevSource) {
        const index = this.selectValue.indexOf(prevSource);
        const nextValue = this.selectValue.slice();
        if (index > -1) {
          if (!_.isNil(source)) {
            nextValue.splice(index, 1, source);
          } else {
            nextValue.splice(index, 1);
          }
          this.selectValue = _.uniqWith(nextValue, _.isEqual);
        }
      } else if (!_.isNil(source)) {
        this.selectValue = _.uniqWith((this.value || []).concat(source), _.isEqual);
      }
      this.handelSubmit();
    },
    updateCustomValueCache() {
      if (!this.custom || _.isNil(this.customSource)) {
        this.cacheCustomValue = undefined;
        return;
      }

      if (!_.isEqual(this.customValue, this.cacheCustomValue)) {
        this.cacheCustomValue = this.customValue;
      }
    },
    forcePopupAlign() {
      if (!this.visible) {
        return;
      }
      this.$nextTick(() => {
        const ref: any = _.get(this.$refs.dropdown, 'popupRef.$refs.alignInstance');
        if (ref && ref.forceAlign) {
          ref.forceAlign();
        }
      });
    },
    renderLabel(
      option: {
        label: NodeType | NodeType[];
        value: any;
        count?: number;
        tip?: string;
        disabled?: boolean;
      },
      value,
      key
      // eslint-disable-next-line camelcase
    ): VNode & { $_checked: boolean } {
      let node;
      let checked = false;
      const countNode = _.isNumber(option.count) ? <span class={styles.count}>（{option.count}）</span> : null;
      const tip = _.get(this.tips, option.value) || option.tip;
      const tipNode = tip ? (
        <a-tooltip getPopupContainer={this.getPopupContainer} placement="bottom" overlayClassName={styles.tipOverlay}>
          <q-icon type="icon-a-shuomingxian" class={shareStyles.tipIcon} />
          <div slot="title" class={styles.tip}>
            {tip}
          </div>
        </a-tooltip>
      ) : null;

      if (this.multiple) {
        checked = _.some(value || [], (v) => _.isEqual(v, option.value));

        if (this.checkbox === 'start') {
          node = (
            <a-checkbox
              nativeOnClick={stopPropagation}
              checked={checked}
              onChange={() => this.handleMenuClick({ key })}
              disabled={option.disabled}
            >
              {option.label}
              {countNode}
              {tipNode}
            </a-checkbox>
          );
        } else {
          node = (
            <div>
              {option.label}
              {countNode}
              {tipNode}
              <q-icon type="icon-gouhao" class={[shareStyles.icon, shareStyles.checkIcon]} />
            </div>
          );
        }
      } else {
        // undefined 是未选择，如需选中，option.value 应设为 null
        checked = value !== undefined && _.isEqual(value, option.value);
        node = (
          <div>
            <span class={shareStyles.dateRange}>{option.label}</span>
            {countNode}
            {tipNode}
          </div>
        );
      }

      // Note: 传递给上层 Menu.Item 添加 className
      node.$_checked = checked;

      return node;
    },
    renderGroupItemWrapper({ label, disabled }) {
      return (
        <SelectTrigger
          disabled={disabled}
          mode={this.mode}
          label={label}
          placeholder={this.placeholder}
          visible={this.visible}
          allowClear={this.allowClear}
          width={this.width}
          onClear={() => {
            this.callChange(CLEAR_VALUE);
          }}
        />
      );
    },
    renderCustomNode() {
      if (!this.custom) {
        return null;
      }
      const data = {
        props: {
          value: this.cacheCustomValue,
        },
        attrs: {
          ...this.custom.props,
        },
        on: {
          change: this.handleCustomValueChange,
          reset: this.handleCustomValueReset,
          submit: this.handleCustomValueSubmit,
        },
      };
      switch (this.custom.type) {
        case 'date-range':
          return <CustomDateRange style={{ marginLeft: '-8px' }} {...data} />;
        case 'number-range':
          return <CustomNumberRange style={{ marginLeft: '-8px' }} {...data} />;
        case 'input-group': {
          return <CustomInput style={{ marginLeft: '-8px' }} {...data} />;
        }
        default:
          return null;
      }
    },
    renderCustom() {
      if (!this.custom) {
        return null;
      }
      const node = this.renderCustomNode();

      if (!node) {
        return null;
      }
      let labelText = '自定义';
      const selected = !_.isNil(this.customSource);

      switch (this.custom.type) {
        case 'date-range':
          labelText = this.customValue?.length
            ? `${moment(this.customValue[0]).format('YYYY-MM-DD')}至${moment(this.customValue[1]).format('YYYY-MM-DD')}`
            : '自定义';
          break;
        case 'number-range':
        case 'input-group':
          break;
        default:
          return null;
      }

      const label = this.renderLabel(
        {
          label: [labelText, <q-icon class={[shareStyles.icon, shareStyles.arrowIcon]} type="icon-a-shixinyou1x" key="icon" />],
          value: true,
        },
        this.multiple ? [selected] : selected,
        -1
      );

      return (
        <a-sub-menu
          key={-1}
          popupClassName={shareStyles.popupSubMenu}
          class={[
            shareStyles.menuItem,
            shareStyles.withChildren,
            {
              [shareStyles.active]: this.customVisible,
              [shareStyles.checked]: selected,
            },
          ]}
          title={label}
        >
          {node}
        </a-sub-menu>
      );
    },
  },
  watch: {
    value: {
      handler(newV, oldV) {
        this.updateCustomValueCache();
        this.selectValue = newV;
      },
      immediate: true,
    },
  },
  render() {
    const { multiple, customOptions, disabled, selectValue, renderReferenceLabel } = this;
    const { reference } = this.$scopedSlots;
    const active: boolean = !multiple ? selectValue !== undefined : _.get(selectValue, 'length', 0) > 0;
    const count: number = active && multiple ? this.value?.length : 0;
    let referenceLabel;

    if (!multiple && active) {
      if (this.customSource) {
        referenceLabel = '自定义';
      } else {
        referenceLabel = _.get(
          customOptions.find((o) => _.isEqual(o.value, selectValue)),
          'label'
        );
      }
    }

    if (multiple && count > 0) {
      referenceLabel = `${this.placeholder ?? ''} ${count}`;
      if (this.showValues) {
        referenceLabel = customOptions
          .filter((o) => _.findIndex(selectValue, (sv) => _.isEqual(o.value, sv)) !== -1)
          .map((o) => o.label)
          .join('/');
      }
    }

    if (renderReferenceLabel) {
      referenceLabel = renderReferenceLabel(referenceLabel, this.$props);
    }
    return (
      <a-dropdown
        ref="dropdown"
        trigger={['hover']}
        placement={this.placement}
        // trigger={['click']}
        v-model={this.visible}
        overlayClassName={styles.overlay}
        disabled={disabled}
        getPopupContainer={this.getPopupContainer}
        onVisibleChange={(visible) => {
          this.$emit('visibleChange', visible);
          // 下拉关闭且未点确定的时候，重置
          if (!visible) {
            this.selectValue = _.cloneDeep(this.value);
          }
        }}
      >
        {reference && _.isFunction(reference)
          ? reference({
              label: referenceLabel,
              active,
              disabled,
              visible: this.visible,
            })
          : this.renderGroupItemWrapper({
              label: referenceLabel,
              disabled,
            })}
        <div
          slot="overlay"
          class={{
            [styles.menu]: true,
            [shareStyles.withCheckbox]: this.checkbox === 'end',
            [styles.multiColumnMenu]: this.multiColumn && customOptions?.length > 7,
            [styles.withFilter]: this.showFilter,
          }}
        >
          {this.showFilter && (
            <div class={styles.searchInput}>
              <Input class={styles.input} v-model={this.inputValue} placeholder={this.searchPlaceHolder} allowClear={true}>
                <q-icon style="color: #999" type="icon-sousuo" slot="prefix" name="search"></q-icon>
              </Input>
            </div>
          )}
          {this.loading && (
            <Spin spinning={this.loading} size="small">
              <div style={{ width: '160px', height: '140px' }}></div>
            </Spin>
          )}
          <a-menu onClick={this.handleMenuClick} class={styles.menuList}>
            {customOptions.length > 0 &&
              !this.loading &&
              customOptions.map((option, index) => {
                const label = this.renderLabel(option, selectValue, index);
                return (
                  <a-menu-item
                    key={index}
                    className={{
                      [shareStyles.menuItem]: true,
                      [shareStyles.checked]: label.$_checked,
                    }}
                    disabled={option.disabled}
                  >
                    {label}
                  </a-menu-item>
                );
              })}
            {customOptions.length === 0 && !this.loading && <Empty image={(Empty as any).PRESENTED_IMAGE_SIMPLE} description="暂无数据" />}
            {this.renderCustom()}
          </a-menu>
          {this.showFooterButton && !this.loading ? (
            <div class={styles.footerBtn}>
              <span
                class={this.selectValue?.length ? '' : styles.disableBtn}
                onClick={() => {
                  this.selectValue = undefined;
                  this.handelSubmit();
                }}
              >
                <q-icon type="icon-zhongzhishaixuanicon" style="margin-right: 5px;"></q-icon>
                {this.cancelText}
              </span>
              <span
                class={styles.submit}
                onClick={() => {
                  if (!this.selectValue?.length && !this.value) {
                    message.warning('请选择选项');
                    return;
                  }
                  this.handelSubmit();
                }}
              >
                {this.confirmText}
              </span>
            </div>
          ) : null}
        </div>
      </a-dropdown>
    );
  },
});

export default QSelect;
