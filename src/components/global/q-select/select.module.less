.overlay {
  user-select: none;
  padding: 0;

  &:not(.matched) {
    min-width: 170px;
  }

  :global(.ant-popover-arrow) {
    display: none;
  }

  :global(.ant-popover-inner-content) {
    padding: 0;
  }

  :global(.ant-dropdown-menu) {
    box-shadow: none;
  }
}

.customOverlay {
  z-index: 1050;
  padding-left: 0;
  padding-right: 0;

  :global {
    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-calendar-range {
      box-shadow: none;
      border: none;
    }
  }
}

.numberRange {
  padding: 12px;

  .btns {
    margin-top: 10px;
  }
}

.numberRange,
.inputGroup {
  .btns {
    text-align: right;

    :global {
      .ant-btn {
        padding: 0;

        & + .ant-btn {
          margin-left: 6px;
        }
      }
    }
  }
}

.inputGroup {
  min-width: 240px;

  .btns {
    padding: 9px 15px;
    border-top: 1px solid #eee;
  }

  .content {
    padding: 10px 15px 5px 0;
  }

  .row {
    display: flex;
    align-items: center;

    & + .row {
      margin-top: 10px;
    }

    & > :first-child {
      width: 48px;
      padding-right: 5px;
      text-align: right;
    }

    & > :last-child {
      flex: 1;
    }
  }
}

.dateRange {
  .resetBtn {
    position: absolute;
    right: 2px;
    top: 5px;
  }
}

.menu {
  max-height: 368px;
  overflow: auto;
}

.icon {
  float: right;
  padding-top: 5px;
}

.divide {
  padding: 0;
  height: 1px;
  background-color: #eee;
  margin: 0 10px;
}
