import { shallowMount } from '@vue/test-utils';

import QModal from '..';

describe('QModal', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof QModal>>(QModal, {
      propsData: {
        title: 'TITLE',
        desc: 'DESC',
        tooltip: 'TOOLTIP',
        size: 'large',
      },
      slots: {
        footer: '<div>FOOTER</div>',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
