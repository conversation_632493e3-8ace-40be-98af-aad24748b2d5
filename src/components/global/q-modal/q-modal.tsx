import { Modal, Toolt<PERSON> } from 'ant-design-vue';
import _ from 'lodash';
import { computed, defineComponent, onActivated, onDeactivated, PropType, ref } from 'vue';

import QIcon from '@/components/global/q-icon';

import styles from './q-modal.module.less';

type Size = 'small' | 'medium' | 'large' | 'x-large' | 'extra-large' | 'huge-large';
const modalProps: {
  width: { type: PropType<number> };
} = (Modal as any).props;

const MODAL_SIZE_MAP = {
  small: 400,
  medium: 600,
  large: 800,
  'x-large': 900,
  'extra-large': 1000,
  'huge-large': 1200,
};

const calcWidth = (size?: Size | number): number => {
  if (!size) {
    return MODAL_SIZE_MAP.medium;
  }
  if (!_.has(MODAL_SIZE_MAP, size)) {
    return size as number;
  }
  return MODAL_SIZE_MAP[size] as number;
};

const QModal = defineComponent({
  name: 'QModal',
  model: {
    prop: 'visible',
    event: 'visibleChange',
  },
  props: {
    ...modalProps,
    visible: {
      type: Boolean,
    },
    size: {
      type: [String, Number] as PropType<Size | number>, // 400 600 800 1000
      default: 'medium' as Size,
    },
    title: {
      type: String,
    },
    desc: {
      type: String,
    },
    tooltip: {
      type: String,
      default: '',
    },
    onOk: {
      type: Function as PropType<() => Promise<boolean | void>>,
    },
    footer: {
      type: [String, Boolean],
      default: undefined,
    },
    value: {
      type: String,
    },
    /** 视窗底部距离 */
    viewportDistance: {
      type: Number,
      required: false,
    },
  },
  setup(props, { emit }) {
    const isActive = ref(true);
    const loading = ref(false);

    const inheritedProps = computed<Record<string, any>>(() => {
      const fields = _.difference(Object.keys(modalProps), ['visible', 'width', 'title']);
      return _.pick(props, fields);
    });

    const closeModal = () => {
      emit('visibleChange', false);
      emit('cancel');
    };

    const handleOk = () => {
      if (props.onOk) {
        loading.value = true;
        props
          .onOk()
          .then(() => {
            (this as any).closeModal();
          })
          .catch(() => ({}))
          .finally(() => {
            loading.value = false;
          });
      }
      emit('ok');
    };

    onActivated(() => {
      isActive.value = true;
    });
    onDeactivated(() => {
      isActive.value = false;
    });

    return {
      isActive,
      loading,
      inheritedProps,

      closeModal,
      handleOk,
    };
  },

  render() {
    const { tooltip } = this;
    const title = this.$slots.title || this.title;
    const desc = this.$slots.desc || this.desc;
    const showTitle = !!(title || desc);

    return (
      <Modal
        {...{
          props: {
            visible: this.isActive && this.visible,
            width: calcWidth(this.size),
            ...this.inheritedProps,
            confirmLoading: this.loading,
            footer: this.footer,
            /** 自动限制弹窗内容的最大高度 */
            bodyStyle: this.viewportDistance
              ? {
                  maxHeight: `calc(100vh - ${this.viewportDistance}px)`,
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  ...this.inheritedProps.bodyStyle,
                }
              : this.inheritedProps.bodyStyle,
          },
          on: {
            cancel: (this as any).closeModal,
            ok: (this as any).handleOk,
          },
        }}
      >
        {showTitle ? (
          <div slot="title" class={styles.title}>
            {/* 有无都需要占位 space-between */}
            <div class={styles.left}>
              {title ? <h3>{title}</h3> : null}
              {tooltip && (
                <Tooltip title={tooltip}>
                  <q-icon class={styles.icon} type="icon-a-shuomingxian" />
                </Tooltip>
              )}
              {desc ? <p>{desc}</p> : null}
            </div>
            {/* <div class={styles.right}>
              <img src={iconLogo} width="92" height="26" alt="企查查" />
            </div> */}
          </div>
        ) : null}

        <QIcon slot="closeIcon" type="icon-tanchuangguanbi" />

        {this.$slots.default}

        {this.$slots.footer ? <template slot="footer">{this.$slots.footer}</template> : null}
      </Modal>
    );
  },
});

export default QModal;
