.qSwitchWrapper {
  position: relative;
  display: inline-block;
  line-height: 1;

  &.default {
    :global(.ant-switch) {
      height: 20px;
      width: 36px;
      min-width: 36px;

      &::after {
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
    }
  }

  &.medium {
    :global(.ant-switch) {
      min-width: 30px;
      width: 30px;
      height: 18px;
      font-size: 12px;
      line-height: 14px;

      &::after {
        width: 14px;
        height: 14px;
        border-radius: 50%;
      }
    }
  }

  &.xs {
    :global(.ant-switch-small) {
      min-width: 26px;
      height: 14px;
      line-height: 1;

      &::after {
        width: 10px;
        height: 10px;
        border-radius: 50%;
      }
    }
  }
}
