import _ from 'lodash';
import { Switch } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import styles from './q-switch.module.less';

type Size = 'default' | 'medium' | 'small' | 'xs';
const switchProps: {
  size: { type: PropType<string> };
} = (Switch as any).props;

const handleSize = (size: Size) => {
  const map = {
    default: 'default',
    medium: 'default',
    small: 'small',
    xs: 'small',
  };

  if (!_.has(map, size)) {
    return map.default;
  }

  return map[size];
};

const QSwitch = defineComponent({
  name: 'QSwitch',
  props: {
    ...switchProps,
    size: {
      type: String as PropType<Size>,
      default: 'default' as Size,
    },
  },
  computed: {
    inheritedProps(): Record<string, any> {
      const fields = _.difference(Object.keys(switchProps), ['size']);

      return _.pick(this.$props, fields);
    },
  },
  methods: {
    handleChange(checked: boolean) {
      this.$emit('change', checked);
    },
  },
  emits: ['change'],
  render() {
    return (
      <span class={[styles.qSwitchWrapper, this.size && styles[`${this.size}`]]}>
        <Switch
          {...{ props: { ...this.inheritedProps } }}
          size={handleSize(this.size)}
          onChange={(checked: boolean) => {
            this.handleChange(checked);
          }}
        ></Switch>
      </span>
    );
  },
});

export default QSwitch;
