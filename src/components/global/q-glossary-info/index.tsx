/**
 * FIXME: 该组件需要重构
 */
import { Popover } from 'ant-design-vue';
import { defineComponent } from 'vue';

import QIcon from '../q-icon';
import styles from './q-glossary-info.module.less';

// 标记加载状态
let REQUEST_MARK = false;
const QUEUE_IDS: number[] = [];
const GLOSSARY_CACHE: Record<string, []> = {};

const QGlossaryInfo = defineComponent({
  name: 'QGlossaryInfo',
  props: {
    infoId: {
      type: [Number, String],
      required: false,
    },
    tooltip: {
      type: String,
      required: false,
    },
    placement: {
      type: String,
      default: 'bottom',
    },
    layout: {
      type: String,
      default: 'inline',
    },
    contentStyle: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      invalid: false,
      explain: [],
    };
  },
  created() {
    if (this.infoId && QUEUE_IDS.indexOf(+this.infoId) === -1) {
      QUEUE_IDS.push(+this.infoId);
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.infoId && GLOSSARY_CACHE[this.infoId]) {
        this.explain = GLOSSARY_CACHE[this.infoId] || [];
      } else {
        REQUEST_MARK = false;
        (this as any).fetchData(QUEUE_IDS);
      }
    });
  },

  methods: {
    fetchData(ids: number[]): void {
      // NOTE: 合并请求, 仅发一次

      if (REQUEST_MARK || ids?.length === 0) {
        return;
      }
      REQUEST_MARK = true;

      this.$service.sns
        .getGlossaryInfo({
          glossaryId: ids.toString(),
        })
        .then((resData) => {
          if (Array.isArray(resData)) {
            try {
              resData.forEach(({ glossaryId, glossaryExplain }) => {
                GLOSSARY_CACHE[glossaryId] = JSON.parse(glossaryExplain);
              });
              // eslint-disable-next-line no-empty
            } catch (err) {}
          }
          this.explain = GLOSSARY_CACHE[this.infoId as number] || [];
        });
    },
    renderTooltip() {
      const { explain, infoId, tooltip, $slots } = this;
      let node;

      if ($slots.tooltip) {
        // Slots 优先
        node = $slots.tooltip;
      } else if (tooltip) {
        // Props.tooltip 优先
        node = tooltip;
      } else if (infoId) {
        // Props.infoId 优先
        node = (
          <div class={styles.content} style={this.contentStyle}>
            {explain.map(({ title, text }, index) => {
              return (
                <div class={styles.line} key={`tooltip_${index}`}>
                  <div v-show={title} class={styles.title}>
                    {title}
                  </div>
                  <div v-show={text} class={styles.text} domPropsInnerHTML={text}></div>
                </div>
              );
            })}
          </div>
        );
      } else {
        // 提前退出
        return null;
      }

      return (
        <Popover placement={this.placement}>
          <div slot="content" class={[styles.content]} style={this.contentStyle}>
            {node}
          </div>
          {$slots.trigger ?? (
            <div class={[[styles.trigger], 'trigger']}>
              <QIcon type="icon-a-shuomingxian" />
            </div>
          )}
        </Popover>
      );
    },
  },

  render() {
    const { infoId, tooltip, layout, renderTooltip } = this as any;
    if (!infoId && !tooltip && !this.$slots.tooltip) {
      return <div class={[styles.container, styles[layout]]}>{this.$slots.default}</div>;
    }
    return (
      <div class={[styles.container, styles[layout]]}>
        {this.$slots.default}
        {renderTooltip()}
      </div>
    );
  },
});

export default QGlossaryInfo;
