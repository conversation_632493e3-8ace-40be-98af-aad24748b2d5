@import '@/styles/token.less';

.container {
  display: flex;
  align-items: center;
  justify-content: center;

  &.inline {
    display: inline-flex;
    vertical-align: middle;
  }

  &.inherit {
    display: inherit;
  }

  .trigger {
    display: inline-block;
    vertical-align: middle;
    line-height: 13px;
    padding-bottom: 2px;
    cursor: pointer;
    margin-left: 4px;
    font-size: 13px;

    &:hover {
      i {
        color: @primary-color;
      }
    }

    i {
      color: @single-icon-color;
    }
  }
}

.content {
  min-width: 120px;
  max-width: 240px;
  max-height: 300px;
  overflow-y: auto;
  margin: -10px;
  padding: 10px;

  .line {
    .title {
      color: #222;
      font-weight: bold;
    }

    .text {
      white-space: pre-wrap;
    }

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}
