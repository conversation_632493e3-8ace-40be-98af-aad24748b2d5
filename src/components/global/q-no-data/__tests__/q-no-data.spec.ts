import { shallowMount } from '@vue/test-utils';

import QNoData from '..';

describe('QNoData', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof QNoData>>(QNoData, {
      propsData: {
        text: '暂无数据',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test.each(['old', 'isAuthIcon', 'grayBackground', 'isEmptyImg'])('genImg - %s', (propField) => {
    const wrapper = shallowMount<InstanceType<typeof QNoData>>(QNoData, {
      propsData: {
        [propField]: true,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
