// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QNoData > genImg - grayBackground 1`] = `
<div class="container bg" style="padding: 200px 10px;"><img src="/src/components/global/q-no-data/images/gray-nodata.png">
  <slot name="text">
    <p>暂时没有找到相关数据</p>
  </slot>
  <slot name="operate"></slot>
</div>
`;

exports[`QNoData > genImg - isAuthIcon 1`] = `
<div class="container bg" style="padding: 200px 10px;"><img src="/src/components/global/q-no-data/images/auth-nodata.png" style="width: 220px; height: 190px;">
  <slot name="text">
    <p>暂时没有找到相关数据</p>
  </slot>
  <slot name="operate"></slot>
</div>
`;

exports[`QNoData > genImg - isEmptyImg 1`] = `
<div class="container bg" style="padding: 200px 10px;"><img src="/src/components/global/q-no-data/images/empty.png" style="width: 100px; height: 100px;">
  <slot name="text">
    <p>暂时没有找到相关数据</p>
  </slot>
  <slot name="operate"></slot>
</div>
`;

exports[`QNoData > genImg - old 1`] = `
<div class="container bg" style="padding: 200px 10px;" old="true"><img src="/src/components/global/q-no-data/images/nodata.png">
  <slot name="text">
    <p>暂时没有找到相关数据</p>
  </slot>
  <slot name="operate"></slot>
</div>
`;

exports[`QNoData > render 1`] = `
<div class="container bg" style="padding: 200px 10px;"><img src="/src/components/global/q-no-data/images/nodata.png">
  <slot name="text">
    <p>暂无数据</p>
  </slot>
  <slot name="operate"></slot>
</div>
`;
