import { defineComponent } from 'vue';

import AuthNoDataImg from './images/auth-nodata.png';
import GrayNoDataImg from './images/gray-nodata.png';
import NoDataImg from './images/nodata.png';
import emptyImg from './images/empty.png';
import styles from './q-no-data.module.less';

const QNoData = defineComponent({
  name: 'QNoData',

  props: {
    padding: {
      type: Number,
      default: 200,
    },
    text: {
      type: String,
      default: '暂时没有找到相关数据',
    },
    bg: {
      type: Boolean,
      default: true,
    },
    absolute: {
      type: Boolean,
      default: false,
    },
    grayBackground: {
      type: Boolean,
      default: false,
    },
    isAuthIcon: {
      type: Boolean,
      default: false,
    },
    isEmptyImg: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    genImg() {
      if (this.isAuthIcon) {
        return <img style="width:220px;height:190px;" src={AuthNoDataImg} />;
      }
      if (this.grayBackground) {
        return <img src={GrayNoDataImg} />;
      }
      if (this.isEmptyImg) {
        return <img style="width:100px;height:100px;" src={emptyImg} />;
      }
      return <img src={NoDataImg} />;
    },
  },

  render() {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.absolute]: this.absolute,
          [styles.bg]: this.bg,
        }}
        style={{
          padding: `${this.padding}px 10px`,
        }}
      >
        {/* 图片 */}
        {this.genImg()}
        {/* 文本 */}
        {this.$slots.text ?? (
          <slot name="text">
            <p>{this.text}</p>
          </slot>
        )}
        {/* 操作 */}
        {this.$slots.operate ?? <slot name="operate"></slot>}
      </div>
    );
  },
});

export default QNoData;
