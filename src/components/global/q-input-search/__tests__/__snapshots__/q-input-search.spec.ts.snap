// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QInputSearch > render 1`] = `
<div>
  <ainput-stub prefixcls="ant-input" placeholder="请输入企业名称、人名、产品名等，多关键词用空格隔开" type="text" size="large" addonafter="[object Object]" allowclear="true" lazy="true" classname="ant-input-search ant-input-search-enter-button ant-input-search-large" class="input" style="width: 600px;"></ainput-stub>
</div>
`;
