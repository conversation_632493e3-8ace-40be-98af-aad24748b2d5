import { Input } from 'ant-design-vue';
import _ from 'lodash';
import { defineComponent } from 'vue';

import styles from './q-input-search.module.less';

const QInputSearch = defineComponent({
  name: 'QInputSearch',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: String,
    placeholder: { type: String, default: '请输入企业名称、人名、产品名等，多关键词用空格隔开' },
    allowClear: {
      default: true,
      type: Boolean,
    },
    minLength: {
      type: Number,
      default: 2,
    },
    width: {
      type: Number,
      default: 600,
    },
    /**
     * 在某些场景下，相同的 value 也需要发送 `search` 事件
     */
    cache: {
      type: Boolean,
      default: true,
    },
    enterButtonText: {
      type: String,
      default: '查一下',
    },
  },
  data() {
    return {
      cachedValue: '',
    };
  },
  methods: {
    callSearch() {
      if (this.cachedValue === this.value && this.cache) {
        return;
      }

      const value = _.trim(this.value);
      if (value && value.length < this.minLength) {
        return;
      }

      this.cachedValue = value;
      this.$emit('search', value);
    },
    focus() {
      (this.$refs.input as any).focus();
    },
  },
  render() {
    const { value } = this;
    return (
      <div>
        <Input.Search
          value={value}
          class={styles.input}
          style={{ width: `${this.width}px` }}
          size="large"
          allowClear={this.allowClear}
          placeholder={this.placeholder}
          enter-button={this.enterButtonText}
          onChange={(e: InputEvent) => {
            this.$emit('change', (e.target as HTMLInputElement).value);
          }}
          onSearch={(v) => {
            this.$emit('change', v);
            if (v && v.length >= this.minLength) {
              (this as any).callSearch();
            }
          }}
          ref="input"
        />
      </div>
    );
  },
});

export default QInputSearch;
