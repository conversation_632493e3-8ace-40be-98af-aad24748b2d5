import { shallowMount } from '@vue/test-utils';

import QTag from '..';

describe('QTag', () => {
  test('props: hoverText', () => {
    const wrapper = shallowMount(QTag, {
      context: {
        props: {
          hoverText: 'test',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: arrow', () => {
    const wrapper = shallowMount(QTag, {
      context: {
        props: {
          arrow: true,
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
