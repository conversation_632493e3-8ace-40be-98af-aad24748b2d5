// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QTag > props: arrow 1`] = `<div class="container link small default"></div>`;

exports[`QTag > props: hoverText 1`] = `
<anonymous-stub trigger="hover" placement="right" transitionname="zoom-big-fast" aftervisiblechange="[Function]" overlay="[object Object]" overlaystyle="[object Object]" prefixcls="ant-tooltip" mouseenterdelay="0.1" mouseleavedelay="0.1" align="[object Object]" builtinplacements="[object Object]">
  <div class="container link small default"></div>
</anonymous-stub>
`;
