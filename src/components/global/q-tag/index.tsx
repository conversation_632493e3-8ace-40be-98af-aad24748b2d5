import _ from 'lodash';
import { Tooltip } from 'ant-design-vue';
import { defineComponent } from 'vue';

import { EventHandler } from '../interfaces';
import styles from './q-tag.module.less';
import QIcon from '../q-icon';

const QTag = defineComponent({
  functional: true,

  props: {
    size: {
      type: String,
      default: 'small',
    },
    type: {
      type: String,
      default: 'default',
    },
    /**
     * 是否显示箭头
     */
    arrow: {
      type: Boolean,
      default: false,
    },
    /**
     * 箭头方向: 'default' | 'down' | 'up'
     */
    arrowDirection: {
      type: String,
      default: 'default',
    },
    ghost: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      required: false,
    },
    hoverText: {
      type: String,
      default: '',
    },
    // hoverText 位置
    placement: {
      type: String,
      default: 'right',
    },

    // 不可点
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  render(h, { props, children, listeners, data }) {
    const { arrow, arrowDirection, ghost, icon, hoverText, placement, disabled } = props;
    const directionUp = arrowDirection === 'up';
    const directionDown = arrowDirection === 'down';
    const clickHandler = listeners.click;

    let customClass = data.class;
    customClass = _.isString(data.class) ? { [customClass]: true } : customClass;

    const contentNode = (
      <div
        onClick={(ev) => {
          if (disabled) {
            return;
          }
          if (clickHandler) {
            (clickHandler as EventHandler)(ev);
          }
        }}
        class={{
          [styles.container]: true,
          [styles.link]: !disabled && (clickHandler || arrow || hoverText),
          [styles[props.size]]: true,
          [styles[props.type]]: true,
          [styles.backgroundGhost]: ghost,
          [styles.disabled]: disabled,
          ...customClass,
        }}
        {...{
          style: {
            border: `1px solid ${(data as any)?.style?.['background-color'] || (data as any)?.style?.background}`,
            ...(data as any).style,
          },
        }}
      >
        {icon ? <QIcon class={styles.icon} type={icon} /> : null}
        {children}
        {arrow && icon ? (
          <QIcon
            class={{
              [styles.arrow]: true,
              [styles.down]: directionDown,
              [styles.up]: directionUp,
            }}
            type="icon-a-shixinyou1x"
          />
        ) : null}
      </div>
    );

    if (hoverText) {
      return (
        <Tooltip placement={placement}>
          <span slot="title" domPropsInnerHTML={hoverText}></span>
          {contentNode}
        </Tooltip>
      );
    }
    return contentNode;
  },
});

export default QTag;
