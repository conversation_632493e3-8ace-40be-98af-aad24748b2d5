@import '@/styles/token.less';

@tag-size-sm: 12px;
@tag-bg-color-primary: #e5f2fd;
@tag-bg-color-danger: #ffcccc;
@tag-bg-color-success: #e3f6ee;
@tag-bg-color-warning: #ffeecc;
@tag-bg-color-list: #fff4ed;
@tag-bg-color-pl: #edeef9;
@tag-bg-color-gray: #f6f6f6;
@tag-bg-color-default: #eee;

@tag-color-primary: #128bed;
@tag-color-danger: #f04040;
@tag-color-success: #00ad65;
@tag-color-warning: #ff8900;
@tag-color-list: #ec9662;
@tag-color-pl: #6f77d1;
@tag-color-gray: #666;
@tag-color-default: #b95959;

.container {
  margin-right: 4px;
  //display: inline-table;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  //align-items: center;
  background-color: @tag-bg-color-default;
  color: @tag-color-default;
  font-size: @tag-size-sm;
  padding: 2px 6px;
  border-radius: 2px;
  vertical-align: middle;
  height: 22px;
  line-height: 22px;
  white-space: nowrap;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      background-color: @tag-bg-color-default;
    }
  }

  .icon {
    font-size: 14px;
    vertical-align: -0.22em;
    margin-right: 4px;
  }

  .arrow {
    // 收缩 arrow 留白区域
    margin-right: -4px;

    &.up {
      transform: rotate(-90deg);
    }

    &.down {
      transform: rotate(90deg);
    }
  }

  &.link {
    cursor: pointer;
  }

  &.default {
    color: #666;
  }
  // Theme
  &.primary {
    background-color: @tag-bg-color-primary;
    color: @tag-color-primary;
    border: 1px solid @tag-bg-color-primary;
  }

  &.danger {
    background-color: @tag-bg-color-danger;
    color: @tag-color-danger;
    border: 1px solid @tag-bg-color-danger;
  }

  &.success {
    background-color: @tag-bg-color-success;
    color: @tag-color-success;
    border: 1px solid @tag-bg-color-success;
  }

  &.warning {
    background-color: @tag-bg-color-warning;
    color: @tag-color-warning;
    border: 1px solid @tag-bg-color-warning;
  }

  &.list {
    background-color: @tag-bg-color-list;
    color: @tag-color-list;
    border: 1px solid @tag-bg-color-list;
  }

  &.pl {
    background-color: @tag-bg-color-pl;
    color: @tag-color-pl;
    border: 1px solid @tag-bg-color-pl;
  }

  &.gray {
    background-color: @tag-bg-color-gray;
    border: 1px solid @tag-bg-color-gray;
    color: @tag-color-gray;
  }

  &.shade {
    background-color: #808080;
    border: 1px solid #808080;
    color: #808080;
  }

  &.backgroundGhost {
    background-color: #fff;
    border-color: currentcolor;
  }
  // Size
  &.small {
    font-size: @tag-size-sm;
    vertical-align: middle;
  }

  &.companyIcon {
    font-size: 14px;
    height: 22px;
    padding: 0 6px;
    line-height: 18px;
    font-weight: normal;

    &.primary {
      background-color: #f8fbfe;
      border-color: #9ccff7;
    }

    &.danger {
      background-color: #fffafa;
      border-color: #ffbcbc;
    }

    &.success {
      background-color: #f7fcfa;
      border-color: #94dcbe;
    }

    &.warning {
      background-color: #fffbf7;
      border-color: #ffce94;
    }

    &.list {
      background-color: #fffbf7;
      border-color: #ec9662;
    }

    &.pl {
      background-color: @tag-bg-color-pl;
      border-color: #6f77d1;
    }

    &.gray {
      background-color: #f9f9f9;
      border-color: #d8d8d8;
    }

    &.default {
      background-color: #fffafa;
      border-color: #b95959;
    }
  }
}
