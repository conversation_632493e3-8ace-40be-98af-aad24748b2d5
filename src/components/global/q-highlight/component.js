import _ from 'lodash';

export default {
  name: 'q-highlight',

  props: {
    viewDt: {
      type: Object,
      default: null,
    },
    boxType: {
      type: String,
      default: 'inline',
    },
  },

  methods: {
    replace(content, highlights) {
      _.forEach(highlights, (o) => {
        const id = o.Id || o.id;
        const name = o.Name || o.name || '';
        const org = o.Org;
        const reg = new RegExp(name.replace(/(\(|\))/g, '\\$1'), 'g');
        if (id) {
          if (org === 2) {
            content = _.replace(content, reg, `<a href="https://www.qcc.com/pl_${id}.html">${name}</a>`);
          } else if (org === 13) {
            content = _.replace(content, reg, `<a href="https://www.qcc.com/investor_${id}.html">${name}</a>`);
          } else {
            content = _.replace(content, reg, `<a href="https://www.qcc.com/firm_${id}.html">${name}</a>`);
          }
        }
      });
      // 把换行替换成空格
      return content.replace(/<br\/>/g, '&nbsp;&nbsp;');
    },

    replaceContents(contents, highlights, category) {
      let result = '';
      _.forEach(contents, (c) => {
        const res = this.replaceObjContent(c, highlights, category);
        result += res;
        result += '<br />';
      });
      return result;
    },

    replaceObjContent(content, highlights, category) {
      let result = '';
      if (content) {
        if (content.Desc) result += `<span style="color: #666">${content.Desc}：</span>`;
        let d = `${content.Value}` || '-';
        if (category && +category === 75) {
          d = d.replace(/00:00:00/g, '');
        } else {
          d = d.replace(/00:00/g, '');
        }
        if (highlights.length > 0) {
          d = this.replace(d, highlights);
        }
        // 案件金额或者含有金额标识字段值需要红色处理
        if (content.Desc === '案件金额' || content.FlagEM) {
          d = `<span style="color: #F04040">${content.Value}</span>`;
        }
        result += d;
      }
      return result;
    },

    getContent(item) {
      if (item?.Desc) {
        if (item.Desc.Contents && _.isArray(item.Desc.Contents)) {
          return this.replaceContents(item.Desc.Contents, item.Desc.Highlight, item.Desc.Category);
        }
        if (item.Desc.Content && _.isObject(item.Desc.Content)) {
          return this.replaceObjContent(item.Desc.Content, item.Desc.Highlight, item.Desc.Category);
        }
        return this.replace(item.Desc.Content, item.Desc.Highlight);
      }
      return '';
    },
  },
};
