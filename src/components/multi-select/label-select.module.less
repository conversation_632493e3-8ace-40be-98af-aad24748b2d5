.container {
  position: relative;
  padding: 6px 25px 6px 6px;
  border: 1px solid #d8d8d8;
  border-radius: 2px;
  min-height: 32px;
  line-height: 22px;

  :global {
    .placeholder {
      color: #bbb;
      margin-left: 4px;
    }
  }
  
  .arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transition: transform 0.3s;
    transform: scaleX(1.4) translateY(-5px) !important;
    color: rgba(0, 0, 0, 0.25);
  }

  &:hover {
    border-color: #3cabfa;

    .arrow {
      color: #128bed;
    }
  }
}

.focused {
  border-color: #3cabfa;

  .arrow {
    color: #128bed;
    transition: transform 0.3s;
    transform: scaleX(1.34) rotate(180deg) translateY(5px) !important;
  }
}

.label {
  white-space: nowrap;
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #666;
  background: #eee;
  border-radius: 2px;
  cursor: pointer;

  &:not(.disabled):hover {
    background: #e2f1fd;
  }

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #e3e3e3;
  }
}


.drop-wapper{
  :global{
    .ant-dropdown-menu{
      max-height: 350px;
      overflow-y: auto;
    }
  }
}