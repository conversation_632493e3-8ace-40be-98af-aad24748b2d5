import { defineComponent, ref, PropType, Ref } from 'vue';
import { Checkbox, Dropdown, Icon, Menu } from 'ant-design-vue';

import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';

import styles from './label-select.module.less';
import QIcon from '../global/q-icon';

type Option = Partial<{
  name: string;
  color: string;
  labelId: string;
  [key: string]: string;
}>;

const MultiSelect = defineComponent({
  name: 'MultiSelect',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    options: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择标签',
    },
    limit: {
      type: Number,
      default: 5,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const dropdownVisible = ref(false);

    // 选中的标签ref
    const selectedRef = ref() as Ref<HTMLElement>;

    return {
      selectedRef,
      dropdownVisible,
    };
  },
  render() {
    const { placeholder } = this;
    const renderResult = () => {
      const tags = this.options.filter((item) => this.value.includes(item.value));
      if (tags.length === 0) {
        return <div class={'placeholder'}>{placeholder}</div>;
      }
      return tags.map((item: any) => {
        return (
          <span
            data-testid="label-select-tag"
            class={{ [styles.label]: true, [styles.disabled]: this.disabled }}
            onClick={(e) => {
              if (this.disabled) return;
              e.stopPropagation();
              e.preventDefault();
              this.$emit(
                'change',
                this.value.filter((i) => i !== item.value)
              );
            }}
          >
            <i
              v-show={item.color}
              style={{
                backgroundColor: item.color,
                borderRadius: '50%',
                display: 'inline-flex',
                width: '6px',
                height: '6px',
                marginRight: '4px',
              }}
            />
            {item.name}
            <QIcon
              v-show={!this.disabled}
              type="icon-a-tianjia1x"
              style={{
                transform: 'rotate(45deg)',
                marginLeft: '5px',
                color: '#999',
              }}
            ></QIcon>
          </span>
        );
      });
    };
    return (
      <Dropdown
        class={[
          styles.container,
          this.dropdownVisible ? styles.focused : '',
          {
            disabled: this.disabled,
          },
        ]}
        overlayClassName={styles.dropWapper} // 选项过多的时候，scroll
        trigger={['click']}
        visible={this.dropdownVisible}
        onVisibleChange={() => {
          this.dropdownVisible = !this.dropdownVisible;
        }}
        disabled={this.disabled}
      >
        <div class={'flex flex-wrap'} style={{ gap: '5px' }} ref="selectedRef">
          {/* 选中的标签样式 */}
          {renderResult()}
          <Icon type="down" class={styles.arrow} />
        </div>
        <Menu slot="overlay">
          {this.options.length ? (
            this.options.map((option: any) => {
              const isSelected = this.value.includes(option.value);
              return (
                <Menu.Item
                  key={option.value}
                  value={option.value}
                  style={{
                    color: isSelected ? '#128bed' : '',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                  disbled
                  onClick={() => {
                    if (isSelected) {
                      this.$emit('change', [...this.value.filter((v) => v !== option.value)]);
                    } else {
                      this.$emit('change', [...this.value, option.value]);
                    }
                  }}
                >
                  <Checkbox checked={isSelected}>
                    <span style={{ color: isSelected ? '#128bed' : '' }}>{option.name}</span>
                  </Checkbox>
                </Menu.Item>
              );
            })
          ) : (
            <Menu.Item>
              <QRichTableEmpty>暂时无标签</QRichTableEmpty>
            </Menu.Item>
          )}
        </Menu>
      </Dropdown>
    );
  },
});

export default MultiSelect;
