import { shallowMount } from '@vue/test-utils';

import MultiSelect from '..';

describe('MultiSelect', () => {
  it('正常渲染并显示占位符', () => {
    const wrapper = shallowMount(MultiSelect, {
      propsData: {
        value: [],
        options: [],
        placeholder: '请选择标签',
      },
    });
    expect(wrapper.find('.placeholder').text()).toBe('请选择标签');
  });

  it('选中标签后正确显示', async () => {
    const wrapper = shallowMount(MultiSelect, {
      propsData: {
        value: ['1'],
        options: [{ value: '1', name: '标签1' }],
      },
    });
    const label = wrapper.find('[data-testid="label-select-tag"]');
    expect(label.text()).toContain('标签1');
    await label.trigger('click');
    expect(wrapper.emitted().change?.[0][0]).toEqual([]);
  });

  it('禁用状态下不响应点击事件', async () => {
    const wrapper = shallowMount(MultiSelect, {
      propsData: {
        value: ['1'],
        options: [{ value: '1', name: '标签1' }],
        disabled: true,
      },
    });
    await wrapper.find('[data-testid="label-select-tag"]').trigger('click');
    expect(wrapper.emitted().change).toBeFalsy();
  });

  it('选项为空时显示空状态', async () => {
    const wrapper = shallowMount(MultiSelect, {
      propsData: {
        // value: [],
        // options: [],
      },
    });
    await wrapper.setData({
      dropdownVisible: true,
    });
    expect(wrapper.text()).toContain('请选择标签');
  });

  it('正确处理选项的选中与取消选中', async () => {
    const wrapper = shallowMount(MultiSelect, {
      propsData: {
        value: ['1'],
        options: [
          { value: '1', name: '标签1' },
          { value: '2', name: '标签2' },
        ],
      },
    });
    await wrapper.setData({
      dropdownVisible: true,
    });
    const menuItems = wrapper.findAllComponents({ name: 'AMenuItem' });
    menuItems.at(1).vm.$emit('click');
    expect(wrapper.emitted().change?.[0][0]).toEqual(['1', '2']);
    menuItems.at(0).vm.$emit('click');
    expect(wrapper.emitted().change?.[1][0]).toEqual([]);
  });

  it('处理下拉框的显示与隐藏', () => {
    const wrapper = shallowMount(MultiSelect, {
      propsData: {},
    });
    const dropdown = wrapper.findComponent({ name: 'ADropdown' });
    dropdown.vm.$emit('visibleChange');
    expect(wrapper.vm.dropdownVisible).toBe(true);
    dropdown.vm.$emit('visibleChange');
    expect(wrapper.vm.dropdownVisible).toBe(false);
  });
});
