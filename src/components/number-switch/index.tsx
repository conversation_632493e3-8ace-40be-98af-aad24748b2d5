import { computed, defineComponent } from 'vue';
import { Switch } from 'ant-design-vue';

const NumberSwitch = defineComponent({
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    const boolValue = computed(() => {
      return Boolean(props.value);
    });
    return { boolValue };
  },
  render() {
    return (
      <Switch
        {...{
          props: {
            ...this.$attrs,
          },
        }}
        onClick={(val, e) => {
          e.stopPropagation();
        }}
        checked={this.boolValue}
        onChange={(val) => {
          this.$emit('change', Number(val));
        }}
      />
    );
  },
});

export default NumberSwitch;
