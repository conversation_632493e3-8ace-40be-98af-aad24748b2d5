import { shallowMount } from '@vue/test-utils';

import NumberSwitch from '..';

describe('NumberSwitch 组件测试', () => {
  it('当 value 为 0 时，Switch 组件应未选中', () => {
    const wrapper = shallowMount(NumberSwitch, {
      propsData: {
        value: 0,
      },
    });
    expect(wrapper.findComponent({ name: 'ASwitch' }).props('checked')).toBe(false);
  });

  it('当 value 为 1 时，Switch 组件应选中', () => {
    const wrapper = shallowMount(NumberSwitch, {
      propsData: {
        value: 1,
      },
    });
    expect(wrapper.findComponent({ name: 'ASwitch' }).props('checked')).toBe(true);
  });

  it('当 Switch 组件状态改变时，应触发 change 事件', async () => {
    const wrapper = shallowMount(NumberSwitch, {
      propsData: {
        value: 0,
      },
    });
    const switchWrapper = wrapper.findComponent({ name: 'ASwitch' });
    switchWrapper.vm.$emit('change');
    expect(wrapper.emitted().change).toBeTruthy();
  });
});
