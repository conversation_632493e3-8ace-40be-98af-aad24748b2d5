import { shallowMount } from '@vue/test-utils';

import UserCountStatistics from '..';

vi.mock('@/store', () => {
  return {
    useStore: vi.fn(() => ({
      getters: {
        'user/usage': {
          bundleUsage: {
            // innerBlacklistQuantity: { stock: 10 },
            thirdPartyQuantity: { stock: 1 },
            monitorCompanyQuantity: { stock: 2 },
            personQuantity: { stock: 3 },
            batchDiligenceDailyQuantity: { stock: 4 },
          },
        },
      },
    })),
  };
});

const dimensions = ['innerBlacklistQuantity', 'thirdPartyQuantity', 'monitorCompanyQuantity', 'personQuantity', undefined];

const dimensionLabel = ['剩余可添加企业数', '剩余可添加企业数', '剩余可监控企业数', '剩余可添加人员数', '剩余可添加企业数'];
describe('UserCountStatistics', () => {
  it('renders loading state when loading prop is true', () => {
    const wrapper = shallowMount(UserCountStatistics, {
      propsData: { loading: true },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('renders correct label and count based on dimension prop', async () => {
    dimensions.forEach((dimension, index) => {
      const wrapper = shallowMount(UserCountStatistics, {
        propsData: { dimension, loading: false },
      });

      const label = wrapper.find('.flex-between > div:first-child').text();
      const count = wrapper.find('[data-testid="remaining-count"]').text();

      expect(label).toBe(`${dimensionLabel[index]}：`);
      expect(count).toBe(`${index}`);
    });
  });
});
