import { mount } from '@vue/test-utils';

import SearchCount from '..';

describe('SearchCount', () => {
  it('renders the total count correctly', async () => {
    const wrapper = mount(SearchCount, {
      propsData: {
        total: 0,
      },
    });
    expect(wrapper.find('em').text()).toBe('0');
    await wrapper.setProps({ total: 10 });
    expect(wrapper.find('em').text()).toBe('10');
    await wrapper.setProps({ total: 100001 });
    expect(wrapper.find('em').text()).toBe('10万+');
  });

  it('shows selected count when selectedIds is not empty and showSelects is true', () => {
    const wrapper = mount(SearchCount, {
      propsData: {
        selectedIds: [1, 2, 3],
        showSelects: true,
      },
    });
    expect(wrapper.text()).toContain('（已选择3条）');
  });

  it('does not show selected count when selectedIds is empty', () => {
    const wrapper = mount(SearchCount, {
      propsData: {
        selectedIds: [],
        showSelects: true,
      },
    });
    expect(wrapper.text()).not.toContain('（已选择');
  });

  it('does not show selected count when showSelects is false', () => {
    const wrapper = mount(SearchCount, {
      propsData: {
        selectedIds: [1, 2, 3],
        showSelects: false,
      },
    });
    expect(wrapper.text()).not.toContain('（已选择');
  });
});
