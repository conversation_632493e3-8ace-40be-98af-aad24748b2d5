import { Icon } from 'ant-design-vue';
import { defineComponent } from 'vue';

import styles from './search-count.module.less';

const SearchCount = defineComponent({
  name: 'SearchCount',
  props: {
    total: {
      type: Number,
      default: 0,
    },
    loading: {
      type: <PERSON>olean,
      default: false,
    },
    selectedIds: {
      type: Array,
      default: () => [],
    },
    showSelects: {
      type: Boolean,
      default: false,
    },
  },
  render() {
    return (
      <div class={styles.container}>
        {this.$scopedSlots.message ? (
          this.$scopedSlots.message(
            <div class="flex items-center">
              <Icon v-show={this.loading} type="sync" spin />
              <em v-show={!this.loading}>{this.total > 100000 ? '10万+' : this.total}</em>
            </div>
          )
        ) : (
          <span>
            共找到
            <Icon v-show={this.loading} type="sync" spin />
            <em v-show={!this.loading}>{this.total > 100000 ? '10万+' : this.total}</em>
            条相关结果
          </span>
        )}
        {this.selectedIds?.length > 0 && this.showSelects && (
          <span class={styles.countTip}>
            （已选择<em>{this.selectedIds?.length}</em>条）
          </span>
        )}
      </div>
    );
  },
});

export default SearchCount;
