.container {
  .tab {
    display: inline-block;
    cursor: pointer;
    color: #999;
    position: relative;
    font-weight: normal;

    &.active {
      color: #333;
      font-weight: bold;
      cursor: default;
      align-items: center;

      &::after {
        content: ' ';
        display: block;
        bottom: -14px;
        left: 0;
        right: 0;
        height: 2px;
        position: absolute;
        background-color: #128bed;
      }
    }

    + .tab {
      margin-left: 30px;
    }
  }

  .singleType {
    &.active {
      &::after {
        display: none;
      }
    }
  }
}
