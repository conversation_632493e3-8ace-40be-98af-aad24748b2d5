import { PropType, defineComponent } from 'vue';

import { hasPermission } from '@/shared/composables/use-permission';

import styles from './tabs.module.less';

const DEFAULT_TABS = [
  { label: '风险动态', value: 'risk-trends-overview', permission: [2091] },
  { label: '舆情动态', value: 'risk-trends-specific', permission: [2092] },
];

const RiskTrendsTab = defineComponent({
  name: 'RiskTrendsTab',
  props: {
    value: {
      type: String,
      default: DEFAULT_TABS[0].value,
    },
    tabs: {
      type: Array as PropType<Array<Record<string, any>>>,
      default: () => DEFAULT_TABS,
    },
  },
  render() {
    // 筛选出有权限的tab
    const tabs = this.tabs.reduce((arr, tab) => {
      if (!tab.permission || hasPermission(tab.permission)) {
        arr.push(tab);
      }
      return arr;
    }, []);

    return (
      <div class={styles.container}>
        {tabs.map((item) => {
          const isActivated = this.value === item.value;
          return (
            <div
              key={item.value}
              data-testid={item.label}
              class={{
                [styles.tab]: true,
                [styles.active]: isActivated,
                [styles.singleType]: tabs.length === 1,
                active: isActivated,
                tab: true,
              }}
              onClick={() => {
                if (!isActivated) {
                  this.$emit('change', item.value);
                }
              }}
            >
              {item.slot ? item.slot() : item.label}
            </div>
          );
        })}
      </div>
    );
  },
});

export default RiskTrendsTab;
