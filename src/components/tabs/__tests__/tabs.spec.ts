import { mount } from '@vue/test-utils';

import { hasPermission } from '@/shared/composables/use-permission';

import RiskTrendsTab from '..';

vi.mock('@/shared/composables/use-permission', () => ({
  hasPermission: vi.fn(),
}));

describe('RiskTrendsTab', () => {
  // 重置mock
  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试默认情况
  it('默认情况下显示所有tab', () => {
    vi.mocked<any>(hasPermission).mockReturnValue(true);
    const wrapper = mount(RiskTrendsTab);
    expect(wrapper.findAll('[data-testid]').length).toBe(2);
  });

  // 测试有权限的情况
  it('当有权限时显示所有tab', () => {
    vi.mocked<any>(hasPermission).mockReturnValue(true);
    const wrapper = mount(RiskTrendsTab);
    expect(wrapper.findAll('[data-testid]').length).toBe(2);
  });

  // 测试无权限的情况
  it('当无权限时只显示有权限的tab', () => {
    vi.mocked<any>(hasPermission).mockImplementation((permission) => {
      return permission[0] !== 2091;
    });
    const wrapper = mount(RiskTrendsTab);
    expect(wrapper.findAll('[data-testid]').length).toBe(1);
    expect(wrapper.find('[data-testid="舆情动态"]').exists()).toBe(true);
  });

  // 测试点击tab时触发change事件
  it('点击tab时触发change事件', () => {
    vi.mocked<any>(hasPermission).mockReturnValue(true);
    const wrapper = mount(RiskTrendsTab);
    wrapper.find('[data-testid="舆情动态"]').trigger('click');
    expect(wrapper.emitted().change).toBeTruthy();
    expect(wrapper.emitted().change[0]).toEqual(['risk-trends-specific']);
  });

  // 测试只有一个tab时的情况
  it('当只有一个tab时', () => {
    vi.mocked<any>(hasPermission).mockImplementation((permission) => {
      return permission[0] === 2091;
    });
    const wrapper = mount(RiskTrendsTab);
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('插槽测试', () => {
    const wrapper = mount(RiskTrendsTab, {
      propsData: {
        tabs: [
          {
            label: 'Tab A',
            value: 'a',
            slot: () => {
              return 'AAA';
            },
          },
          {
            label: 'Tab B',
            value: 'b',
          },
        ],
      },
    });
    expect(wrapper.text()).toContain('AAA');
    expect(wrapper.text()).not.toContain('Tab A');
    expect(wrapper.text()).toContain('Tab B');
  });
});
