import { del, markRaw, Ref, ref, set } from 'vue';

export const wrapperExists = ref(false);
type Component = any;

interface DialogData<P, R> {
  component: Component;
  params: P;
  promiseResolve: (value: R) => void;
  promiseReject: (error: unknown) => void;
  unmountDelay?: number;
}

export const dialogsData: Ref<Record<string, DialogData<any, any>>> = ref({});

export function add<P, R>(component: Component, params: P, unmountDelay?: number): Promise<R> {
  return new Promise<R>((resolve, reject) => {
    set(dialogsData.value, Math.random().toString(32).slice(2), {
      component: markRaw(component),
      params,
      promiseResolve: resolve,
      promiseReject: reject,
      unmountDelay,
    });
  });
}

function unmountDialog(id: string, delay?: number) {
  const unmount = () => del(dialogsData.value, id);
  if (delay) {
    setTimeout(unmount, delay);
  } else {
    unmount();
  }
}

export function resolveDialog(id: string, result: unknown, unmountDelay?: number) {
  dialogsData.value[id].promiseResolve(result);
  unmountDialog(id, unmountDelay);
}

export function rejectDialog(id: string, error: unknown, unmountDelay?: number) {
  dialogsData.value[id].promiseReject(error);
  unmountDialog(id, unmountDelay);
}
