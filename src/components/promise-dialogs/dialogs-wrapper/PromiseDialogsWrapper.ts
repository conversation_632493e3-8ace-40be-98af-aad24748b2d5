import { defineComponent, h, onBeforeUnmount } from 'vue';

import { dialogsData, rejectDialog, resolveDialog, wrapperExists } from './store';

export default defineComponent({
  name: 'PromiseDialogsWrapper',
  props: {
    unmountDelay: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    if (wrapperExists.value) {
      console.error('PromiseDialogsWrapper instance already exists');
    }

    wrapperExists.value = true;

    onBeforeUnmount(() => {
      wrapperExists.value = false;

      dialogsData.value = {};
    });

    return () =>
      h(
        'div',
        Object.keys(dialogsData.value).map((id) => {
          const value = dialogsData.value[id];
          const component = value.component;
          const params = value.params;

          const resolve = (result: unknown, unmountDelay?: number) =>
            resolveDialog(id, result, unmountDelay || value.unmountDelay || props.unmountDelay);

          const reject = (error: unknown, unmountDelay?: number) =>
            rejectDialog(id, error, unmountDelay || value.unmountDelay || props.unmountDelay);

          return h(component as any, {
            key: id as any,
            props: {
              params,
            },
            on: {
              resolve,
              reject,
            },
          });
        })
      );
  },
});
