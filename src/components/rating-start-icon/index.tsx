import { defineComponent } from 'vue';

/**
 * 图标
 */
const RatingStarIcon = defineComponent({
  functional: true,
  props: {
    /**
     * 前景色，默认为 currentColor
     */
    color: {
      type: String,
      default: 'currentColor',
    },
    /**
     * 背景色，默认为 #e3e3e3
     */
    background: {
      type: String,
      default: '#e3e3e3',
    },
    /**
     * 半选状态
     */
    half: {
      type: Boolean,
      default: false,
    },
  },
  render(h, { props }) {
    const path = `M617.779 325.427q2.048 6.144 5.837 11.469 3.789 5.222 9.011 9.011 5.222 3.891 11.469 5.837 6.144 2.048 12.595 2.048l219.443 0.102 3.072 0.41 3.277 0.614 3.072 0.819 3.072 1.126 2.867 1.331 2.765 1.638 2.662 1.741 2.56 2.048 2.355 2.15 2.253 2.355 1.946 2.56q1.024 1.229 1.741 2.662 0.922 1.331 1.638 2.765l1.331 2.97q0.614 1.434 1.024 3.072 0.614 1.434 1.024 3.072l0.512 3.072 0.41 3.174 0.102 3.277v3.174l-0.512 3.174q-0.205 1.638-0.614 3.174-0.307 1.638-0.819 3.072l-1.126 3.072-1.331 2.867-1.638 2.867-1.741 2.662-2.048 2.56-2.048 2.355-2.458 2.15-2.56 2.048-174.797 126.976q-5.325 3.891-9.114 9.114t-5.837 11.366-2.048 12.698q0 6.451 2.048 12.698l66.867 205.517 0.819 3.072 0.614 3.174 0.41 3.277 0.102 3.174-0.102 3.174-0.41 3.277-0.614 3.072-0.819 3.072-1.126 3.072-1.331 2.97-1.638 2.765-1.741 2.662-2.048 2.56-2.15 2.355-2.355 2.15-2.56 2.048-2.662 1.741-2.765 1.536q-1.434 0.819-2.97 1.434-1.434 0.614-3.072 1.024l-3.072 0.922-3.072 0.614-3.174 0.41-3.277 0.102-3.174-0.102q-1.638-0.102-3.174-0.41-1.638-0.205-3.174-0.614l-3.072-0.922-3.072-1.024-2.867-1.434-2.867-1.536-2.662-1.741-174.899-127.181q-5.222-3.789-11.366-5.837t-12.698-1.946-12.698 2.048-11.366 5.837l-177.562 128.717-2.867 1.536-2.867 1.434-3.072 1.024-3.072 0.922-3.072 0.614-3.277 0.41-3.174 0.102-3.277-0.102-3.072-0.41-3.277-0.614-3.072-0.922-3.072-1.024-2.867-1.434-2.765-1.536-2.662-1.741-2.56-2.048-2.355-2.15-2.15-2.355-2.048-2.56-1.741-2.662-1.638-2.765-1.331-2.97q-0.614-1.434-1.024-3.072l-1.024-3.072-0.512-3.072-0.41-3.277-0.102-3.174 0.102-3.174 0.41-3.277 0.614-3.072 0.819-3.072 66.867-205.619q2.048-6.144 2.048-12.698 0-6.451-2.048-12.698-2.048-6.144-5.837-11.366t-9.114-9.114l-174.797-126.771-2.56-2.048-2.355-2.15-2.253-2.355-1.946-2.56q-1.024-1.331-1.741-2.662l-1.638-2.867-1.331-2.867-1.024-3.072q-0.614-1.434-1.024-3.072l-0.512-3.072-0.41-3.277-0.102-3.174v-3.277l0.512-3.072 0.614-3.277 0.819-3.072 1.126-3.072 1.331-2.867q0.717-1.434 1.638-2.765l1.741-2.662 2.048-2.56 2.048-2.355 2.458-2.15 2.56-2.048 2.662-1.741 2.765-1.638 2.97-1.331q1.434-0.614 2.97-1.024l3.072-1.024 3.174-0.512 3.174-0.41 3.277-0.102h216.064q6.554 0 12.698-2.048t11.469-5.837 9.011-9.011q3.891-5.222 5.837-11.469l66.867-205.517 1.024-3.072 1.434-2.867 1.536-2.867 1.741-2.662 2.048-2.56 2.15-2.355 2.355-2.048q1.229-1.126 2.56-2.048l2.56-1.946 2.765-1.536 2.97-1.331 3.072-1.126 3.072-0.922 3.072-0.614 3.277-0.307q1.638-0.205 3.277-0.205t3.174 0.205q1.638 0 3.277 0.307l3.072 0.614 3.072 0.922 3.072 1.024 2.97 1.434 2.765 1.536 2.662 1.843q1.331 0.922 2.56 2.048l2.355 2.048 2.15 2.355 2.048 2.56 1.741 2.662 1.536 2.867 1.434 2.867 1.024 3.072z`;

    // 半选状态
    if (!props.half) {
      return (
        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 1024 1024">
          {/* 背景色 */}
          <path fill={props.color} d={path}></path>
        </svg>
      );
    }

    return (
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 1024 1024">
        <path fill={props.background} d={path}></path>
        <g mask="url(#mask)">
          <path fill={props.color} d={path}></path>
        </g>
        {/* Mask */}
        <defs>
          <mask id="mask" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <rect width="512" height="1024" fill="#fff" rx="0" />
          </mask>
        </defs>
      </svg>
    );
  },
});

export default RatingStarIcon;
