import { shallowMount } from '@vue/test-utils';

import RatingStarIcon from '..';

describe('RatingStarIcon', () => {
  test('renders correctly with default props', () => {
    const wrapper = shallowMount(RatingStarIcon);
    expect(wrapper.find('path').attributes('fill')).toBe('currentColor');
  });

  test('renders correctly with custom color', () => {
    const wrapper = shallowMount(RatingStarIcon, {
      propsData: {
        color: 'red',
      },
    });
    expect(wrapper.find('path').attributes('fill')).toBe('red');
  });

  test('renders correctly with custom background', () => {
    const wrapper = shallowMount(RatingStarIcon, {
      propsData: {
        background: 'blue',
      },
    });
    expect(wrapper.find('path').attributes('fill')).toBe('currentColor');
  });

  test('renders correctly in half state', () => {
    const wrapper = shallowMount(RatingStarIcon, {
      propsData: {
        half: true,
      },
    });
    expect(wrapper.find('path').attributes('fill')).toBe('#e3e3e3');
    expect(wrapper.find('g path').attributes('fill')).toBe('currentColor');
  });

  test('renders correctly in half state with custom color and background', () => {
    const wrapper = shallowMount(RatingStarIcon, {
      propsData: {
        half: true,
        color: 'green',
        background: 'yellow',
      },
    });
    expect(wrapper.find('path').attributes('fill')).toBe('yellow');
    expect(wrapper.find('g path').attributes('fill')).toBe('green');
  });
});
