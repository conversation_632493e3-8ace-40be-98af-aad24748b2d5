import _, { difference, get, intersection, isNil } from 'lodash';

import { RoleTagColorMap, RoleTagMap } from '@/components/global/q-role-text/config';
import { dateFormat } from '@/utils/format';
import { isJSONString } from '@/utils/data-type/is-json-string';
import moment from 'moment';

const replaceBr = (str) => {
  if (typeof str === 'string') {
    str = str.slice(Number(str.startsWith('\n'))).replace(/\n/g, '<br/>');
  }
  return str;
};

export const regMatch = (highlights, text, orgFilter, replacer) => {
  const reg = orgFilter.map((v) => v.Name || v.name).join('|');
  if (reg) {
    return _.replace(text, new RegExp(`(${reg})`, 'g'), (match) => {
      const data = highlights.find((v) => v.Name === match);
      if (!data) {
        return match;
      }
      const id = data.Id || data.id;
      const title = encodeURIComponent(match);
      return replacer({ id, title, name: match });
    });
  }
  return text;
};

/**
 * 当事人标签高亮
 * @param text
 */
const replaceRoleTag = (text) => {
  let res = text;
  const matchList = text.match(/\[[\u4e00-\u9fa5]+\]/g);
  if (!matchList?.length) return res;
  let unmatched = [...matchList];
  Object.keys(RoleTagMap).forEach((type) => {
    const roleTagList = RoleTagMap[type].map((v) => `[${v}]`);
    const matched = intersection(matchList, roleTagList);
    unmatched = difference(unmatched, matched);
    matched.forEach((v) => {
      res = res.replaceAll(v, `<span style="color: ${RoleTagColorMap[type]};">${v}</span>`);
    });
  });
  unmatched.forEach((v) => {
    res = res.replaceAll(v, `<span style="color: ${RoleTagColorMap.default};">${v}</span>`);
  });
  return res;
};

const replace = (content, highlights) => {
  const uniqHighlights = _.uniqBy(highlights, 'Id');
  const contentArr = content.split('<br/>');
  const _case = (uniqHighlights as any).filter((x) => x.Type === 'CASE');
  const _product = (uniqHighlights as any).filter((x) => x.Type === 'PRODUCT');
  const _cerfitication = (uniqHighlights as any).filter((x) => x.Type === 'Certification');
  let _target = (uniqHighlights as any).filter((x) => !x.Type && (x.Id || x.id));
  // 名称长的排在前面，优先匹配，防止被部分重名的名字短的公司匹配到
  _target = _.sortBy(_target, (o) => {
    const name = o.Name || o.name;
    return -name.length;
  });
  const ware = contentArr.map((text) => {
    text = replaceRoleTag(text);
    if (_.includes(text, '案号') || _.includes(text, '执行通知书文号')) {
      if (_case.length) {
        _.forEach(_case, (o) => {
          const id = o.Id;
          const name = o.Name;
          if (name && id) {
            text = _.replace(
              text,
              name,
              `<a href="/embed/courtCaseDetail?caseId=${id}&title=${encodeURIComponent(name)}" target="_blank">${name}</a>`
            );
          }
        });
      }
    }
    if (_.includes(text, '决定文书号')) {
      if (_case.length) {
        _.forEach(_case, (o) => {
          const id = o.Id;
          const name = o.Name;
          if (name && id) {
            text = _.replace(
              text,
              name,
              `<a href="/embed/adminpenaltydetail?id=${id}&title=${encodeURIComponent(name)}" target="_blank">${name}</a>`
            );
          }
        });
      }
    }
    if (_.includes(text, '产品名称')) {
      if (_product.length) {
        _.forEach(_product, (o) => {
          const id = o.Id;
          const name = o.Name;

          if (name && id) {
            text = _.replace(
              text,
              name,
              `<a href="/embed/product-info?id=${id}&title=${encodeURIComponent(name)}" target="_blank">${name}</a>`
            );
          }
        });
      }
    }
    if (_target.length) {
      _.forEach(_target, (o) => {
        const id = o.Id || o.id;
        const name = o.Name || o.name || '';
        const org = Number(o.Org);
        if (name && !name.includes('*')) {
          const reg = new RegExp(_.escapeRegExp(name), 'g');
          if (id) {
            const title = encodeURIComponent(name);
            if (org === 2) {
              text = _.replace(text, reg, `<a href="/embed/beneficaryDetail?personId=${id}&title=${title}" target="_blank">${name}</a>`);
            } else if (org === 13) {
              text = _.replace(text, reg, `<a href="/embed/investAgency?investId=${id}&title=${title}" target="_blank">${name}</a>`);
            } else {
              // org 0, 3, 9
              text = _.replace(text, reg, `<a href="/embed/companyDetail?keyNo=${id}&title=${title}" target="_blank">${name}</a>`);
            }
          }
        }
      });
    }

    if (_cerfitication.length) {
      _.forEach(_cerfitication, (o) => {
        const name = o.Name;
        text = _.replace(text, name, `<a href="javascript: void(0)">${name}</a>`);
      });
    }
    /**
     * 正则匹配<a><a>**公司</a>**中文（例如分公司、分行等）</a>，清空内部a标签
     */
    const regex = /(<a href="[a-zA-z]+:\/\/[^\s]*">)(.*?)<a href="[a-zA-z]+:\/\/[^\s]*">(.*)<\/a>(.*?)(<\/a>)/g;
    if (text.includes('，')) {
      const textArr = text.split('，');
      text = textArr
        .map((el) => {
          if (regex.test(el)) {
            el = el.replace(regex, '$1$2$3$4$5');
          }
          return el;
        })
        .join('，');
    } else if (text.includes('、')) {
      const textArr = text.split('、');
      text = textArr
        .map((el) => {
          if (regex.test(el)) {
            el = el.replace(regex, '$1$2$3$4$5');
          }
          return el;
        })
        .join('，');
    } else if (regex.test(text)) {
      text = text.replace(regex, '$1$2$3$4$5');
    }
    return text;
  });

  return ware.join('<br/>');
};
/**
 * @desc replaceObjContent
 * @param {Boolean} single 是否是单行，只获取内容，不需要key
 */
const replaceObjContent = (content, highlights, category, leftSpace = false, single = false) => {
  let result = '';
  if (content) {
    // 对外投资变更 IsBP 只有desc  不展示value
    if (content.Desc && !single)
      result += `<span style="color: #999${leftSpace ? ';margin-left: 30px;' : ''}">${content.Desc}${
        content.Key === 'IsBP' ? '' : '：'
      }</span>`;
    let d = `${content.Value}` || '-';
    if (category && +category === 75) {
      d = d.replace(/00:00:00/g, '');
    } else {
      d = d.replace(/00:00/g, '');
    }
    if (highlights.length > 0) {
      d = replace(d, highlights);
    }
    // 案件金额或者含有金额标识字段值需要红色处理
    if (content.Desc === '案件金额' || content.FlagEM) {
      d = `<span style="color: #F04040">${content.Value}</span>`;
    }
    // IsBP 只有desc  不展示value
    if (content.Key === 'IsBP') {
      d = '';
    }
    result += d;
  }
  return result;
};

const replaceContents = (contents, highlights, category) => {
  let result = '';
  _.forEach(contents, (c, index: number) => {
    const rowSecond = (index + 1) % 2 === 0;
    const res = replaceObjContent(c, highlights, category, rowSecond);
    result += res;
    if (rowSecond) {
      result += '<br />';
    }
  });
  return result;
};

// 动态变化的内容
export const getRiskContent = (item) => {
  /**
   * 合并 Highlight & OtherHighlight
   */
  const highlights = [...(item?.Highlight || []), ...(item?.OtherHighlight || [])];
  // Content '' 默认的
  // Contents []自定义内容样式
  if (item.Contents && _.isArray(item.Contents)) {
    item.Contents = replaceBr(item.Contents);
    return replaceContents(item.Contents, item.Highlight, item.Category);
  }
  if (item.Content && _.isObject(item.Content)) {
    item.Content = replaceBr(item.Content);
    return replaceObjContent(item.Content, item.Highlight, item.Category, false, false);
  }

  item.Content = replaceBr(item.Content);
  return replace(item.Content || '', highlights);
};

// 关联信息的内容
const getRelationContent = (item) => {
  const { companyNameRelated, companyKeynoRelated, relatedTypeDescList, riskTypeDescList, shortStatus, isMonitor } = item;
  let res = '';
  if (companyNameRelated && companyKeynoRelated) {
    res += '<div>关联公司：';
    res += `<a href="/embed/companyDetail?keyNo=${companyKeynoRelated}&title=${companyNameRelated}" target="_blank">${companyNameRelated}</a>`;
    res += '</div>';
  }
  if (relatedTypeDescList) {
    res += '<div>关联类型：';
    res += relatedTypeDescList.join('，');
    res += '</div>';
  }

  if (shortStatus) {
    res += '<div>企业状态：';
    res += riskTypeDescList?.join('，') ?? shortStatus;
    res += '</div>';
  }

  if (!isNil(isMonitor)) {
    res += `<div>监控状态：${isMonitor ? '已监控' : '未监控'}</div>`;
  }
  return res;
};

const renderContent = (config, item) => {
  const renderCompanies = (list: Array<{ KeyNo: string; Name: string }>) => {
    return (
      list?.map((v) => {
        return `<a href="/embed/companyDetail?keyNo=${v.KeyNo}&title=${v.Name}" target="_blank">${v.Name}</a>`;
      }) || '-'
    );
  };
  return config
    .map((option) => {
      if (option.scopedSlots) {
        const renderMap = {
          company: renderCompanies,
        };
        const renderFn = renderMap[option.scopedSlots.customRender];
        const data = option.key ? get(item, option.key) : item;
        return `<div>${option.title}：${renderFn?.(data, item) ?? '-'}</div>`;
      }
      if (option.customRender) {
        const data = option.key ? get(item, option.key) : item;
        return `<div>${option.title}：${option.customRender(data, item)}</div>`;
      }
      if (option.key) {
        return `<div>${option.title}：${get(item, option.key) || '-'}</div>`;
      }
      return option.title;
    })
    .join('');
};

const getAssetInvestigationAndFreezingContent = (item) => {
  const config = [
    { title: '案号', key: 'caseno' },
    {
      title: '被执行人',
      customRender: (item) => {
        const arr = isJSONString(item.subjectnameandkeyno)
          ? JSON.parse(item.subjectnameandkeyno)
          : [{ KeyNo: item.subjectnames[1], Name: item.subjectnames[0] }];
        return arr
          .map((v) => {
            return `<a href="/embed/companyDetail?keyNo=${v.KeyNo}&title=${v.Name}" target="_blank">${v.Name}</a>`;
          })
          .join('，');
      },
    },
    { title: '查封法院', key: 'court' },
    {
      title: '查封期限',
      customRender: (item) => {
        return `${dateFormat(item.startdate)} 至 ${dateFormat(item.enddate)}`;
      },
    },
  ];
  return renderContent(config, item);
};

const getPledgeMergerContent = (item) => {
  const config = [
    {
      title: '抵押人',
      key: 'DebtorJson',
      scopedSlots: { customRender: 'company' },
    },
    {
      title: '抵押权人',
      key: 'PledgeeJson',
      scopedSlots: { customRender: 'company' },
    },
    { title: '登记机关', key: 'registeroffice' },
    { title: '登记日期', customRender: (item) => dateFormat(item.registerstartdate) },
  ];
  return renderContent(config, item);
};

const getTaxpayerCertificationChangeContent = (item) => {
  const { changeAfter, changeBefore, changeDate } = item;
  return `变更前：${changeBefore}<br />变更后：${changeAfter}<br />变更日期：${dateFormat(changeDate)}`;
};

const getPatentInfo = (item) => {
  const { Title, ApplicationNumber, PublicationNumber, KindCodeDesc, NameAndKeyno, TransferOutTime } = item;
  const applicator = NameAndKeyno.map(
    (v) => `<a href="/embed/companyDetail?keyNo=${v.KeyNo}&title=${v.Name}" target="_blank">${v.Name}</a>`
  ).join('，');
  const transferOutTime = TransferOutTime?.length ? moment(TransferOutTime[TransferOutTime.length - 1] * 1000).format('YYYY-MM-DD') : '-';
  return `专利名称：${Title}<br />申请号：${ApplicationNumber}<br />公开（公告）号：${PublicationNumber}<br />专利类型：${KindCodeDesc}<br />申请（专利权）人： ${applicator}<br />专利转出时间： ${transferOutTime}`;
};

const getControllerCompanyContent = (item) => {
  const { KeyNo, Name, ShortStatus, Industry, PercentTotal, added, removed, changeDate } = item;
  if (added || removed || changeDate) {
    return getOutwardInvestment(item);
  }
  const company = `<a href="/embed/companyDetail?keyNo=${KeyNo}&title=${Name}" target="_blank">${Name}</a>`;
  return `控股公司：${company}<br />控股比例：${PercentTotal}<br />控股公司状态：${ShortStatus}<br />控股公司行业：${Industry}`;
};

const getCompanyDetailChange = (item) => {
  const { changeAfter, changeBefore, changeDate } = item;
  return `变更前：${changeBefore}<br />变更后：${changeAfter}<br />变更时间：${changeDate}`;
};

const getOutwardInvestment = (item) => {
  const { added, removed, changeDate } = item;
  let res = '';
  if (added?.data?.length) {
    res += `新增：${added?.data?.length} <br />`;
  }
  if (removed?.data?.length) {
    res += `减少：${removed?.data?.length} <br />`;
  }
  return `${res}变更时间：${changeDate}`;
};

const getTradeDetailContent = (item) => {
  return item?.displayContent || '-';
};

const getConvertibleBondListingDate = (item) => {
  const config = [{ title: '上市日期', customRender: (item) => dateFormat(item.IssueDate) }];
  return renderContent(config, item);
};
const getAdditionalShareListingDate = (item) => {
  const config = [{ title: '增发新股上市日期', customRender: (item) => dateFormat(item.IssueDate) }];
  return renderContent(config, item);
};

const getOverseasListing = (item) => {
  const config = [{ title: '更新内容', key: 'dynamiccontent' }];
  return renderContent(config, item);
};

/**
 * @desc 获取渲染内容
 * @param {Boolean} single 是否是单行，只获取内容，不需要key
 */
export const getContent = (item, dimensionKey = 'RiskChange') => {
  let newContent;
  if (!item) {
    return '-';
  }
  switch (dimensionKey) {
    case 'TaxpayerCertificationChange':
      newContent = getTaxpayerCertificationChangeContent(item);
      break;
    // 关联方
    case 'RelatedCompanyChange':
    case 'RelatedCompanies':
      newContent = getRelationContent(item);
      break;
    // 资产查冻
    case 'AssetInvestigationAndFreezing':
      newContent = getAssetInvestigationAndFreezingContent(item);
      break;
    // 动产抵押
    case 'PledgeMerger':
      newContent = getPledgeMergerContent(item);
      break;
    // 专利转让出质
    case 'PatentInfo':
      newContent = getPatentInfo(item);
      break;
    // 投资异常
    case 'ControllerCompany':
      newContent = getControllerCompanyContent(item);
      break;
    // 工商信息变更
    case 'CompanyDetailChange':
      newContent = getCompanyDetailChange(item);
      break;
    // 控股股东数量变化
    case 'OutwardInvestmentChange':
      newContent = getOutwardInvestment(item);
      break;
    case 'SupplierOrCustomer': // 供应商和客户交易金额同比变化
    case 'TradeDetail': // 财报披露关联方交易风险
      newContent = getTradeDetailContent(item);
      break;
    case 'ConvertibleBondListingDate': // 可转债上市时间
      newContent = getConvertibleBondListingDate(item);
      break;
    case 'AdditionalShareListingDate': // 增发上市时间
      newContent = getAdditionalShareListingDate(item);
      break;
    case 'OverseasListing': // 境内企业境外上市
      newContent = getOverseasListing(item);
      break;
    default:
      newContent = getRiskContent(item);
      break;
  }
  return newContent;
};
