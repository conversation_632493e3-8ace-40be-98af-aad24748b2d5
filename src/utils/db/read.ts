import { onMounted, ref } from 'vue';

import { db } from '.';

const readMap = ref({});

export const getReadMap = async () => {
  const list = await db.read.toArray();
  const map = list.reduce((acc, cur) => {
    acc[cur.id] = cur.status;
    return acc;
  }, {});
  readMap.value = map;
  return map;
};

export const setRead = async (arr: string[]) => {
  await db.read.bulkPut(arr.map((id) => ({ id, status: true, timestamp: Date.now() })));
  await getReadMap();
};

export const clear = async () => {
  await db.read.clear();
  await getReadMap();
};

export const useReadStore = () => {
  onMounted(() => {
    getReadMap();
  });

  return {
    readMap,
    setRead,
    clear,
  };
};
