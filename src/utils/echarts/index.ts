import ECharts from 'vue-echarts';
import echarts from 'echarts/lib/echarts';
import echartsDefaults from 'echarts/lib/model/globalDefault';
import geo from '@/config/geo.json';
import 'echarts/lib/chart/map';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/visualMap';
import 'echarts/lib/component/title';

echartsDefaults.color = [
  '#62A5E7',
  '#7ECF52',
  '#EECB5F',
  '#9470E5',
  '#E3935C',
  '#E06757',
  '#605EF0',
  '#45C2CE',
  '#4DD593',
  '#ECB556',
  '#A877D1',
];

// FIXME: 异步加载地图数据
echarts.registerMap('china', geo);

export default (Vue) => {
  Vue.component('vue-echarts', ECharts);
};
