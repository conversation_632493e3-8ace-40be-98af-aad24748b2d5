/**
 * 匹配最长公共字符串
 * @param str1
 * @param str2
 */
export const longestSubstring = (str1: string, str2: string): string => {
  const len1: number = str1.length;
  const len2: number = str2.length;

  const dp = Array.from({ length: len1 + 1 }, () => Array(len2 + 1).fill(0));

  let maxLength = 0; // 最长公共子串的长度
  let endIndex = 0; // 最长公共子串的结束位置

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
        if (dp[i][j] > maxLength) {
          maxLength = dp[i][j];
          endIndex = i;
        }
      }
    }
  }

  return str1.slice(endIndex - maxLength, endIndex);
};
