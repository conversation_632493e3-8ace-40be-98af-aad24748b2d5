import { replace } from 'lodash';

export const replaceEnter2Br = (value) => replace(value, /\n/g, '<br />');

export const replaceWhitespace2Nbsp = (value) => replace(value, / /g, '&nbsp;');

export const formatChar2HtmlEntity = (value) => replaceEnter2Br(replaceWhitespace2Nbsp(value));

export const limitLength =
  (len = 32) =>
  (str = '') => {
    const safetyStr = String(str);

    if (safetyStr.length > len) {
      return `${safetyStr.slice(0, len)}...`;
    }
    return safetyStr;
  };
