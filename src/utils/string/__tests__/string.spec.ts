import { formatChar2HtmlEntity, limitLength } from '..';

describe('formatChar2HtmlEntity', () => {
  test('should replace whitespace with &nbsp;', () => {
    const value = 'hello world';
    const expectedResult = 'hello&nbsp;world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });

  test('should replace line breaks with <br>', () => {
    const value = 'hello\nworld';
    const expectedResult = 'hello<br />world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });

  test('should replace multiple whitespaces with multiple &nbsp;', () => {
    const value = 'hello  world';
    const expectedResult = 'hello&nbsp;&nbsp;world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });

  test('should replace whitespaces and line breaks correctly', () => {
    const value = 'hello  \nworld';
    const expectedResult = 'hello&nbsp;&nbsp;<br />world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });
});

describe('limitLength function', () => {
  test('should return the empty string if no arguments are passed', () => {
    const limited = limitLength()();
    expect(limited).toBe('');
  });

  test('should return the original string if length is less than or equal to the limit', () => {
    const input = 'Hello';
    const limited = limitLength(10)(input);
    expect(limited).toBe(input);
  });

  test('should return a string with max length and trailing dots if input length exceeds the limit', () => {
    const input = 'Hello, World!';
    const limit = 10;
    const expectedOutput = `${input.slice(0, limit)}...`;
    const limited = limitLength(limit)(input);
    expect(limited).toBe(expectedOutput);
  });

  test('should return an empty string if the input is empty', () => {
    const input = '';
    const limited = limitLength(10)(input);
    expect(limited).toBe('');
  });

  test('should handle non-string inputs by converting them to strings', () => {
    const input = 12345;
    const limited = limitLength(10)(input as any);
    expect(limited).toBe(input.toString());
  });
});
