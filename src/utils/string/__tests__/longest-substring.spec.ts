import { longestSubstring } from '../longest-substring';

describe('longestSubstring', () => {
  test('两个普通字符串的最长公共子串', () => {
    expect(longestSubstring('abcdef', 'zabcf')).toBe('abc');
  });

  test('其中一个字符串为空', () => {
    expect(longestSubstring('', 'abc')).toBe('');
  });

  test('两个字符串都为空', () => {
    expect(longestSubstring('', '')).toBe('');
  });

  test('两个字符串完全相同', () => {
    expect(longestSubstring('abc', 'abc')).toBe('abc');
  });

  test('没有公共子串', () => {
    expect(longestSubstring('abc', 'def')).toBe('');
  });

  test('公共子串在字符串末尾', () => {
    expect(longestSubstring('abcdef', 'xyzdef')).toBe('def');
  });

  test('公共子串在字符串开头', () => {
    expect(longestSubstring('abcdef', 'abcxyz')).toBe('abc');
  });

  test('公共子串长度为1', () => {
    expect(longestSubstring('a', 'b')).toBe('');
  });

  test('一个字符串是另一个字符串的子串', () => {
    expect(longestSubstring('abcdef', 'abc')).toBe('abc');
  });

  test('包含特殊字符的字符串', () => {
    expect(longestSubstring('a#b$c', 'x#y$z')).toBe('#');
  });
});
