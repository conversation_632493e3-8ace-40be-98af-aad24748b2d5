import { uuid } from '..';

describe('uuid', () => {
  test('should generate a valid uuid with default pattern', () => {
    const uuidString = uuid();
    const defaultPattern = [8, 4, 4, 12];
    const parts = uuidString.split('-');

    expect(parts.length).toBe(4);
    expect(parts[0].length).toBe(defaultPattern[0]);
    expect(parts[1].length).toBe(defaultPattern[1]);
    expect(parts[2].length).toBe(defaultPattern[2]);
    expect(parts[3].length).toBe(defaultPattern[3]);
  });

  test('should generate a valid uuid with custom pattern', () => {
    const customPattern = [10, 6, 6, 8];
    const uuidString = uuid(...customPattern);
    const parts = uuidString.split('-');

    expect(parts.length).toBe(4);
    expect(parts[0].length).toBe(customPattern[0]);
    expect(parts[1].length).toBe(customPattern[1]);
    expect(parts[2].length).toBe(customPattern[2]);
    expect(parts[3].length).toBe(customPattern[3]);
  });

  test('should generate unique uuids on each call', () => {
    const uuid1 = uuid();
    const uuid2 = uuid();

    expect(uuid1).not.toBe(uuid2);
  });
});
