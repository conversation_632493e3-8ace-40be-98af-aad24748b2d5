export const supportsNativeSmoothScroll = 'scrollBehavior' in document.documentElement.style;

export default (top = 0) => {
  if (supportsNativeSmoothScroll) {
    window.scrollTo({
      top,
      left: 0,
      behavior: 'smooth',
    });
  } else if (window.scrollTo) {
    window.scrollTo(0, top);
  } else if (document.scrollingElement) {
    document.scrollingElement.scrollTop = 0;
  } else {
    document.documentElement.scrollTop = 0;
  }
};
