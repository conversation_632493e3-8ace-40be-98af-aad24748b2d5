import type { AxiosResponse, InternalAxiosRequestConfig, AxiosRequestConfig } from 'axios';

interface RetryConfig {
  condition: (res: AxiosResponse) => boolean;
  times: number; // 重试次数
  delay?: number; // 请求间隔时长(毫秒)
}

/**
 * 扩展 request config
 * TODO: 类型扩展移到 `interceptor` 实现部分
 */
export interface HttpClientRequestConfig<D = any> extends AxiosRequestConfig<D> {
  tpl?: Record<string, string | number>;
  strip?: boolean;
  retry?: RetryConfig;
  skipInterceptor?: string[]; // 定义要跳过的拦截器
}

export interface InternalHttpClientRequestConfig<D = any> extends InternalAxiosRequestConfig<D> {
  tpl?: Record<string, string | number>;
  strip?: boolean;
  retry?: RetryConfig;
  skipInterceptor?: string[]; // 定义要跳过的拦截器
}

export interface VerbHttpClientRequestConfig extends InternalHttpClientRequestConfig {
  url: string;
}

interface ExtendAxiosResponse extends AxiosResponse {
  config: InternalHttpClientRequestConfig;
}

export type HttpClientResponse = ExtendAxiosResponse;
