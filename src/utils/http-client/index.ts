import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

import { urlInterceptor } from './interceptors';
import { HttpClientRequestConfig } from './interfaces';
import { sleep } from '..';

export class HttpClient {
  private instance: AxiosInstance;

  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(config);
    // NOTE: Default intercepters
    urlInterceptor(this.instance);
  }

  /**
   * 注入拦截器
   * @param {Function} interceptor
   */
  inject(interceptor: (instance: AxiosInstance, options?) => void, options?: any): void {
    interceptor(this.instance, options);
  }

  request<R = any, D = any>(config: HttpClientRequestConfig<D>): Promise<R> {
    return this.instance.request(config);
  }

  get<R = any, D = any>(url: string, config?: HttpClientRequestConfig<D>): Promise<R> {
    return this.instance.get(url, config);
  }

  post<R = any, D = any>(url: string, data?: D, config?: HttpClientRequestConfig<D>): Promise<R> {
    return this.instance.post(url, data, config);
  }

  put<R = any, D = any>(url: string, data?: D, config?: HttpClientRequestConfig<D>): Promise<R> {
    return this.instance.put(url, data, config);
  }

  delete<R = any, D = any>(url: string, config?: HttpClientRequestConfig<D>): Promise<R> {
    return this.instance.delete(url, config);
  }
}

type RetryOptions = {
  delay?: number;
  maxRetries?: number;
  condition?: (response) => boolean;
};

export const retry = async (request: () => Promise<any>, options: RetryOptions = {}) => {
  const { maxRetries = 3, delay = 1000 } = options;
  const fn = async (count) => {
    count -= 1;
    const res = await request();
    const isNeedRetry = (options?.condition?.(res) ?? false) && count > 0;
    if (isNeedRetry) {
      await sleep(delay);
      return fn(count);
    }

    return res;
  };

  return fn(maxRetries);
};

const CACHE_RESULT_MAP = {};

export const cache = async (request, key) => {
  if (CACHE_RESULT_MAP[key]) {
    return CACHE_RESULT_MAP[key];
  }
  const res = await request();
  CACHE_RESULT_MAP[key] = res;
  return res;
};
