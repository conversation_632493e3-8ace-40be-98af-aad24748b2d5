import { AxiosInstance } from 'axios';

import { HttpClientResponse } from '../interfaces';

function processResponse(response: HttpClientResponse): HttpClientResponse {
  const { strip = true } = response.config;
  if (strip && response.data) {
    const responseDataKeys = Object.keys(response.data);
    if (responseDataKeys.length === 1 && responseDataKeys[0] === 'data') {
      return response.data.data;
    }
    return response.data;
  }
  return response;
}

/**
 * URL template interceptor
 */
export function stripInterceptor(instance: AxiosInstance) {
  instance.interceptors.response.use(processResponse);
}
