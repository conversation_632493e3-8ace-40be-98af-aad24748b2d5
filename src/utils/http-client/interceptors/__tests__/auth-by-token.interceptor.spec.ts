import { getTokenFromURL, removeTokenFromURL } from '../auth-by-token.interceptor';

describe('auth-by-token-interceptor', () => {
  test('getTokenFromURL', () => {
    const expected = '1234567890';
    const tokenField = 'kzzsessid';
    const url = `https://www.example.com/?${tokenField}=${expected}`;
    const result = getTokenFromURL(url, tokenField);
    expect(result).toBe(expected);
  });

  test('removeTokenFromURL', () => {
    const expected = 'https://www.example.com/?';
    const tokenField = 'kzzsessid';
    const url = `${expected}${tokenField}=1234567890`;
    const result = removeTokenFromURL(url, tokenField);
    expect(result).toBe(expected);
  });
});
