import { captureException } from '@sentry/vue';
import { AxiosInstance } from 'axios';

// Error handler chain
function logError(error) {
  console.error('Sentry log :>> ', error);
  const req = error.config;
  const res = error.response;
  const extra: { request?: Record<string, unknown>; response?: Record<string, unknown> } = {};
  extra.request = {
    url: req.url,
    headers: req.headers,
    method: req.method,
    params: req.params,
    data: req.data ? JSON.stringify(req.data) : null,
  };
  if (error.response) {
    extra.response = {
      headers: res.headers,
      data: res.data ? JSON.stringify(res.data) : null,
    };
  }
  captureException(error, {
    tags: {
      'x-kzz-request-id': req.headers['x-kzz-request-id'],
      'x-kzz-request-from': req.headers['x-kzz-request-from'],
      'error.group': 'ajax',
      'error.status': error.response.status || -1,
    },
    extra,
  });
  // Pass through
  throw error;
}

/**
 * URL template interceptor
 */
export function sentryInterceptor(instance: AxiosInstance) {
  instance.interceptors.response.use(undefined, logError);
}
