import { AxiosInstance } from 'axios';

import { InternalHttpClientRequestConfig } from '../interfaces';

export interface AuthByTokenInterceptorOptions {
  /** 指定请求头取值字段 */
  headerField: string;
  /** 保存token的Key */
  storageField: string;
}

/**
 * 从 localStorage 中获取 token
 */
export const retrieveToken = (storageField: string) => {
  return localStorage.getItem(storageField);
};

/**
 * 保存 token 到 localStorage
 */
export const saveToken = (storageField: string, value: string) => {
  localStorage.setItem(storageField, value);
};

/**
 * 移除 token
 */
export const removeToken = (storageField: string) => {
  localStorage.removeItem(storageField);
};

/** 从 URL 中获取 token */
export const getTokenFromURL = (url: string, field: string) => {
  const re = new RegExp(`[?&]${field}=([a-zA-Z0-9]+)`, 'i');
  const matched = url.match(re);
  return matched && matched[1] ? matched[1] : null;
};

/** 从 URL 中删除 token */
export const removeTokenFromURL = (url: string, field: string) => {
  const re = new RegExp(`&?${field}=[a-zA-Z0-9]+`, 'i');
  return url.replace(re, '');
};

export function authByTokenInterceptor(instance: AxiosInstance, options: AuthByTokenInterceptorOptions): void {
  instance.interceptors.request.use((requestConfig: InternalHttpClientRequestConfig) => {
    // from url
    const tokenFromURL = getTokenFromURL(window.location.href, options.headerField);
    if (tokenFromURL) {
      saveToken(options.storageField, tokenFromURL);
    }

    // from storage
    const tokenFromStorage = retrieveToken(options.storageField);
    if (tokenFromStorage && requestConfig.headers) {
      requestConfig.headers[options.headerField] = tokenFromStorage;
    }
    return requestConfig;
  });
}
