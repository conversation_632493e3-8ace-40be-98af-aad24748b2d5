import * as Sentry from '@sentry/vue';
import { AxiosInstance } from 'axios';
import { get } from 'lodash';
import { message } from 'ant-design-vue';

import env from '@/shared/config/env';

export const ERROR_CODE_WARNING = [
  400002,
  400005,
  400004,
  300007,
  300104,
  500101,
  300013,
  500004,
  600002,
  900195,
  900105,
  400203,
  400205, // 尽调排查次数
  400207, // 尽调排查生成报告数量
  400209,
  900104,
  600005,
  50004,
  400605,
  400301,
  400401,
  400501,
  400008,
  1100004,
  1100005,
  113001,
  500502,
];

// 根据code来判断报错，会覆盖默认error报错信息
export const MESSAGE_MAP = {
  400203: '您的风险排查企业额度已用完，请联系管理员增加。',
  400205: '您的风险排查可用次数已用完，请联系管理员增加。',
  400207: '您的风险排查报告下载额度已用完，请联系管理员增加。',
  400209: '您今日排查企业数已达到当日限制！',
  400605: '您今日批量年检次数已达到当日限制！',
  400609: '您的批量年检额度已用完，请联系管理员增加。',
  400301: '您可添加的企业额度已达到上限，请联系管理员增加。',
  400401: '您可添加的黑名单额度已达到上限，请联系管理员增加。',
  400501: '您可添加的人员额度已达到上限，请联系管理员增加。',
};

export const getErrorMsg = (error) => {
  const errorData = error?.response?.data ?? {};

  // 错误代码（预设文案）
  const errorCode = errorData.code;
  if (MESSAGE_MAP[errorCode]) {
    return MESSAGE_MAP[errorCode];
  }

  // 错误消息（可能是数组）
  const errorMessage = errorData.error || errorData.message;
  if (Array.isArray(errorMessage)) {
    return errorMessage.join(', ');
  }
  if (errorMessage) {
    return errorMessage;
  }

  return '系统错误';
};

export const showErrorMsg = (error) => {
  const code = get(error, `response.data.code`);
  if (ERROR_CODE_WARNING.includes(code)) {
    return message.warning({
      key: code,
      content: getErrorMsg(error),
    });
  }
  return message.error({
    key: code,
    content: getErrorMsg(error),
  });
};

export function errorInterceptor(instance: AxiosInstance): void {
  instance.interceptors.response.use(undefined, (error) => {
    const { response = {}, code } = error;

    const key = get(error, 'response.data.key'); // 解封 token key
    const reqId = get(error, 'response.data.id'); // Request ID: 分析用户被封锁原因

    const status = get(error, 'response.status', 0);
    if (response?.config?.skipInterceptor?.includes('error')) {
      return Promise.reject(error);
    }
    // 用户被风控后的处理逻辑
    if (status === 429) {
      if (key) {
        // 跳转到解封页面（人机验证）
        const redirectUrl = window.location.href;
        const url = `${env.WAF_SITE}/captcha?matchKey=${key}&replaceUrl=${encodeURIComponent(redirectUrl)}`;
        window.location.href = url;
      } else if (reqId) {
        // 跳转到封锁页面（无法操作）
        const url = `${env.WAF_SITE}/block?reqid=${reqId}`;
        window.location.href = url;
      } else {
        // 访问过多、请稍后再试
        message.error({
          key: status,
          content: '您的账号访问超频，请稍后访问或联系客服人员：400-928-2212',
        });
      }
      return Promise.reject(error);
    }

    if ([400, 403].includes(status)) {
      showErrorMsg(error);
      return Promise.reject(error);
    }

    // Sentry 警告级别错误
    if (
      [504].includes(status) || // HTTP 504 错误处理为 warning
      (error.code === 'ECONNABORTED' && error.message.includes('timeout')) // 超时
    ) {
      Sentry.withScope((scope) => {
        scope.setLevel('warning');
        Sentry.captureException(error);
      });
      return Promise.reject(error);
    }

    return Promise.reject(error);
  });
}
