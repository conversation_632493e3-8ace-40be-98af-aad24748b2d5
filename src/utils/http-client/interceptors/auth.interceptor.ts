import { AxiosInstance } from 'axios';

export interface AuthInterceptorOptions {
  redirect?: (path?: string, code?: string, response?: Record<string, any>) => void;
}

export function authInterceptor(instance: AxiosInstance, options: AuthInterceptorOptions = {}): void {
  instance.interceptors.response.use(undefined, (error) => {
    if (error?.response?.config?.skipInterceptor?.includes('auth')) {
      return Promise.reject(error);
    }
    if (error?.isAxiosError) {
      const statusCode = error?.response?.status;
      if ((statusCode === 401 || statusCode === 403) && typeof options.redirect === 'function') {
        const errorCode = error?.response?.data?.code;
        // 401 && 403 redirect操作
        if (statusCode === 401 || [200509, 200011, 200008, 200003].includes(errorCode)) {
          options.redirect(statusCode, errorCode, error.response?.data);
          return Promise.resolve(error);
        }
        return Promise.reject(error);
      }
    }
    return Promise.reject(error);
  });
}
