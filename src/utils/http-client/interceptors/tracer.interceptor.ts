import { AxiosInstance } from 'axios';

import { uuid } from '@/utils/uuid';

import { InternalHttpClientRequestConfig } from '../interfaces';

export interface TracerInterceptorOptions {
  traceId: string;
}

const processRequest = (options: TracerInterceptorOptions) => (requestConfig: InternalHttpClientRequestConfig) => {
  requestConfig.headers = requestConfig.headers || {};
  requestConfig.headers[options.traceId] = uuid();
  return requestConfig;
};

/**
 * 为请求添加动态 traceId ()
 */
export function tracerInterceptor(instance: AxiosInstance, options: TracerInterceptorOptions) {
  instance.interceptors.request.use(processRequest(options));
}
