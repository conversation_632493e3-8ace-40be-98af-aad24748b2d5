import type { AxiosInstance } from 'axios';

import { HttpClientResponse } from '../interfaces';

/**
 * 延迟: 非精准时长
 * @param milliseconds 毫秒
 */
const delay = (milliseconds: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, milliseconds));

/**
 * 请求失败重试
 */
const processResponse = (instance: AxiosInstance) => async (response: HttpClientResponse) => {
  if (response.config.retry) {
    const defaults = {
      delay: 0,
    };
    if (response.config.retry.condition(response) && response.config.retry.times > 0) {
      response.config.retry.times -= 1;
      return delay(response.config.retry.delay ?? defaults.delay).then(() => instance(response.config));
    }
  }
  return Promise.resolve(response);
};

/**
 * URL template interceptor
 */
export function retryInterceptor(instance: AxiosInstance) {
  instance.interceptors.response.use(processResponse(instance));
}
