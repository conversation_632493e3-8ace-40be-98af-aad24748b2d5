import template from 'url-template';
import { AxiosInstance } from 'axios';

import { InternalHttpClientRequestConfig, VerbHttpClientRequestConfig } from '../interfaces';

function processURL(config: InternalHttpClientRequestConfig): InternalHttpClientRequestConfig | VerbHttpClientRequestConfig {
  const { tpl, ...rest } = config;
  if (tpl) {
    const url = template.parse(config.url).expand(tpl);
    return {
      ...rest,
      url,
    };
  }
  return config;
}

/**
 * URL template interceptor
 */
export function urlInterceptor(instance: AxiosInstance): void {
  instance.interceptors.request.use(processURL);
}
