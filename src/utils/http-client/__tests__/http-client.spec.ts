import axios, { AxiosInstance, AxiosStatic } from 'axios';

import { cache, HttpClient, retry } from '..';
import { Mocked } from 'vitest';

vi.mock('axios');

describe('HttpClient', () => {
  let mockAxios;
  let mockMethods;
  let mockInterceptors;

  beforeEach(() => {
    mockMethods = {
      get: vi.fn().mockResolvedValue({}),
      post: vi.fn().mockResolvedValue({}),
      put: vi.fn().mockResolvedValue({}),
      delete: vi.fn().mockResolvedValue({}),
      request: vi.fn().mockResolvedValue({}),
    };
    mockInterceptors = {
      request: {
        use: vi.fn(),
        eject: vi.fn(),
      },
      response: {
        use: vi.fn(),
        eject: vi.fn(),
      },
    };
    mockAxios = axios as Mocked<AxiosStatic>;
    mockAxios.create.mockReturnValue({
      get: mockMethods.get,
      post: mockMethods.post,
      put: mockMethods.put,
      delete: mockMethods.delete,
      request: mockMethods.request,
      interceptors: mockInterceptors,
    });
  });

  test.each([
    ['request', [{ url: '/test', params: { a: '1' } }]],
    ['get', ['/test', { params: { a: '1' } }]],
    ['delete', ['/test', { params: { a: '1' } }]],
    ['put', ['/test', { data: true }, { tpl: { x: '1' } }]],
    ['post', ['/test', { data: true }, { tpl: { x: '1' } }]],
  ])('method: %s', (method, args) => {
    const httpClient = new HttpClient({});
    httpClient[method](...args);
    expect(mockMethods[method]).toHaveBeenCalledWith(...args);
  });

  test('inject', () => {
    const httpClient = new HttpClient({});
    const interceptor = (instance: AxiosInstance) => {
      instance.interceptors.request.use();
    };
    httpClient.inject(interceptor);
    expect(mockInterceptors.request.use).toHaveBeenCalled();
  });

  test('retry: once', async () => {
    const mockRequest = vi.fn().mockResolvedValue(false);
    await retry(mockRequest);
    expect(mockRequest).toHaveBeenCalledTimes(1);
  });

  test('retry: multiple times', async () => {
    const mockRequest = vi.fn().mockResolvedValue(false);
    await retry(mockRequest, { maxRetries: 2, delay: 0, condition: () => true });
    expect(mockRequest).toHaveBeenCalledTimes(2);
  });

  test('cache', async () => {
    const mockRequest = vi.fn().mockResolvedValue(true);
    await cache(mockRequest, 'TEST'); // Called once
    await cache(mockRequest, 'TEST'); // Called twice
    expect(mockRequest).toHaveBeenCalledTimes(1);
  });
});
