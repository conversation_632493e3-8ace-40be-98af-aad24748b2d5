import { Group } from '@/components/global/q-filter/interface';
import AREA_OPTIONS from '@/shared/constants/area.constant';
import { DateRangeSource, NumberRangeSource } from '@/interfaces/data.interface';
import { Path } from '@/utils/tree';
import { INDUSTRY_OPTIONS } from '@/shared/constants/industry.constant';
import NATIONAL_INDUSTRY_OPTIONS from '@/shared/constants/national-industry.constant';
import {
  DEFAULT_DATE_RANGE,
  DEFAULT_AMOUNT_RANGE,
  COMPANY_STATUS,
  COMPANY_TYPES,
  ORGANIZATION_TYPES,
  REGISTERED_UNIT,
  ASSET_OPTIONS,
} from '@/config/tender.config';

export type SimpleFilterState = {
  keyword?: string;
  filters?: Partial<{
    r: Path[];
    sd: DateRangeSource[];
    i: Path[];
    tag: Path[];
    rca: NumberRangeSource[];
    sc: string[];
    ekc: string[];
    ot: string[];
    ru: string[];
  }>;
};

export const filterGroups: Group[] = [
  {
    field: 'r',
    type: 'cascader-multiple',
    label: '省份地区',
    options: AREA_OPTIONS,
    layout: 'inline',
  },
  {
    field: 'sd',
    type: 'multiple',
    label: '成立日期',
    options: DEFAULT_DATE_RANGE,
    custom: {
      type: 'date-range',
    },
  },
  {
    field: 'i',
    type: 'cascader-multiple',
    label: '国标行业',
    options: NATIONAL_INDUSTRY_OPTIONS,
  },
  {
    field: 'tag',
    type: 'cascader-multiple',
    label: '新兴行业',
    options: INDUSTRY_OPTIONS,
  },
  {
    field: 'rca',
    type: 'multiple',
    label: '注册资本',
    options: DEFAULT_AMOUNT_RANGE,
    custom: {
      type: 'number-range',
      props: {
        unit: '万元',
      },
    },
  },
  {
    field: 'sc',
    type: 'multiple',
    label: '登记状态',
    options: COMPANY_STATUS,
    transform: 'split',
  },
  {
    field: 'ekc',
    type: 'multiple',
    label: '企业类型',
    options: COMPANY_TYPES,
  },
  {
    field: 'ot',
    type: 'multiple',
    label: '组织机构',
    options: ORGANIZATION_TYPES,
    transform: 'split',
  },
  {
    field: 'ru',
    type: 'multiple',
    label: '资本类型',
    options: REGISTERED_UNIT,
  },
  {
    field: 'zjrhc',
    type: 'multiple',
    label: '营收水平',
    options: ASSET_OPTIONS,
  },
  {
    field: 'zjphc',
    type: 'multiple',
    label: '利润水平',
    options: ASSET_OPTIONS,
  },
  {
    field: 'zjasc',
    type: 'multiple',
    label: '资产规模',
    options: ASSET_OPTIONS,
  },
];
