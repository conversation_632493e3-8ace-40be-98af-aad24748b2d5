import { forEach, isEmpty, isNil, isPlainObject, last } from 'lodash';

import { Path } from '@/utils/tree';
import { tree as IndustryTree } from '@/shared/constants/industry.constant';

import toQuery, { parseArea, parseNational } from '../to-query';
import { filterGroups, SimpleFilterState } from './simple-filter';

export const stateToQuery = ({ keyword, filters }: SimpleFilterState) => {
  const query = {} as any;
  const filter = toQuery(filterGroups, filters) as any;

  if (keyword) {
    query.searchKey = keyword;
  }
  if (isPlainObject(filters)) {
    forEach(filters, (v, k) => {
      switch (k) {
        case 'r':
          {
            const value = parseArea(v as Path[]);
            if (value?.length) {
              filter[k] = value;
            }
          }
          break;
        case 'i':
          {
            const value = parseNational(v as Path[]);
            if (value?.length) {
              filter[k] = value;
            }
          }
          break;
        case 'tag':
          if (!isNil(v) && v.length) {
            IndustryTree.setCheckedPaths(v as Path[]);
            filter[k] = IndustryTree.getMergedCheckedNodes().map((item) => item.value as string);
          }
          break;
        case 'econType':
          if (!isNil(v) && v.length) {
            filter[k] = v.map((item) => last(item));
          }
          break;
        default:
          filter[k] = v;
          break;
      }
    });
  }
  if (!isEmpty(filter)) {
    query.filter = filter;
  }

  return query;
};
