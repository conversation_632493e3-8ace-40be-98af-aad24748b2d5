import _ from 'lodash';

import { Group } from '@/components/global/q-filter/interface';
import AREA_OPTIONS from '@/shared/constants/area.constant';
import { DateRangeSource, NumberRangeSource } from '@/interfaces/data.interface';
import { Path } from '@/utils/tree';
import IFB_PROGRESS_OPTIONS, { ROOT_MAP as IFB_PROGRESS_ROOT_MAP } from '@/shared/constants/ifbprogress.constant';
import {
  DEFAULT_DATE_RANGE,
  DEFAULT_AMOUNT_RANGE,
  TENDER_INDUSTRY,
  TENDER_CONTACTS_TYPE,
  TENDER_OWNER_TYPES,
} from '@/config/tender.config';

const MAIN_CITY = AREA_OPTIONS.map((pr) => ({
  ...pr,
  children: pr.children ? pr.children.map((ct) => _.omit(ct, ['children'])) : [],
}));

export enum TENDER_TYPE {
  all = 'all',
  ifb = 'ifb', // 招标
  wtb = 'wtb', // 中标
  pps = 'pps', // 拟建
}

export type FilterState = Partial<{
  keyword: string;
  filters: {
    publishdate: DateRangeSource;
    region: Path[];
    industryv2: string[];
    ifbprogress: Path[];
    ifbprogress1: string[];
    budgetvalue: NumberRangeSource[];
    wtbamtes: NumberRangeSource[];
    teltypes: number[][];
    ownertype: number[];
  };
}>;

export type FilterQuery = Partial<{
  searchKeyList: string[];
  filter: Partial<{
    publishdate: DateRangeSource[];
    province: string[];
    city: number[];
    industryv2: string[];
    ifbprogress: string[];
    budgetvalue: NumberRangeSource[];
    wtbamtes: NumberRangeSource[];
    teltypes: number[];
    ownertype: number[];
  }>;
}>;

export const genFilterGroups = (options: { type: TENDER_TYPE }): Group[] => {
  const { type } = options;
  const groups: Group[] = [
    {
      field: 'publishdate',
      type: 'single',
      label: '发布时间',
      options: DEFAULT_DATE_RANGE,
      custom: {
        type: 'date-range',
      },
      transform: 'array',
    },
    {
      field: 'region',
      type: 'cascader-multiple',
      label: '省份地区',
      options: MAIN_CITY,
    },
    {
      field: 'industryv2',
      type: 'multiple',
      label: '行业分类',
      options: TENDER_INDUSTRY,
    },
  ];

  switch (type) {
    case TENDER_TYPE.all:
      groups.push({
        field: 'ifbprogress',
        type: 'cascader-multiple',
        label: '信息类型',
        options: IFB_PROGRESS_OPTIONS,
      });
      break;
    case TENDER_TYPE.pps:
      groups.push({
        field: 'ifbprogress1',
        type: 'multiple',
        label: '审批阶段',
        options: IFB_PROGRESS_ROOT_MAP.pps.children,
      });
      break;
    case TENDER_TYPE.ifb:
      groups.push({
        field: 'ifbprogress1',
        type: 'multiple',
        label: '招标类型',
        options: IFB_PROGRESS_ROOT_MAP.ifb.children,
      });
      break;
    case TENDER_TYPE.wtb:
      groups.push({
        field: 'ifbprogress1',
        type: 'multiple',
        label: '中标类型',
        options: IFB_PROGRESS_ROOT_MAP.wtb.children,
      });
      break;
    default:
      break;
  }

  if ([TENDER_TYPE.all, TENDER_TYPE.ifb].includes(type)) {
    groups.push({
      field: 'budgetvalue',
      type: 'multiple',
      label: '预算金额',
      options: DEFAULT_AMOUNT_RANGE,
      custom: {
        type: 'number-range',
        props: { unit: '万元' },
      },
    });
  }
  if ([TENDER_TYPE.all, TENDER_TYPE.wtb].includes(type)) {
    groups.push({
      field: 'wtbamtes',
      type: 'multiple',
      label: '中标金额',
      options: DEFAULT_AMOUNT_RANGE,
      custom: {
        type: 'number-range',
        props: { unit: '万元' },
      },
    });
  }

  if (type !== TENDER_TYPE.pps) {
    groups.push(
      {
        field: 'teltypes',
        type: 'multiple',
        label: '联系方式',
        options: TENDER_CONTACTS_TYPE,
        transform: 'flatten',
      },
      {
        field: 'ownertype',
        type: 'multiple',
        label: '招标/采购单位类型',
        options: TENDER_OWNER_TYPES,
      }
    );
  }

  return groups;
};

export const SORT_OPTIONS = [
  {
    label: '智能排序',
    value: null,
  },
  {
    label: '发布时间从早到晚',
    value: {
      sortOrder: 'ASC',
      sortField: 'publishdate',
    },
  },
  {
    label: '发布时间从晚到早',
    value: {
      sortOrder: 'DESC',
      sortField: 'publishdate',
    },
  },
];

export const SEARCH_TYPES = [
  {
    label: '精准搜索',
    value: 'accurate',
  },
  {
    label: '模糊搜索',
    value: 'fuzzy',
  },
];

export const QUERY_LINKS = [
  {
    label: '同时满足',
    value: 'and',
    tip: '以“同时满足”连接多关键词。订阅结果包含同时满足各关键字的全部结果。如关键词1=“园林”，关键词2=“绿化”，则包含“园林”并且“绿化”的结果才会出现在订阅结果内。',
  },
  {
    label: '满足任意',
    value: 'or',
    tip: '以“满足任意”连接多关键词。订阅结果包含满足任意关键字的全部结果。如关键词1=“园林”，关键词2=“绿化”，则包含“园林”或“绿化”的结果均会出现在订阅结果内。',
  },
];

export const TENDER_SEARCH_FIELDS = [
  {
    value: null,
    label: '全文',
  },
  {
    value: 'title',
    label: '标题',
  },
  {
    value: 'projectno',
    label: '项目编号',
  },
  {
    value: 'ifbunit',
    label: '招标/采购单位',
  },
  {
    value: 'agent',
    label: '代理单位',
  },
  {
    value: 'wtbunit',
    label: '中标单位',
  },
];

export const PROPOSED_SEARCH_FIELDS = [
  {
    value: null,
    label: '全文',
  },
  {
    value: 'title',
    label: '标题',
  },
  {
    value: 'projectno',
    label: '项目代码',
  },
];
