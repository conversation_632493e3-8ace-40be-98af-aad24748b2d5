import _ from 'lodash';

import { typedForEach } from '@/utils/typed';
import { tree as areaTree } from '@/shared/constants/area.constant';
import { Path } from '@/utils/tree';
import { tree as progressTree } from '@/shared/constants/ifbprogress.constant';
import { NumberRangeSource } from '@/interfaces/data.interface';

import { FilterState, FilterQuery, genFilterGroups, TENDER_TYPE } from './filter';
import toQuery from '../to-query';

export const normalizeZmtValue = (value: NumberRangeSource): NumberRangeSource => {
  const [min, max] = [value.min, value.max].map((v) => (_.isNumber(v) ? v * 10000 : v));

  return { min, max };
};

const tenderToQuery = (state: FilterState): FilterQuery => {
  const groups = genFilterGroups({ type: TENDER_TYPE.all });
  const query: FilterQuery = {};
  const filter: FilterQuery['filter'] = toQuery(groups, state.filters, [
    'region',
    'budgetvalue',
    'wtbamtes',
    'ifbprogress',
    'ifbprogress1',
  ]);

  if (state.keyword) {
    query.searchKeyList = [state.keyword];
  }

  if (state.filters) {
    typedForEach(state.filters, (v, k) => {
      if (_.isNil(v) || (Array.isArray(v) && !v.length)) {
        return;
      }

      switch (k) {
        case 'region':
          {
            areaTree.setCheckedPaths(v as Path[]);
            const nodes = areaTree.getMergedCheckedNodes();
            let province: string[] = [];
            let city: number[] = [];

            if (nodes.length) {
              nodes.forEach((node) => {
                const [pr, ct] = node.path;
                if (pr) {
                  province.push(pr as string);
                }
                if (ct) {
                  city.push(ct as number);
                }
              });
            }
            province = _.uniq(province);
            city = _.uniq(city);

            if (province.length) {
              filter.province = province;
            }
            if (city.length) {
              filter.city = city;
            }
          }
          break;
        case 'ifbprogress':
          {
            progressTree.setCheckedPaths(v as Path[]);
            const nodes = progressTree.getMergedCheckedNodes();

            if (nodes.length) {
              filter.ifbprogress = nodes.map(({ path }) => _.last(path) as string);
            }
          }
          break;
        case 'ifbprogress1':
          filter.ifbprogress = v as string[];
          break;
        case 'budgetvalue':
        case 'wtbamtes':
          filter[k] = (v as NumberRangeSource[]).map(normalizeZmtValue);
          break;
        default:
          break;
      }
    });
  }

  if (!_.isEmpty(filter)) {
    query.filter = filter;
  }

  return query;
};

export default tenderToQuery;
