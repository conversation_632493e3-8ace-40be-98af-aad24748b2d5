import * as _ from 'lodash';

import { DateRangeValue, SearchSelectSource, StepCascaderSource } from '@/interfaces';

import { mapTreePath, parseSearchSelect } from '../to-query';
// import {
//   StepCascaderSource,
//   DateRangeValue,
//   mapTreePath,
//   parseSearchSelect,
//   SearchSelectSource,
// } from './filters';

type AwardState = {
  keyword?: string;
  filters?: Partial<{
    honortype: StepCascaderSource;
    'keynocomp,compname': SearchSelectSource;
    pername: string;
    projectname: string;
    awardingdate: DateRangeValue;
  }>;
};

type AwardQuery = {
  searchKeyList?: string[];
  filter?: Partial<{
    honortype: {
      honortypes: Array<{
        honortype: string;
        honorlevel?: string;
        awardsname?: string;
      }>;
      andOrNot: boolean;
    };
    keynocomp: string;
    compname: string;
    pername: string;
    projectname: string;
    awardingdate: DateRangeValue[];
  }>;
};

const awardToQuery = ({ keyword, filters }: AwardState): AwardQuery => {
  const query: AwardQuery = {};
  const filter: AwardQuery['filter'] = {};
  if (keyword) {
    query.searchKeyList = [keyword];
  }

  if (_.isPlainObject(filters)) {
    _.forEach(filters, (v, k) => {
      switch (k) {
        case 'honortype':
          {
            const value = _.filter(
              (v as StepCascaderSource)?.value.map((c) => mapTreePath(c, ['honortype', 'honorlevel', 'awardsname'])),
              (c) => !_.isEmpty(c)
            );
            if (value && value.length) {
              filter[k] = {
                honortypes: value as any,
                andOrNot: (v as StepCascaderSource).logic !== 'or',
              };
            }
          }
          break;
        case 'awardingdate':
          if (v) {
            filter[k] = [v] as DateRangeValue[];
          }
          break;
        case 'keynocomp,compname':
          Object.assign(filter, parseSearchSelect(k, v as SearchSelectSource, false));
          break;
        default:
          if (v) {
            filter[k] = v as any;
          }
          break;
      }
    });
  }

  if (!_.isEmpty(filter)) {
    query.filter = filter;
  }

  return query;
};

export default awardToQuery;
