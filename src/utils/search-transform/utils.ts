import * as _ from 'lodash';

import { Group, BasicGroupItem } from '@/components/global/q-filter/interface';

export const toMap = (groups: Group[]) => {
  return _.reduce(
    groups,
    (map, v) => {
      if (v.type === 'breakLine') {
        return map;
      }

      if (v.type === 'groups' || v.type === 'fold-groups') {
        v.children.forEach((c) => {
          map[c.field] = c;
        });
      } else {
        map[v.field] = v as BasicGroupItem;
      }

      return map;
    },
    {} as Record<string, BasicGroupItem>
  );
};
