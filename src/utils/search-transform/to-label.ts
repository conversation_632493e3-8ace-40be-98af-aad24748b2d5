import * as _ from 'lodash';

import Tree, { Path } from '@/utils/tree';
import { SelectOption, CascaderOption, Label } from '@/interfaces/data.interface';
import { inputGroupToLabel, dateRangeToLabel, numberRangeToLabel } from '@/utils/parser';
import { Group } from '@/components/global/q-filter/interface';

import { toMap } from './utils';

const DIVIDER = '/';

export const parseSelectValue = (
  values: any[] | undefined,
  options: SelectOption[],
  custom?: 'number-range' | 'date-range' | 'input-group',
  customProps?: any
): string => {
  if (!Array.isArray(values) || !Array.isArray(options)) {
    return '';
  }

  return _.compact(
    values.map((v) => {
      const o = options.find(({ value }) => _.isEqual(value, v));

      if (o) {
        return o.label;
      }

      if (!custom) {
        return '';
      }
      switch (custom) {
        case 'date-range':
          return dateRangeToLabel(v);
        case 'number-range':
          return numberRangeToLabel(v, customProps);
        case 'input-group':
          return inputGroupToLabel(v);
        default:
          return '';
      }
    })
  ).join(DIVIDER);
};

export const parseCascaderValue = (values: Path[] | undefined, options: CascaderOption[]): string => {
  if (!Array.isArray(values)) {
    return '';
  }

  const tree = new Tree(options);

  tree.setCheckedPaths(values);
  return tree
    .getMergedCheckedNodes()
    .map((node) => node.label)
    .join(DIVIDER);
};

export const parseCascaderSingleValue = (value: Path, options: CascaderOption[]): string => {
  if (!Array.isArray(value)) {
    return '';
  }

  const tree = new Tree(options);

  tree.setCheckedPaths([value]);
  return tree
    .getMergedCheckedNodes()
    .map((node) => node.label)
    .join(DIVIDER);
};

const toLabel = (groups: Group[], filters) => {
  const labels: Label[] = [];
  const groupsMap = toMap(groups);
  if (filters) {
    _.forEach(filters, (v, k) => {
      const group = groupsMap[k];
      let value: any = v;
      if (!group) {
        return;
      }

      // 全部按照多选来处理
      if (['cascader', 'single', 'radio', 'button'].includes(group.type)) {
        value = _.compact([value]);
      }

      switch (group.type) {
        case 'cascader':
        case 'cascader-multiple':
          {
            const text = parseCascaderValue(value, group.options);

            if (text) {
              labels.push({
                field: k,
                label: group.label,
                text,
              });
            }
          }
          break;
        case 'single':
        case 'multiple':
        case 'radio':
        case 'button':
        case 'button-multiple':
          {
            const text = parseSelectValue(value, group.options, _.get(group, 'custom.type'), _.get(group, 'custom.props'));

            if (text) {
              labels.push({
                field: k,
                label: group.label,
                text,
              });
            }
          }
          break;
        case 'checkbox':
          if (Array.isArray(v)) {
            const text = _.compact(v.map((x) => x.label)).join('/');
            if (text) {
              labels.push({
                field: k,
                label: group.label,
                text,
              });
            }
          }
          break;
        default:
          break;
      }
    });
  }

  return labels;
};

export default toLabel;
