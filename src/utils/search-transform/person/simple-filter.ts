import { Group } from '@/components/global/q-filter/interface';
import AREA_OPTIONS from '@/shared/constants/area.constant';
import { Path } from '@/utils/tree';
import NATIONAL_INDUSTRY_OPTIONS from '@/shared/constants/national-industry.constant';

export type SimpleFilterState = {
  keyword?: string;
  filters?: Partial<{
    areaInfo: Path[];
    industryInfo: Path[];
  }>;
};

export const filterGroups: Group[] = [
  {
    field: 'areaInfo',
    type: 'cascader',
    label: '省份地区',
    options: AREA_OPTIONS,
    layout: 'inline',
    meta: {
      leafOnly: false,
    },
  },
  {
    field: 'industryInfo',
    type: 'cascader',
    label: '国标行业',
    options: NATIONAL_INDUSTRY_OPTIONS,
    meta: {
      leafOnly: false,
    },
  },
];
