import _ from 'lodash';

import { Path } from '@/utils/tree';

import toQuery, { parseSingleArea, parseSingleIndustry } from '../to-query';
// import { toMap } from '../utils';
import { filterGroups } from './simple-filter';

// const filterGroupsMap = toMap(filterGroups);

const personToQuery = ({ keyword, filters }) => {
  const query = toQuery(filterGroups, filters) as any;

  if (keyword) {
    query.key = keyword;
    query.name = keyword;
  }
  if (_.isPlainObject(filters)) {
    _.forEach(filters, (v, k) => {
      switch (k) {
        case 'areaInfo':
          {
            const value = parseSingleArea(v as Path);
            query[k] = value;
          }
          break;
        case 'industryInfo':
          {
            const value = parseSingleIndustry(v as Path);
            query[k] = value;
          }
          break;

        default:
          break;
      }
    });
  }

  return query;
};

export default personToQuery;
