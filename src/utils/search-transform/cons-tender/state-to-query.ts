import * as _ from 'lodash';
import moment from 'moment';

import { AreaSource, AreaValue, SearchSelectSource } from '@/interfaces/data.interface';
import { numberRangeToSource } from '@/utils/parser';
import { Path } from '@/utils/tree';

import { parseArea, parseSearchSelect } from '../to-query';

interface DateRangeValue {
  number: number;
  unit: string;
  currently: boolean;
  flag: number;
  min?: string;
  max?: string;
  year?: number;
}

interface NumberRangeValue {
  min?: number;
  max?: number;
}

type TenderState = {
  keyword?: string;
  filters?: Partial<{
    wtbamtes: {
      value: string[];
      fuzzy: boolean;
    };
    publishdate: string[];
    'wtbkeynos,wtbunit': SearchSelectSource;
    projectleader: string;
    'conskeynos,consunit': SearchSelectSource;
    region: AreaSource[];
    ifbprogress: string[];
  }>;
};

type DateRangeSource = {
  flag: number;
  max?: string;
  min?: string;
  currently: boolean;
  number: number;
  unit: string;
};

type TenderQuery = {
  searchKeyList?: string[];
  filter?: Partial<{
    publishdate: DateRangeValue[];
    wtbamtes: NumberRangeValue;
    wtbamtesSearchType: 'fuzzy' | 'accurate';
    wtbkeynos: string;
    wtbunit: string;
    conskeynos: string;
    consunit: string;
    projectleader: string;
    ifbprogress: string[];
    region: AreaValue[];
  }>;
};

const dateRangeToSource = (
  value: Array<moment.Moment | undefined>,
  single = false // 是否支持只选单边值
): DateRangeSource | void => {
  if (!Array.isArray(value)) {
    return undefined;
  }

  const [min, max] = value;
  const vMin = moment.isMoment(max);
  const vMax = moment.isMoment(min);

  if (!single && (!vMin || !vMax)) {
    return undefined;
  }

  if (single && !vMin && !vMax) {
    return undefined;
  }

  const source = {
    currently: true,
    flag: 5,
    number: 1,
    unit: 'day',
    min: min ? min.startOf('day').toISOString() : '',
    max: max ? max.endOf('day').toISOString() : '',
  };

  return source;
};

const tenderToQuery = ({ keyword, filters }: TenderState): TenderQuery => {
  const query: TenderQuery = {};
  const filter: TenderQuery['filter'] = {};

  if (keyword) {
    query.searchKeyList = [keyword];
  }

  if (_.isPlainObject(filters)) {
    _.forEach(filters, (v, k) => {
      switch (k) {
        case 'publishdate':
          if (Array.isArray(v)) {
            const value = dateRangeToSource(
              (v as any).map((s) => (s ? moment(s) : undefined)),
              true
            );

            if (value) {
              filter.publishdate = [value];
            }
          }
          break;
        case 'wtbamtes':
          if (!_.isNil(v)) {
            const value = numberRangeToSource(
              (_.get(v, 'value', []) as unknown[]).map((s) => {
                if (!s) {
                  return undefined;
                }

                const n = _.toNumber(s);

                return _.isNaN(s) ? undefined : n * 10000;
              })
            );
            if (value) {
              filter.wtbamtes = value;
              filter.wtbamtesSearchType = _.get(v, 'fuzzy', false) ? 'fuzzy' : 'accurate';
            }
          }
          break;

        case 'wtbkeynos,wtbunit':
        case 'conskeynos,consunit':
          Object.assign(filter, parseSearchSelect(k, v as SearchSelectSource, false));
          break;
        case 'region':
          {
            const value = parseArea(v as Path[]);
            if (value?.length) {
              filter[k] = value;
            }
          }
          break;
        case 'projectleader':
        case 'ifbprogress':
          if (!_.isNil(v)) {
            filter[k] = v as any;
          }
          break;
        default:
          break;
      }
    });
  }

  if (!_.isEmpty(filter)) {
    query.filter = filter;
  }

  return query;
};

export default tenderToQuery;
