import * as _ from 'lodash';

import { ProjectPurposeTree } from '@/config/construction.config';
import { AreaSource, AreaValue, NumberRangeValue, SearchSelectSource } from '@/interfaces';
import { Path } from '@/utils/tree';

import { parseArea, parseSearchSelect } from '../to-query';

type AchievementState = {
  keyword?: string;
  filters?: Partial<{
    'compkeywords,compnames': SearchSelectSource;
    region: AreaSource[];
    purposescode: Path[];
    projecttype: string[];
    projectpurposes: string;
    constructnature: string[];
    itemlevel: string[];
    totalinvest: NumberRangeValue;
    // 竣工要求
    completionnum: boolean;
  }>;
};

type AchievementQuery = {
  searchKeyList?: string[];
  filter?: Partial<{
    // 相关单位
    compkeywords: string[];
    compnames: string[];
    // 项目地区
    region: AreaValue[];
    // 项目类别
    projecttype: string[];
    // 工程用途
    projectpurposes: string[];
    // 建设性质
    constructnature: string[];
    // 立项级别
    itemlevel: string[];
    // 总投资
    totalinvest: NumberRangeValue[];
    // 竣工要求
    completionnum: boolean;
    purposescode: string[];
  }>;
};

const achievementToQuery = ({ keyword, filters }: AchievementState): AchievementQuery => {
  const query: AchievementQuery = {};
  const filter: AchievementQuery['filter'] = {};

  if (keyword) {
    query.searchKeyList = [keyword];
  }

  if (_.isPlainObject(filters)) {
    _.forEach(filters, (v, k) => {
      switch (k) {
        case 'region':
          {
            const value = parseArea(v as Path[]);
            if (value?.length) {
              filter[k] = value;
            }
          }
          break;
        case 'purposescode':
          if (!_.isNil(v) && (v as string[]).length) {
            ProjectPurposeTree.setCheckedPaths(v as Path[]);
            filter[k] = ProjectPurposeTree.getMergedCheckedNodes().map((item) => item.value as string);
          }
          break;
        case 'constructkeyno,constructunit':
        case 'compkeywords,compnames':
          Object.assign(filter, parseSearchSelect(k, v as SearchSelectSource));
          break;
        case 'projecttype':
          if (v) {
            filter[k] = v as string[];
          }
          break;
        case 'constructnature':
          if (v) {
            filter[k] = v as string[];
          }
          break;
        case 'itemlevel':
          if (v) {
            filter[k] = v as string[];
          }
          break;
        case 'totalinvest':
          if (!_.isNil(v)) {
            filter[k] = [v as NumberRangeValue];
          }
          break;
        case 'completionnum':
          if (!_.isNil(v)) {
            filter[k] = v as boolean;
          }
          break;
        default:
          break;
      }
    });
  }

  if (!_.isEmpty(filter)) {
    query.filter = filter;
  }

  return query;
};

export default achievementToQuery;
