import _ from 'lodash';

import { Path } from '@/utils/tree';
import { tree as areaTree, municipalities } from '@/shared/constants/area.constant';
import { tree as nationalTree } from '@/shared/constants/national-industry.constant';
import { AreaValue, SearchSelectSource } from '@/interfaces/data.interface';
import { Group } from '@/components/global/q-filter/interface';

import { toMap } from './utils';

/**
 * 递归获取父级总深度
 */
function getParentDepth(node) {
  let depth = 0;
  if (node.parent) {
    depth = getParentDepth(node.parent) + 1;
  }
  return depth;
}

// path [1,2,3] & fields ['a','b','c'] => {a:1,b:2,c:3}
export const mapTreePath = (path: Path, fields: string[], parser?) =>
  path.reduce<Record<string, string | number>>((map, v, i) => {
    map[fields[i]] = parser ? parser(v) : v;
    return map;
  }, {});

export const parseArea = (paths?: Path[]): AreaValue[] | undefined => {
  if (!paths) {
    return undefined;
  }
  const munFields = ['pr', 'dt'];
  const fields = ['pr', 'ct', 'dt'];
  areaTree.setCheckedPaths(paths);
  return areaTree.getMergedCheckedNodes().map((node) => {
    const path = node.path.slice();
    const municipalityCode = municipalities.includes(path[0] as string);
    // 直辖市不要 city
    if (municipalityCode) {
      return mapTreePath(path, munFields);
    }
    return mapTreePath(path, fields);
  });
};

/**
 * 由 `dt` 转换为 `cc`
 * @param paths
 */
export const parseQCCArea = (paths?: Path[]): AreaValue[] | undefined => {
  if (!paths) {
    return undefined;
  }
  const munFields = ['pr', 'cc'];
  const fields = ['pr', 'cc', 'cc']; // [JS, 400300, 400300] => [JS, 400300]
  areaTree.setCheckedPaths(paths);
  return areaTree.getMergedCheckedNodes().map((node) => {
    const path = node.path.slice();
    const municipalityCode = municipalities.includes(path[0] as string);
    // 直辖市不要 city
    if (municipalityCode) {
      return mapTreePath(path, munFields);
    }
    return mapTreePath(path, fields);
  });
};

/*
  "C_31": "黑色金属冶炼和压延加工业 ",
  "C_31_311_3110": "炼铁",
  "C_31_312_3120": "炼钢",
  "C_31_313_3130": "钢压延加工",
  "C_31_314_3140": "铁合金冶炼",

  这种格式的数据会直接被加工成

  "C_31": "黑色金属冶炼和压延加工业 ",
  "C_31_3110": "炼铁",
  "C_31_3120": "炼钢",
  "C_31_3130": "钢压延加工",
  "C_31_3140": "铁合金冶炼",

  由于是直接调用的Tree的方法，所以得手动去处理

*/

export const reformPath = (path: Path) => {
  const length = path.length;
  if (length > 2) {
    const isGap = (path[length - 1] as string).length - (path[length - 2] as string).length === 2;
    if (isGap) {
      path.splice(2, 0, (path[2] as any).slice(0, -1));
    }
  }
  return path;
};

export const parseNational = (paths?: Path[]): AreaValue[] | undefined => {
  if (!paths) {
    return undefined;
  }
  const fields = ['i1', 'i2', 'i3', 'i4'];
  nationalTree.setCheckedPaths(paths);
  return nationalTree.getMergedCheckedNodes().map((node) => {
    // 解决如果path的nodekey变化的问题
    const path = node.path.slice();
    return mapTreePath(reformPath(path), fields);
  });
};

export const parseQCCNational = (paths?: Path[]): any[] | undefined => {
  if (!paths) {
    return undefined;
  }
  nationalTree.setCheckedPaths(paths);
  const result = nationalTree.getMergedCheckedNodes().map((node) => {
    // 解决如果path的nodekey变化的问题
    const path = node.path.slice();

    const reformedPath = reformPath(path);
    // 从 path 中移除父级
    const parentDepth = getParentDepth(node);
    return reformedPath.slice(parentDepth);
  });
  return _.uniq(_.flatten(result));
};

export const parseSingleArea = (path?: Path) => {
  if (!path) {
    return undefined;
  }
  areaTree.setCheckedPaths([path]);

  const { data } = areaTree.getMergedCheckedNodes()[0];
  const { label } = data;

  return label;
};

export const parseSingleIndustry = (path?: Path) => {
  if (!path) {
    return undefined;
  }
  nationalTree.setCheckedPaths([path]);

  const { data } = nationalTree.getMergedCheckedNodes()[0];
  const { label } = data;

  return label;
};

export const parseSearchSelect = (
  field: string,
  value: SearchSelectSource | undefined,
  array = true
): Record<string, string | string[]> | undefined => {
  const keyword = _.trim(_.get(value, 'value') || '');
  if (!value || !keyword) {
    return undefined;
  }

  const [select, input] = field.split(',');
  let key;

  if (value.type === 'input') {
    if (keyword.length < 2) {
      return undefined;
    }
    key = input;
  } else if (value.type === 'select') {
    key = select;
  }

  if (!key) {
    return undefined;
  }

  return {
    [key]: array ? [keyword] : keyword,
  };
};

const toQuery = (groups: Group[], filters, excludes: string[] = []) => {
  const query: Record<string, any> = {};
  const groupsMap = toMap(groups);

  if (filters) {
    _.forEach(filters, (v, k) => {
      if (excludes.includes(k)) {
        return;
      }
      const group = groupsMap[k];
      let value: any = v;
      if (!group) {
        return;
      }

      switch (group.transform) {
        case 'array':
          if (!_.isNil(v)) {
            value = [v];
          }
          break;
        case 'flatten':
          if (Array.isArray(v)) {
            value = _.uniq(_.flatten(v));
          }
          break;
        case 'split':
          if (Array.isArray(v)) {
            value = _.uniq(_.flatten(v.map((sv) => _.compact(sv.split(',')))));
          } else if (_.isString(v)) {
            value = _.compact(v.split(','));
          }
          break;
        default:
          break;
      }

      if (Array.isArray(value)) {
        if (value.length) {
          query[k] = value;
        }
      } else if (!_.isNil(value)) {
        query[k] = value;
      }
    });
  }

  return query;
};

export default toQuery;
