import { numberFormat, numberToHuman, numberToHumanWithUnit } from '..';

describe('numberFormatter', () => {
  describe('numberToHuman', () => {
    test('should handle undefined input', () => {
      expect(numberToHuman(undefined)).toBe('');
    });

    test('should handle precision option', () => {
      expect(numberToHuman(1234567890.123, { precision: 2 })).toBe('1,234,567,890.12');
    });

    test('should handle separator option', () => {
      expect(numberToHuman(1234567890, { separator: '.' })).toBe('1.234.567.890');
    });

    test('should handle default options', () => {
      expect(numberToHuman(1234567890)).toBe('1,234,567,890');
    });
  });

  describe('numberFormat', () => {
    test('should return default value when input string is empty and division is not -3', () => {
      expect(numberFormat('', 2, 1)).toBe('-');
    });

    test('should parse and format number with division -1', () => {
      expect(numberFormat('1234567890', 2, -1)).toBe('1.23G');
      expect(numberFormat('1000001', 2, -1)).toBe('1.00M');
      expect(numberFormat('1001', 2, -1)).toBe('1.00K');
    });

    test('should parse and format number with division -2', () => {
      expect(numberFormat('1234', 2, -2)).toBe('1,234.00元');
      expect(numberFormat('12345', 2, -2)).toBe('1.23万元');
    });

    test('should parse and format number with division -3', () => {
      expect(numberFormat('12345', 2, -3)).toBe('<b class="font-16">1.23</b> <span class="text-gray">万元</span>');
    });

    test('should parse and format number with custom division', () => {
      expect(numberFormat('12345', 2, 1000)).toBe('12.35');
    });
  });

  describe('numberToHumanWithUnit', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('should return "-" if the value is null', () => {
      expect(numberToHumanWithUnit(null)).toBe('-');
      expect(numberToHumanWithUnit(undefined)).toBe('-');
    });

    it('should return the original value if it does not start with a digit', () => {
      expect(numberToHumanWithUnit('abc123')).toBe('abc123');
      expect(numberToHumanWithUnit('-')).toBe('-');
    });

    it('should format the number part and keep the unit', () => {
      expect(numberToHumanWithUnit('1234kg')).toBe('1,234kg');
      expect(numberToHumanWithUnit('1234567g')).toBe('1,234,567g');
      expect(numberToHumanWithUnit('1234.56ml')).toBe('1,234.56ml');
      expect(numberToHumanWithUnit('1234.1234ml1234')).toBe('1,234.1234ml1234');
    });
  });
});
