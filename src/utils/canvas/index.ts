export const PIXEL_RATIO = (() => {
  if (typeof window !== 'undefined') {
    const ctx = document.createElement('canvas').getContext('2d') as any;
    const dpr = window.devicePixelRatio || 1;
    const bsr =
      ctx.webkitBackingStorePixelRatio ||
      ctx.mozBackingStorePixelRatio ||
      ctx.msBackingStorePixelRatio ||
      ctx.oBackingStorePixelRatio ||
      ctx.backingStorePixelRatio ||
      1;
    return dpr / bsr;
  }

  return 1;
})();

const link = typeof window !== 'undefined' ? document.createElement('a') : null;

// 用贝塞尔曲线画带圆角的矩形
export const drawRoundRect = (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius = 2) => {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
};

export const exportImage = (canvas: HTMLCanvasElement, filename: string) => {
  if ('msToBlob' in canvas) {
    // IE
    const blob = (canvas as any).msToBlob();
    (window.navigator as any).msSaveBlob(blob, filename);
  } else if (link) {
    link.href = canvas.toDataURL();
    link.download = filename;
    link.click();
  }
};
