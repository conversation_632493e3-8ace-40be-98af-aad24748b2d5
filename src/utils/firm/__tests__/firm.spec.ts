import {
  formatDetail,
  getActingList,
  getCompanyDesc,
  getLogoByKeyNo,
  getOperTypeLabelMapper,
  getSameList,
  setActingList,
  setCompanyInfo,
  setGraphInfo,
  setSameList,
  setStockInfo,
  sumPartialObject,
} from '..';

describe('Firm', () => {
  describe('formatDetail', () => {
    test('should add gwlink if gw is present and not starting with http', () => {
      const company: any = {
        info: {
          gw: 'www.example.com',
          gwlink: undefined,
        },
      };
      formatDetail(company);
      expect(company.info.gwlink).toBe('http://www.example.com');
    });

    test('should not add gwlink if gw is not present', () => {
      const company = {
        info: {},
      };
      formatDetail(company);
      expect(company.info).not.toHaveProperty('gwlink');
    });

    test('should add bond property if tag type is 17', () => {
      const company: any = {
        Tags: [
          {
            Type: 17,
            DataExtend: '{"bondData":"example"}',
          },
        ],
        info: {},
      };
      formatDetail(company);
      expect(company.bond).toEqual({ bondData: 'example' });
    });

    test('should not add bond property if tag type is not 17', () => {
      const company = {
        Tags: [
          {
            Type: 18,
            DataExtend: '{"bondData":"example"}',
          },
        ],
        info: {},
      };
      formatDetail(company);
      expect(company).not.toHaveProperty('bond');
    });

    test('should add aStock property if tag type is 2', () => {
      const company: any = {
        TagsInfo: [
          {
            Type: 2,
            Name: 'A股',
            Value: '{"symbol":"000001"}',
          },
        ],
        info: {},
      };
      formatDetail(company);
      expect(company.aStock).toEqual({ Type: 2, Name: 'A股', Value: '{"symbol":"000001"}' });
    });

    test('should not add aStock property if tag type is not 2', () => {
      const company = {
        TagsInfo: [
          {
            Type: 3,
            Name: 'B股',
            Value: '{"symbol":"B000001"}',
          },
        ],
        info: {},
      };
      formatDetail(company);
      expect(company).not.toHaveProperty('aStock');
    });

    test('should add pos property if CommonList item key is 24', () => {
      const company: any = {
        CommonList: [
          {
            Key: 24,
            Value: '{"latitude":31.230416,"longitude":121.473701}',
          },
        ],
        info: {},
      };
      formatDetail(company);
      expect(company.pos).toEqual({ latitude: 31.230416, longitude: 121.473701 });
    });

    test('should not add pos property if CommonList item key is not 24', () => {
      const company = {
        CommonList: [
          {
            Key: 25,
            Value: '{"address":"Shanghai"}',
          },
        ],
        info: {},
      };
      formatDetail(company);
      expect(company).not.toHaveProperty('pos');
    });
  });

  describe('setStockInfo', () => {
    test('should set stocks and stock info for company with TagsInfo', () => {
      const company: any = {
        TagsInfo: [
          { Name: '港股', ShortName: 'HK', Type: 6, DataExtend: '001.HK' },
          { Name: '科创板', ShortName: 'GS', Type: 2 },
        ],
        Tags: [],
      };
      setStockInfo(company);
      expect(company.stocks).toEqual([
        { type: '港股', name: 'HK', tagType: 6, code: '001', area: 'HK' },
        { type: 'A股', name: 'GS', tagType: 2 },
      ]);
      expect(company.stock).toEqual({ type: '港股', name: 'HK', tagType: 6, code: '001', area: 'HK' });
    });

    test('should set stock info for company with Tags but no TagsInfo', () => {
      const company: any = {
        TagsInfo: [
          {
            Type: 20,
            Name: 'A股',
            Value: '{"symbol":"000001"}',
          },
        ],
        Tags: [{ Name: '上会', Type: 14 }],
      };
      setStockInfo(company);
      expect(company.stock).toEqual({ type: '上会' });
    });

    test('should not set stocks and stock info for company without TagsInfo and Tags', () => {
      const company = {
        TagsInfo: [],
        Tags: [],
      };
      const initialCompany = { ...company };
      setStockInfo(company);
      expect(company).toEqual(initialCompany);
    });
  });

  describe('sumPartialObject', () => {
    test('should return 0 if countInfo or fields is null or empty', () => {
      expect(sumPartialObject(null, [])).toBe(0);
      expect(sumPartialObject({}, [])).toBe(0);
      expect(sumPartialObject({}, null)).toBe(0);
      expect(sumPartialObject(null, null)).toBe(0);
    });

    test('should return the sum of specified fields in countInfo', () => {
      const countInfo = {
        field1: '10',
        field2: '20',
        field3: '30',
      };
      const fields = ['field1', 'field2'];

      expect(sumPartialObject(countInfo, fields)).toBe(30);
    });

    test('should handle fields that are not present in countInfo', () => {
      const countInfo = {
        field1: '10',
        field2: '20',
      };
      const fields = ['field1', 'field2', 'field3'];

      expect(sumPartialObject(countInfo, fields)).toBe(30);
    });

    test('should handle countInfo values that are not numbers', () => {
      const countInfo = {
        field1: '10',
        field2: '20',
        field3: 'invalid',
      };
      const fields = ['field1', 'field2', 'field3'];

      expect(sumPartialObject(countInfo, fields)).toBe(30);
    });
  });

  describe('setGraphInfo', () => {
    test('should set graphInfo correctly', () => {
      const company = {
        CommonList: [{ Key: '10' }, { Key: '20' }],
        Partners: [{ StockPercent: 0.5 }],
        IpoPartners: [{ StockPercent: 0.6 }],
        type: 'normalcom',
        CountInfo: {
          RiskGraphCount: 1,
          InvesterCount: 2,
          AllBenefiterCount: 3,
          ACCount: 4,
          SACCount: 5,
          HCCount: 6,
          GuarantorCount: 7,
          BranchCount: 8,
          SupplierCountV2: 9,
          CustomerCountV2: 10,
        },
        hisCountInfo: {
          InvestCount: 11,
          OperCount: 12,
          Partner2Count: 13,
        },
        IpoOpers: [{ KeyNo: '1' }],
        DJInfo: {
          Oper: { KeyNo: '2' },
        },
        IpoEmployees: [{}, {}],
        Employees: [{}],
        graphInfo: undefined,
      };
      setGraphInfo(company);
      expect(company.graphInfo).toEqual({
        beneficiaryOrg: true,
        beneficiaryPerson: true,
        disable: true,
        disableKzrtupu: true,
        equityChart: true,
        glfgraph: true,
        guqanview: true,
        uinfo: '[1,1,1,1,1,1,1,1,1]',
        muhou: true,
        newgraph: true,
        kzr: true,
        riskChart: true,
      });
    });

    test('should set graphInfo correctly when company type is us', () => {
      const company = {
        type: 'us',
        graphInfo: undefined,
      };
      setGraphInfo(company);
      expect(company.graphInfo).toEqual({
        equityChart: false,
        glfgraph: false,
        guqanview: false,
        kzr: false,
        beneficiaryOrg: false,
        beneficiaryPerson: false,
        disable: true,
        muhou: false,
        newgraph: false,
        riskChart: false,
        uinfo: '[0,0,0,0,0,0,0,0,0]',
        disableKzrtupu: true,
      });
    });
  });

  describe('setSameList', () => {
    test('should return an empty object when SameTelAddressList is empty', () => {
      const company = {
        SameTelAddressList: [],
        info: {
          phone: '*********',
          address: '123 Street',
        },
      };

      const result = setSameList(company);
      expect(result).toEqual({});
    });

    test('should return an object with SameTelAddressList items', () => {
      const company = {
        SameTelAddressList: [
          { A: 'phone', K: 'phone_key', C: 50 },
          { A: 'address', K: 'address_key', C: 100 },
        ],
        info: {
          phone: '*********',
          address: '123 Street',
        },
      };

      const result = setSameList(company);
      expect(result).toEqual({
        phone: {
          key: 'phone_key',
          value: 'phone',
          count: '50',
        },
        address: {
          key: 'address_key',
          value: 'address',
          count: '99+',
        },
      });
    });

    test('should return an object with count as "99+" when C is greater than 99', () => {
      const company = {
        SameTelAddressList: [{ A: 'phone', K: 'phone_key', C: 100 }],
        info: {
          phone: '*********',
          address: '123 Street',
        },
      };

      const result = setSameList(company);
      expect(result).toEqual({
        phone: {
          key: 'phone_key',
          value: 'phone',
          count: '99+',
        },
      });
    });

    test('should set phone and address properties when they exist in sameList', () => {
      const company = {
        SameTelAddressList: [
          { A: 'phone', K: 'phone_key', C: 50 },
          { A: 'address', K: 'address_key', C: 100 },
        ],
        info: {
          phone: '*********',
          address: '123 Street',
        },
      };

      const result = setSameList(company);
      expect(result).toHaveProperty('phone');
      expect(result).toHaveProperty('address');
      expect(result.phone).toEqual({
        key: 'phone_key',
        value: 'phone',
        count: '50',
      });
      expect(result.address).toEqual({
        key: 'address_key',
        value: 'address',
        count: '99+',
      });
    });
  });

  describe('setActingList', () => {
    test('should return an empty object if CommonList is empty', () => {
      const company = { CommonList: [] };
      const result = setActingList(company);
      expect(result).toEqual({});
    });

    test('should return an empty object if CommonList does not contain an item with Key 40', () => {
      const company = {
        CommonList: [
          { Key: '10', Value: JSON.stringify([{ k: 'user1' }]) },
          { Key: '20', Value: JSON.stringify([{ k: 'user2' }]) },
        ],
      };
      const result = setActingList(company);
      expect(result).toEqual({});
    });

    test('should return an object with keys set to true if CommonList contains an item with Key 40', () => {
      const company = {
        CommonList: [{ Key: '40', Value: JSON.stringify([{ k: 'user1' }, { k: 'user2' }]) }],
      };
      const result = setActingList(company);
      expect(result).toEqual({ user1: true, user2: true });
    });

    test('should handle multiple items with Key 40 in CommonList', () => {
      const company = {
        CommonList: [
          { Key: '40', Value: JSON.stringify([{ k: 'user1' }, { k: 'user2' }]) },
          { Key: '40', Value: JSON.stringify([{ k: 'user3' }, { k: 'user4' }]) },
        ],
      };
      const result = setActingList(company);
      expect(result).toEqual({ user1: true, user2: true, user3: true, user4: true });
    });
  });

  describe('setCompanyInfo', () => {
    it('should set company info for normal company', () => {
      const company = {
        KeyNo: '123456',
        Name: 'ABC Company',
        ImageUrl: 'https://example.com/logo.png',
        ShortStatus: 'Active',
        ContactInfo: {
          PhoneNumber: '************',
          Email: '<EMAIL>',
          WebSite: [{ Url: 'https://abccompany.com' }],
        },
        Address: '123 Main St',
        info: undefined,
      };

      setCompanyInfo(company);

      expect(company.info).toEqual({
        keyNo: '123456',
        name: 'ABC Company',
        logo: 'https://example.com/logo.png',
        status: 'Active',
        phone: '************',
        email: '<EMAIL>',
        gw: 'https://abccompany.com',
        address: '123 Main St',
        gwlink: 'https://abccompany.com',
      });
    });

    // Add more tests for different company types (e.g. 'so', 'hk', 'tw', 'oversea', 'us')
  });

  describe('getLogoByKeyNo', () => {
    test('should return empty string when keyNo is empty', () => {
      expect(getLogoByKeyNo()).toBe('');
    });

    test('should return empty string when keyNo is undefined', () => {
      expect(getLogoByKeyNo(undefined)).toBe('');
    });

    test('should return empty string when keyNo is null', () => {
      expect(getLogoByKeyNo(null)).toBe('');
    });

    test('should return url for person with image when keyNo starts with "p" and hasImage is true', () => {
      const keyNo = 'p123456';
      const expectedUrl = '//image.qcc.com/person/p123456.jpg?x-oss-process=style/logo_200';
      expect(getLogoByKeyNo(keyNo, true)).toBe(expectedUrl);
    });

    test('should return url for non-person with image when keyNo does not start with "p" and hasImage is true', () => {
      const keyNo = '123456';
      const expectedUrl = '//image.qcc.com/logo/123456.jpg?x-oss-process=style/logo_200';
      expect(getLogoByKeyNo(keyNo, true)).toBe(expectedUrl);
    });

    test('should return url for non-person without image when keyNo does not start with "p" and hasImage is false', () => {
      const keyNo = '123456';
      const expectedUrl = '//image.qcc.com/auto/123456.jpg?x-oss-process=style/logo_200';
      expect(getLogoByKeyNo(keyNo, false)).toBe(expectedUrl);
    });

    test('should return url for non-person with image when keyNo does not start with "p" and keyNo[0] is not "l" or "z"', () => {
      const keyNo = 'xyz123';
      const expectedUrl = '//image.qcc.com/logo/xyz123.jpg?x-oss-process=style/logo_200';
      expect(getLogoByKeyNo(keyNo, true)).toBe(expectedUrl);
    });

    test('should return url for non-person with image when keyNo starts with "g" and hasImage is true', () => {
      const keyNo = 'g123456';
      const expectedUrl = '//co-image.qichacha.com/CompanyImage/g123456.jpg';
      expect(getLogoByKeyNo(keyNo, true)).toBe(expectedUrl);
    });

    test('should append query parameter to url for non-person without image when keyNo starts with "g" and hasImage is false', () => {
      const keyNo = 'g123456';
      const expectedUrl = '//image.qcc.com/auto/g123456.jpg?x-oss-process=style/logo_200';
      expect(getLogoByKeyNo(keyNo, false)).toBe(expectedUrl);
    });
  });

  describe('getCompanyDesc', () => {
    it('should return the correct description', () => {
      const company = {
        StartDate: '2020-01-01',
        Oper: { Name: 'John Doe' },
        RegistCapi: '1000000',
        CreditCode: '*********012345678',
        Address: '123 Main St',
        Industry: { SubIndustry: 'Technology' },
        Scope: 'Software development and consulting.',
      };
      const companyInfo = {
        name: 'ACME Inc.',
      };
      const result = getCompanyDesc({ company, companyInfo });
      expect(result).toEqual(
        `ACME Inc.成立于2020-01-01，法定代表人为John Doe，注册资本为1000000，统一社会信用代码为*********012345678，企业地址位于123 Main St，所属行业为Technology，经营范围包含：Software development and consulting.。`
      );
    });

    // Add more test cases here
  });

  describe('getSameList', () => {
    test('should return an empty object when SameTelAddressList is empty', () => {
      const company = {
        SameTelAddressList: [],
      };

      const result = getSameList(company);
      expect(result).toEqual({});
    });

    test('should return an object with SameTelAddressList items', () => {
      const company = {
        SameTelAddressList: [
          { A: 'phone', K: '123', C: 50 },
          { A: 'address', K: '456', C: 10 },
        ],
      };

      const result = getSameList(company);
      expect(result).toEqual({
        phone: {
          key: '123',
          value: 'phone',
          count: '50',
        },
        address: {
          key: '456',
          value: 'address',
          count: '10',
        },
      });
    });

    test('should set the phone key if company.info.phone exists', () => {
      const company = {
        SameTelAddressList: [{ A: 'phone', K: '123', C: 50 }],
        info: {
          phone: '123',
        },
      };

      const result = getSameList(company);
      expect(result).toHaveProperty('phone');
      expect(result.phone).toEqual({
        key: '123',
        value: 'phone',
        count: '50',
      });
    });

    test('should set the phone key if company.ContactInfo.PhoneNumber exists', () => {
      const company = {
        SameTelAddressList: [{ A: 'phone', K: '123', C: 50 }],
        info: {
          phone: 'phone',
        },
      };

      const result = getSameList(company);
      expect(result).toHaveProperty('phone');
      expect(result.phone).toEqual({
        key: '123',
        value: 'phone',
        count: '50',
      });

      const company2 = {
        SameTelAddressList: [{ A: 'phone', K: '123', C: 50 }],
        ContactInfo: {
          PhoneNumber: 'phone',
        },
      };
      expect(getSameList(company2).phone).toEqual({
        key: '123',
        value: 'phone',
        count: '50',
      });
    });

    test('should set the address key if company.info.address exists', () => {
      const company = {
        SameTelAddressList: [{ A: 'address', K: '456', C: 10 }],
        info: {
          address: '456',
        },
      };

      const result = getSameList(company);
      expect(result).toHaveProperty('address');
      expect(result.address).toEqual({
        key: '456',
        value: 'address',
        count: '10',
      });
    });

    test('should handle item.C greater than 99', () => {
      const company = {
        SameTelAddressList: [{ A: 'phone', K: '123', C: 100 }],
      };

      const result = getSameList(company);
      expect(result.phone.count).toBe('99+');
    });
  });

  describe('getOperTypeLabelMapper', () => {
    test('should return "法定代表人" for input "1"', () => {
      const result = getOperTypeLabelMapper('1');
      expect(result).toBe('法定代表人');
    });

    test('should return "执行事务合伙人" for input "2"', () => {
      const result = getOperTypeLabelMapper('2');
      expect(result).toBe('执行事务合伙人');
    });

    test('should return "负责人" for input "3"', () => {
      const result = getOperTypeLabelMapper('3');
      expect(result).toBe('负责人');
    });

    test('should return "经营者" for input "4"', () => {
      const result = getOperTypeLabelMapper('4');
      expect(result).toBe('经营者');
    });

    test('should return "投资人" for input "5"', () => {
      const result = getOperTypeLabelMapper('5');
      expect(result).toBe('投资人');
    });

    test('should return "董事长" for input "6"', () => {
      const result = getOperTypeLabelMapper('6');
      expect(result).toBe('董事长');
    });

    test('should return "理事长" for input "7"', () => {
      const result = getOperTypeLabelMapper('7');
      expect(result).toBe('理事长');
    });

    test('should return "法定代表人" for invalid input', () => {
      const result = getOperTypeLabelMapper('invalid');
      expect(result).toBe('法定代表人');
    });
  });

  describe('getActingList', () => {
    test('should return an empty object when company is undefined', () => {
      const result = getActingList(undefined);
      expect(result).toEqual({});
    });

    test('should return an empty object when CommonList is undefined', () => {
      const company = {};
      const result = getActingList(company);
      expect(result).toEqual({});
    });

    test('should return an empty object when CommonList is empty', () => {
      const company = { CommonList: [] };
      const result = getActingList(company);
      expect(result).toEqual({});
    });

    test('should return the actingList when CommonList has item with Key 40', () => {
      const company = {
        CommonList: [
          { Key: '40', Value: JSON.stringify([{ k: 'actor1' }, { k: 'actor2' }]) },
          { Key: '41', Value: JSON.stringify([{ k: 'actor3' }]) },
        ],
      };
      const result = getActingList(company);
      expect(result).toEqual({ actor1: true, actor2: true });
    });

    test('should return the actingList when CommonList has multiple items with Key 40', () => {
      const company = {
        CommonList: [
          { Key: '40', Value: JSON.stringify([{ k: 'actor1' }, { k: 'actor2' }]) },
          { Key: '40', Value: JSON.stringify([{ k: 'actor3' }, { k: 'actor4' }]) },
        ],
      };
      const result = getActingList(company);
      expect(result).toEqual({ actor1: true, actor2: true, actor3: true, actor4: true });
    });
  });
});
