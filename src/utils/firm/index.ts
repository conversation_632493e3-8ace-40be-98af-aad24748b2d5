/* eslint-disable no-param-reassign */
/* eslint-disable prettier/prettier */
import _ from 'lodash';

/**
 * FIXME: 消除副作用
 */
export const formatDetail = (company: any) => {
  if (company.info.gw) {
    company.info.gwlink = _.startsWith(company.info.gw, 'http')
      ? company.info.gw
      : `http://${company.info.gw}`;
  }
  if (company.Tags?.length) {
    company.Tags.forEach((tag) => {
      // 处理债券
      if (tag.Type === 17) {
        company.bond = JSON.parse(tag.DataExtend || '{}');
      }
    });
  }
  if (company.TagsInfo?.length) {
    company.TagsInfo.forEach((tag) => {
      // A股
      if (tag.Type === 2) {
        company.aStock = { ...tag };
      }
    });
  }
  if (company.CommonList?.length) {
    company.CommonList.forEach((vo) => {
      // 经纬度
      if (vo.Key === 24) {
        company.pos = JSON.parse(vo.Value);
      }
    });
  }
};

/**
 * FIXME: 消除副作用
 */
export const setStockInfo = (company: any, mtlist?: any) => {
  if (company.TagsInfo?.length) {
    let stocks: Array<any> = [];
    company.TagsInfo.forEach((tag) => {
      // 上市信息处理 港股：6, 401 科创板：501，502，503 创业板：601，602，603
      if ([1, 8, 2, 9, 13, 6, 401, 501, 502, 503, 601, 602, 603].includes(tag.Type)) {
        const extendArr = tag.DataExtend?.split('.');
        const stock: any = {
          type: tag.Name,
          name: tag.ShortName,
          tagType: tag.Type,
        };
        if (extendArr?.[0]) {
          stock.code = extendArr[0];
        }
        if (extendArr?.[1]) {
          stock.area = extendArr[1];
        }
        if (tag.Type === 8 || tag.Type === 9) {
          stock.status = '已退市';
        }
        if (tag.Type === 13) {
          stock.status = '暂停上市';
        }
        if (tag.Name === '科创板' && tag.Type === 2) {
          stock.type = 'A股';
        }
        stocks.push(stock);
      }
    });
    if (company.Tags?.length) {
      company.Tags.forEach((tag) => {
        // 上会
        if (tag.Type === 14 && stocks.filter((s) => s.tagType !== 8).length === 0) {
          const stock = {
            type: tag.Name,
          };
          stocks.push(stock);
        }
      });
    }
    if (stocks.length > 1) {
      company.stocks = stocks;
      stocks = stocks.filter((s) => s.status !== '已退市');
      company.stock = stocks[0];
      if (stocks.length > 1) {
        company.stock = mtlist === 'h' ? stocks[1] : stocks[0];
      }
    } else if (stocks.length === 1) {
      company.stock = stocks[0];
    }
  }
};

export const sumPartialObject = (countInfo, fields) => {
  let sum = 0;
  if (countInfo && fields?.length) {
    fields.forEach((field) => {
      const n = Number(countInfo?.[field] ?? 0);
      sum += _.isNaN(n) ? 0 : n;
    });
  }
  return sum;
};

export const setGraphInfo = (company: any) => {
  // 是否为分公司
  let isBranch = false;
  const commonList = company?.CommonList || [];
  commonList.forEach((val) => {
    if (+val.Key === 10) {
      isBranch = true;
    }
  });
  const hasPartners = company.Partners?.length > 0;
  const hasIpoPartners = company.IpoPartners?.length > 0;
  const hasBeneficiary = company.Partners?.[0]?.StockPercent || hasIpoPartners;

  const hasRiskChart =
    ['normalcom', 'individual'].includes(company.type) && company.CountInfo?.RiskGraphCount;
  // 法定代表人
  const operNum = company.Oper?.KeyNo || company.IpoOpers?.length || company?.DJInfo?.Oper?.KeyNo ? 1 : 0;
  // 股东
  const parntersNum = company?.IpoPartners?.length || company.Partners ? company.Partners.length : 0;

  // 主要人员
  const employeesNum = company?.IpoEmployees?.length  || company?.Employees?.length || 0;

  if (company.CountInfo) {
    company.CountInfo.operNum = operNum;
    company.CountInfo.parntersNum = parntersNum;
    company.CountInfo.employeesNum = employeesNum;
  }

  // 企业图谱
  // eslint-disable-next-line max-len
  let corpChartNum = sumPartialObject(company.CountInfo, [
    'operNum',
    'parntersNum',
    'employeesNum',
    'InvesterCount',
    'AllBenefiterCount',
    'ACCount',
    'SACCount',
    'HCCount',
    'GuarantorCount',
    'BranchCount',
    'SupplierCountV2',
    'CustomerCountV2',
  ]);
  corpChartNum += sumPartialObject(company.hisCountInfo, ['InvestCount', 'OperCount', 'Partner2Count']);
  // 关联方认定图
  const relatedChartNum = sumPartialObject(company.CountInfo, ['parntersNum', 'employeesNum']);
  // 关系图谱
  const relaChartNum = sumPartialObject(company.CountInfo, ['operNum', 'employeesNum', 'InvesterCount']);
  // 实际控制人
  const controlNum = company.CountInfo?.ACCount === 0 || company.CountInfo?.parntersNum === 0 ? 0 : 1;
  // eslint-disable-next-line max-len
  const controlNum1 =
    company.CountInfo?.ACCount + company.CountInfo?.SACCount === 0 || company.CountInfo?.parntersNum === 0
      ? 0
      : 1;
  // 美股企业
  const isUsEnt = company.type === 'us';
  const graphArr = [
    {
      id: 'muhou',
      name: '关系图谱',
      disable: ['hk', 'individual', 'us'].includes(company.type) || relaChartNum === 0,
    },
    {
      id: 'newgraph',
      name: '企业图谱',
      disable: ['hk', 'individual', 'us'].includes(company.type) || corpChartNum === 0,
    },
    {
      id: 'equityChart',
      name: '股权穿透图',
      disable: !hasPartners || isUsEnt,
    },
    {
      id: 'guqanview',
      name: '股权结构图',
      disable: !hasPartners || isUsEnt,
    },
    {
      id: 'beneficiaryPerson',
      name: '企业受益股东',
      disable: !hasBeneficiary || isUsEnt,
    },
    {
      id: 'beneficiaryOrg',
      name: '企业股权分布',
      disable: !hasBeneficiary || isUsEnt,
    },
    {
      // 没有股东且没有主要人员，非大陆企业
      id: 'glfgraph',
      name: '关联方认定图',
      disable: !['normalcom'].includes(company.type) || relatedChartNum === 0,
    },
    {
      id: 'kzr',
      name: '实际控制人',
      disable: controlNum === 0 || isUsEnt,
    },
    {
      id: 'riskChart',
      name: '风险图谱',
      disable: !hasRiskChart || isUsEnt,
    },
  ];

  company.graphInfo = {};
  const uinfo: Array<number> = [];
  graphArr.forEach((item) => {
    company.graphInfo[item.id] = !item.disable;
    uinfo.push(item.disable ? 0 : 1);
  });
  company.graphInfo.uinfo = JSON.stringify(uinfo);
  company.graphInfo.disable = isBranch || !JSON.stringify(uinfo).includes('1');
  company.graphInfo.disableKzrtupu = controlNum1 === 0 || isBranch || isUsEnt;
};

export const setSameList = (company) => {
  const sameList: any = {};
  if (company.SameTelAddressList?.length) {
    company.SameTelAddressList.forEach((item: any) => {
      sameList[item.A] = {
        key: item.K,
        value: item.A,
        count: item.C > 99 ? '99+' : `${item.C}`,
      };
    });
    if (sameList[company.info?.phone]) {
      sameList.phone = sameList[company.info?.phone];
    }
    if (sameList[company.info?.address]) {
      sameList.address = sameList[company.info?.address];
    }
  }
  return sameList;
};

// 疑似代账列表
export const setActingList = (company) => {
  const actingList = {};
  if (company.CommonList && company.CommonList.length > 0) {
    company.CommonList.forEach((item) => {
      if (`${item.Key}` === '40') {
        const value = JSON.parse(item.Value);
        value.forEach((val) => {
          actingList[val.k] = true;
        });
      }
    });
  }
  return actingList;
};

export const setCompanyInfo = (company) => {
  // eslint-disable-next-line no-underscore-dangle
  const keyNo = company.KeyNo;
  const firstC = keyNo[0];
  let companyType = 'normalcom';
  if (firstC === 's') { // 1
    // 社会组织
    companyType = 'so';
  } else if (firstC === 'h') { // 1
    // 香港
    companyType = 'hk';
  } else if (firstC === 't') { // 1
    // 台湾
    companyType = 'tw';
  } else if (firstC === 'z') { // 1
    // 海外企业
    companyType = 'oversea';
  } else if (firstC === 'l') { // 1
    // 美股
    companyType = 'us';
  }
  if (companyType === 'oversea') {
    company.info = {
      keyNo,
      name: company.HeadInfo?.EntName,
      code: company.HeadInfo?.CountryCode,
    };
    if (company.HeadInfo?.SearchResult?.length) {
      company.HeadInfo.SearchResult.forEach((item) => {
        if (item.DisplayCNName?.includes('地址')) {
          company.info.address = item.Value;
        }
      });
    }
    company.CommonList = company.HeadInfo?.CommonList;
  } else if (companyType === 'so') {
    company.info = {
      keyNo,
      name: company.DJInfo?.name,
      logo: company.ImageUrl,
      status: company.DJInfo?.status,
      phone: company.ContactInfo?.PhoneNumber,
      email: company.ContactInfo?.Email,
      gw: company.ContactInfo?.WebSite?.[0]?.Url,
      address: company.ContactInfo?.Address,
    };
  } else if (companyType === 'hk') {
    company.info = {
      keyNo,
      name: company.CompanyName,
      logo: company.ImageUrl,
      status: company.ShortStatus,
      phone: company.Tel,
      email: company.Email,
      gw: company.WebSite,
      address: company.Address,
    };
  } else if (companyType === 'tw') {
    company.info = {
      keyNo,
      name: company.CompanyName,
      logo: company.ImageUrl,
      status: company.Status,
      phone: company.Tel,
      email: company.Email,
      gw: company.WebSite,
      address: company.Address,
    };
  } else if (companyType === 'us') {
    company.info = {
      keyNo,
      name: company.CompanyNameEn,
      logo: company.ImageUrl,
      phone: company.ContactInfo?.PhoneNumber,
      email: company.ContactInfo?.Email,
      gw: company.ContactInfo?.WebSite?.[0]?.Url,
      address: company.ContactInfo?.Address,
    };
  } else {
    company.info = {
      keyNo: company.KeyNo,
      name: company.Name,
      logo: company.ImageUrl,
      status: company.ShortStatus,
      phone: company.ContactInfo?.PhoneNumber,
      email: company.ContactInfo?.Email,
      gw: company.ContactInfo?.WebSite?.[0]?.Url,
      address: company.Address,
    };
  }
  if (company?.info?.gw) {
    company.info.gwlink = _.startsWith(company.info.gw, 'http')
      ? company.info.gw
      : `http://${company.info.gw}`;
  }
};

export const getLogoByKeyNo = (keyNo = '', hasImage = false) => {
  if (!keyNo) {
    return '';
  }

  const isPerson = keyNo.startsWith('p');
  let url = '';
  // 人的情况
  if (isPerson && hasImage) {
      url = `//image.qcc.com/person/${keyNo}.jpg`;
  } else if(!isPerson && hasImage){
      url = `//image.qcc.com/logo/${keyNo}.jpg`;
  } else if(!isPerson && !['l', 'z'].includes(keyNo[0])) {
    url = `//image.qcc.com/auto/${keyNo}.jpg`;
  }

  if (url) {
    if (keyNo.startsWith('g') && hasImage) {
      url = `//co-image.qichacha.com/CompanyImage/${keyNo}.jpg`;
    } else {
      url += '?x-oss-process=style/logo_200';
    }
  }
  return url;
};

// 企业简介
export const getCompanyDesc = ({ company, companyInfo, operType = '法定代表人' }) => {
  let desc = companyInfo.name;
  if (company.StartDate) {
    desc += `成立于${company.StartDate}，`;
  }
  if (company.Oper) {
    desc += `${operType}为${company.Oper.Name}，`;
  }
  if (company.RegistCapi) {
    desc += `注册资本为${company.RegistCapi}，`;
  }
  if (company.CreditCode) {
    desc += `统一社会信用代码为${company.CreditCode}，`;
  }
  if (company.Address) {
    desc += `企业地址位于${company.Address}，`;
  }
  const industry = company.Industry?.SubIndustry || company.Industry?.Industry;
  if (industry) {
    desc += `所属行业为${industry}，`;
  }
  if (company.Scope) {
    desc += `经营范围包含：${company.Scope}`;
    if (!company.Scope.endsWith('。')) {
      desc += '。';
    }
  }
  if (company.Status || company.ShortStatus) {
    desc += `${companyInfo.name}目前的经营状态为${company.Status || company.ShortStatus}。`;
  }
  return desc;
};

// 具有相同电话的企业
export const getSameList = (company) => {
  const sameList: any = {};
  if (company.SameTelAddressList?.length) {
    company.SameTelAddressList.forEach((item) => {
      sameList[item.A] = {
        key: item.K,
        value: item.A,
        count: item.C > 99 ? '99+' : `${item.C}`,
      };
    });
    // FIXME: 逻辑不通 (参考测试用例)
    if (sameList[company.info?.phone] || company.ContactInfo?.PhoneNumber) {
      sameList.phone = sameList[company.info?.phone || company.ContactInfo?.PhoneNumber];
    }
    if (sameList[company.info?.address]) {
      sameList.address = sameList[company.info?.address];
    }
  }
  return sameList;
};

// 法定代表人label
export const getOperTypeLabelMapper = (t) => {
  const labelMap = {
    1: '法定代表人',
    2: '执行事务合伙人',
    3: '负责人',
    4: '经营者',
    5: '投资人',
    6: '董事长',
    7: '理事长',
    8: '代表人'
  }
  return labelMap[t] || labelMap[1];
};

export const getActingList = (company) => {
  const actingList = {};
  if (company?.CommonList && company?.CommonList?.length > 0) {
    company.CommonList.forEach((item) => {
      if (`${item.Key}` === '40') {
        const value = JSON.parse(item.Value);
        value.forEach((val) => {
          actingList[val.k] = true;
        });
      }
    });
  }
  return actingList;
};
