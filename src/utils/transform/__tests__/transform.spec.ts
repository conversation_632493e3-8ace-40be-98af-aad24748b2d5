import { calculateByteLength, isLargeMaxthLength, transformEmptyString } from '..';

describe('transformEmptyString', () => {
  test('should remove empty values', () => {
    const input = { a: '', b: null, c: undefined, d: NaN, e: [] };
    const output = transformEmptyString(input);
    expect(output).toEqual({});
  });

  test('should remove empty arrays', () => {
    const input = { a: ['', null, undefined], b: [1, 2, 3] };
    const output = transformEmptyString(input);
    expect(output).toEqual({ b: [1, 2, 3] });
  });

  test('should recursively remove empty values', () => {
    const input = { a: { b: '', c: null }, d: { e: [] } };
    const output = transformEmptyString(input);
    expect(output).toEqual({ a: {}, d: {} });
  });

  test('should preserve non-empty values', () => {
    const input = { a: 'value', b: 1, c: true, d: {} };
    const output = transformEmptyString(input);
    expect(output).toEqual({ a: 'value', b: 1, c: true, d: {} });
  });
});

describe('calculateByteLength', () => {
  test('should calculate the byte length of a string', () => {
    expect(calculateByteLength('hello')).toBe(5);
    expect(calculateByteLength('世界')).toBe(6);
    expect(calculateByteLength('こんにちは')).toBe(15);
  });

  test('should calculate the byte length of a number', () => {
    expect(calculateByteLength(12345)).toBe(5);
    expect(calculateByteLength(987654321)).toBe(9);
  });

  test('should handle empty string', () => {
    expect(calculateByteLength('')).toBe(0);
  });

  test('should handle special characters', () => {
    expect(calculateByteLength('!@#$%^&*()')).toBe(10);
    expect(calculateByteLength('`~[]{}|;:,.<>?/')).toBe(15);
  });
});

describe('isLargeMaxthLength', () => {
  test('should return true if string byte length is greater than specified length', () => {
    const str = '漢字';
    const length = 2;

    expect(isLargeMaxthLength(str, length)).toBeTruthy();
  });

  test('should return false if string byte length is less than or equal to specified length', () => {
    const str = 'abc';
    const length = 3;

    expect(isLargeMaxthLength(str, length)).toBeFalsy();
  });
});
