import { cloneDeep, isArray, isEmpty, isObject, transform } from 'lodash';

// 过滤掉对象中的空值
export const transformEmptyString = (obj: Record<string, any>) => {
  obj = cloneDeep(obj);
  return transform(obj, (result: any, value, key) => {
    if (['', null, undefined, NaN].includes(value)) {
      // 过滤掉空值
    } else if (isArray(value) && isEmpty(value?.filter(Boolean))) {
      // 过滤掉空数组
    } else if (isObject(value)) {
      result[key] = transformEmptyString(value);
    } else {
      result[key] = value;
    }
  });
};

export const calculateByteLength = (input: string | number): number => {
  const str = typeof input === 'number' ? input.toString() : (input as string);
  let byteLength = 0;

  for (let i = 0; i < str.length; i++) {
    const codePoint = str.codePointAt(i);

    if (codePoint !== undefined) {
      // 单个16位编码单元
      if (codePoint >= 0 && codePoint <= 0x7f) {
        byteLength += 1;
      }
      // 两个16位编码单元
      else if (codePoint >= 0x80 && codePoint <= 0x7ff) {
        byteLength += 2;
      }
      // 四个16位编码单元
      else if (codePoint >= 0x800 && codePoint <= 0xffff) {
        byteLength += 3;
      }
      // 其他情况
      else if (codePoint >= 0x10000 && codePoint <= 0x10ffff) {
        byteLength += 4;
        i++; // 跳过额外的编码单元
      }
    }
  }
  return byteLength;
};

export const isLargeMaxthLength = (str: string, length) => {
  return calculateByteLength(str) > length;
};

// 各种类型的企业的注册资本名称
export const translateRegistCapiLabel = (enterpriseType: string[] | string) => {
  const defaultLabel = '注册资本';
  const enterpriseTypeToLabelMap: Record<string, string> = {
    '001006': '出资额',
    '001007001': '出资额',
    '001007002': '出资额',
    '001015': '出资额',
    '001009': '资金数额',
    '001008': '成员出资总额',
    '001004': '开办资金',
    '001005': '注册资金',
    org: '注册资金',
  };
  if (Array.isArray(enterpriseType)) {
    const type = enterpriseType.find((v) => enterpriseTypeToLabelMap[v]);
    if (type) {
      return enterpriseTypeToLabelMap[type] || defaultLabel;
    }
    return defaultLabel;
  }
  return enterpriseTypeToLabelMap[enterpriseType] || defaultLabel;
};
