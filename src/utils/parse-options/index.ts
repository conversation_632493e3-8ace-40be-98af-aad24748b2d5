import _ from 'lodash';

import { tree as areaTree } from '@/shared/constants/area.constant';
import { ACHIEVEMENT_TYPE, DATE_OPTIONS } from '@/config/search.config';
import { defaultDisabledDate } from '@/utils/date';
import CERTIFICATE from '@/config/certificate.config';

import Tree from '../tree';

export const ALL = '__all__';

type Option = {
  label: string;
  value: any;
};

const groupsMap = {
  publishdate: {
    label: '中标时间',
    type: 'select',
    field: 'publishdate',
    props: {
      placeholder: '中标时间',
      allowClear: true,
      autoResize: true,
    },
    custom: {
      type: 'date-range',
      props: {
        disabledDate: defaultDisabledDate,
        autoResize: true,
      },
    },
    // customizeProps: { disabledDate: defaultDisabledDate, autoResize: true },
    options: [
      {
        label: '全部时间',
        value: undefined,
      } as Option,
    ].concat(DATE_OPTIONS),
  },
  province: {
    label: '项目属地',
    type: 'select',
    field: 'province',
    props: {
      multiple: true,
      placeholder: '项目属地',
      allowClear: true,
      autoResize: true,
      checkbox: 'end',
      showValues: true,
    },
    style: {
      maxWidth: '300px',
    },
  },
  projecttype: {
    label: '项目类别',
    type: 'select',
    field: 'projecttype',
    props: {
      multiple: true,
      placeholder: '项目类别',
      allowClear: true,
      autoResize: true,
      checkbox: 'end',
      showValues: true,
    },
    options: [
      {
        label: '全部',
        value: undefined,
      },
    ],
  },
  regtypecode: {
    label: '执业注册证书',
    field: 'regtypecode',
    type: 'select',
    props: {
      placeholder: '执业注册证书',
      autoResize: true,
      allowClear: true,
    },
  },
};

const valueToTree = (value, tree: Tree) => {
  const nodes = _.compact(
    _.map(value, (v) => {
      const node = tree.getNodeByValue(v.key);

      if (!node) {
        return;
      }

      // eslint-disable-next-line consistent-return
      return {
        ...node,
        label: `${node.label}（${v.count}）`,
        value: node.value,
      };
    })
  )
    // 根据 level 排序
    .sort((x, y) => x.path.length - y.path.length);
  const map = {};
  const data: Array<{ label: string; value: string | number }> = [];

  nodes.forEach((node) => {
    const item = {
      label: node.label,
      value: node.value,
    };
    map[node.value] = item;

    if (!node.parent) {
      data.push(item);
    } else {
      const parent = map[node.parent.value];
      if (!parent) {
        return;
      }

      if (!parent.children) {
        parent.children = [
          {
            label: '全部',
            value: ALL,
          },
        ];
      }

      parent.children.push(item);
    }
  });

  return data;
};

const parseArea = (items): Option[] => {
  return valueToTree(items, areaTree);
};

const parseProjectType = (items): Option[] => {
  // 排序和选项保持一致，先 map options
  return _.compact(
    _.map(ACHIEVEMENT_TYPE, (o) => {
      const it = _.find(items, (v) => v.key === o.value);

      return it ? { ...o, label: `${o.label}（${it.count}）` } : undefined;
    })
  );
};

const parseCertificate = (items): Option[] => {
  return _.compact(
    items.map((it) => {
      const o = CERTIFICATE.find((v) => v.value === it.key);

      if (!o) {
        return undefined;
      }

      return {
        ...o,
        label: `${o.label}（${it.count}）`,
      };
    })
  );
};

export const generateGroups = (fields: string[], groupItems, total?: number): any => {
  return _.compact(
    fields.map((field) => {
      const items = _.get(
        _.find(groupItems, ({ type }) => type === field),
        'items',
        []
      );

      switch (field) {
        case 'publishdate':
          return groupsMap.publishdate;
        case 'projecttype':
          return {
            ...groupsMap.projecttype,
            options: parseProjectType(items),
          };
        case 'provincecode':
        case 'province':
          return {
            ...groupsMap.province,
            field,
            options: parseArea(items),
          };
        case 'regtypecode':
          return {
            ...groupsMap.regtypecode,
            options: [
              {
                label: `全部（${total}）`,
                value: undefined,
              },
            ].concat(parseCertificate(items)),
          };
        default:
          return undefined;
      }
    })
  );
};

export const isValidFilters = (filters: any): boolean => {
  return (
    _.isPlainObject(filters) &&
    !_.isEmpty(filters) &&
    !_.every(filters, (v) => {
      if (_.isNil(v) || v === '') {
        return true;
      }

      if (Array.isArray(v) && !v.length) {
        return true;
      }

      return false;
    })
  );
};
