type OriginalRelationData = {
  endCompanyKeyno: string;
  startCompanyKeyno: string;
  steps: number;
  relations: unknown[];
  role?: string;
  data?: any;
};

/**
 * 合并相同起始点的关系路径: 依据相同的 startCompanyKeyno 和 endCompanyKeyno 合并
 */
export const mergeRelationPaths = <T extends OriginalRelationData>(dataSource: T[]) => {
  const uniquePathsMap = new Map<string, T>();

  dataSource.forEach((item) => {
    // NOTE: 当两家公司处于不同位置时（A-B、B-A），需要合并关系路径
    const startToEnd = [item.startCompanyKeyno, item.endCompanyKeyno].join('-');
    const endToStart = [item.endCompanyKeyno, item.startCompanyKeyno].join('-');

    // 合并相同起始点的关系路径
    let uniquePathKey: string | undefined;
    uniquePathKey = uniquePathsMap.has(startToEnd) ? startToEnd : uniquePathKey;
    uniquePathKey = uniquePathsMap.has(endToStart) ? endToStart : uniquePathKey;

    if (uniquePathKey) {
      const targetItem = uniquePathsMap.get(uniquePathKey) || ({} as T);
      uniquePathsMap.set(uniquePathKey, {
        ...targetItem,
        relations: [...targetItem.relations, item.relations],
        data: [...targetItem.data, item.data || null],
      });
    } else {
      // 转换 `relations` 为嵌套数组
      const targetItem = {
        ...item,
        relations: [item.relations],
        data: [item.data || null], // 保证 data 数组与 relations 数量保持一一对应
      };
      uniquePathsMap.set(startToEnd, targetItem);
    }
  });
  const result = [...uniquePathsMap.values()];
  return result;
};
