/* eslint-disable no-restricted-properties */
import * as _ from 'lodash';
import * as d3 from 'd3v7';

import { PIXEL_RATIO } from '@/utils/canvas';

import { DataNode, DataPath } from './parse';

const FONT_FAMILY = "'Microsoft YaHei', Arial";
interface Options {
  canvas: HTMLCanvasElement;
  width: number;
  height: number;
  autoCursor: boolean;
  waterMarkerImage?: string;
  onClick?(node: DataNode): void;
}
interface Coordinate {
  x: number;
  y: number;
}

const parseEventCoordinate = (e: TouchEvent | MouseEvent) => {
  if ('touches' in e) {
    if (e.touches.length === 1) {
      const { clientX, clientY } = e.touches[0];

      return { x: clientX, y: clientY };
    }

    return undefined;
  }

  return {
    x: e.offsetX,
    y: e.offsetY,
  };
};

// const stopPropagation = (e: TouchEvent) => e.stopPropagation();
export default class {
  private source!: DataPath;

  private graph!: DataPath;

  private ctx!: CanvasRenderingContext2D;

  private transform = {
    x: 0,
    y: 0,
    k: 1,
  };

  private zoom!: d3.ZoomBehavior<HTMLCanvasElement, unknown>;

  private waterMark = new Image();

  private dragRecord: Coordinate & { node?: DataNode; time: number } = {
    node: undefined,
    x: 0,
    y: 0,
    time: Date.now(),
  };

  private exporting = false;

  private ready = false;

  constructor(private options: Options) {
    const { canvas, width, height } = options;
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;

    canvas.width = width * PIXEL_RATIO;
    canvas.height = height * PIXEL_RATIO;
    // 高分屏缩放
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    this.ctx = ctx;

    if (options.waterMarkerImage) {
      this.waterMark.setAttribute('crossorigin', 'anonymous');
      this.waterMark.src = options.waterMarkerImage;
    }
  }

  init() {
    const { canvas, autoCursor } = this.options;
    const debounceDetectCursor = _.throttle(this.detectCursor, 100).bind(this);
    const handleDrag = this.handleDrag.bind(this);
    const handleDragStart = this.handleDragStart.bind(this);
    const handleDragEnd = this.handleDragEnd.bind(this);
    const zoom = d3
      .zoom<HTMLCanvasElement, unknown>()
      .filter(this.zoomFilter.bind(this))
      .scaleExtent([0.1, 3])
      .clickDistance(10)
      // .tapDistance(10)
      .on('zoom', this.handleZoom.bind(this));
    const drag = d3.drag<HTMLCanvasElement, unknown>().on('start', handleDragStart).on('end', handleDragEnd).on('drag', handleDrag);

    d3.select(canvas).call(zoom).on('dblclick.zoom', null);
    d3.select(canvas).call(drag);

    if (autoCursor) {
      canvas.addEventListener('mousemove', debounceDetectCursor);
    }

    this.zoom = zoom;

    this.ready = true;
  }

  detectCursor(e: MouseEvent) {
    const { canvas } = this.options;
    const node = this.getActiveNode(e.offsetX, e.offsetY);
    canvas.style.cursor = node ? 'pointer' : 'default';
  }

  getActiveNode(targetX: number, targetY: number): DataNode | undefined {
    let activeNode: DataNode | undefined;
    const { transform } = this;
    const { k } = transform;
    const r = 30 * k;

    this.graph.nodes.some((node) => {
      const { x, y } = node;
      const currentX = x * k + transform.x;
      const currentY = y * k + transform.y;

      const isNode = Math.sqrt(Math.pow(targetX - currentX, 2) + Math.pow(targetY - currentY, 2)) < r;

      if (isNode) {
        activeNode = node;
      }

      return isNode;
    });

    return activeNode;
  }

  zoomFilter(event: TouchEvent | MouseEvent) {
    if (event.ctrlKey) {
      return false;
    }

    if ('touches' in event) {
      if (event.touches.length > 1) {
        return true;
      }
    } else if (event.button) {
      return false;
    }

    const coordinate = parseEventCoordinate(event);

    if (!coordinate) {
      return true;
    }
    const node = this.getActiveNode(coordinate.x, coordinate.y);

    if (!node) {
      return true;
    }

    return false;
  }

  handleZoom(e: d3.D3ZoomEvent<HTMLCanvasElement, unknown>) {
    this.transform = e.transform;
    this.refreshCanvas();
  }

  handleDrag(e: d3.D3DragEvent<HTMLCanvasElement, unknown, Coordinate>) {
    const { node } = this.dragRecord;
    const { k } = this.transform;

    if (node) {
      node.x += e.dx / k;
      node.y += e.dy / k;
      this.refreshCanvas();
    }
  }

  handleDragStart(e: d3.D3DragEvent<HTMLCanvasElement, unknown, Coordinate>) {
    const node = this.getActiveNode(e.x, e.y);

    if (node) {
      Object.assign(this.dragRecord, {
        x: e.x,
        y: e.y,
        node,
        time: Date.now(),
      });
    }
  }

  handleDragEnd(e: d3.D3DragEvent<HTMLCanvasElement, unknown, Coordinate>) {
    if (!this.options.onClick) {
      return;
    }
    const { time, node, x, y } = this.dragRecord;
    if (node && Date.now() - time <= 300 && Math.abs(x - e.x) < 5 && Math.abs(y - e.y) < 5) {
      this.options.onClick(node);
    }
  }

  setDataSource(source: DataPath) {
    if (!this.ready) {
      this.init();
    }
    this.source = source;
    this.graph = _.cloneDeep(source);
    if (!this.waterMark.complete) {
      this.waterMark.onload = () => {
        this.toCenter();
      };
    } else {
      this.toCenter();
    }
  }

  toCenter() {
    const { width } = this.options;
    const k = 1;
    const transform = {
      x: width / 2,
      y: 80,
      k,
    };
    // zoom 会记录 transform 状态，如果只修改 $_transform，下次拖动或缩放时会还原
    d3.select(this.options.canvas).call(this.zoom.transform, d3.zoomIdentity.translate(transform.x, transform.y).scale(transform.k));
  }

  refreshCanvas() {
    const { ctx } = this;
    const { width, height } = this.options;
    const { k, x, y } = this.transform;
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.clearRect(0, 0, width, height);
    ctx.save();
    if (!this.exporting) {
      ctx.scale(PIXEL_RATIO, PIXEL_RATIO);
    }
    this.drawBackground();
    ctx.translate(x, y);
    ctx.scale(k, k);
    this.drawLinks();
    this.drawNodes();
    ctx.restore();
  }

  drawBackground() {
    const {
      ctx,
      options: { width, height },
    } = this;
    ctx.save();

    const ptrn = ctx.createPattern(this.waterMark, 'repeat') as CanvasPattern;

    ctx.fillStyle = ptrn;
    ctx.fillRect(0, 0, width, height);

    ctx.restore();
  }

  drawLinks() {
    const { ctx } = this;

    const { links } = this.graph;

    ctx.save();
    ctx.font = `10px ${FONT_FAMILY}`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.lineWidth = 1;

    links.forEach((link) => {
      ctx.save();
      const { from, to, labels } = link;
      const [x, y] = (['x', 'y'] as Array<'x' | 'y'>).map((k) => (from[k] + to[k]) / 2);
      const reverseArrow = to.x < from.x;
      const radians = Math.atan((to.y - from.y) / (to.x - from.x));
      const text = labels.join(',');
      const lineWidth = Math.sqrt(Math.pow(to.x - from.x, 2) + Math.pow(to.y - from.y, 2)) - 60;

      ctx.translate(x, y);
      ctx.rotate(radians);

      const textWidth = ctx.measureText(text).width;

      // 线
      ctx.strokeStyle = '#999';
      ctx.beginPath();
      ctx.moveTo(-lineWidth / 2, 0);
      ctx.lineTo(lineWidth / 2, 0);
      ctx.stroke();

      // 箭头
      ctx.fillStyle = '#999';
      ctx.beginPath();
      if (!reverseArrow) {
        ctx.moveTo(lineWidth / 2, 0);
        ctx.lineTo(lineWidth / 2 - 5, 2.5);
        ctx.lineTo(lineWidth / 2 - 5, -2.5);
      } else {
        ctx.moveTo(-lineWidth / 2, 0);
        ctx.lineTo(-lineWidth / 2 + 5, 2.5);
        ctx.lineTo(-lineWidth / 2 + 5, -2.5);
      }
      ctx.closePath();
      ctx.fill();

      // 关系文案白底
      ctx.fillStyle = '#fff';
      ctx.beginPath();
      ctx.fillRect(-(textWidth / 2) - 3, -7, textWidth + 6, 14);

      // 关系文案
      ctx.fillStyle = '#333';
      ctx.beginPath();
      ctx.fillText(text, 0, 0);
      ctx.fill();

      ctx.restore();
    });
    ctx.restore();
  }

  drawNodes() {
    const { ctx } = this;
    const { nodes } = this.graph;
    ctx.save();
    ctx.font = `10px ${FONT_FAMILY}`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    nodes.forEach((node) => {
      let color: string = _.get({ company: '#1f8cea', person: '#fa4461' }, [node.type]);
      const { labelGroup } = node;
      if (node.active) {
        color = '#f8621e';
      }
      if (!node.relatedKeys.length) {
        color = '#999';
      }
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(node.x, node.y, 30, 0, Math.PI * 2);
      ctx.closePath();
      ctx.fill();

      ctx.fillStyle = '#fff';
      switch (labelGroup.length) {
        case 1:
          ctx.fillText(labelGroup[0], node.x, node.y);
          break;
        case 2:
          ctx.fillText(labelGroup[0], node.x, node.y - 8);
          ctx.fillText(labelGroup[1], node.x, node.y + 8);
          break;
        case 3:
          ctx.fillText(labelGroup[0], node.x, node.y - 14);
          ctx.fillText(labelGroup[1], node.x, node.y);
          ctx.fillText(labelGroup[2], node.x, node.y + 14);
          break;
        default:
          break;
      }
    });
    ctx.restore();
  }

  resetChart() {
    this.setDataSource(this.source);
  }

  exportCanvas() {
    const scale = 1.5;
    const cache = _.pick(this, ['ctx', 'graph', 'transform', 'options']);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    const exportGraph = _.cloneDeep(this.source);
    const xs = exportGraph.nodes.map((n) => n.x);
    const ys = exportGraph.nodes.map((n) => n.y);
    const minX = _.min(xs) || 0;
    const maxX = _.max(xs) || 0;
    const width = (maxX - minX + 100) * scale;
    const height = ((_.max(ys) || 0) - (_.min(ys) || 0) + 80) * scale;

    canvas.width = width;
    canvas.height = height;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    this.exporting = true;

    this.ctx = ctx;
    this.graph = exportGraph;
    this.transform = {
      k: scale,
      x: width / 2 - (maxX + minX) / 2,
      y: 40 * scale,
    };
    this.options = {
      ...this.options,
      width,
      height,
    };

    this.refreshCanvas();

    this.exporting = false;

    Object.assign(this, cache);

    return canvas;
  }
}
