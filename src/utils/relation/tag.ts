import { intersection } from 'lodash';

import { getRiskLevelStyle } from '@/config/risk.config';

const strongLevelDimension = [
  'biddingcompanyrelationship', // 直接关系
  'BiddingCompanyRelationship', // 直接关系
  'legal',
  'employ',
  'invest',
  'hislegal',
  'hisemploy',
  'hisinvest',
  'branch',
  'actualcontroller',
  'guarantor',
  'equitypledge',
  'chattelmortgage',
  'controlrelation',
  '法定代表人',
  '董监高',
  '股东',
  '历史股东',
  '持股/投资关联',
  '历史法定代表人',
  '历史董监高',
  '历史持股/投资关联',
  '分支机构',
  '控制关系',
  '实际控制人',
  '相互担保关联',
  '股权出质关联',
  '动产抵押关联',
];
const normalLevelDimension = [
  'contactnumber',
  'mail',
  'address',
  'upanddownrelation',
  'bidcollusive',
  'website',
  '相同电话号码',
  '相同邮箱',
  '相同经营地址',
  '上下游关联',
  '围串标关联',
  '客户',
  '供应商',
  '相同域名信息',
];
const weakLevelDimension = [
  'patent',
  'intpatent',
  'softwarecopyright',
  'case',
  'samenameemployee',
  '相同专利信息',
  '相同国际专利信息',
  '相同软件著作权',
  '相同司法案件',
  '疑似同名主要人员',
];

/**
 * 获取关系类型 风险强弱等级 样式, 图谱的关系可能出现多种
 * @param dimension: [] | string
 * @returns
 */
export const getTenderRaltionLevelStyle = (dimension: any[] | string = []) => {
  let level = 3;
  let label = '';
  const dimensionData = Array.isArray(dimension) ? dimension.filter((item) => item).map((d) => d.toLowerCase()) : [dimension.toLowerCase()];
  if (intersection(strongLevelDimension, dimensionData).length) {
    level = 2;
    label = '强关系';
  } else if (intersection(normalLevelDimension, dimensionData).length) {
    level = 1;
    label = '中等关系';
  } else if (intersection(weakLevelDimension, dimensionData).length) {
    level = 0;
    label = '弱关系';
  } else {
    level = 3;
    label = '';
  }
  const style = getRiskLevelStyle(level);
  return {
    style: {
      ...style,
      width: '62px',
      'text-align': 'center',
      transform: 'translateY(-2px)',
      color: '#333',
    },
    label,
  };
};
