import * as _ from 'lodash';

export interface RelationSource {
  block: number[][];
  data: Array<{
    graph: {
      nodes: Array<{
        id: string;
        labels: string[];
        properties: {
          keyNo: string;
          name: string;
        };
      }>;
      relationships: Array<{
        startNode: string;
        endNode: string;
        id: string;
        properties: { inLineText: string };
        type: string;
      }>;
    };
  }>;
}

export interface DataNode {
  key: string;
  id: string;
  label: string;
  labelGroup: string[];
  type: string;
  level: number;
  active: boolean;
  relatedKeys: string[];
  position: number;
  x: number;
  y: number;
}

export interface DataLink {
  key: string;
  from: DataNode;
  to: DataNode;
  labels: string[];
}

export interface DataPath {
  nodes: DataNode[];
  links: DataLink[];
}

const deepSetLevel = (ids: string[], nodesMap: Record<string, DataNode>, depth: number) => {
  const nextIds: string[] = [];
  if (!ids.length) {
    return;
  }

  ids.forEach((id) => {
    const node = nodesMap[id];
    if (!node || node.level > -1) {
      return;
    }
    node.level = depth;
    nextIds.push(...node.relatedKeys);
  });
  deepSetLevel(nextIds, nodesMap, depth + 1);
};

const initLayout = (nodes: DataNode[], nodesMap: Record<string, DataNode>, links: DataLink[]) => {
  const group = _.groupBy(nodes, 'level');

  if (group[-1]) {
    let maxLevel = _.max(Object.keys(group).map(_.toNumber)) || 0;
    group[-1].forEach((node) => {
      maxLevel += 1;
      group[maxLevel] = [node];
    });

    delete group[-1];
  }

  Object.keys(group)
    .sort((x, y) => _.toNumber(x) - _.toNumber(y))
    .forEach((key, index) => {
      const groupNodes = group[key];

      // 最后一层且只有一个节点时居中
      if (index > 0 && groupNodes.length === 1) {
        groupNodes[0].position = 0;
      }
      const usedPosition: Record<number, string> = {};
      groupNodes.forEach((node) => {
        node.relatedKeys.forEach((k) => {
          const childNode = nodesMap[k];
          if (!childNode || !_.isNaN(childNode.position)) {
            return;
          }

          const leftPosition = usedPosition[node.position - 1];
          const rightPosition = usedPosition[node.position + 1];

          // 是否允许向左拓展
          const allowLeft = !leftPosition || leftPosition === node.id;
          const allowRight = !rightPosition || rightPosition === node.id;
          let left = true;
          let times = 1;
          let position = node.position;

          if (_.isNaN(position)) {
            return;
          }

          while (usedPosition[position]) {
            if (!allowLeft && allowRight) {
              // 只往右拓展
              position += 1;
            } else if (!allowRight && allowLeft) {
              position -= 1;
            } else {
              // 先往左找
              if (left) {
                position = node.position - times;
              } else {
                position = node.position + times;
                times += 1;
              }

              left = !left;
            }
          }

          usedPosition[position] = node.id;
          childNode.position = position;
        });
      });
    });

  let totalYGap = 0;
  _.forEach(group, (ns, level) => {
    if (_.toNumber(level) === 0) {
      return;
    }

    // 在关系链中找到所有跟上一层关联的文本，取最大值设置 y
    const relatedLinks: DataLink[] = [];
    const idsX = ns.map((n) => n.id);
    const idsY = group[_.toNumber(level) - 1].map((b) => b.id);
    links.forEach((link) => {
      if (
        (!relatedLinks.includes(link) && idsX.includes(link.from.id) && idsY.includes(link.to.id)) ||
        (idsX.includes(link.to.id) && idsY.includes(link.from.id))
      ) {
        relatedLinks.push(link);
      }
    });
    const xGap = 150;
    let yGap = 120;

    if (relatedLinks.length) {
      yGap += (_.max(relatedLinks.map((link) => link.labels.join('').length)) || 0) * 10;
    }

    totalYGap += yGap;

    ns.forEach((node) => {
      node.x = xGap * node.position;
      node.y = totalYGap;
    });
  });
};

const createLabelGroup = (label: string): string[] => {
  if (label.length <= 8) {
    return _.compact([label.slice(0, 4), label.slice(4)]);
  }

  const clippedLabel = label.length > 11 ? `${label.slice(0, 11)}...` : label;

  return _.compact([clippedLabel.slice(0, 3), clippedLabel.slice(3, 8), clippedLabel.slice(8)]);
};

export const parseRelationSource = (source: RelationSource, activeIds: string[]) => {
  const activeIdsMap: Record<string, boolean> = activeIds.reduce((map, id) => ({ ...map, [id]: true }), {});
  const nodes: DataNode[] = [];
  const nodesMap: Record<string, DataNode> = {};
  const links: DataLink[] = [];
  const linksMap: Record<string, DataLink> = {};
  const unMergedPaths: DataPath[] = [];
  const paths: DataPath[] = [];
  let rootNode!: DataNode;

  source.block.forEach((indexes) => {
    // 先生成所有节点
    indexes.forEach((i) => {
      const { nodes: sourceNodes } = source.data[i].graph;
      sourceNodes.forEach((n) => {
        if (nodesMap[n.id]) {
          return;
        }
        const node: DataNode = {
          key: n.id,
          id: n.properties.keyNo,
          label: n.properties.name,
          labelGroup: createLabelGroup(n.properties.name),
          type: _.toLower(n.labels[0]),
          level: -1,
          position: NaN,
          active: !!activeIdsMap[n.properties.keyNo],
          relatedKeys: [],
          x: 0,
          y: 0,
        };

        // 挑一个当根节点
        if (!rootNode && node.active) {
          rootNode = node;
        }

        nodesMap[node.key] = node;
        nodes.push(node);
      });
    });

    rootNode.position = 0;

    indexes.forEach((i) => {
      // 生成 links
      const { relationships } = source.data[i].graph;
      const activeLinks: DataLink[] = [];

      relationships.forEach(({ startNode, endNode, properties }) => {
        const sNode = nodesMap[startNode];
        const eNode = nodesMap[endNode];
        const linkKey = `${startNode}_${endNode}`;
        let link: DataLink = linksMap[linkKey];

        if (!link) {
          link = {
            key: linkKey,
            from: sNode,
            to: eNode,
            labels: [],
          };
          links.push(link);
          linksMap[link.key] = link;
        }

        if (!link.labels.includes(properties.inLineText)) {
          link.labels.push(properties.inLineText);
        }

        // 和主站顺序保持一致，用 unshift
        if (!_.includes(sNode.relatedKeys, endNode)) {
          sNode.relatedKeys.push(endNode);
        }

        if (!_.includes(eNode.relatedKeys, startNode)) {
          eNode.relatedKeys.push(startNode);
        }

        activeLinks.push(link);
      });

      // 从 relationships 捞关系
      const linkNodes: DataNode[] = [];
      const linkLinks: DataLink[] = [];
      let nextNode: DataNode | undefined = activeLinks
        .reduce((arr, link) => {
          arr.push(link.from);
          arr.push(link.to);
          return arr;
        }, [] as DataNode[])
        .find((link) => link.active);

      if (nextNode) {
        linkNodes.push(nextNode);
      }

      while (nextNode) {
        // eslint-disable-next-line no-loop-func
        const linkIndex = activeLinks.findIndex((link) => link.from === nextNode || link.to === nextNode);
        if (linkIndex < 0) {
          break;
        }

        const link = activeLinks[linkIndex];

        activeLinks.splice(linkIndex, 1);
        linkLinks.push(link);

        if (link.from === nextNode) {
          nextNode = link.to;
          linkNodes.push(nextNode);
        } else if (link.to === nextNode) {
          nextNode = link.from;
          linkNodes.push(nextNode);
        } else {
          nextNode = undefined;
        }
      }

      if (linkLinks.length && _.get(_.last(linkNodes), 'active')) {
        unMergedPaths.push({
          nodes: linkNodes,
          links: linkLinks,
        });
      }
    });

    const depth = _.get(_.maxBy(nodes, 'level'), 'level', -1);
    deepSetLevel([rootNode.key], nodesMap, depth + 1);
  });

  // 去重
  unMergedPaths.forEach((path) => {
    const keys = path.nodes.map((n) => n.id);
    const a = keys.join(',');
    const b = keys.reverse().join(',');

    if (
      paths.find((p) => {
        const k = p.nodes.map((n) => n.id).join(',');
        // 包含当前路径的不需要
        return _.includes(k, a) || _.includes(k, b);
      })
    ) {
      return;
    }

    const index = paths.findIndex((p) => {
      const k = p.nodes.map((n) => n.id).join(',');
      // 当前路径包含了之前的路径，前值不需要了
      return _.includes(a, k) || _.includes(b, k);
    });

    if (index > -1) {
      paths.splice(index, 1);
    }
    paths.push(path);
  });
  initLayout(nodes, nodesMap, links);

  Object.freeze(nodes);
  Object.freeze(links);
  Object.freeze(paths);

  return Object.freeze({
    nodes,
    links,
    paths,
  });
};

export const reflowPath = (nodes: DataNode[], links: DataLink[]) => {
  const nodesMap: Record<string, DataNode> = {};

  nodes.forEach((node, i) => {
    nodesMap[node.key] = node;
    Object.assign(node, {
      level: i,
      position: 0,
      x: 0,
      y: 0,
    });
  });

  links.forEach((link) => {
    const fKey = link.from.key;
    const tKey = link.to.key;

    link.from = nodesMap[fKey];
    link.to = nodesMap[tKey];
  });

  initLayout(nodes, nodesMap, links);
};
