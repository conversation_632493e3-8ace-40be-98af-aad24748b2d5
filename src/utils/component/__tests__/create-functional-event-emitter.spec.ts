import { createFunctionalEventEmitter } from '../create-functional-event-emitter';

describe('createFunctionalEventEmitter', () => {
  test('should call the event handler function when the event is triggered', () => {
    // Arrange
    const listeners = {
      click: vi.fn(),
    };
    const eventEmitter = createFunctionalEventEmitter(listeners);

    // Act
    eventEmitter('click')('arg1', 'arg2');

    // Assert
    expect(listeners.click).toHaveBeenCalledWith('arg1', 'arg2');
  });

  test('should call all event handler functions when the event is triggered', () => {
    // Arrange
    const listeners = {
      click: vi.fn(),
      change: vi.fn(),
    };
    const eventEmitter = createFunctionalEventEmitter(listeners);

    // Act
    eventEmitter('click')('arg1', 'arg2');
    eventEmitter('change')('arg1', 'arg2');

    // Assert
    Object.entries(listeners).forEach(([, handler]) => {
      expect(handler).toHaveBeenCalledWith('arg1', 'arg2');
    });
  });

  test('should not throw an error if the event handler is not defined', () => {
    // Arrange
    const listeners = {};
    const eventEmitter = createFunctionalEventEmitter(listeners);

    // Act and Assert
    expect(() => eventEmitter('click')('arg1', 'arg2')).not.toThrow();
  });
});
