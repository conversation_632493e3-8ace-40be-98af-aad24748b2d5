import _ from 'lodash';
import { message } from 'ant-design-vue';

export const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;

// 手机号
export const phoneRegex = /^((\+|00)86)?1([3568][0-9]|4[579]|6[67]|7[01235678]|9[012356789])[0-9]{8}$/;

// 固定电话或者手机号
export const landlineRegex = /^(0\d{2,3}-\d{7,8}(-\d{1,4})?|1[3456789]\d{9})$/;

export const telNumberRegex = /^[\d][\d-]*[\d]$/;

const formatPhone = (input) => (input || '').replace(/[\s-]/g, '');
// 手机格式校验
export const isValidPhone = (input: string) => phoneRegex.test(formatPhone(input));
// 邮箱格式校验
export const isValidEmail = (input: string) => emailRegex.test(input);
export const isPhone = (phone: string) => phoneRegex.test(phone);

// 非表情验证（反向验证：允许中文、数字、标点符号）
const allowInputRegex =
  /^[\u4e00-\u9fffa-zA-Z，。？！、：；“”（） 《》{}【】~——·＃＊|〖〗『』〔〕「」¥￥…,.?;':'{}[\]/!~@#$%^&*()\-\\`_+=><0-9]+$/;

export const isAllowInput = (name: string) => {
  return allowInputRegex.test(name);
};

export const createNumberRangeValidator = (errorMessage = '请输入合法范围') => {
  return (value) => {
    let isValid = false;
    if (Array.isArray(value) && value.length) {
      const numbers = value.filter((v) => !_.isNil(v));
      const [min, max] = value;
      isValid = numbers.length > 0 && numbers.every((v) => !_.isNaN(_.toNumber(v)));

      if (isValid && numbers.length > 1 && min > max) {
        isValid = false;
      }

      if (max <= 0) {
        isValid = false;
      }

      if (min === max && min === 0) {
        isValid = false;
      }
    }

    if (!isValid && errorMessage) {
      message.warn(errorMessage);
    }

    return isValid;
  };
};

// 中英文
export const characterPattern = /^[\u4e00-\u9fa5a-zA-Z\s]+$/;
// 中英文数字
export const characterAndNumberPattern = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
// 中英文数字以及有限的特殊字符：_、-、（）、()
export const employeeIdPattern = /^[\u4e00-\u9fa5a-zA-Z0-9_\-()（）]+$/;

export const residentIdentityPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

// 身份证校验（后端版
export const idNumberPattern = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;

// 统一社会信用代码
export const creditCodePattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;

export const residentIdentityValidator = (rid?: string) => {
  if (!rid) {
    return false;
  }
  return residentIdentityPattern.test(rid);
};
