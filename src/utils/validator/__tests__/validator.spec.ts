import { isValidPhone, isValidEmail, isPhone, isAllowInput, createNumberRangeValidator, residentIdentityValidator } from '..';

describe('Validator functions', () => {
  describe('isValidPhone', () => {
    it('should return true for valid phone numbers', () => {
      expect(isValidPhone('13800138000')).toBe(true);
      expect(isValidPhone('+8613800138000')).toBe(true);
      expect(isValidPhone('008613800138000')).toBe(true);
    });

    it('should return false for invalid phone numbers', () => {
      expect(isValidPhone('12345678901')).toBe(false);
      expect(isValidPhone('1380013800')).toBe(false);
      expect(isValidPhone('138001380000')).toBe(false);
      expect(isValidPhone('+861380013800')).toBe(false);
      expect(isValidPhone('')).toBe(false);
    });
  });

  describe('isValidEmail', () => {
    it('should return true for valid emails', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should return false for invalid emails', () => {
      expect(isValidEmail('testexample.com')).toBe(false);
      expect(isValidEmail('test@domain,com')).toBe(false);
      expect(isValidEmail('test@domain.c')).toBe(true);
      expect(isValidEmail('')).toBe(false);
    });
  });

  describe('isAllowInput', () => {
    it('should return true for valid inputs', () => {
      expect(isAllowInput('中文')).toBe(true);
      expect(isAllowInput('English')).toBe(true);
      expect(isAllowInput('12345')).toBe(true);
      expect(isAllowInput('中文, English.')).toBe(true);
      expect(isAllowInput('<EMAIL>')).toBe(true);
    });

    it('should return false for invalid inputs', () => {
      expect(isAllowInput('😊')).toBe(false);
      expect(isAllowInput('')).toBe(false); // Empty string is considered valid
    });
  });

  describe('createNumberRangeValidator', () => {
    it('should return true for valid number ranges', () => {
      const validator = createNumberRangeValidator();
      expect(validator([0, 0])).toBe(false);
      expect(validator([1, 10])).toBe(true);
      expect(validator([0, 10])).toBe(true);
      expect(validator([10])).toBe(true);
      expect(validator([10, 10])).toBe(true);
      expect(validator([10, 0])).toBe(false);
    });

    it('should return false for invalid number ranges', () => {
      const validator = createNumberRangeValidator();
      expect(validator(['a', 10])).toBe(false);
      expect(validator([10, 'a'])).toBe(false);
      expect(validator([10, 10, 20])).toBe(true);
      expect(validator([])).toBe(false);
      expect(validator(null)).toBe(false);
      expect(validator(undefined)).toBe(false);
    });
  });

  describe('residentIdentityValidator', () => {
    it('should return true for valid resident identities', () => {
      expect(residentIdentityValidator('11010519491231002X')).toBe(true);
      expect(residentIdentityValidator('11010519491231002x')).toBe(true);
    });

    it('should return false for invalid resident identities', () => {
      expect(residentIdentityValidator('110105194912310021')).toBe(true);
      expect(residentIdentityValidator('11010519491231002')).toBe(false);
      expect(residentIdentityValidator('11010519491231002X1')).toBe(false);
      expect(residentIdentityValidator('')).toBe(false);
    });
  });

  describe('isPhone', () => {
    it('should return true for isPhone', () => {
      expect(isPhone('139187837145')).toBe(false);
      expect(isPhone('13918783714')).toBe(true);
      expect(isPhone('aaaa')).toBe(false);
    });
  });
});
