import { isBoolean } from 'lodash';

// type Pagination = Partial<{ current: 1; pageSize: 10; total: 0 }> | boolean;

// 是否最后一页
export const isLatestPage = (pagination: any) => {
  if (isBoolean(pagination)) return false;
  const { current = 0, pageSize = 10, total = 0 } = pagination;
  return Math.ceil(total / pageSize) === current;
};

// 是否是最后一条记录
export const isLatestRow = (pagination: any) => {
  if (isBoolean(pagination)) return false;
  const { pageSize = 10, total = 0 } = pagination;
  return isLatestPage(pagination) && total % pageSize === 1;
};

// 返回当页的数据量
export const currentPageCount = (pagination: any) => {
  const { current = 1, pageSize = 10, total = 0 } = pagination;
  //  总共页数
  const pageSizeCount = Math.ceil(total / pageSize);
  if (current < pageSizeCount) {
    return pageSize;
  }
  return total - pageSize * (pageSizeCount - 1);
};
