import { findIndex, uniq } from 'lodash';

import { mergeRelationPaths } from '@/utils/relation/merge-relation-paths';

import {
  createEdge,
  createPersonNode,
  createCompanyNode,
  createGraphEdge,
  createGraphNode,
  isCompany,
  isEdge,
  isPerson,
} from './data-node';

interface RelationPath {
  type: string;
  entityType?: string;
  id?: string;
  name?: string;
  label?: string;
  role?: string;
  endid?: string;
  startid?: string;
  direction?: string;
  roles?: string[];
  roleType?: string;
  isTrueId?: string;
  iscal?: string;
  shouldcapi?: number;
  stockpercent?: number;
  // create_time?: number;
}

interface RelationData {
  endCompanyKeyno: string;
  endCompanyName: string;
  startCompanyKeyno: string;
  startCompanyName: string;
  steps: number;
  relations: RelationPath[];
  history?: boolean;
}

/**
 * 数据转换
 * @param relations
 */
export const processRelationRoles = (relations: any[]) => {
  return relations.map((relation: any) => {
    // 转换 Edge 结构
    if (isEdge(relation)) {
      return createEdge(relation);
    }
    if (isPerson(relation)) {
      return createPersonNode(relation);
    }
    if (isCompany(relation)) {
      return createCompanyNode(relation);
    }
    return relation;
  });
};

/**
 * 合并相分组: 根据 `startCompanyKeyno` 和 `endCompanyKeyno`
 * @param dataSource
 */
const mergeGroups = (dataSource: any[]) => {
  const result: Record<string, any>[] = [];

  dataSource.forEach((item) => {
    // 根据 `startCompanyKeyno` 和 `endCompanyKeyno` 合并数据
    let index: number;

    // startToEnd
    index = findIndex(result, {
      startCompanyKeyno: item.startCompanyKeyno,
      endCompanyKeyno: item.endCompanyKeyno,
    });

    // endToStart
    if (index === -1) {
      index = findIndex(result, {
        startCompanyKeyno: item.endCompanyKeyno,
        endCompanyKeyno: item.startCompanyKeyno,
      });
    }

    if (item.data?.length) {
      item.relations?.forEach((relation) => {
        if (isEdge(relation)) {
          // FIXME: 在修改源数据，会影响渲染
          relation.data = [
            {
              data: item.data,
              role: relation.role,
              type: relation.type,
            },
          ];
        }
      });
    }
    if (index > -1) {
      const target = result[index];
      result.splice(index, 1, {
        ...target,
        relations: [...target.relations, processRelationRoles(item.relations)],
      });
    } else {
      result.push({
        ...item,
        relations: [processRelationRoles(item.relations)],
      });
    }
  });
  return result;
};

/**
 * 合并实体关系路径
 * @param dataSource
 */
export const mergeRelations = (dataSource: any[]) => {
  const dedupByEdgeMap: Record<string, any> = {};
  const result: any[] = [];
  dataSource.forEach((item) => {
    const relations: any[] = [];
    item.relations.forEach((nodes) => {
      // 基于 edges 生成唯一ID
      const dedupEdgeId = nodes
        .reduce((r, c) => {
          if (isEdge(c)) {
            return r.concat([`${c.startid}-${c.endid}`]);
          }
          return r.concat();
        }, [])
        .join('-');
      // 命中
      if (Array.isArray(dedupByEdgeMap[dedupEdgeId])) {
        dedupByEdgeMap[dedupEdgeId].map((t, i) => {
          if (isEdge(t)) {
            t.roles = uniq([...(t?.roles || []), ...(nodes[i]?.roles || [])]);
            const dataTypeArr = t?.data?.map((_item) => _item.type);
            nodes[i]?.data?.forEach((_item) => {
              const typeIndex = dataTypeArr.indexOf(_item.type);
              if (typeIndex > -1) {
                t.data[typeIndex].data = [...t.data[typeIndex].data, ..._item.data];
              } else {
                t.data = uniq([...(t?.data || []), _item]);
              }
            });
            t.groups = [...(t?.groups || []), nodes[i]];
            return t;
          }
          return t;
        });
      } else {
        dedupByEdgeMap[dedupEdgeId] = nodes;
        relations.push(nodes);
      }
    });
    result.push({
      ...item,
      relations,
    });
  });

  return result;
};

/**
 * 修复图数据中, 边的 `startid` 与 `endid` 与上下游节点顺序不一致
 */
const correctEdgeDirection = (
  relations: { startid: string; endid: string; id: string; direction: string; roles?: any[] }[],
  index: number
) => {
  const edge = relations[index];
  const prevNode = relations[index - 1];
  const nextNode = relations[index + 1];
  if (prevNode.id === edge.endid && nextNode.id === edge.startid) {
    let direction = edge.direction;
    if (direction === 'left') {
      direction = 'right';
    } else if (direction === 'right') {
      direction = 'left';
    }
    return {
      ...edge,
      direction,
    };
  }
  return edge;
};

/**
 * 原始数据转换为关系链路
 */
export function parseRelationsData<T = Record<string, any>>(dataSource: T[], filterBy?: (item: T) => boolean) {
  const filterData = typeof filterBy === 'function' ? dataSource.filter(filterBy) : dataSource;
  const groups = mergeGroups(filterData);
  const result = mergeRelations(groups);
  return result;
}

/**
 * 关系链接转换为关系图数据
 */
export function parseGraphData<T = Record<string, any>>(dataSource: T[]) {
  const nodes: any[] = [];
  const edges: any[] = [];

  const uniqNodeMap = new Map<string, boolean>();
  const uniqEdgeMap = new Map<string, boolean>();

  const dedupMap = new Map<string, boolean>();

  dataSource.forEach((item: any) => {
    item.relations.forEach((relations, rIndex) => {
      relations.forEach((node, index) => {
        if (node.type === 'node') {
          const isEndpoint = [item.startCompanyName, item.endCompanyName].includes(node.name);
          // nodes 节点去重
          const newNode = createGraphNode(node, isEndpoint);
          const newNodeId = newNode.data.id;
          if (!uniqNodeMap.has(newNodeId)) {
            nodes.push(newNode);
            uniqNodeMap.set(newNodeId, true);
          }
        } else if (node.type === 'edge') {
          const edgeNode = correctEdgeDirection(relations, index);
          // edges 边去重
          const newEdge = createGraphEdge(edgeNode);
          // 通过 `正向端点ID` 和 `反向端点ID` 同时进行匹配
          const newEdgeId = newEdge.data.id;
          const reversedNewEdgeId = newEdge.data.id.split('-').reverse().join('-');

          if (!uniqEdgeMap.has(newEdgeId) && !uniqEdgeMap.has(reversedNewEdgeId)) {
            edges.push(newEdge);
            uniqEdgeMap.set(newEdgeId, true);
          }
        }
      });
    });
  });

  return {
    nodes,
    edges,
  };
}

/**
 * 统计命中条数
 * */

export const getRelationList = (isDirectRelation, data) => {
  // 疑似关系，数据取默认
  if (!isDirectRelation) {
    // 互为担保对象，只保留一条
    const RelationExsitMap = {};
    return (
      data.reduce((dataArr, item) => {
        if (item.type === 'Guarantor') {
          const guarantorKey1 = item.startCompanyKeyno + item.endCompanyKeyno;
          const guarantorKey2 = item.endCompanyKeyno + item.startCompanyKeyno;
          // 都不存在关联记录的时候，数据才加1
          if (!RelationExsitMap[guarantorKey1] && !RelationExsitMap[guarantorKey2]) {
            dataArr.push(item);
            RelationExsitMap[guarantorKey1] = true;
            RelationExsitMap[guarantorKey2] = true;
          }
        } else {
          dataArr.push(item);
        }
        return dataArr;
      }, []) || []
    );
  }
  return data.flatMap((item) => {
    return item.relations.map((relation) => {
      return {
        ...item,
        relations: relation,
      };
    });
  });
};

const getRelationsCount = (relationsPath) => {
  return relationsPath.reduce((r, t) => {
    return r + (t?.relations?.length ?? 0);
  }, 0);
};

export const getRelationRelated = (isDirectRelation, tabData) => {
  const relationsPath = parseRelationsData(tabData);
  const relationsCount = getRelationsCount(relationsPath);
  const originRelationList = getRelationList(isDirectRelation, isDirectRelation ? relationsPath : tabData);
  // 合并相同路径
  const relationList = mergeRelationPaths<RelationData>(originRelationList);
  return {
    relationsPath,
    relationsCount,
    relationList,
  };
};
