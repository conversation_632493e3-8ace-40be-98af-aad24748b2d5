import { cloneDeep, isObject, groupBy, trim, uniq } from 'lodash';

import { createChartLine } from '@/shared/charts/relationship-chart/helper/create-chart-data';
import { RalationKeyMap, RelationTypeMap } from '@/config/dimension.config';

import type { CompanyNode, PersonNode, Edge, RawBiddingCompanyRelationDetail } from './data-types';

// 针对人员没有keyno的时候手动赋值
const genNodeId = () => new Date().getTime();

export const getSource = (arr) => {
  const resultArr = arr.map((v) => {
    const ns = Number(v);
    if (ns > 1000) {
      return `${v}年报`;
    }
    if (ns === 3) {
      return '企业自主上传';
    }
    return '互联网';
  });

  return uniq(resultArr).join(', ');
};

export const getRoles = (array) => {
  return Object.entries(groupBy(array, 't'))?.map(([key]) => {
    return key;
  });
};

export const getGraphEdgeData = (val) => {
  const { data = [], roles = [] } = val;

  if (!data.length) {
    return null;
  }

  const newData = data.flatMap((item2) => {
    if (item2.type === 'ContactNumber' && item2.data?.some(isObject)) {
      // 保留原始数据，方便数据读取
      const phones: string[] = [];
      let originalData: any[] = [];
      Object.entries(groupBy(item2.data, 't'))?.forEach(([key, value]) => {
        phones.push(key);
        originalData = [...originalData, ...value];
      });
      return {
        ...item2,
        data: phones,
        originalData,
      };
    }
    return item2;
  });

  if (roles?.length) {
    return roles.map((role) => {
      const find = newData.find((v) => trim(v.role) === trim(role));
      return {
        ...find,
        role: find?.role || role,
        data: find?.data || [],
      };
    });
  }
  return newData;
};

export const createGraphNode = (item, isEndpoint: boolean) => {
  return {
    data: {
      id: item.id,
      label: item.name,
      name: item.name,
      type: item.entityType, // company | person
      endpoint: isEndpoint, // 是否是起点或终点
      isTrueId: item.isTrueId, // 是否是真实ID，非真实不支持点击跳转，因为id是时间戳，是假的
    },
  };
};

export const createGraphEdge = (node) => {
  const { startid, endid, roles = [] } = node;
  // 箭头类型
  return {
    data: {
      id: `${startid}-${endid}`,
      label: roles?.filter(Boolean).join(','),
      source: startid,
      target: endid,
      data: getGraphEdgeData(node),
      ...createChartLine(node.direction),
    },
  };
};

export const createEdge = (edge): Edge => {
  const node = cloneDeep(edge);

  if (node.roles?.some(isObject) && !node.data) {
    node.data = node.roles.flatMap((item2) => item2);
  }
  if (!node.role && node.roles?.length) {
    node.role = node.roles.map((item) => item.role).join(', ');
    node.direction = 'null';
    // node.render = () => {
    //   return node.roles.flatMap((role, index) => {
    //     if (isObject(role) as any) {
    //       if (role.type === 'ContactNumber' && role.data?.some(isObject)) {
    //         role.data = getRoles(role.data);
    //         return [index !== 0 ? ', ' : null, <Tooltip title={role.data?.join(', ')}>{role.role}</Tooltip>];
    //       }
    //       // if (role.type === 'Address' && role.data?.some(isObject)) {
    //       //   return <Tooltip title={role.role}>{role.role}</Tooltip>;
    //       // }
    //       // return <Tooltip title={role.role}>{role.role}</Tooltip>;
    //     }
    //     return <Tooltip title={role}>{role}</Tooltip>;
    //   });
    // };
  } else {
    const directionsMap = {
      '0': 'null',
      '1': 'right',
      '-1': 'left',
    };
    node.direction = directionsMap[node.direction];
  }

  let roles = node.role?.split(','); // 当前边的角色 (可能存在多个, 以`,`分隔)
  const typeUpperCase = String(node.type).toUpperCase();
  // 角色特殊逻辑处理 (历史董监高、历史法定代表人)
  if (typeUpperCase === 'HISEMPLOY' || typeUpperCase === 'HISLEGAL') {
    roles = roles.map((roleName: string) => (roleName ? `历史${roleName}` : RalationKeyMap[typeUpperCase])); // 历史任职
  } else if (typeUpperCase === 'HISINVEST' && !!node.stockpercent) {
    roles = [`历史投资${node.stockpercent}%`]; // 历史投资关系
  } else if (typeUpperCase === 'INVEST' && !!node.stockpercent) {
    roles = [`投资${node.stockpercent}%`]; // 投资关系
  } else if (typeUpperCase === 'BRANCH') {
    roles = ['分支机构'];
  } else if (typeUpperCase !== 'UPANDDOWNRELATION' && RelationTypeMap[typeUpperCase]) {
    // `上下游关系`显示为真实的 role: 供应商、客户
    roles = [RelationTypeMap[typeUpperCase]?.label || RalationKeyMap[typeUpperCase]];
  } else if (typeUpperCase === '0') {
    roles = [RelationTypeMap['actualcontroller'.toUpperCase()]?.label];
  }
  return {
    ...node,
    type: 'edge',
    roles: roles?.filter(Boolean),
    startid: node.startid || genNodeId(),
    endid: node.endid || genNodeId(),
    direction: node.direction,
    roleType: node.type,
  };
};

export const createCompanyNode = (entity: Partial<RawBiddingCompanyRelationDetail>): CompanyNode => {
  return {
    type: 'node',
    entityType: 'company',
    id: entity['Company.keyno'],
    name: entity['Company.name'],
    label: entity['Company.name'],
  };
};

export const createPersonNode = (entity: Partial<RawBiddingCompanyRelationDetail>): PersonNode => {
  return {
    type: 'node',
    entityType: 'person',
    id: entity['Person.keyno'] || genNodeId(),
    name: entity['Person.name'],
    label: entity['Person.name'],
    isTrueId: entity['Person.keyno'], // 是否是真实id，如果不是真实id就不能跳转
  };
};

export const isCompany = (entity: Partial<RawBiddingCompanyRelationDetail>) => {
  return entity['Company.name'] !== undefined;
};

export const isPerson = (entity: Partial<RawBiddingCompanyRelationDetail>) => {
  return entity['Person.name'] !== undefined;
};

export const isEdge = (entity: Partial<RawBiddingCompanyRelationDetail>) => {
  return [entity.startid, entity.endid, entity.direction].every((t) => t !== undefined);
};
