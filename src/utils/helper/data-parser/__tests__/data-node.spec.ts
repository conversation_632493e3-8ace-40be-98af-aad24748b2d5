import { parseGraphData } from '..';
import { createEdge, createGraphEdge, createGraphNode, isCompany, isEdge, isPerson } from '../data-node';

describe('dataNode', () => {
  describe('isCompany', () => {
    test('returns true if entity has Company.name property', () => {
      const entityWithCompany = {
        'Company.name': 'ACME Inc.',
      };
      expect(isCompany(entityWithCompany)).toBe(true);
    });

    test('returns false if entity does not have Company.name property', () => {
      const entityWithoutCompany = {
        'Person.name': '<PERSON>',
      };
      expect(isCompany(entityWithoutCompany)).toBe(false);
    });
  });

  describe('isPerson', () => {
    test('should return true if entity has Person.name property', () => {
      const entityWithPersonName = {
        'Person.name': '<PERSON>',
      };

      expect(isPerson(entityWithPersonName)).toBe(true);
    });

    test('should return false if entity does not have Person.name property', () => {
      const entityWithoutPersonName = {
        'Animal.name': 'Dog',
      };

      expect(isPerson(entityWithoutPersonName as unknown)).toBe(false);
    });

    test('should return false if entity has empty object as Person property', () => {
      const entityWithEmptyPerson = {
        Person: {},
      };

      expect(isPerson(entityWithEmptyPerson as unknown)).toBe(false);
    });
  });

  describe('isEdge', () => {
    test('should return true for a valid edge', () => {
      const validEdge = {
        startid: '1',
        endid: '2',
        direction: 1,
      };
      expect(isEdge(validEdge)).toBe(true);
    });

    test('should return false for an edge missing startid', () => {
      const invalidEdge = {
        endid: '2',
        direction: 1,
      };
      expect(isEdge(invalidEdge)).toBe(false);
    });

    test('should return false for an edge missing endid', () => {
      const invalidEdge = {
        startid: '1',
        direction: 1,
      };
      expect(isEdge(invalidEdge)).toBe(false);
    });

    test('should return false for an edge missing direction', () => {
      const invalidEdge = {
        startid: '1',
        endid: '2',
        direction: undefined,
      };
      expect(isEdge(invalidEdge)).toBe(false);
    });

    test('should return false for a non-object argument', () => {
      const invalidArgument = 'not-an-object';
      expect(isEdge(invalidArgument as any)).toBe(false);
    });
  });
});

describe('createGraphNode', () => {
  test('should generate correct node', () => {
    const source = {
      data: {
        type: 'node',
        entityType: 'company',
        id: 'c96819a04e5434d81da18bcf7abc3f89',
        name: '山东华泰纸业股份有限公司',
        label: '山东华泰纸业股份有限公司',
      },
      isEndpoint: true,
    };
    const expected = {
      data: {
        id: 'c96819a04e5434d81da18bcf7abc3f89',
        label: '山东华泰纸业股份有限公司',
        name: '山东华泰纸业股份有限公司',
        type: 'company',
        endpoint: true,
        isTrueId: undefined,
      },
    };
    expect(createGraphNode(source.data, source.isEndpoint)).toStrictEqual(expected);
  });
});

describe('createGraphEdge', () => {
  // 生正正确的边，并保持正确的指向
  test('should generate correct edge and direction', () => {
    const source = {
      startid: 'pd0ad169bcc5fe15192b7fb246239d87',
      endid: 'c96819a04e5434d81da18bcf7abc3f89',
      direction: 'left',
      role: '历史董监高',
      type: 'edge',
      roles: ['历史董监高'],
    };
    const expected = {
      data: {
        id: 'pd0ad169bcc5fe15192b7fb246239d87-c96819a04e5434d81da18bcf7abc3f89',
        label: '历史董监高',
        source: 'pd0ad169bcc5fe15192b7fb246239d87',
        target: 'c96819a04e5434d81da18bcf7abc3f89',
        sourceArrow: 'triangle',
        targetArrow: 'none',
        data: null,
      },
    };
    expect(createGraphEdge(source)).toStrictEqual(expected);
  });
});

describe('createEdge', () => {
  test('should convert nodeValue to edge type', () => {
    const source = {
      role: '历史法定代表人,历史董监高',
      endid: '2d76a66c3ed6c46ad8abff8ad5fe0576',
      startid: 'pe18ebb6c88e35851b15419718f5af73',
      direction: -1,
    };
    const expected = {
      direction: 'left',
      endid: '2d76a66c3ed6c46ad8abff8ad5fe0576',
      startid: 'pe18ebb6c88e35851b15419718f5af73',
      role: '历史法定代表人,历史董监高', // originalData, 不需要
      roles: ['历史法定代表人', '历史董监高'],
      roleType: undefined,
      type: 'edge',
    };
    expect(createEdge(source)).toEqual(expected);

    const source2 = {
      role: '上下游关联',
      type: 'UpAndDownRelation',
      endid: '1edb7a6ce89e7feb01fa97bf9a7719e5',
      startid: '2d76a66c3ed6c46ad8abff8ad5fe0576',
      direction: -1,
    };
    const expected2 = {
      direction: 'left',
      endid: '1edb7a6ce89e7feb01fa97bf9a7719e5',
      startid: '2d76a66c3ed6c46ad8abff8ad5fe0576',
      role: '上下游关联', // originalData, 不需要
      roles: ['上下游关联'],
      roleType: 'UpAndDownRelation',
      type: 'edge',
    };
    expect(createEdge(source2)).toEqual(expected2);
  });
});

// 关系链接转换为关系图数据
describe('parseGraphData', () => {
  // 生正正确的边，并保持正确的指向
  test('should generate correct edge and nodes', () => {
    const source = [
      {
        type: 'SameNameEmployee',
        role: '疑似同名主要人员',
        startCompanyName: '山东华泰纸业股份有限公司',
        startCompanyKeyno: 'c96819a04e5434d81da18bcf7abc3f89',
        endCompanyName: '山东博汇纸业股份有限公司',
        endCompanyKeyno: '2d76a66c3ed6c46ad8abff8ad5fe0576',
        history: false,
        steps: 2,
        relations: [
          [
            {
              type: 'node',
              entityType: 'company',
              id: 'c96819a04e5434d81da18bcf7abc3f89',
              name: '山东华泰纸业股份有限公司',
              label: '山东华泰纸业股份有限公司',
            },
            {
              startid: 'pd0ad169bcc5fe15192b7fb246239d87',
              endid: 'c96819a04e5434d81da18bcf7abc3f89',
              direction: 'left',
              role: '历史董监高',
              type: 'edge',
              roles: ['历史董监高'],
            },
            {
              type: 'node',
              entityType: 'person',
              id: 'pd0ad169bcc5fe15192b7fb246239d87',
              name: '李刚',
              label: '李刚',
              isTrueId: 'pd0ad169bcc5fe15192b7fb246239d87',
            },
            {
              startid: 'pd0ad169bcc5fe15192b7fb246239d87',
              endid: 'pe18ebb6c88e35851b15419718f5af73',
              direction: 'right',
              type: 'edge',
              role: '疑似同名主要人员',
              roles: ['疑似同名主要人员'],
              roleType: 'SameNameEmployee',
            },
            {
              type: 'node',
              entityType: 'person',
              id: 'pe18ebb6c88e35851b15419718f5af73',
              name: '李刚',
              label: '李刚',
              isTrueId: 'pe18ebb6c88e35851b15419718f5af73',
            },
            {
              startid: 'pe18ebb6c88e35851b15419718f5af73',
              endid: '2d76a66c3ed6c46ad8abff8ad5fe0576',
              direction: 'right',
              role: '历史法定代表人,历史董监高',
              type: 'edge',
              roles: ['历史法定代表人', '历史董监高'],
            },
            {
              type: 'node',
              entityType: 'company',
              id: '2d76a66c3ed6c46ad8abff8ad5fe0576',
              name: '山东博汇纸业股份有限公司',
              label: '山东博汇纸业股份有限公司',
            },
          ],
        ],
      },
    ];

    const expected = {
      edges: [
        {
          data: {
            data: null,
            id: 'pd0ad169bcc5fe15192b7fb246239d87-c96819a04e5434d81da18bcf7abc3f89',
            label: '历史董监高',
            source: 'pd0ad169bcc5fe15192b7fb246239d87',
            sourceArrow: 'none',
            target: 'c96819a04e5434d81da18bcf7abc3f89',
            targetArrow: 'triangle',
          },
        },
        {
          data: {
            data: null,
            id: 'pd0ad169bcc5fe15192b7fb246239d87-pe18ebb6c88e35851b15419718f5af73',
            label: '疑似同名主要人员',
            source: 'pd0ad169bcc5fe15192b7fb246239d87',
            sourceArrow: 'none',
            target: 'pe18ebb6c88e35851b15419718f5af73',
            targetArrow: 'triangle',
          },
        },
        {
          data: {
            data: null,
            id: 'pe18ebb6c88e35851b15419718f5af73-2d76a66c3ed6c46ad8abff8ad5fe0576',
            label: '历史法定代表人,历史董监高',
            source: 'pe18ebb6c88e35851b15419718f5af73',
            sourceArrow: 'none',
            target: '2d76a66c3ed6c46ad8abff8ad5fe0576',
            targetArrow: 'triangle',
          },
        },
      ],
      nodes: [
        {
          data: {
            endpoint: true,
            id: 'c96819a04e5434d81da18bcf7abc3f89',
            isTrueId: undefined,
            label: '山东华泰纸业股份有限公司',
            name: '山东华泰纸业股份有限公司',
            type: 'company',
          },
        },
        {
          data: {
            endpoint: false,
            id: 'pd0ad169bcc5fe15192b7fb246239d87',
            isTrueId: 'pd0ad169bcc5fe15192b7fb246239d87',
            label: '李刚',
            name: '李刚',
            type: 'person',
          },
        },
        {
          data: {
            endpoint: false,
            id: 'pe18ebb6c88e35851b15419718f5af73',
            isTrueId: 'pe18ebb6c88e35851b15419718f5af73',
            label: '李刚',
            name: '李刚',
            type: 'person',
          },
        },
        {
          data: {
            endpoint: true,
            id: '2d76a66c3ed6c46ad8abff8ad5fe0576',
            isTrueId: undefined,
            label: '山东博汇纸业股份有限公司',
            name: '山东博汇纸业股份有限公司',
            type: 'company',
          },
        },
      ],
    };
    expect(parseGraphData(source)).toEqual(expected);
  });
});
