/* eslint-disable no-underscore-dangle,max-classes-per-file */
import { get } from 'lodash';

export type Path = (string | number)[];

export type TreeNodeData = {
  label: string;
  value: string | number;
  children?: TreeNodeData[];
};

export type TreeNodeAttrs = {
  key: string;
  path: Path;
  checked: boolean;
  value: string | number;
  label: string;
  parent?: TreeNodeAttrs;
  indeterminate: boolean;
  data: TreeNodeData;
  children: TreeNodeAttrs[];
};

const generateKey = (path: Path) => `__${path.join('_')}`;
const generateValueKey = (value: string | number) => `__${value}`;

export class TreeNode {
  _checked = false;

  indeterminate = false;

  children: TreeNode[] = [];

  path: Path;

  key: string;

  constructor(
    public data: TreeNodeData,
    public parent?: TreeNode
  ) {
    const path: Path = [];
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    let node: TreeNode | void = this;

    while (node) {
      path.push(node.value);
      node = node.parent;
    }

    path.reverse();

    this.path = path;
    this.key = generateKey(path);
  }

  get label() {
    return this.data.label;
  }

  get value() {
    return this.data.value;
  }

  get checked() {
    return this._checked;
  }

  set checked(checked) {
    if (this._checked === checked) {
      return;
    }
    const children = this.children.slice();

    this._checked = checked;

    // 递归标记所有子级的选中状态，且无需判断父级状态
    while (children.length) {
      const node = children.shift();
      if (node) {
        node._checked = checked;
        if (node.children.length) {
          children.push(...node.children);
        }
      }
    }

    // 向上检测是否选中父级
    if (this.parent) {
      if (checked) {
        // 检测父节点的 children，若全部为选中状态，此时也要选中父节点，并且递归检测祖先节点
        let node: TreeNode | void = this.parent;

        while (node) {
          if (node.children.some((v) => !v.checked)) {
            break;
          }

          node._checked = true;
          node = node.parent;
        }
      } else {
        // 子节点取消勾选后，如果父节点为选中状态，需要取消父节点勾选
        // 向上传播「取消勾选」时，如果直接标记 checked = false，会导致该节点所有的子节点取消勾选
        // 并且当节点有子/孙节点处于非选中状态，该节点必然也是非选中状态
        let node: TreeNode | void = this.parent;

        while (node) {
          node._checked = false;
          node = node.parent;
        }
      }
    }
  }

  toJS(parent?: TreeNodeAttrs): TreeNodeAttrs {
    const { checked, key, path, label, value, indeterminate, data } = this;
    const node = {
      key,
      path,
      checked,
      value,
      label,
      parent,
      indeterminate,
      data,
      children: [],
    };

    node.children = Object.freeze(this.children.map((child) => child.toJS(node))) as any;

    return Object.freeze(node);
  }
}

export default class Tree {
  nodes!: TreeNode[];

  nodesMap!: Record<string, TreeNode>;

  nodesValueMap!: Record<string, TreeNode>;

  root!: TreeNode[];

  constructor(data: TreeNodeData[]) {
    this.init(data);
  }

  public init(data: TreeNodeData[]) {
    this.nodes = [];
    this.nodesMap = {};
    this.nodesValueMap = {};
    this.root = this._generateNodes(data);
  }

  private _generateNodes(data?: TreeNodeData[], parent?: TreeNode) {
    const { nodes, nodesMap, nodesValueMap } = this;
    if (Array.isArray(data)) {
      return data.map((d) => {
        const node = new TreeNode(d, parent);
        nodes.push(node);
        nodesMap[node.key] = node;
        nodesValueMap[generateValueKey(node.value)] = node;
        node.children = this._generateNodes(d.children, node);

        return node;
      });
    }

    return [];
  }

  reset() {
    this.nodes.forEach((node) => {
      node._checked = false;
      node.indeterminate = false;
    });
  }

  /**
   * 根据 node.checked 计算半选状态
   *
   * @memberof Tree
   */
  markIndeterminate() {
    const marked: Record<string, boolean> = {};
    this.nodes.forEach((node) => {
      node.indeterminate = false;
      if (!node.checked || marked[node.key]) {
        return;
      }

      // 从下往上推，标记父级状态
      let parent = node.parent;
      while (parent) {
        if (!parent.checked) {
          parent.indeterminate = true;
        }
        marked[parent.key] = true;
        parent = parent.parent;
      }
    });
  }

  getCheckedNodes() {
    return this.nodes.filter((node) => node.checked);
  }

  getCheckedPaths() {
    return this.getCheckedNodes().map((node) => node.path);
  }

  getMergedCheckedNodes() {
    return this.getCheckedNodes().filter((node) => {
      if (!node.parent) {
        return true;
      }

      return !get(node, 'parent.checked');
    });
  }

  setCheckedPaths(values?: Path[]) {
    const { nodesMap } = this;
    this.reset();

    if (!Array.isArray(values)) {
      return;
    }
    values.forEach((v) => {
      const key = generateKey(v);
      const node = nodesMap[key];
      if (node) {
        node.checked = true;
      }
    });

    this.markIndeterminate();
  }

  getNodeByPath(path: Path) {
    const key = generateKey(path);
    return this.nodesMap[key] || null;
  }

  getNodeByKey(key: string) {
    return this.nodesMap[key] || null;
  }

  // value 必须是唯一值时才可以使用
  getNodeByValue(value: string | number) {
    return this.nodesValueMap[generateValueKey(value)] || null;
  }

  toJS() {
    return Object.freeze(this.root.map((node) => node.toJS()));
  }
}
