import toFlatMap, { Item } from '../to-flat-map';

describe('toFlatMap', () => {
  // 测试用例1：空数组
  test('should return an empty object when the source is an empty array', () => {
    const source: Item[] = [];
    const expectedResult: Record<string, Item> = {};
    const result = toFlatMap(source);
    expect(result).toEqual(expectedResult);
  });

  // 测试用例2：单个元素数组
  test('should return an object with a single key-value pair when the source contains a single element', () => {
    const source: Item[] = [{ value: 'A', children: [] }];
    const expectedResult: Record<string, Item> = { A: { value: 'A', children: [] } };
    const result = toFlatMap(source);
    expect(result).toEqual(expectedResult);
  });

  // 测试用例3：嵌套数组
  test('should return an object with flattened keys when the source contains nested arrays', () => {
    const source: Item[] = [
      {
        value: 'A',
        children: [
          {
            value: 'B',
            children: [
              {
                value: 'C',
                children: [],
              },
            ],
          },
        ],
      },
    ];
    const expectedResult: Record<string, Item> = {
      A: { value: 'A', children: [{ value: 'B', children: [{ value: 'C', children: [] }] }] },
      B: { value: 'B', children: [{ value: 'C', children: [] }] },
      C: { value: 'C', children: [] },
    };
    const result = toFlatMap(source);
    expect(result).toEqual(expectedResult);
  });

  // 测试用例4：void参数
  test('should return an empty object when the source is undefined or null', () => {
    const source: Item[] | void = undefined;
    const expectedResult: Record<string, Item> = {};
    const result = toFlatMap(source);
    expect(result).toEqual(expectedResult);

    const source2: Item[] | void = null;
    const expectedResult2: Record<string, Item> = {};
    const result2 = toFlatMap(source2);
    expect(result2).toEqual(expectedResult2);
  });
});
