interface ExpiryStorageDataType<T> {
  value: T;
  expiry: number;
}

export default function useStorageWithExpire<T>(storageKey: string, expireTime = 7 * 24 * 60 * 60) {
  const getParseData = (): ExpiryStorageDataType<T> | null => {
    const data = localStorage.getItem(storageKey);
    if (!data) {
      return null;
    }
    return JSON.parse(data);
  };

  const isExpiry = (): boolean => {
    const item = getParseData();
    if (!item) {
      return true;
    }
    return new Date().getTime() > item.expiry;
  };

  const getStorage = (): T | null => {
    const item = getParseData();
    // 已过期则清除数据，返回空
    if (isExpiry()) {
      localStorage.removeItem(storageKey);
      return null;
    }
    return item?.value ?? null;
  };

  const setStorage = (value: T) => {
    const data = getParseData();
    // 如果当前key对应数据未过期，则更新
    if (!isExpiry() && data) {
      localStorage.setItem(storageKey, JSON.stringify({ expiry: data.expiry, value }));
      return;
    }
    // 已过期则重新设置过期时间
    const item: ExpiryStorageDataType<T> = {
      value,
      expiry: new Date().getTime() + expireTime * 1000,
    };
    localStorage.setItem(storageKey, JSON.stringify(item));
  };

  return {
    setStorage,
    getStorage,
  };
}
