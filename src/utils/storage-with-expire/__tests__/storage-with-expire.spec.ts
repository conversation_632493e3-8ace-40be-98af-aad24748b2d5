import useStorageWithExpire from '..';

describe('useStorageWithExpire', () => {
  const key = 'testKey';
  const value = 'testValue';
  const expireTime = 1; // 1 second

  beforeEach(() => {
    localStorage.clear();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test('should set and get storage correctly', () => {
    const { setStorage, getStorage } = useStorageWithExpire<string>(key, expireTime);
    setStorage(value);
    expect(getStorage()).toBe(value);
  });

  test('should return null if storage is expired', () => {
    const { setStorage, getStorage } = useStorageWithExpire<string>(key, expireTime);
    setStorage(value);
    vi.advanceTimersByTime(expireTime * 1000 + 1);
    expect(getStorage()).toBeNull();
  });

  test('should update existing storage if not expired', () => {
    const { setStorage, getStorage } = useStorageWithExpire<string>(key, expireTime);
    setStorage(value);
    const newValue = 'newValue';
    setStorage(newValue);
    expect(getStorage()).toBe(newValue);
  });

  test('should remove item from storage if expired', () => {
    const { setStorage, getStorage } = useStorageWithExpire<string>(key, expireTime);
    setStorage(value);
    vi.advanceTimersByTime(expireTime * 1000 + 1);
    getStorage();
    expect(localStorage.getItem(key)).toBeNull();
  });

  test('should handle non-existent storage gracefully', () => {
    const { getStorage } = useStorageWithExpire<string>(key, expireTime);
    expect(getStorage()).toBeNull();
  });

  test('should handle setting storage with default expire time', () => {
    const { setStorage, getStorage } = useStorageWithExpire<string>(key);
    setStorage(value);
    expect(getStorage()).toBe(value);
  });

  test('should handle expired storage with default expire time', () => {
    const { setStorage, getStorage } = useStorageWithExpire<string>(key);
    setStorage(value);
    vi.advanceTimersByTime(7 * 24 * 60 * 60 * 1000 + 1);
    expect(getStorage()).toBeNull();
  });
});
