/* eslint-disable */
import _ from 'lodash';

export const uuid = (): string => {
  if ('crypto' in window && 'getRandomValues' in window.crypto) {
    // `${1e7}-${1e3}-${4e3}-${8e3}-${1e11}`
    return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, (c) => {
      const n = _.toNumber(c);
      return (n ^ (window.crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (n / 4)))).toString(16);
    });
  }

  let dt = Date.now();

  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (dt + Math.random() * 16) % 16 | 0;
    dt = Math.floor(dt / 16);
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
};
