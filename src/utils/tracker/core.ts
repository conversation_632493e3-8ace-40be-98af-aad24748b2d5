import Adapter, { EventData } from './adapter';
import { uuid } from './utils';

const SESSION_KEY = 'kzz_event_session';

export type PluginOptions = {
  adapters: Adapter[];
  enable?: boolean;
  prefix?: string;
  delay?: number;
  beforeSend?(data: EventData): EventData;
};

export function createTrack(options: PluginOptions) {
  const { adapters, prefix, enable, beforeSend } = options;

  return function track(event: string, data?: EventData) {
    const _event = prefix ? prefix + event : event;
    let _data: EventData = { ...data };

    try {
      const eventSession = window.localStorage.getItem(SESSION_KEY) || undefined;

      if (eventSession) {
        _data.search_keyno = eventSession;
      }
    } catch (e) {
      //
    }

    if (beforeSend) {
      _data = beforeSend(_data);
    }

    if (!_data) {
      return;
    }

    if (!enable && 'toLocaleTimeString' in Date.prototype) {
      console.groupCollapsed(`%cevent: ${_event} ${new Date().toLocaleTimeString('en-US')}`, 'color:#CD6155');
      console.log(_data);
      console.groupEnd();
      return;
    }

    adapters.forEach((adp) => {
      if (adp.track) {
        adp.track(_event, _data);
      }
    });
  };
}

export const updateEventSession = () => {
  try {
    window.localStorage.setItem(SESSION_KEY, uuid());
  } catch (e) {
    //
  }
};
