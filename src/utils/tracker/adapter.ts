// eslint-disable-next-line camelcase
export type EventData = { search_keyno?: string; [key: string]: any };

export type User = {
  id: string;
  nickname: string;
  phone: string;
  groupid: number;
  registTime: string;
  isLogin: number;
  email?: string;
  vipstartdate?: string;
  bundle?: {
    startDate: string;
    serviceName: string;
  };
};

const formatDate = (date: Date) => {
  return new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString().split('T')[0];
};

export const parseUser = (user: User) => {
  if (!user || !user.isLogin) {
    return user;
  }

  const userProps: Record<string, string | number> = {
    name: user.nickname,
    mobile: user.phone,
    groupid: user.groupid,
    email: user.email || '',
    regist_time: user.registTime,
  };

  if (user.vipstartdate) {
    userProps.vip_start_time = user.vipstartdate;
  }

  if (user.bundle) {
    Object.assign(userProps, {
      saas_bundle_name: user.bundle.serviceName,
      saas_bundle_start_time: formatDate(new Date(parseInt(user.bundle.startDate, 10) * 1000)),
    });
  }

  return userProps;
};
abstract class Adapter {
  public abstract track(event: string, data?: EventData): void;

  static identify?(user?: User): void;
}

export default Adapter;
