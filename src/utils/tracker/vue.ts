import { PluginFunction } from 'vue';
import { noop } from 'lodash';

import { PluginOptions, createTrack } from './core';

const install: PluginFunction<PluginOptions> = (Vue, options) => {
  if (!options) {
    throw new Error('missing options');
  }
  const track = createTrack(options);

  // Vue.prototype.$track = track;
  Vue.prototype.$track = noop;

  Vue.directive('track', {
    inserted(el, binding) {
      const { eventName, ...data } = binding.value || {};
      el.addEventListener('click', () => {
        track(eventName, data);
      });
    },
  });
};

export default install;
