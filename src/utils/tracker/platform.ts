import _ from 'lodash';
import cookie from 'js-cookie';

import { uuid } from './utils';
import Adapter, { EventData } from './adapter';

class PlatformAdapter implements Adapter {
  private api: string;

  private appName: string;

  constructor(appName: string, api: string) {
    this.appName = appName;
    this.api = api;
  }

  static identify() {
    const deviceId = cookie.get('qcc_did');

    if (!deviceId) {
      const domain = _.get(window.location.hostname.match(/(greatld|qcc)\.com/), '1');

      if (domain) {
        cookie.set('qcc_did', uuid(), {
          expires: 1000,
          path: '/',
          domain: `.${domain}.com`,
        });
      }
    }
  }

  public track(event: string, data: EventData) {
    const payload = {
      appName: this.appName,
      event,
      entity: JSON.stringify(data),
      ref: document.referrer,
    };
    const params = _.compact(
      _.map(payload, (v, k) => {
        return v ? `${k}=${encodeURIComponent(v)}` : undefined;
      })
    ).join('&');

    if (params) {
      const img = new Image();
      img.src = `${this.api}?${params}`;
    }
  }
}

export default PlatformAdapter;
