import CompanyDefault from '@/assets/images/default.png';

/**
 * @param {string} id
 * @param {number} hasimage
 */
export const formatCompanyLogoUrl = (id?: string, hasimage?: number) => {
  if (id) {
    if (hasimage) {
      return `//image.qcc.com/logo/${id}.jpg?x-oss-process=style/logo_120`;
    }
    return `//image.qcc.com/auto/${id}.jpg?x-oss-process=style/logo_120`;
  }
  return CompanyDefault;
};

enum ColorsEnum {
  Default = '#999',
  Positive = '#0aad65',
  Negative = '#F04040',
}

export const getColor = (text: string): { color: ColorsEnum } => {
  const colorMap = new Map<string, ColorsEnum>([
    // 正面
    ['支持', ColorsEnum.Positive],
    ['解除财产保全', ColorsEnum.Positive],
    ['对方被驳回', ColorsEnum.Positive],
    ['执行完毕', ColorsEnum.Positive],
    ['申请人被驳回', ColorsEnum.Positive],
    ['部分支持', ColorsEnum.Positive],
    ['同意追加被执行人', ColorsEnum.Positive],
    // 负面
    ['驳回', ColorsEnum.Negative],
    ['不支持', ColorsEnum.Negative],
    ['驳回上诉', ColorsEnum.Negative],
    ['财产保全', ColorsEnum.Negative],
    ['终结本次执行', ColorsEnum.Negative],
  ]);

  return {
    color: colorMap.get(text) || ColorsEnum.Default,
  };
};

export const getStyleByJob = (job: string) => {
  const jobStyleMap = new Map([
    [
      '企业主体',
      {
        color: '#666',
        backgroundColor: '#eee',
      },
    ],
    [
      'default',
      {
        backgroundColor: '#f6f0e7',
        color: '#bb833d',
      },
    ],
  ]);
  return jobStyleMap.get(job) || jobStyleMap.get('default');
};
