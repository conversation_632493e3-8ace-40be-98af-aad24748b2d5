import { formatCompanyLogoUrl, getColor, getStyleByJob } from '..';

vi.mock('@/shared/services');

describe('CompanyFormat', () => {
  describe('formatCompanyLogoUrl', () => {
    // 测试带有id和 hasimage 的情况
    test('should return the correct url when id and hasimage are provided', () => {
      const id = '12345';
      const hasimage = 1;
      const expectedUrl = `//image.qcc.com/logo/${id}.jpg?x-oss-process=style/logo_120`;
      expect(formatCompanyLogoUrl(id, hasimage)).toBe(expectedUrl);
    });

    // 测试只带有 id 的情况
    test('should return the correct url when only id is provided', () => {
      const id = '12345';
      const expectedUrl = `//image.qcc.com/auto/${id}.jpg?x-oss-process=style/logo_120`;
      expect(formatCompanyLogoUrl(id)).toBe(expectedUrl);
    });

    // 测试没有提供 id 的情况
    test('should return the default company url when no id is provided', () => {
      vi.mock('@/assets/images/default.png', () => ({
        default: '/mock/default.png', // ES模块需通过default属性模拟
      }));
      const expectedUrl = '/mock/default.png';
      expect(formatCompanyLogoUrl()).toBe(expectedUrl);
    });
  });

  describe('getColor', () => {
    test('should return green color for supported texts', () => {
      const texts = ['支持', '解除财产保全', '对方被驳回', '执行完毕', '申请人被驳回', '部分支持', '同意追加被执行人'];
      texts.forEach((text) => {
        expect(getColor(text)).toEqual({ color: '#0aad65' });
      });
    });

    test('should return red color for rejected texts', () => {
      const texts = ['驳回', '不支持', '驳回上诉', '财产保全', '终结本次执行'];
      texts.forEach((text) => {
        expect(getColor(text)).toEqual({ color: '#F04040' });
      });
    });

    test('should return gray color for other texts', () => {
      const texts = ['other text', 'random text', 'any text'];
      texts.forEach((text) => {
        expect(getColor(text)).toEqual({ color: '#999' });
      });
    });
  });

  describe('getStyleByJob', () => {
    test('should return the correct style for "企业主体"', () => {
      const job = '企业主体';
      const expectedStyle = {
        color: '#666',
        backgroundColor: '#eee',
      };
      expect(getStyleByJob(job)).toEqual(expectedStyle);
    });

    test('should return the default style for any other job', () => {
      const job = 'Other Job';
      const expectedStyle = {
        backgroundColor: '#f6f0e7',
        color: '#bb833d',
      };
      expect(getStyleByJob(job)).toEqual(expectedStyle);
    });
  });
});
