import { fromNow } from '../date';

describe('date', () => {
  afterEach(() => {
    vi.clearAllTimers();
  });

  test('returns formatted date if the date is before today', () => {
    const dateString = '2021-01-01';
    const format = 'YYYY-MM-DD';
    const result = fromNow(dateString, format);
    expect(result).toBe('2021-01-01');
  });

  test('returns "1 分钟前" if the date is in the future', () => {
    const dateString = '2024-06-20 16:00:00';
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    vi.useFakeTimers({
      now: new Date(dateString),
    });
    vi.advanceTimersByTime(1000 * 60);

    const result = fromNow(dateString, dateFormat);
    expect(result).toBe('1 分钟前');
  });

  test('returns number of hours ago if the date is more than 1 hour ago', () => {
    const dateString = '2024-06-20 16:00:00';
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    vi.useFakeTimers({
      now: new Date(dateString),
    });
    vi.advanceTimersByTime(1000 * 60 * 60);

    const result = fromNow(dateString, dateFormat);
    expect(result).toBe('1 小时前');
  });

  test('returns number of minutes ago if the date is more than 1 minute ago but less than 1 hour ago', () => {
    const dateString = '2024-06-20 16:00:00';
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    vi.useFakeTimers({
      now: new Date(dateString),
    });
    vi.advanceTimersByTime(1000 * 60 * 5);

    const result = fromNow(dateString, dateFormat);
    expect(result).toBe('5 分钟前');
  });

  test('returns number of seconds ago if the date is less than 1 minute ago', () => {
    const dateString = '2024-06-20 16:00:00';
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    vi.useFakeTimers({
      now: new Date(dateString),
    });
    vi.advanceTimersByTime(1000 * 10);

    const result = fromNow(dateString, dateFormat);
    expect(result).toBe('10 秒前');
  });
});
