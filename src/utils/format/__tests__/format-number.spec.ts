import { formatNumber } from '../format-number';

describe('formatNumber', () => {
  test('should format a number without options', () => {
    expect(formatNumber(123456789)).toBe('123,456,789');
  });

  test('should format a number with separator option', () => {
    expect(formatNumber(123456789, { separator: ',' })).toBe('123,456,789');
    expect(formatNumber(123456789, { separator: '.' })).toBe('123.456.789');
  });

  test('should format a number with decimal part', () => {
    expect(formatNumber(123456789.123)).toBe('123,456,789.123');
    expect(formatNumber(123456789.123, { separator: ',' })).toBe('123,456,789.123');
  });

  test('should handle numbers that are not integers', () => {
    expect(formatNumber(1234.567)).toBe('1,234.567');
    expect(formatNumber(1234.567, { separator: ',' })).toBe('1,234.567');
  });

  test('should return an empty string for zero', () => {
    expect(formatNumber(0)).toBe('0');
  });
});
