import numberFormat from '../number-format';

describe('numberFormat', () => {
  test('should return default value when input is empty and division is not -3', () => {
    expect(numberFormat('', 2, 1, '-')).toBe('-');
  });

  test('should format number with division -1', () => {
    expect(numberFormat('1234567890', 2, -1)).toBe('1.23G');
  });

  test('should format number with division -2', () => {
    expect(numberFormat('12345', 2, -2)).toBe('1.23万元');
  });

  test('should format number with division -3', () => {
    expect(numberFormat('12345', 2, -3)).toBe('<b class="font-16">1.23</b> <span class="text-gray">万元</span>');
  });

  test('should format number with custom division', () => {
    expect(numberFormat('1234', 2, 1000)).toBe('1.23');
  });

  test('should handle exception and return default value', () => {
    expect(numberFormat('abc', 2, 1, '-')).toBe('NaN');
  });
});
