// 123.456人民币
// export const formatRawAmt = (amt: string): string => {
//   if (!_.isString(amt)) {
//     return '';
//   }
//   const match = amt.match(/([\d.]+)(.*)/);

//   if (!match) {
//     return amt;
//   }
// };

export default function numberFormat(str, pattern = 2, division = 1, defaultVal = '-') {
  if (!str && division !== -3) {
    return defaultVal;
  }
  let number;
  let unit;
  try {
    if (division === -1) {
      number = parseFloat(str.replace(/,/g, ''));
      const absNumber = Math.abs(number);
      if (absNumber > 1000000000) {
        number /= 1000000000;
        unit = 'G';
      } else if (absNumber > 1000000) {
        number /= 1000000;
        unit = 'M';
      } else if (absNumber > 1000) {
        number /= 1000;
        unit = 'K';
      }
    } else if (division === -2 || division === -3) {
      number = parseFloat(str);
      const absNumber = Math.abs(number);
      if (absNumber > 10000) {
        number /= 10000;
        unit = '万元';
      } else {
        unit = '元';
      }
    } else {
      number = parseFloat(str);
      if (division !== 1) {
        number /= division;
      }
    }
    number = number.toFixed(pattern);
  } catch (e) {
    console.log(e);
  }
  const tmp = `${number}`;
  let formatNumber = tmp.replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');

  if (unit) {
    if (division === -3) {
      formatNumber = `<b class="font-16">${formatNumber}</b> <span class="text-gray">${unit}</span>`;
    } else {
      formatNumber += unit;
    }
  }
  return formatNumber;
}
