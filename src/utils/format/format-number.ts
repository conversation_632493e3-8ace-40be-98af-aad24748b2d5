interface Options {
  separator?: string;
}

export function formatNumber(num: number, options?: Options): string {
  // 先将数字转成字符串，方便处理
  const strNum = num.toString();

  // 获取分隔符
  const separator = options?.separator ?? ',';

  // 将整数部分和小数部分分别处理
  const [intPart, decimalPart] = strNum.split('.');

  // 处理整数部分
  let result = '';
  let count = 0;
  for (let i = intPart.length - 1; i >= 0; i--) {
    count++;
    result = intPart[i] + result;
    if (count === 3 && i !== 0) {
      result = separator + result;
      count = 0;
    }
  }

  // 处理小数部分
  if (decimalPart !== undefined) {
    result += '.';
    for (let i = 0; i < decimalPart.length; i++) {
      result += decimalPart[i];
    }
  }

  return result;
}
