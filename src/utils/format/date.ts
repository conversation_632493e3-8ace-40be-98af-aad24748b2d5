import _ from 'lodash';
import moment from 'moment';

// tPattern? = 'YYYY-MM-DD'; defaultVal? = '-'; tX1000?: boolean

export const fromNow = (dateString: string | number, format?: string) => {
  const date = moment(dateString, format);
  if (!dateString || !date.isValid()) {
    return '-';
  }
  const now = new Date();
  if (date.isBefore(now, 'day')) {
    return date.format('YYYY-MM-DD');
  }
  // NOTE: 发布日期如果在未来, 特殊处理为 `1 分钟前`
  if (date.isAfter(now)) {
    return `1 分钟前`;
  }
  // NOTE: `diff` 取精确值方法小于1会直接返回0
  const durationBySeconds = moment(now).diff(date, 'seconds');
  // 大于1小时，显示小时
  if (durationBySeconds >= 3600) {
    return `${Math.floor(durationBySeconds / 3600)} 小时前`;
  }
  if (durationBySeconds >= 60) {
    return `${Math.floor(durationBySeconds / 60)} 分钟前`;
  }
  return `${durationBySeconds} 秒前`;
};

export const autoFormat = (value: string | number, format?: string) => {
  if (!value) {
    return undefined;
  }

  const date = moment(value, format);
  if (!date.isValid()) {
    return undefined;
  }

  if (date.hour() === 0 && date.minute() === 0) {
    return date.format('YYYY-MM-DD');
  }

  return date.format('YYYY-MM-DD HH:mm');
};

const formatDate = (
  dateStr?: string | number,
  options: Record<string, any> = {
    pattern: 'YYYY-MM-DD',
    defaultVal: '-',
    x1000: false,
  }
): string => {
  const { pattern, defaultVal, x1000 } = options || {};
  const s = _.trim(_.toString(dateStr));
  let format: string | undefined;

  // fix: 增加formatDate对于返回值为’-‘的兼容处理，解决momentjs对‘-’解析失败而显示 Invalid date 的问题
  if (_.isNil(dateStr) || !s || ['-', '0'].includes(s) || !pattern) {
    return defaultVal;
  }

  if (x1000) {
    format = 'X';
  }
  // 20051217
  else if (/^(\d){4}[0|1]\d[0|1|2|3]\d$/.test(s)) {
    format = 'YYYYMMDD';
  }
  // 秒
  else if (/^(\d){1,10}$/.test(s)) {
    format = 'X';
  }
  // 毫秒
  else if (/^(\d){11,}$/.test(s)) {
    format = 'x';
  }

  return moment(s, format).format(pattern);
};

export default formatDate;
