import moment from 'moment';

export const defaultDisabledDate = (date: moment.Moment) => date.isAfter(moment(), 'day');

export const fromNow = (dateTime: string | number, format?: string) => {
  if (!dateTime || dateTime === '-') {
    return '-';
  }
  const ts = dateTime?.toString()?.length <= 10 ? Number(dateTime) * 1000 : Number(dateTime);
  const publishedAt = moment(ts, format);
  if (!publishedAt.isValid()) {
    return '-';
  }
  const now = new Date();
  if (publishedAt.isBefore(now, 'day')) {
    return publishedAt.format('YYYY-MM-DD');
  }
  // NOTE: 发布日期如果在未来, 特殊处理为 `1 分钟前`
  if (publishedAt.isAfter(now)) {
    return `1 分钟前`;
  }
  // NOTE: `diff` 取精确值方法小于1会直接返回0
  const durationBySeconds = moment(now).diff(publishedAt, 'seconds');
  // 大于1小时，显示小时
  if (durationBySeconds >= 3600) {
    return `${Math.floor(durationBySeconds / 3600)} 小时前`;
  }
  if (durationBySeconds >= 60) {
    return `${Math.floor(durationBySeconds / 60)} 分钟前`;
  }
  return `${durationBySeconds} 秒前`;
};
