import moment from 'moment';

import { defaultDisabledDate, fromNow } from '..';

describe('date', () => {
  describe('defaultDisabledDate', () => {
    it('应该返回 true 当日期是今天之后的日期', () => {
      const futureDate = moment().add(1, 'day');
      expect(defaultDisabledDate(futureDate)).toBe(true);
    });

    it('应该返回 false 当日期是今天或之前的日期', () => {
      const today = moment();
      const pastDate = moment().subtract(1, 'day');
      expect(defaultDisabledDate(today)).toBe(false);
      expect(defaultDisabledDate(pastDate)).toBe(false);
    });
  });

  describe('fromNow', () => {
    it('应该返回 "-" 当 dateTime 为空或 "-"', () => {
      expect(fromNow('')).toBe('-');
      expect(fromNow('-')).toBe('-');
    });

    it('应该返回 "-" 当 dateTime 不是有效的时间戳', () => {
      expect(fromNow('invalid-timestamp')).toBe('-');
    });

    it('应该返回格式化日期 当 dateTime 是过去的日期', () => {
      const pastDate = moment().subtract(1, 'day').valueOf();
      expect(fromNow(pastDate)).toBe(moment(pastDate).format('YYYY-MM-DD'));
    });

    it('应该返回 "1 分钟前" 当 dateTime 是未来的日期', () => {
      const futureDate = moment().add(1, 'day').valueOf();
      expect(fromNow(futureDate)).toBe('1 分钟前');
    });

    it('应该返回 "X 小时前" 当 dateTime 是 1 小时到 24 小时之间的日期', () => {
      const oneHourAgo = moment().subtract(1, 'hour').valueOf();
      const res = fromNow(oneHourAgo);
      expect(res).toBe('1 小时前');
    });

    it('应该返回 "X 分钟前" 当 dateTime 是 1 分钟到 59 分钟之间的日期', () => {
      const oneMinuteAgo = moment().subtract(1, 'minute').valueOf();
      expect(fromNow(oneMinuteAgo)).toBe('1 分钟前');
    });

    it('应该返回 "X 秒前" 当 dateTime 是 1 秒到 59 秒之间的日期', () => {
      const oneSecondAgo = moment().subtract(1, 'second').valueOf();
      expect(fromNow(oneSecondAgo)).toBe('1 秒前');
    });

    it('应该正确处理 10 位时间戳', () => {
      const tenDigitTimestamp = Math.floor(Date.now() / 1000);
      expect(fromNow(tenDigitTimestamp)).not.toBe('-');
    });

    it('应该正确处理 13 位时间戳', () => {
      const thirteenDigitTimestamp = Date.now();
      expect(fromNow(thirteenDigitTimestamp)).not.toBe('-');
    });
  });
});
