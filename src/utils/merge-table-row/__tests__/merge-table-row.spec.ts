import mergeTableRow from '../merge-table-row';

describe('mergeTableRow', () => {
  test('正常合并带有单层字段的数据', () => {
    const input = [
      { id: 1, category: 'A' },
      { id: 2, category: 'B' },
      { id: 3, category: 'A' },
    ];
    const expectedOutput = [
      {
        _category: {
          attrs: {
            rowSpan: 2,
          },
          children: 'A',
        },
        _index: {
          attrs: {
            rowSpan: 2,
          },
          children: 1,
        },
        category: 'A',
        id: 1,
      },
      {
        _category: {
          attrs: {
            rowSpan: 0,
          },
        },
        _index: {
          attrs: {
            rowSpan: 0,
          },
        },
        category: 'A',
        id: 3,
      },
      {
        _category: {
          attrs: {
            rowSpan: 1,
          },
          children: 'B',
        },
        _index: {
          attrs: {
            rowSpan: 1,
          },
          children: 2,
        },
        category: 'B',
        id: 2,
      },
    ];
    expect(mergeTableRow(input, ['category'], 0)).toEqual(expectedOutput);
  });

  test('空输入数组', () => {
    const input: any[] = [];
    const expectedOutput: any[] = [];
    expect(mergeTableRow(input, ['category'], 0)).toEqual(expectedOutput);
  });

  test('没有级别字段的输入', () => {
    const input = [
      { id: 1, category: 'A' },
      { id: 2, category: 'B' },
    ];
    const expectedOutput = [
      { id: 1, category: 'A' },
      { id: 2, category: 'B' },
    ];
    expect(mergeTableRow(input, [], 0)).toEqual(expectedOutput);
  });

  test('只有一个分组且没有其他重复的字段', () => {
    const input = [
      { id: 1, category: 'A' },
      { id: 2, category: 'A' },
    ];
    const expectedOutput = [
      {
        _category: {
          attrs: {
            rowSpan: 2,
          },
          children: 'A',
        },
        _index: {
          attrs: {
            rowSpan: 2,
          },
          children: 1,
        },
        category: 'A',
        id: 1,
      },
      {
        _category: {
          attrs: {
            rowSpan: 0,
          },
        },
        _index: {
          attrs: {
            rowSpan: 0,
          },
        },
        category: 'A',
        id: 2,
      },
    ];
    expect(mergeTableRow(input, ['category'], 0)).toEqual(expectedOutput);
  });

  test('多个级别的字段', () => {
    const input = [
      { id: 1, category: 'A', subCategory: 'X' },
      { id: 2, category: 'A', subCategory: 'Y' },
      { id: 3, category: 'B', subCategory: 'X' },
    ];
    const expectedOutput = [
      {
        _category: {
          attrs: {
            rowSpan: 2,
          },
          children: 'A',
        },
        _index: {
          attrs: {
            rowSpan: 2,
          },
          children: 1,
        },
        _subCategory: {
          attrs: {
            rowSpan: 1,
          },
          children: 'X',
        },
        category: 'A',
        id: 1,
        subCategory: 'X',
      },
      {
        _category: {
          attrs: {
            rowSpan: 0,
          },
        },
        _index: {
          attrs: {
            rowSpan: 0,
          },
        },
        _subCategory: {
          attrs: {
            rowSpan: 1,
          },
          children: 'Y',
        },
        category: 'A',
        id: 2,
        subCategory: 'Y',
      },
      {
        _category: {
          attrs: {
            rowSpan: 1,
          },
          children: 'B',
        },
        _index: {
          attrs: {
            rowSpan: 1,
          },
          children: 2,
        },
        _subCategory: {
          attrs: {
            rowSpan: 1,
          },
          children: 'X',
        },
        category: 'B',
        id: 3,
        subCategory: 'X',
      },
    ];
    expect(mergeTableRow(input, ['category', 'subCategory'], 0)).toEqual(expectedOutput);
  });
});
