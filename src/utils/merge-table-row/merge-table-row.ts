/* eslint-disable @typescript-eslint/ban-types */
import * as _ from 'lodash';
import { VNode } from 'vue';

const mergeTableRow = <T extends object>(data: T[], levelFields: string[], topIndex?: number): T[] => {
  if (!levelFields.length) {
    return data;
  }
  // 保留原有排序
  const field = levelFields[0];
  const orders: string[] = [];

  data.forEach((v) => {
    const value = v[field];
    if (!orders.includes(value)) {
      orders.unshift(value);
    }
  });

  const result: T[] = [];
  const groups = _.map(_.groupBy(data, field), (g) => g).sort((x, y) => orders.indexOf(y[0][field]) - orders.indexOf(x[0][field]));

  groups.forEach((group, index) => {
    let next = group;
    if (levelFields.length > 1) {
      next = mergeTableRow(group, levelFields.slice(1)) as [T, ...T[]];
    }

    next.forEach((v, i, a) => {
      if (i === 0) {
        if (_.isNumber(topIndex)) {
          // 加顶层索引
          _.set(v, '_index', {
            children: topIndex + index + 1,
            attrs: {
              rowSpan: a.length,
            },
          });
        }

        _.set(v, `_${field}`, {
          children: v[field],
          attrs: {
            rowSpan: a.length,
          },
        });
      } else {
        if (_.isNumber(topIndex)) {
          // 加顶层索引
          _.set(v, '_index', {
            attrs: {
              rowSpan: 0,
            },
          });
        }
        _.set(v, `_${field}`, {
          attrs: {
            rowSpan: 0,
          },
        });
      }
    });

    result.push(...next);
  });

  return result;
};

type renderFunc = (data, record, index) => VNode | string | number;

export const renderCellData = (data) => data;
export const renderShareCellData = (field: string | renderFunc) => (data, record, index) => {
  if (!data.children) {
    return { ...data };
  }

  let children;

  if (_.isFunction(field)) {
    children = field(data, record, index);
  } else {
    children = record[field];
  }
  return { ...data, children };
};

export default mergeTableRow;
