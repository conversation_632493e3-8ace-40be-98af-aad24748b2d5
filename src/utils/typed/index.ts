import * as _ from 'lodash';

type ReduceCallback<T, TResult, TList> = (prev: TResult, curr: T, key: keyof TList, list: TList) => TResult;
type ForeachCallback<T, TList> = (value: T, key: keyof TList, list: TList) => void;

export const typedReduce = <T extends object, TResult>(
  collection: T,
  callback: ReduceCallback<T[keyof T], TResult, T>,
  accumulator: TResult
): TResult => {
  if (!_.isPlainObject(collection)) {
    return accumulator;
  }

  return (Object.keys(collection) as Array<keyof T>).reduce((r, k) => {
    return callback(r, collection[k], k, collection);
  }, accumulator);
};

export const typedForEach = <T extends object>(collection: T, callback: ForeachCallback<T[keyof T], T>): void => {
  if (!_.isPlainObject(collection)) {
    return;
  }

  (Object.keys(collection) as Array<keyof T>).forEach((k) => {
    callback(collection[k], k, collection);
  });
};
