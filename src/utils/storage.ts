import _ from 'lodash';

const prefix = 'kzz_';
const idPrefix = `${prefix}id_`;
const disabled = typeof window === 'undefined' || !window.localStorage;

let userId: number;

const genKey = (key: string, id?: any) => {
  if (id === undefined) {
    id = userId;
  }
  if (id) {
    return `${idPrefix}${id}_${key}`;
  }

  return prefix + key;
};

export const init = (id: number) => {
  userId = id;
};

export const getItem = (key: string, defaultValue: any = null) => {
  if (disabled) {
    return defaultValue;
  }
  let v = localStorage.getItem(genKey(key));

  if (_.isNil(v)) {
    v = localStorage.getItem(genKey(key, null));
    if (_.isNil(v)) {
      return defaultValue;
    }
  }
  try {
    return JSON.parse(v);
  } catch (e) {
    return defaultValue;
  }
};

export const setItem = (key: string, value) => {
  if (disabled) {
    return null;
  }

  try {
    return localStorage.setItem(genKey(key), JSON.stringify(value));
  } catch (e) {
    return null;
  }
};

export const removeItem = (key: string) => {
  if (disabled) {
    return;
  }

  // eslint-disable-next-line consistent-return
  return localStorage.removeItem(genKey(key));
};
