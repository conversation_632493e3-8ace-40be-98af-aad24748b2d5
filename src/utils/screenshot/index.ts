import html2canvas from 'html2canvas';

export function downloadDataURL(dataURL: string, fileName: string) {
  const link = document.createElement('a');
  if (typeof link.download !== 'string') {
    window.open(dataURL);
  } else {
    link.href = dataURL;
    link.download = fileName;
    link.click();
  }
}

export async function createScreenshot(el: HTMLElement, format = 'image/png') {
  const clientRect = el?.getBoundingClientRect?.();
  const canvas = await html2canvas(el, {
    useCORS: true,
    backgroundColor: null, // 透明背景
    allowTaint: true,
    foreignObjectRendering: true,
    removeContainer: false,
    // 图片长宽
    windowWidth: clientRect.width,
    windowHeight: clientRect.height,
    // 计算偏移量
    x: clientRect.x * -1,
    y: clientRect.y * -1,
    scrollX: 0,
    scrollY: 0,
    // 放大
    scale: window.devicePixelRatio > 2 ? window.devicePixelRatio : 2,
    ignoreElements: (element) => {
      return element.classList.value.includes('screen-ignore') || ['button', 'svg'].includes(element.tagName.toLowerCase());
    },
  });
  return canvas.toDataURL(format, 1).replace(format, 'image/octet-stream');
}

export async function downloadScreenshot(el: HTMLElement, filename = `${Date.now()}.png`, format = 'image/png') {
  const dataURL = await createScreenshot(el, format);
  downloadDataURL(dataURL, filename);
}
