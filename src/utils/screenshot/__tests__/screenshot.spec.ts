import { downloadDataURL } from '..';

describe('Screenshot', () => {
  describe('downloadDataURL', () => {
    let link: HTMLAnchorElement;
    beforeEach(() => {
      link = document.createElement('a');
      vi.spyOn(document, 'createElement').mockReturnValue(link);
      // windowOpen = vi.spyOn(window, 'open');
    });
    afterEach(() => {
      vi.restoreAllMocks();
    });

    test('should open the URL in a new window if download property is not supported', () => {
      const dataURL = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA';
      const fileName = 'test.png';
      const linkClickFn = vi.spyOn(link, 'click');

      downloadDataURL(dataURL, fileName);

      expect(link.href).toBe(dataURL);
      expect(link.download).toBe(fileName);
      expect(linkClickFn).toHaveBeenCalled();
    });
  });
});
