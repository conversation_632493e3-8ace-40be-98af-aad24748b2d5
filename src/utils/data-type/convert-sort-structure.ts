/**
 * 转换排序数据结构
 * @param antdSorter
 * @example { data, total, pageIndex, pageSize } => { data, pagination: { total, pageIndex, pageSize }}
 */
export function convertSortStructure(antdSorter?: { columnKey: string; field?: string; order: string; column: unknown[] }): {
  sortField: string | undefined; // 排序字段
  isSortAsc: boolean | undefined; // 是否升序
  sortOrder?: 'ASC' | 'DESC';
} {
  const defaults = {
    sortField: undefined,
    isSortAsc: undefined,
    sortOrder: undefined,
  };
  if (!antdSorter) {
    return defaults;
  }

  const { columnKey, order } = antdSorter;
  if (!columnKey || !order) {
    return defaults;
  }

  return {
    sortField: columnKey,
    isSortAsc: order === 'ascend',
    sortOrder: order === 'ascend' ? 'ASC' : 'DESC',
  };
}
