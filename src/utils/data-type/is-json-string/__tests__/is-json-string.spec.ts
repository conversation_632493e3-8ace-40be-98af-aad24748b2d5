import { isJSONString } from '..';

describe('isJSONString', () => {
  test('should return false for non-string inputs', () => {
    expect(isJSONString(null)).toBe(false);
    expect(isJSONString(undefined)).toBe(false);
    expect(isJSONString(123)).toBe(false);
  });

  test('should return true for valid JSON strings', () => {
    expect(isJSONString('{"name":"<PERSON>","age":30}')).toBe(true);
    expect(isJSONString('["item1","item2","item3"]')).toBe(true);
    expect(isJSONString('{"key": "value"}')).toBe(true);
    expect(isJSONString('[]')).toBe(true);
    expect(isJSONString('{}')).toBe(true);
  });

  test('should return false for invalid JSON strings', () => {
    expect(isJSONString('{name:"<PERSON>",age:30}')).toBe(false);
    expect(isJSONString('["item1","item2",]')).toBe(false);
    expect(isJSONString('{"key": "value"')).toBe(false);
    expect(isJSONString('[')).toBe(false);
    expect(isJSONString('{')).toBe(false);
    expect(isJSONString('')).toBe(false);
    expect(isJSONString('not a JSON string')).toBe(false);
  });
});
