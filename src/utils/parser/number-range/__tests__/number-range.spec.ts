import { numberRangeToLabel, numberRangeToSource, numberRangeToValue } from '..';

describe('numberRangeToValue', () => {
  test(`should return an array with min and max values when source is a plain object with min and max properties`, () => {
    const source = { max: 10, min: 1 };
    const expected = [1, 10];
    expect(numberRangeToValue(source)).toEqual(expected);
  });

  test('should return an empty array when source is not a plain object', () => {
    const source = [1, 2, 3];
    const expected = [];
    expect(numberRangeToValue(source as any)).toEqual(expected);
  });
});

describe('numberRangeToSource', () => {
  test('returns undefined when value is not an array', () => {
    const result = numberRangeToSource(null);
    expect(result).toBeUndefined();

    const result2 = numberRangeToSource({} as any); // or any non-array value
    expect(result2).toBeUndefined();
  });

  test('returns undefined when both min and max are nil', () => {
    const result = numberRangeToSource([undefined, undefined]);
    expect(result).toBeUndefined();
  });

  test('returns correct source object when min and max are valid numbers', () => {
    const result = numberRangeToSource([1, 5]);
    expect(result).toEqual({ min: 1, max: 5 });
  });

  test('returns source object with min and max as undefined when respective values are not numbers', () => {
    const result = numberRangeToSource(['a', 5] as any);
    expect(result).toEqual({ min: undefined, max: 5 });

    const result2 = numberRangeToSource([1, 'b'] as any);
    expect(result2).toEqual({ min: 1, max: undefined });
  });
});

describe('numberRangeToLabel', () => {
  test('should return an empty string if source is not a plain object', () => {
    const source = null;
    const result = numberRangeToLabel(source);
    expect(result).toBe('');
  });

  test('should return an empty string if source does not have min and max properties', () => {
    const source = { min: undefined, max: undefined };
    const result = numberRangeToLabel(source);
    expect(result).toBe('');
  });

  test('should return a string with min and max values if both are present', () => {
    const source = { min: 1, max: 10 };
    const result = numberRangeToLabel(source);
    expect(result).toBe('1至10');
  });

  test('should return a string with min value if max is undefined', () => {
    const source = { min: 1, max: undefined };
    const result = numberRangeToLabel(source);
    expect(result).toBe('1以上');
  });

  test('should return a string with max value if min is undefined', () => {
    const source = { min: undefined, max: 10 };
    const result = numberRangeToLabel(source);
    expect(result).toBe('10以下');
  });

  test('should append the unit to the min and max values if provided', () => {
    const source = { min: 1, max: 10 };
    const props = { unit: 'kg' };
    const result = numberRangeToLabel(source, props);
    expect(result).toBe('1kg至10kg');
  });
});
