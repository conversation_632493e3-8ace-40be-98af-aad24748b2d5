import _ from 'lodash';

import { Parser } from '../interface';

type Source = { max?: number; min?: number };
type Value = Array<number | undefined>;

export const numberRangeToValue = (source: Source): Value => {
  if (_.isPlainObject(source)) {
    const { max, min } = source;
    return [min, max];
  }

  return [];
};

export const numberRangeToSource = (value: Value): Source | undefined => {
  if (!Array.isArray(value)) {
    return undefined;
  }
  const [min, max] = value.map((v) => (!_.isNumber(v) ? undefined : v));
  if (_.isNil(min) && _.isNil(max)) {
    return undefined;
  }

  return { min, max };
};

export const numberRangeToLabel = (source: Source, props?: { unit?: string }): string => {
  if (!_.isPlainObject(source)) {
    return '';
  }
  const unit = _.get(props, 'unit', '');
  const [min, max] = [source.min, source.max].map((v) => (_.isNumber(v) ? `${v}${unit}` : undefined));
  if (min && max) {
    return `${min}至${max}`;
  }
  if (min) {
    return `${min}以上`;
  }
  if (max) {
    return `${max}以下`;
  }

  return '';
};

export const numberRangeParser = {
  toValue: numberRangeToValue,
  toSource: numberRangeToSource,
  toLabel: numberRangeToLabel,
} as Parser<Source, Value>;
