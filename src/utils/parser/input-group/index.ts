import _ from 'lodash';

import type { Parser } from '../interface';

type Data = {
  value: string[];
};

const noop = (v: Data): Data => v;

export const inputGroupToValue = noop;
export const inputGroupToSource = noop;
export const inputGroupToLabel = (source: Data) => {
  if (!_.get(source, 'value.length')) {
    return '';
  }

  return _.uniq(_.compact(source.value.map(_.trim))).join(',');
};

export const inputGroupParser = {
  toValue: inputGroupToValue,
  toSource: inputGroupToSource,
  toLabel: inputGroupToLabel,
} as Parser<Data, Data>;
