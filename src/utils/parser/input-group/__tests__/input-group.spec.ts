import { inputGroupToLabel, inputGroupToValue } from '..';

describe('inputGroupToLabel', () => {
  test('should return an empty string if the value property is not present', () => {
    const source = { value: undefined };
    expect(inputGroupToLabel(source)).toEqual('');
  });

  test('should return an empty string if the value property is null', () => {
    const source = { value: null };
    expect(inputGroupToLabel(source)).toEqual('');
  });

  test('should return an empty string if the value property is an empty array', () => {
    const source = { value: [] };
    expect(inputGroupToLabel(source)).toEqual('');
  });

  test('should return a comma-separated string of trimmed unique values', () => {
    const source = {
      value: ['  foo  ', '  bar  ', '  foo  ', '  baz  ', '  '],
    };
    expect(inputGroupToLabel(source)).toEqual('foo,bar,baz');
  });
});

describe('noop function', () => {
  test('should return the same data passed as argument', () => {
    const data: any = { test: 'test data' };
    const result = inputGroupToValue(data);
    expect(result).toBe(data);
  });
});
