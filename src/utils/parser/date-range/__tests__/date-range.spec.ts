import moment from 'moment';

import { dateRangeToLabel, dateRangeToSource, daterRangeToValue } from '..';

describe('daterRangeToValue', () => {
  test('should return empty array when source is not a plain object or does not have flag property', () => {
    const source = { flag: 3, max: '2021-01-01', min: '2020-12-31' };
    const result = daterRangeToValue(source);
    expect(result).toEqual([]);
  });

  test('should return empty array when source does not have max or min property', () => {
    const source = { flag: 5, max: '', min: '' };
    const result = daterRangeToValue(source);
    expect(result).toEqual([undefined, undefined]);
  });

  test('should return array with ISO string when max and min are valid dates', () => {
    const source = { flag: 5, max: '2021-01-01', min: '2020-12-31' };
    const expected = [moment('2020-12-31').toISOString(), moment('2021-01-01').toISOString()];
    const result = daterRangeToValue(source);
    expect(result).toEqual(expected);
  });

  test('should return array with undefined when max or min are invalid dates', () => {
    const source = { flag: 5, max: 'invalid-date', min: '2020-12-31' };
    const result = daterRangeToValue(source);
    expect(result).toEqual(['2020-12-30T16:00:00.000Z', undefined]);
  });

  test('should handle null values for max and min', () => {
    const source = { flag: 5, max: '', min: '' };
    const result = daterRangeToValue(source);
    expect(result).toEqual([undefined, undefined]);
  });
});

describe('dateRangeToSource', () => {
  test('should return undefined if either date is undefined', () => {
    const value1 = [undefined, '2022-01-01'];
    const result1 = dateRangeToSource(value1);
    expect(result1).toBeUndefined();

    const value2 = ['2022-01-01', undefined];
    const result2 = dateRangeToSource(value2);
    expect(result2).toBeUndefined();
  });

  test('should return undefined if either date is invalid', () => {
    const value1 = ['invalid-date', '2022-01-01'];
    const result1 = dateRangeToSource(value1);
    expect(result1).toBeUndefined();

    const value2 = ['2022-01-01', 'invalid-date'];
    const result2 = dateRangeToSource(value2);
    expect(result2).toBeUndefined();
  });

  test('should return source object if both dates are valid', () => {
    const value = ['2022-01-01', '2022-01-31'];
    const result = dateRangeToSource(value);

    expect(result).toBeDefined();
    expect(result).toEqual({
      currently: true,
      flag: 5,
      number: 1,
      unit: 'day',
      min: moment('2022-01-01').startOf('day').toISOString(),
      max: moment('2022-01-31').endOf('day').toISOString(),
    });
  });
});

describe('dateRangeToLabel', () => {
  test('should return empty string when source is not a plain object', () => {
    const source = [1, 2, 3]; // or any non-plain object
    expect(dateRangeToLabel(source as any)).toEqual('');
  });

  test('should return formatted date range when source has valid min and max', () => {
    const source = { min: '2022-01-01', max: '2022-01-31' };
    const expected = '2022-01-01~2022-01-31';
    expect(dateRangeToLabel(source)).toEqual(expected);
  });

  test('should return empty string when source has invalid min and max', () => {
    const source = { min: 'invalid-date', max: 'invalid-date' };
    expect(dateRangeToLabel(source)).toEqual('');
  });

  test('should return "start date after" when source has valid min and invalid max', () => {
    const source = { min: '2022-01-01', max: 'invalid-date' };
    const expected = '2022-01-01之后';
    expect(dateRangeToLabel(source)).toEqual(expected);
  });

  test('should return "end date before" when source has invalid min and valid max', () => {
    const source = { min: 'invalid-date', max: '2022-01-31' };
    const expected = '2022-01-31之前';
    expect(dateRangeToLabel(source)).toEqual(expected);
  });
});
