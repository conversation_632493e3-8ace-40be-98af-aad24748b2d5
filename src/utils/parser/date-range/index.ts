import moment from 'moment';
import _ from 'lodash';

import { Parser } from '../interface';

type Source = {
  flag: number;
  max: string;
  min: string;
  currently: boolean;
  number: number;
  unit: string;
};
type Value = Array<string | undefined>;

export const daterRangeToValue = (source: Pick<Source, 'flag' | 'min' | 'max'>): Value => {
  if (_.isPlainObject(source) && source.flag === 5) {
    const { max, min } = source;
    return [min, max].map((v) => {
      if (v) {
        const date = moment(v);

        if (date.isValid()) {
          return date.toISOString();
        }
      }

      return undefined;
    });
  }

  return [];
};

export const dateRangeToSource = (value: Value): Source | undefined => {
  const [min, max]: Array<undefined | moment.Moment> = value.map((v) => {
    if (!v) {
      return undefined;
    }
    const date = moment(v);

    if (date.isValid()) {
      return date;
    }

    return undefined;
  });

  if (!min || !max) {
    return undefined;
  }

  const source: Source = {
    currently: true,
    flag: 5,
    number: 1,
    unit: 'day',
    min: min.startOf('day').toISOString(),
    max: max.endOf('day').toISOString(),
  };

  return source;
};

export const dateRangeToLabel = (source: Pick<Source, 'min' | 'max'>): string => {
  if (!_.isPlainObject(source)) {
    return '';
  }
  const [start, end] = [source.min, source.max].map((v) => {
    const date = moment(v);

    if (v && date.isValid()) {
      return moment(v).format('YYYY-MM-DD');
    }

    return '';
  });

  if (start && end) {
    return `${start}~${end}`;
  }
  if (start) {
    return `${start}之后`;
  }
  if (end) {
    return `${end}之前`;
  }

  return '';
};

export const dateRangeParser = {
  toValue: daterRangeToValue,
  toSource: dateRangeToSource,
  toLabel: dateRangeToLabel,
} as Parser<Source, Value>;
