import _ from 'lodash';

import { GroupItems, GroupItemMap } from '@/interfaces/response.interface';

export type GroupOption = { label: string; value: string | number | undefined };

export const normalizeGroupItems = (groupItems: GroupItems): GroupItemMap => {
  let items: GroupItemMap;

  if (Array.isArray(groupItems)) {
    items = _.reduce(
      groupItems,
      (map, v) => {
        return {
          [(v.key || v.Key) as any]: v.items || v.Items,
          ...map,
        };
      },
      {} as GroupItemMap
    );
  } else {
    items = groupItems || {};
  }

  if (_.isPlainObject(items)) {
    Object.keys(items).forEach((k) => {
      const v = items[k];

      if (!Array.isArray(v)) {
        // _.unset(items, k);
        return;
      }

      _.set(
        items,
        k,
        v.filter((sv) => {
          if (!_.isPlainObject(sv) || (!sv.value && !sv.Value)) {
            return false;
          }

          return true;
        })
      );
    });
  }

  return items;
};

export const genGroupItemsOptions = (groupItems: GroupItemMap, excludeDefaultKeys?: string[]) => {
  if (!_.isPlainObject(groupItems)) {
    return {};
  }

  return _.reduce(
    groupItems,
    (map, v, k) => {
      if (!Array.isArray(v)) {
        return map;
      }
      const options: GroupOption[] = [];

      if (!excludeDefaultKeys || !excludeDefaultKeys.includes(k)) {
        options.push({
          label: '不限',
          value: undefined,
        });
      }

      return {
        ...map,
        [k]: options.concat(
          v.map((sv) => ({
            label: `${sv.desc || sv.Desc || sv.value || sv.Value}`,
            value: sv.value || sv.Value,
            count: sv.count || Number(sv.Count),
          }))
        ),
      };
    },
    {} as Record<string, GroupOption[]>
  );
};
