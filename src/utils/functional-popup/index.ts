// 创建函数式打开模态框组件的方法

import Vue, { ComponentOptions } from 'vue';
// import { CombinedVueInstance } from 'vue/types/vue';
import _ from 'lodash';

type InstanceData = {
  visible: boolean;
};

type InstanceMethods = {
  close(): void;
  handleCancel(): any;
  handleOk(): any;
  handleVisibleChange(visible: boolean): void;
};

type InstanceOptions = {
  parent?: Vue;
  props?: Record<string, unknown>;
  on?: Record<string, (...args: unknown[]) => unknown>;
};

export default (Component: ComponentOptions<Vue> | typeof Vue) => {
  let instance: (InstanceData & InstanceMethods) | void;

  return function show(this, { parent, ...data }: InstanceOptions) {
    if (instance) {
      instance.close();
    }
    const onCancel = _.get(data, 'props.onCancel') || _.get(data, 'on.cancel');
    const onOk = _.get(data, 'props.onOk') || _.get(data, 'on.ok');
    const el = document.createElement('div');
    const _parent = parent || (this instanceof Vue ? this : _.get(window.__VUE_ROOT__, '$refs.entry.$refs.layout'));
    document.body.appendChild(el);

    instance = new Vue<InstanceData, InstanceMethods>({
      // 需要继承 ConfigProvider
      parent: _parent,
      el,
      data() {
        return { visible: false };
      },
      mounted() {
        this.visible = true;
      },
      methods: {
        close() {
          if (!this.visible) {
            return;
          }
          this.visible = false;
          instance = undefined;

          setTimeout(() => {
            if (el && el.parentElement) {
              el.parentElement.removeChild(el);
            }
            this.$destroy();
          }, 300);
        },
        handleCancel() {
          if (_.isFunction(onCancel)) {
            return onCancel();
          }

          return this.close();
        },
        handleOk() {
          if (_.isFunction(onOk)) {
            return onOk();
          }

          return this.close();
        },
        handleVisibleChange(visible) {
          if (!visible) {
            this.close();
          }
        },
      },
      render(h) {
        return h(Component, {
          ...data,
          props: {
            ..._.get(data, 'props'),
            visible: this.visible,
            onOk: this.handleOk,
            onCancel: this.handleCancel,
          },
          on: {
            visibleChange: this.handleVisibleChange,
            ..._.get(data, 'on'),
          },
        });
      },
    });

    return instance;
  };
};
