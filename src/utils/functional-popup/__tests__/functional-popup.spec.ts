import modalFactory from '..';

describe('modalFactory', () => {
  let Component;
  let showModal;

  beforeEach(() => {
    Component = {
      render(h) {
        return h('div', 'Modal Content');
      },
    };
    showModal = modalFactory(Component);
  });

  it('应该正确创建并显示模态框', () => {
    const instance = showModal({});
    expect(instance.visible).toBe(true);
    expect(document.body.contains(instance.$el)).toBe(true);
  });

  it('应该正确关闭模态框', () => {
    const instance = showModal({});
    expect(instance.visible).toBe(true);
    expect(document.body.contains(instance.$el)).toBe(true);
    instance.close();
    setTimeout(() => {
      expect(instance.visible).toBe(false);
      expect(document.body.contains(instance.$el)).toBe(false);
    }, 350);
  });

  it('应该调用onCancel回调', () => {
    const onCancel = vi.fn();
    const instance = showModal({ props: { onCancel } });
    instance.handleCancel();
    expect(onCancel).toHaveBeenCalled();

    const instance2 = showModal({ props: { onCancel: null } });
    const spy = vi.spyOn(instance2, 'close');
    instance2.handleCancel();
    expect(spy).toHaveBeenCalled();
  });

  it('应该调用onOk回调', () => {
    const onOk = vi.fn();
    const instance = showModal({ props: { onOk } });
    instance.handleOk();
    expect(onOk).toHaveBeenCalled();

    const instance2 = showModal({ props: { onOk: null } });
    const spy = vi.spyOn(instance2, 'close');
    instance2.handleOk();
    expect(spy).toHaveBeenCalled();
  });

  it('应该在visibleChange为false时关闭模态框', () => {
    const instance = showModal({});
    instance.handleVisibleChange(false);
    setTimeout(() => {
      expect(instance.visible).toBe(false);
      expect(document.body.contains(instance.$el)).toBe(false);
    }, 350);
  });

  it('应该在多次调用时正确关闭之前的模态框', () => {
    const firstInstance = showModal({});
    const secondInstance = showModal({});
    expect(firstInstance.visible).toBe(false);
    expect(secondInstance.visible).toBe(true);
  });
});
