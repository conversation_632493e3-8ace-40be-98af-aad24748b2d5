import { isNil, sortBy } from 'lodash';

import { company as companyService, monitor } from '@/shared/services';

export const transformCompanyListLogo = async (data: Record<string, any>[]) => {
  const keyNos = data?.map((item) => item.KeyNo).filter(Boolean);
  if (!keyNos?.length) return data;
  const Result = await companyService.getLogoByKeyNos(keyNos);
  const map = Result.reduce((acc, cur) => {
    acc[cur.KeyNo] = cur;
    return acc;
  }, {});
  return data.map((item) => {
    return {
      ...item,
      ...map[item.KeyNo],
      HasImage: map[item.KeyNo].HasImage || 0,
      LogoUrl: map[item.KeyNo]?.ImageUrl,
    };
  });
};

// 移除货币单位中的人民币
export const removeRMBFromCurrency = (str?: string) => {
  if (typeof str === 'string') {
    return str.replace('人民币', '');
  }
  return str;
};

// 移除公司列表中货币单位中的人民币
export const transformCompanyRegistcapi = async (res) => {
  const { data = [] } = res;
  const transformData = data.map((item) => {
    const reccap = removeRMBFromCurrency(item.reccap);
    const registcapi = removeRMBFromCurrency(item.registcapi);
    return {
      ...item,
      reccap,
      registcapi,
    };
  });
  return {
    ...res,
    data: transformData,
  };
};

// 给公司列表加上logo
export const transformCompanyImageUrl = async (res) => {
  const { data = [] } = res;
  const keyNos = data.map((item) => item.companyId)?.filter(Boolean);
  if (!keyNos?.length) return res;
  const Result = await companyService.getLogoByKeyNos(keyNos);
  const transformData = data.map((item) => {
    const findResult = Result.find((v) => v.KeyNo === item.companyId);
    return {
      ...findResult,
      HasImage: isNil(findResult.HasImage) ? 0 : findResult.HasImage,
      ...item,
    };
  });
  return {
    ...res,
    data: transformData,
  };
};

export const getRelatedTrendsByCompanyHashKey = async (res) => {
  const { data = [] } = res;
  const hashKeys = data.map((item) => (item.primaryObject === 1 ? item.relatedDynamicHashKey : undefined)).filter(Boolean);
  if (!hashKeys?.length) {
    return res;
  }
  const hashkeyMAp = await monitor.queryRelatedTrendsByCompanyHashKey({ hashKeys });

  res.data.forEach((item) => {
    const hashData = hashkeyMAp[item.relatedDynamicHashKey];
    if (hashData) {
      const { metricsContent } = hashData[0];
      const { displayContent } = metricsContent;
      item.relatedTrends = sortBy(displayContent, 'operate').reduce((acc, cur) => {
        acc.push({
          operate: cur.operate,
          count: cur.count,
        });
        return acc;
      }, []);
      item.hashData = hashData[0];
      item.displayContent = displayContent;
    }
  });
  return res;
};
