import { message as Message } from 'ant-design-vue';
import { intersection } from 'lodash';

import { CompanyTypeEnum } from '@/shared/constants/company-type.constant';

/**
 * 是否为系统所支持的企业或组织类型
 */
export const isValidCompanyType = (companyType: number | string) => {
  // 0, 1, 4, 10, 11, 12
  return [
    CompanyTypeEnum.Company,
    CompanyTypeEnum.Org,
    CompanyTypeEnum.Government,
    CompanyTypeEnum.Fund,
    CompanyTypeEnum.Institution,
    CompanyTypeEnum.LawOffice,
  ].includes(Number(companyType));
};

/**
 * 验证是否为系统所支持的企业或组织类型，如果不支持应当弹出警示消息
 */
export const validateCompanyTypeWithWarning = (type?: number | string): boolean => {
  if (type === undefined) {
    return true;
  }
  const isValid = isValidCompanyType(type);
  if (isValid) {
    return true;
  }

  const content = '注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查';
  Message.warning({
    key: 'isNotMainlandChinaCompany',
    content,
  });
  return false;
};

// 检测企业是否是中国大陆的
// 社会组织 keyNo 开头: `s`
// export const checkCompanyIsMianlandChinaWithOutType = (companyId) => {
//   if (!companyId) {
//     return false;
//   }
//   return !['h', 't', 's', 'g'].includes(companyId[0]);
// };

// RA-7185 【优化】梳理企业类型枚举映射关系-解决页面嵌入与系统查询不一致的问题 通过countInfo 筛选出key = 30
// TODO！！如果没有key30，那么拦截判断交由后端（后端需查询额外接口），此时会进入详情页，待优化
// 如果没有则return，如果有，再取 ['001003', '001013', '001014']的交集，有也不支持
export const validateCompanyWithCountInfo = (option): boolean => {
  const standardCode = option?.CountInfo?.find((item) => item.k === '30');
  if (standardCode && intersection(['001003', '001013', '001014'], standardCode.v).length) {
    return true;
  }
  return false;
};
