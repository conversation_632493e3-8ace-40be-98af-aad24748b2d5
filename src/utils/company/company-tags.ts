import moment from 'moment';

type Styles = {
  color: string;
  backgroundColor: string;
};

type Color = [string, string, string];

export type TagDataV2 = {
  DataExtend?: string;
  DataExtend2?: string;
  ShortName?: string;
  Name: string;
  Type: number;
};

export const COLOR_THEMES: Record<string, Color> = {
  green: ['#13c261', '#ebfff4', 'success'],
  blue: ['#128bed', '#e7f4ff', 'primary'],
  red: ['#f04040', '#ffecec', 'danger'],
  orange: ['#ff8900', '#ffeedb', 'warning'],
  gray: ['#999', '#eee', 'gray'],
  techBlue: ['#367dff', '#e6efff', 'techBlue'],
  cobaltBlue: ['#6171ff', '#eceeff', 'cobaltBlue'],
  orangeRed: ['#ff722d', '#ffdecc', 'orangeRed'],
};

const color2styles = (color: Color): Styles => {
  return {
    color: color[0],
    backgroundColor: color[1],
  };
};

// 中国银行股份有限公司 837e8c3db3440424d29a579e27bd4b95
// 宣城市金芙蓉药业有限公司 3656f9581849cba3662f36dc47f50b94: 破产案件

const getTagStyles = (data: TagDataV2) => {
  const theme = {
    orangeRed: [1, 2, 6, 7, 11, 12, 13, 14, 18, 26, 27, 29, 30, 31, 32, 33, 34, 35, 301, 401, 501, 502, 601, 602, 171, 17],
    // 中国银行股份有限公司 837e8c3db3440424d29a579e27bd4b95
    cobaltBlue: [15, 16, 111, 203, 506, 510, 507, 908, 622, 508],
    // 宁波市金融控股有限公司
    techBlue: [
      88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 102, 103, 104, 105, 106, 107, 108, 916, 505, 508, 913, 911, 404, 405, 1000001, 1000002,
      1000003, 1000004, 1000005, 1000006, 1000007, 1000008, 1000009, 1000010, 1000011,
    ],
    gray: [8, 9, 10, 25, 28, 99, 503, 603, 700, 620],
    blueViolet: [207, 208, 209, 402, 114, 910, 112, 115],
    red: [302, 901, 605, 902, 606, 904, 604, 907, 608, 607, 704, 705, 706, 907, 707],
    brandBlue: [905, 109, 110, 200, 201, 202, 204, 205, 206],
    mingQing: [3],
    orange: [623],
    green: [9992],
  };

  let color = COLOR_THEMES.blue[2];
  let styles = color2styles(COLOR_THEMES.blue);

  if (theme.red.includes(data.Type)) {
    color = COLOR_THEMES.red[2];
    styles = color2styles(COLOR_THEMES.red);
  } else if (theme.gray.includes(data.Type)) {
    color = COLOR_THEMES.gray[2];
    styles = color2styles(COLOR_THEMES.gray);
  } else if (theme.cobaltBlue.includes(data.Type)) {
    color = COLOR_THEMES.cobaltBlue[2];
    styles = color2styles(COLOR_THEMES.cobaltBlue);
  } else if (theme.techBlue.includes(data.Type)) {
    color = COLOR_THEMES.techBlue[2];
    styles = color2styles(COLOR_THEMES.techBlue);
  } else if (theme.orangeRed.includes(data.Type)) {
    color = COLOR_THEMES.orangeRed[2];
    styles = color2styles(COLOR_THEMES.orangeRed);
  }

  // 标签颜色补丁处理
  if (+data.Type === 3) {
    if (data.Name.includes('已退市')) {
      color = COLOR_THEMES.gray[2];
      styles = color2styles(COLOR_THEMES.gray);
    }
  }
  // 金融标签置灰逻辑补充
  if (data?.DataExtend2 && typeof data?.DataExtend2 === 'string') {
    const extraData = JSON.parse(data?.DataExtend2 || '{}');
    const NG = extraData?.NG || ''; // 上次更新时间
    if (NG === '1' || NG === '0') {
      color = COLOR_THEMES.gray[2];
      styles = color2styles(COLOR_THEMES.gray);
    }
  }
  // 港美股 VIE 特殊处理
  if ([30, 31].includes(+data.Type)) {
    const extraData = JSON.parse(data.DataExtend2 || '{}');
    if (extraData.LS === '3') {
      color = COLOR_THEMES.gray[2];
      styles = color2styles(COLOR_THEMES.gray);
    }
  }

  return {
    color,
    styles,
  };
};

const yiyuanMap = {
  三级特等:
    '简称“三特医院”，是依照中国现行《医院分级管理办法》等的规定而划分的医院等级之一。医院建设成绩显著，科室设置、人员配备、管理水平、技术水平、工作质量和技术设施等，按分等标准综合考核检查达900分及以上。三级特等医院从级别上是我国医院的最高级别。',
  三级甲等:
    '三级甲等医院是向所在地区以及周边辐射区域提供高水平医疗卫生服务和执行高等教育、科研任务的区域性以上医院。医院建设升级显著，科室设置、人员配备、管理水平、技术水平、工作质量和技术设施等，按分等标准综合考核检查达900分及以上。',
  三级乙等: '三级乙等医院是指建设成绩尚好，科室设置、人员配备、技术水平、工作质量、技术设施等，按分等标准综合考核检查达750-899分。',
  三级丙等:
    '三级丙等医院是依照中国现行《医院分级管理办法》等的规定而划分的医院等级之一。医院建设有一定成绩，基本标准考核合格，但与本标准要求尚有较大差距。按分等标准综合考核检查在749分及以下。三级丙等医院应有切实可行的改进措施。',
  二级甲等:
    '二级甲等医院属于二级医院的一种，在二级医院中等级最高，二级甲等是按照医院的功能、任务、设施条件、技术建设、医疗服务质量和科学管理的综合水平进行等级评定而确定的医院等级。',
  二级乙等:
    '二级乙等医院属于二级医院的一种，在二级医院中等级中等，主要指一般市、县医院及省辖市的区级医院，以及相当规模的工矿、企事业单位的职工医院。',
  二级丙等:
    '二级丙等医院主要指一般市、县医院及省辖市的区级医院，以及相当规模的工矿企事业单位的职工医院。二级医院是向多个社区提供综合医疗卫生服务和承担一定教学、科研任务的地区性医院。',
  一级甲等:
    '一级甲等医院属于一级医院的一种，在一级医院中等级最高，它是指直接向具有⼀定⼈⼝的社区提供医疗、预防、保健和康复服务的基层医疗卫⽣机构。',
  一级乙等:
    '一级乙等医院属于一级医院的一种，在一级医院中等级中等，它是指直接为一定人口的社区提供预防、治疗、保健、康复服务的基层医院、卫生院。',
  一级丙等: '一级丙等医院属于一级医院的等级之一，一级医院是直接为一定人口的社区提供预防、治疗、保健、康复服务的基层医院、卫生院。',
  一级医院:
    '是直接为社区提供医疗、预防、康复、保健综合服务的基层医院，是初级卫生保健机构。其主要功能是直接对人群提供一级预防，在社区管理多发病常见病现症病人并对疑难重症做好正确转诊，协助高层次医院搞好中间或院后服务，合理分流病人。',
  二级医院:
    '是跨几个社区提供医疗卫生服务的地区性医院，是地区性医疗预防的技术中心。其主要功能是参与指导对高危人群的监测，接受一级转诊，对一级医院进行业务技术指导，并能进行一定程度的教学和科研。',
  三级医院:
    '是跨地区、省、市以及向全国范围提供医疗卫生服务的医院，是具有全面医疗、教学、科研能力的医疗预防技术中心。主要提供专科（包括特殊专科）的医疗服务，解决危重疑难病症，接受二级转诊；完成培养各种高级医疗专业人才的教学和承担省以上科研项目的任务。',
  三级: '是跨地区、省、市以及向全国范围提供医疗卫生服务的医院，是具有全面医疗、教学、科研能力的医疗预防技术中心。主要提供专科（包括特殊专科）的医疗服务，解决危重疑难病症，接受二级转诊；完成培养各种高级医疗专业人才的教学和承担省以上科研项目的任务。',
};

const xuexiaoMap = {
  985: '985工程是指中国共产党和中华人民共和国国务院在世纪之交为建设具有世界先进水平的一流大学而做出的重大决策。',
  211: '211工程是指面向21世纪，重点建设100所左右的高等学校和一批重点学科的建设工程。是新中国成立以来由国家立项在高等教育领域进行的规模最大、层次最高的重点建设工作',
  双一流:
    '“双一流”是指世界一流大学和一流学科。建设世界一流大学和一流学科，是中共中央、国务院作出的重大战略决策，也是继“211工程”，“985工程”之后的又一国家战略。',
};

const formatFinancialHoverText = (code) => {
  switch (code) {
    case '1':
      return '注册制，拟于沪主板上市';
    case '2':
      return '注册制，拟于深主板上市';
    case '3':
      return '注册制，拟于创业板上市';
    case '4':
      return '注册制，拟于科创板上市';
    case '107':
      return '注册制，拟于京主板上市';
    case '101':
      return '核准制，拟于沪主板上市';
    case '102':
      return '核准制，拟于深主板上市';
    case '31':
      return '当前为基础层，基础层包含的个股都是准入标准最低的个股，这里的公司风险是最大的，一方面它都是创新层和精选层中淘汰下来的个股，另一方面它是一个极其庞大的股票群体，近乎代表整个新三板市场';
    case '32':
      return '当前为创新层，创新层企业资质、财务状况较好，会被要求公司信息披露更加公开公正，支持连续竞价的交易方式使交易更灵活';
    case '33':
      return '当前为精选层，精选层是从创新层中挑出的优质企业，它的挑选标准是从利润指标、收入增长指标、市值指标三者中选择，满足其一即可入选';
    case '34':
      return '两网代表的是staq、net两个系统，退市是指股票退出交易市场。股票退市后会进入该市场交易，投资者可以在证券公司进行确权，并开通三板交易权限就能在三板市场挂牌转让';
    case '0':
    default:
      return '';
  }
};

const stockNameMap = [
  { id: 1, stockStatus: '' },
  { id: 2, stockStatus: '' },
  { id: 401, stockStatus: '' },
  { id: 301, stockStatus: '' },
  { id: 7, stockStatus: '' },
  { id: 17, stockStatus: '' },
  { id: 11, stockStatus: '' },
  { id: 8, stockStatus: '已退市' },
  { id: 9, stockStatus: '已退市' },
  { id: 12, stockStatus: '待上市' },
  { id: 13, stockStatus: '暂停上市' },
];
const getStockName2 = (tag, stockStatus) => {
  let name = tag.name;
  if (tag.shortName) {
    name += ` | ${tag.shortName}`;
  }
  if (tag.dataExtend) {
    name += ` ${tag.dataExtend} `;
  }
  if (tag.name === '新三板' && stockStatus === '已退市') {
    stockStatus = '已摘牌';
  }
  if (stockStatus) {
    name += ` ${stockStatus}`;
  }
  return name;
};

const getStockName4Type = (tag) => {
  const find = stockNameMap.find((e) => e.id === tag.type);
  if (find) {
    return getStockName2(tag, find.stockStatus);
  }
  return '';
};

const getTagLabel = (tag: TagDataV2) => {
  let label = tag.Name;
  let color: string | undefined = undefined;
  let extendObj: string | any;
  let trackName = '';
  let hoverText = '';

  const caseObj = {};

  const extraData = JSON.parse(tag?.DataExtend2 || '{}');

  switch (tag.Type) {
    case 707:
      // click = () => this.navPosJump('经营风险', 'mktScpecOverdueCountAreaId')
      break;
    case 705:
      // click = () => this.navPosJump('经营风险', 'bankRuptcyListAreaId')
      break;
    // 科技型企业
    case 88:
    case 89:
    case 90:
    case 91:
    case 92:
    case 93:
    case 94:
    case 95:
    case 97:
    case 102:
    case 103:
    case 105:
    case 106:
    case 107:
    case 108:
    case 916:
      // const tips = tecTipMap[tag.type] || '';
      // hoverText = handleTagCards(tag, tips);
      // click = () => this.navPosJump('企业发展', 'tecCountAreaId', '', '', tag.isExpired ? {
      //   tab: '历史信息',
      //   id: 'tecCountAreaId'
      // } : null)
      break;
    case 3: // 产品信息
      // link = `/product-info?id=${tag.dataExtend}`
      // hoverText = `
      //               <span style="font-size: 14px;line-height: 22px">
      //                 <span style="font-weight: bold;">当前轮次：</span>${tag.name}
      //                 <br />
      //                 <span style="font-weight: bold;">日期：</span>${extraData.D || '-'}
      //                 <br />
      //                 <span style="font-weight: bold;">融资金额：</span>${extraData.A || '-'}
      //                 <br />
      //                 <span style="font-weight: bold;">投资方：</span>${extraData.I || '-'}
      //               </span>
      //           `;
      break;
    case 110: // 医院
      label = tag.Name;
      hoverText =
        '按照法律法规和行业规范，为病员开展必要的医学检查、治疗措施、护理技术、接诊服务、康复设备、救治运输等服务，以救死扶伤为主要目的医疗机构。';
      break;
    case 15: // 医院标签附加
      label = tag.DataExtend as string;
      hoverText = yiyuanMap[tag.DataExtend as string];
      break;
    case 16: // 学校标签附加
      hoverText = xuexiaoMap[tag.Name];
      break;
    case 99: // 曾用名
      label = `${tag.Name}`;
      // if (this.originalName && this.originalName.length) {
      //   hoverText = this.originalName
      //     .map((vo) => {
      //       let text = vo.name;
      //       if (vo.startDate || vo.changeDate) {
      //         text += `（${toNumberOnlyFormat(vo.startDate, 'YYYY-MM', '~')} 至 ${toNumberOnlyFormat(
      //           vo.changeDate,
      //           'YYYY-MM',
      //           '~'
      //         )}）`;
      //       }
      //       return `${text}<br>`;
      //     })
      //     .join('');
      // }
      // // suffixIcon = 'fa el-icon-caret-bottom'
      // autoWidth = true;
      // toolTipMaxWidth = 400;
      break;
    case 207: // 私募基金管理人
      // link = `/company_simu?companykey=${this.keyNo}&companyname=${encodeURIComponent(this.company.info?.name)}&id=${this.keyNo}`
      break;
    case 700:
      label = tag.Name;
      if (extraData?.date && extraData?.type) {
        hoverText += `
                    <span style="font-size: 14px;line-height: 22px">
                    <span style="font-weight: bold;">注销时间：</span>${extraData?.Date || '-'}
                    <br />
                    <span style="font-weight: bold;">注销类型：</span>${extraData?.Type || '-'}
                    <br />
                    <span style="font-weight: bold;">来源：</span>中国证券投资基金业协会披露
                    </span>
                  `;
      }
      break;
    case 208: // 投资机构
      label = '投资机构';
      // link = `/investAgency?investId=${tag.DataExtend}&searchKey=${tag.name}`
      break;
    case 209: // 私募基金
      // extendObj = JSON.parse(tag.DataExtend)
      // link = `/company_simu?companykey=${extendObj.Id}&id=${extendObj.Id}&modalkeyno=${extendObj.ProductId}`
      break;
    case 301: // 新四板
      trackName = '新四板';
      break;
    case 302: // 非正常户
      label = tag.Name;
      extendObj = JSON.parse(tag.DataExtend || '{}');
      if (extendObj.JoinTime) {
        hoverText = `列入日期：${moment(extendObj.JoinTime * 1000).format('YYYY-MM-DD')} <br />`;
      }
      if (extendObj.JoinAuthority) {
        hoverText += `列入机关：${extendObj.JoinAuthority} <br />`;
      }
      hoverText +=
        '说明：纳税人负有纳税申报义务，但连续三个月所有税种均未进行纳税申报的，税收征管系统自动将其认定为非正常户，并停止其发票领用簿和发票的使用。';
      break;
    case 506: // 银保监会
      // click = () => this.navPos('', 'zizhi')
      break;
    // case 601:
    // case 602:
    // case 603: // 科创板
    //   name = tag.DataExtend
    //   break
    case 623: // 假冒国企
      label = tag.Name;
      hoverText =
        '来源于中央企业公告的假冒国企名单，明确有关公司及其下设各级子公司均为假冒国企，与中央企业无任何隶属或股权关系，也不存在任何投资、合作、业务等关系，其一切行为均与中央企业无关。';
      break;
    case 901: // 经营异常
    case 605:
      // click = () => this.navPos('fengxian', 'yichang')
      break;
    case 902: // 严重违法
    case 606:
      // click = () => this.navPos('fengxian', 'svlist')
      break;
    case 904: // 失信
    case 604:
      // click = () => this.navPos('susong', 'shixinlist')
      break;
    case 704:
      // click = () => this.navPosJump('法律风险', 'sumptuaryListAreaId')
      break;
    case 608: // 被执行人
      // click = () => this.navPos('susong', 'zhixinglist')
      break;
    case 907: // 债券违约
      if (label === '债券违约') {
        hoverText =
          '发行人未能按约定足额偿付债务融资工具本金或利息，以及因破产等法定或约定原因，导致债务融资工具提前到期且发行人未能按期足额偿付本息的情形。';
      } else if (label === '债券展期') {
        hoverText =
          '债券违约后，发行人可以与违约债务融资工具持有人协商调整当期债务融资工具的基本偿付条款并变更登记要素，若没有相关法律、法规或发行文件约定的，应经全体持有人表决同意，签订变更协议。';
      }
      // click = () => this.navPosJump('经营状况', 'operCreditorRightsAreaId', 'bondTabIndex', 1)
      break;
    case 17: // 债券
      extendObj = JSON.parse(tag.DataExtend || '{}');
      label = getStockName4Type({ ...tag, dataExtend: extendObj.symbol });
      trackName = '债券';
      break;
    case 171: // 债券
      // link = `${this.$qccdomain}/webbond/${this.keyNo}.html`
      trackName = '债券';
      // click = () => this.navPosJump('经营状况', 'operCreditorRightsAreaId')
      break;
    case 510: // 药品临床试验机构
      // click = () => this.navPos('assets', 'zhengshulist')
      break;
    case 620: // 历史迁出
      label = tag.Name;
      color = 'grey';
      break;
    case 908: // 机关单位
      label = tag.Name;
      hoverText = '机关单位指从事国家管理和行使国家权力的机关。';
      break;
    case 910: // 媒体
      label = tag.Name;
      color = 'cobalt-blue';
      break;
    // 制造业企业
    case 913:
      if (['国家级冠军企业', '省级冠军企业', '冠军企业'].includes(label)) {
        label = '制造业单项冠军企业';
      }
      // caseObj.tagObj = { type: name };
      // handleManufacturingTag(caseObj.tagObj);
      // hoverText = caseObj.tagObj.tips;
      // hoverText = handleTagCards(tag, hoverText);
      break;
    case 706:
      extendObj = JSON.parse(tag.DataExtend || '{}');
      hoverText = '';
      if (+extendObj?.CX) {
        hoverText += `<span style="font-weight: bold;">诚信信息：</span>${extendObj?.CX || 0}条<br />`;
      }
      if (+extendObj?.TS) {
        hoverText += `<span style="font-weight: bold;">提示信息：</span>${extendObj?.TS || 0}条<br />`;
      }
      hoverText += '<span style="font-weight: bold;">来源：</span><span>中国证券投资基金业协会披露</span>';
      break;
    case 622: // 国有企业
      label = tag.Name;
      color = 'cobalt-blue';
      if (label === '国有全资') {
        // safari 15.6 鼠标移入时，文字会有变动，应该是浏览器的行为，故将字间距稍微放大
        hoverText = `
    <div>
    <span style="font-weight: bold;">定义：</span>指政府部门、机构、事业单位、国有独资企业单独或共同出资，直接或间接合计持股为100%的企业。<br>
    <span style="font-weight: bold;">来源：</span>依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露，综合认定${label}类型。
    </div>
    `;
      } else if (label === '国有独资') {
        // safari 15.6 鼠标移入时，文字会有变动，应该是浏览器的行为，故将字间距稍微放大
        hoverText = `
    <div>
    <span style="font-weight: bold;">定义：</span>指国家单独出资、由国务院或者地方人民政府授权本级人民政府国有资产监督管理机构履行出资人职责的企业。<br>
    <span style="font-weight: bold;">来源：</span>依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露，综合认定${label}类型。
    </div>
    `;
      } else if (label === '国有控股') {
        // safari 15.6 鼠标移入时，文字会有变动，应该是浏览器的行为，故将字间距稍微放大
        hoverText = `
    <div>
    <span style="font-weight: bold;">定义：</span>是指在企业的全部资本中，国家资本（股本）所占比例大于50%的企业。<br>
    <span style="font-weight: bold;">来源：</span>依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露，综合认定${label}类型。
    </div>
    `;
        // } else if (name === '国有企业') {
        //   hoverText = '即疑似实际控制人为国务院、各地区国有资产监督管理委员会、地方人民政府或机关单位的企业。'
      } else if (label === '央企') {
        hoverText = `
    <span style="font-weight: bold;">定义：</span>指国务院授权国有资产监督管理委员会履行出资人职责的企业。<br>
    <span style="font-weight: bold;">来源：</span>依据国务院、中央及地方国资委等相关官方公开披露信息认定${label}类型。
    `;
      } else if (label === '省管国企') {
        hoverText = `
    <span style="font-weight: bold;">定义：</span>指各省国有资产监督管理委员会披露的省属监管企业。<br>
    <span style="font-weight: bold;">来源：</span>依据国务院、中央及地方国资委等相关官方公开披露信息认定${label}类型。
    `;
      } else if (label === '市管国企') {
        hoverText = `
    <span style="font-weight: bold;">定义：</span>指各直辖市和地级市国有资产监督管理委员会所披露的市属监管企业。<br>
    <span style="font-weight: bold;">来源：</span>依据国务院、中央及地方国资委等相关官方公开披露信息认定${label}类型。
    `;
      } else if (label === '央企子公司') {
        hoverText = `
    <span style="font-weight: bold;">定义：</span>指国有资产监督管理委员会披露的央企旗下的子公司或成员企业。<br>
    <span style="font-weight: bold;">来源：</span>依据国务院、中央及地方国资委等相关官方公开披露信息认定${label}类型。
    `;
      }
      break;
    case 404:
      label = tag.Name;
      // link = `https://www.biaozhaozhao.com/ccompany/${this.keyNo}`
      hoverText = '建筑企业是指从事建筑商品生产和经营，具有法人资格的经济实体。';
      break;
    case 405: // 建筑企业
      label = tag.Name;
      // link = `https://www.biaozhaozhao.com/ccompany/${this.keyNo}`
      hoverText =
        '工程企业是指从事为人类生活、生产提供物质技术基础的各类建（构）筑物和工程设施，具有法人资格的经济实体。包括建筑工程、土木工程 、机电工程相关企业。';
      break;
    case 9990:
      hoverText = `国籍（或地区）/注册地：${tag.Name}`;
      break;
    // 2022/9/29 金融标签体系 http://doc.greatld.com/pages/viewpage.action?pageId=34071377
    case 1:
    case 8: // 新三板 两网及退市  8 退市
      label = extraData.WL || tag.DataExtend || tag.Name;
      // click = () => this.navPos('sanban', 'sanbanrealtime')
      trackName = '新三板';
      hoverText = tag.Type === 1 ? formatFinancialHoverText(extraData.WH) : ''; // 退市无hover
      break;
    case 2:
    case 12: // 已申报有代码
    case 9: // 沪深A股已上市  // A股 退市
      label = extraData.WL || tag.DataExtend || tag.Name;
      // click = () => this.navPos('ipo', 'iporealtime')
      trackName = 'A股';
      break;
    case 7: // 中概股 上市
    case 10: // 港股 退市 不跳转详情
    case 11: // 美股 上市
    case 25: // 美股退市
    case 13: // 暂停上市
    case 18: // 北交所
    case 26: // A股 辅导期
    case 28: // 终止发行
    case 29: // 港股待上市
    case 30: // 美股 VIE
    case 31: // 港股 VIE
    case 32: // 新三板 挂牌申报
    case 33: // 新三板 待挂牌
    case 34: // 美股 待上市
      // case 14: // A股 主板
      // case 602:
      // case 603: // 创业板
      label = extraData.WL || tag.Name;
      hoverText = formatFinancialHoverText(extraData.WH);
      break;
    case 6: // 大陆公司港股
    case 401: // 香港公司港股
      label = extraData.WL || tag.Name;
      // click = () => this.navPos('hkstock', 'hkstockrealtime')
      trackName = '港股';
      break;
    case 27: // 北交所 已申报无代码
    case 14: // A股 主板申报 未上会
    case 35: // A股 主板 注册制
    case 502: // 科创板
    case 503: // 科创板
    case 602: // 创业板
    case 603: // 创业板
      label = extraData.WL || tag.DataExtend || tag.Name;
      // link = `/companyDetail?keyNo=${this.keyNo}#科创板`
      // click = () => this.navPos('ipo', 'iporealtime')
      hoverText = formatFinancialHoverText(extraData.WH);
      break;
    default:
      break;
  }
  return {
    label,
    hoverText,
    color,
    trackName,
  };
};

export const parseTagV2 = (tag: TagDataV2) => {
  const { color, styles } = getTagStyles(tag);
  const { label } = getTagLabel(tag);

  return {
    label,
    color,
    styles,
  };
};

/**
 * 允许展示的标签类型
 */
const VALID_TAG_TYPES = [
  1, 2, 3, 6, 401, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 121, 622, 79, 88, 89, 90, 91, 92,
  93, 94, 95, 96, 97, 102, 103, 104, 105, 106, 107, 108, 112, 115, 200, 911, 912, 99, 109, 110, 111, 114, 119, 203, 207, 208, 209, 301, 302,
  402,
  // 404, 405, 501, 502, 503, 506, 601,
  501, 502, 503, 506, 601, 602, 603, 623, 901, 605, 902, 606, 904, 604, 608, 907, 17, 171, 510, 620, 700, 701, 702, 704, 705, 706, 707, 708,
  908, 910, 913, 916, 90, 91, 92, 93, 94, 95, 505, 507, 508, 9990, 9991, 9992, 1000001, 1000002, 1000003, 1000004, 1000005, 1000006,
  1000007, 1000008, 1000009, 1000010, 1000011,
];
export const getTagsV2 = (tags?: TagDataV2[]) => {
  if (!Array.isArray(tags)) {
    return [];
  }
  return tags.filter(({ Type }) => VALID_TAG_TYPES.includes(Type)).map(parseTagV2);
};
