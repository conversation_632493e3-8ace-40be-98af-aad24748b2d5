import { getTagsV2, parseTagV2 } from '../company-tags';

describe('companyTags', () => {
  describe('parseTagV2', () => {
    test('should return red theme for Type in [604, 608, 704, 605, 606, 302, 623, 705, 707, 907]', () => {
      const tagData = { Type: 604, Name: 'Tag 1' };
      const expectedResult = {
        label: 'Tag 1',
        color: 'danger',
        styles: {
          color: '#f04040',
          backgroundColor: '#ffecec',
        },
      };
      expect(parseTagV2(tagData)).toEqual(expectedResult);
    });

    test('should return blue theme for Type not in [604, 608, 704, 605, 606, 302, 623, 705, 707, 907]', () => {
      const tagData = { Type: 100, Name: 'Tag 2' };
      const expectedResult = {
        label: 'Tag 2',
        color: 'primary',
        styles: {
          color: '#128bed',
          backgroundColor: '#e7f4ff',
        },
      };
      expect(parseTagV2(tagData)).toEqual(expectedResult);
    });
  });

  describe('getTagsV2', () => {
    test('should return an empty array if input is not an array', () => {
      let result = getTagsV2([]);
      expect(result).toEqual([]);

      result = getTagsV2();
      expect(result).toEqual([]);
    });

    test('should map tags using parseTagV2 function', () => {
      const source = {
        tags: [
          {
            Type: 1,
            Name: 'A',
          },
          {
            Type: 604,
            Name: 'B',
          },
        ],
      };
      const expected = {
        tags: [
          {
            color: 'orangeRed',
            label: 'A',
            styles: {
              backgroundColor: '#ffdecc',
              color: '#ff722d',
            },
          },
          {
            color: 'danger',
            label: 'B',
            styles: {
              backgroundColor: '#ffecec',
              color: '#f04040',
            },
          },
        ],
      };
      expect(getTagsV2(source.tags)).toEqual(expected.tags);
    });
  });
});
