import { isValidCompanyType, validateCompanyTypeWithWarning, validateCompanyWithCountInfo } from '../company-type';

describe('isValidCompanyType', () => {
  test('支持的企业类型', () => {
    expect(isValidCompanyType(0)).toBe(true);
    expect(isValidCompanyType(1)).toBe(true);
  });

  test('不支持的非法类型', () => {
    expect(isValidCompanyType(20)).toBe(false);
  });
});

describe('validateCompanyTypeWithWarning', () => {
  test('空值返回true', () => {
    expect(validateCompanyTypeWithWarning()).toBe(true);
  });

  test('支持的企业类型', () => {
    expect(validateCompanyTypeWithWarning(0)).toBe(true);
    expect(validateCompanyTypeWithWarning(1)).toBe(true);
  });

  test('不支持的非法类型', () => {
    expect(validateCompanyTypeWithWarning(20)).toBe(false);
  });

  test('不支持的非法类型应当弹出警示消息', () => {
    expect(validateCompanyTypeWithWarning(20)).toBe(false);
  });
});

describe('validateCompanyWithCountInfo', () => {
  test('should return false when standardCode is not present', () => {
    const option = {};
    const result = validateCompanyWithCountInfo(option);
    expect(result).toBeFalsy();
  });

  test('should return false when standardCode is present but does not match the required values', () => {
    const option = {
      CountInfo: [{ k: '30', v: ['002003', '002013', '002014'] }],
    };
    const result = validateCompanyWithCountInfo(option);
    expect(result).toBe(false);
  });

  test('should return true when standardCode is present and contains at least one of the required values', () => {
    const option = {
      CountInfo: [{ k: '30', v: ['001003', '002013', '002014'] }],
    };
    const result = validateCompanyWithCountInfo(option);
    expect(result).toBe(true);
  });
});
