import Vue, { unref } from 'vue';
import VueRouter, { RouteConfig } from 'vue-router';
import { message } from 'ant-design-vue';

import { hasPermission } from '@/shared/composables/use-permission';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useStore } from '@/store';

import { externalRoutes } from './routes/external.routes';

Vue.use(VueRouter);

const routes: RouteConfig[] = externalRoutes();

export const externalRouter = new VueRouter({
  mode: 'history',
  base: '/external',
  routes,
  linkActiveClass: 'active',
  linkExactActiveClass: 'exact-active',
  scrollBehavior() {
    return { x: 0, y: 0 };
  },
});

// 鉴权
externalRouter.beforeEach(async (to, from, next) => {
  const { permissions, profile } = useUserStore();
  const { permission } = to?.meta ?? {};

  const store = useStore();
  // 判断是否有权限，没有就获取
  if (!unref(permissions).length) {
    try {
      await store.dispatch('user/getProfile');
    } catch (error) {
      console.error(error);
    }
  }

  // 无权限提示
  const bundleError = profile.value?.bundleError;
  if (bundleError) {
    message.warn({
      key: bundleError?.code,
      content: bundleError?.error,
    });
  }

  // 权限检测
  if (permission && !hasPermission(permission) && !bundleError) {
    // 无权限访问跳转到错误提示页
    window.location.replace('/error/auth');
  }
  next();
});

externalRouter.afterEach(() => {
  const layoutEl = document.getElementById('workbench-layout-main');
  if (layoutEl) {
    layoutEl.scrollTo(0, 0);
  }
});

// 发版过程中Chunk文件加载失败刷新页面
externalRouter.onError((error) => {
  const { message, name } = error;
  const pattern = /^Failed to fetch dynamically imported module/;
  if (pattern.test(message) || ['ChunkLoadError'].includes(name)) {
    window.location.reload();
  }
});
