import { RouteConfig } from 'vue-router';

// Apps
import { companySearchRoutes, companyDetailRoutes } from '@/apps/company/routes/company.routes';
import { investigationRoutes } from '@/apps/investigation/routes/investigation.routes';
import { riskModelRoutes } from '@/apps/risk-model/routes/risk-model.routes';
import { switchRoutes } from '@/apps/switch/routes/switch.routes';
import { riskMonitorRoutes } from '@/apps/risk-monitor/routes/risk-monitor.routes';
import { taskRoutes } from '@/apps/task/routes/task.routes';
import { notificationRoutes } from '@/apps/notification/routes/notification.routes';
import { identityVerificationRoutes } from '@/apps/identity-verification/routes/identity-verification.routes';
import { accountRoutes } from '@/apps/account/routes/account.routes';

export const routes: RouteConfig[] = [
  {
    path: '/',
    name: 'index-page',
    component: () => import('@/pages/portal/redirect'),
    meta: {
      layout: 'default',
    },
  },
  // 企业尽调
  ...investigationRoutes(),
  // 风险模型
  ...riskModelRoutes(),
  // 风险监控
  ...riskMonitorRoutes(),
  // 人企核验
  ...identityVerificationRoutes(),
  // 尽调模型
  // ...monitorModelRoutes(),
  // 工商搜索
  ...companySearchRoutes(),
  ...companyDetailRoutes(),
  // 切换组织
  ...switchRoutes(),
  // 合作关系
  // ...supplierRoutes(),

  // // 设置中心
  // ...settingRoutes(),
  // 任务中心
  ...taskRoutes(),
  // 消息中心
  ...notificationRoutes(),
  // 用户中心: 个人中心、套餐权益
  ...accountRoutes(),
  // 通用错误页
  {
    path: '*',
    name: 'not-found',
    meta: {
      layout: 'default',
    },
    props: {
      errorCode: 404,
    },
    component: () => import('@/apps/error/pages/generic-error'),
  },
];
