import { Permission } from '@/config/permissions.config';
import type { RouteConfig } from 'vue-router';

const investigationModelRoutes = () => [
  /**
   * 尽调模型列表
   * @example /external/investigation/models
   */
  {
    path: '/investigation/models',
    name: 'external-investigation-model-list',
    component: () => import('@/apps/risk-model/pages/risk-model-list'),
    props: {
      isExternal: true,
    },
    meta: {
      layout: 'external',
      title: '尽调模型',
      permission: [Permission.INVESTIGATION_MODEL_VIEW],
    },
  },
  /**
   * 尽调模型详情
   * @example /external/investigation/models/detail/3886
   */
  {
    path: '/investigation/models/detail/:riskModelId([0-9]+)',
    name: 'external-investigation-model-detail',
    component: () => import('@/apps/risk-model/pages/risk-model-detail'),
    props: {
      isExternal: true,
    },
    meta: {
      layout: 'external',
      title: '尽调模型详情',
      permission: [Permission.INVESTIGATION_MODEL_VIEW],
    },
  },
];

const investigationRoutes = () => [
  /**
   * 风险排查详情
   * @example /external/investigation/detail/84c17a005a759a5e0d875c1ebb6c9846?diligenceId=50000059&orgModelIds=1014,1007
   */
  {
    path: '/investigation/detail/:id([a-z0-9,]+)',
    name: `external-investigation-detail`,
    component: () => import('@/apps/investigation/pages/investigation-detail'),
    props: {
      isExternal: true,
    },
    meta: {
      layout: 'external',
      title: '排查详情',
    },
  },
  /**
   * 风险排查详情card
   * @example /external/investigation/detail-card/84c17a005a759a5e0d875c1ebb6c9846?diligenceId=50000059&orgModelIds=1014
   */
  {
    path: '/investigation/detail-card/:id([a-z0-9,]+)',
    component: () => import('@/apps/investigation/pages/investigation-card'),
    props: {
      isExternal: true,
    },
    meta: {
      layout: 'external',
      title: '排查详情卡片',
    },
  },
];

/**
 * 对外嵌入页面
 */
export const externalRoutes = (): RouteConfig[] => [
  // 用户鉴权中转
  {
    path: '/auth/authorize',
    component: () => import('@/pages/external/auth/authorize'),
    meta: {
      layout: 'default',
      title: '用户鉴权',
    },
  },
  // 尽调模型
  ...investigationModelRoutes(),
  // 尽调详情
  ...investigationRoutes(),
];
