import { useRouter } from 'vue-router/composables';
import { defineComponent } from 'vue';

import QLoading from '@/components/global/q-loading';
import { hasPermission } from '@/shared/composables/use-permission';

const RedirectPage = defineComponent({
  name: 'RedirectPage',
  setup() {
    const router = useRouter();
    // 跳转到第一个有权限的路由中
    const handleRedirect = () => {
      // 将权限点的路由name拿出来
      const ROUTE_LIST =
        router?.options?.routes?.reduce<string[]>((list, cur) => {
          if (cur.children && cur.children.length > 0) {
            cur.children.forEach((item) => {
              if (item.meta?.permission) {
                list.push(item.name as string);
              }
            });
          }
          return list;
        }, []) ?? [];
      const route = ROUTE_LIST.find((item) => {
        const { resolved } = router.resolve({ name: item });
        if (!resolved) return false;
        return hasPermission(resolved.meta?.permission);
      });
      // 没拿到就说明所有菜单都没权限，跳转落地页
      if (!route) {
        window.location.href = '/';
        return;
      }
      router.replace({ name: route });
    };
    handleRedirect();
  },
  render() {
    return (
      <div style={{ height: '100vh' }}>
        <QLoading size="fullsize" />
      </div>
    );
  },
});

export default RedirectPage;
