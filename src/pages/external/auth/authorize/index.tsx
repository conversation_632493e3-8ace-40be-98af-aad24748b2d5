import { defineComponent, onMounted } from 'vue';
import { useRoute } from 'vue-router/composables';

const AuthorizePage = defineComponent({
  name: 'AuthorizePage',
  setup() {
    const route = useRoute();
    onMounted(() => {
      const { accessToken, redirectUrl } = route.query;
      if (accessToken && redirectUrl) {
        const host = '/rover/account/loginByToken';
        const url = `${host}?accessToken=${encodeURIComponent(accessToken as string)}&redirectUrl=${encodeURIComponent(
          redirectUrl as string
        )}`;
        window.location.href = url;
      }
    });
  },
  render() {
    return <div></div>;
  },
});

export default AuthorizePage;
