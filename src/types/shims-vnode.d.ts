import { Ability } from '@/libs/plugins/user-ability/ability';
import * as service from '@/shared/services';

declare module 'vue/types/vue' {
  interface Vue {
    $ability: typeof Ability;
    $service: typeof service;
    $modal: {
      showDimension(field: string, params: Record<string, any>, ...rests: unknown[]): Promise<void>;
    };
    // $track(event: string, data?: EventData): void;
    // $auth: any;
    // $helper: any;
    // $t: any;
  }
}

// https://github.com/vuejs/vue/issues/12680#issuecomment-1211672009
declare module 'vue/types/vnode' {
  export interface ComponentCustomProps {
    [key: string]: any;
  }
}

export {};
