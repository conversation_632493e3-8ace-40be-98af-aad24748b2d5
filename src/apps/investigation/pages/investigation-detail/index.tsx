import { compact, noop, pick } from 'lodash';
import { Breadcrumb, message as Message, Tooltip } from 'ant-design-vue';
import { computed, defineComponent, provide, ref, shallowReactive, unref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useToggle } from '@vueuse/core';
import moment from 'moment';
import axios from 'axios';

import { company as companyService, diligence as diligenceService } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import { uesInvestStore } from '@/hooks/use-invest-store';
import { useAbility } from '@/libs/plugins/user-ability';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { useWebTitle } from '@/shared/composables/use-web-title';
import QCard from '@/components/global/q-card';
import RiskAction from '@/shared/components/risk-action';
import { useI18n } from '@/shared/composables/use-i18n';
import { useExpandKeys } from '@/apps/investigation/pages/investigation-detail/hook/use-expand-keys';
import { useRiskModels } from '@/hooks/use-risk-models';
import { MESSAGE_MAP } from '@/utils/http-client/interceptors';
import { GenerateReportStatus, useGenerateReportFile } from '@/shared/composables/use-generate-report-file';

import BasicInfo from './widgets/basic-info/default';
import RiskContentDefault from './widgets/risk-content/default';
import styles from './investigation-detail.page.module.less';
import { openRiskModelModal } from './widgets/risk-model-modal';
import LoadingIcon from '../assets/icon-loading.png';
import { Permission } from '@/config/permissions.config';

function getBodyStyle({ paddingTop }) {
  const defaultStyle = {
    display: 'flex',
    flexDirection: 'column',
    paddingTop,
    backgroundColor: 'transparent',
    marginTop: '8px',
  };
  return defaultStyle;
}

const InvestigateDetailPage = defineComponent({
  name: 'InvestigateDetailPage',
  props: {
    isExternal: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const {
      riskInfo,
      companyInfo: company,
      riskLevel,
      loading,
      getDiligence,
      dimensionDetails,
      allModelRiskInfoList,
      isCurrentDDResultsDesc,
      setAllModelRiskInfoList,
    } = uesInvestStore();

    const { setExpandKeys } = useExpandKeys();

    const { tc } = useI18n();

    const ability = useAbility();
    const track = useTrack();
    const { setWebTitle } = useWebTitle();

    const router = useRouter();
    const route = useRoute();
    const snapshotDate = computed(() => {
      const currentCreateDate = unref(riskInfo)?.createDate ? moment(unref(riskInfo).createDate).format('YYYY-MM-DD HH:mm:ss') : '-';
      const lastCreateDate = allModelRiskInfoList.value.length
        ? moment.max(allModelRiskInfoList.value.map((item) => moment(item.createDate))).format('YYYY-MM-DD HH:mm:ss')
        : null;
      return isCurrentDDResultsDesc.value ? lastCreateDate : currentCreateDate;
    });

    // 最新的diligenceId
    const cacheDiligenceId = ref(unref(riskInfo).id || route?.query?.diligenceId);
    // 错误处理
    const errorOutOfLimitation = shallowReactive<{
      error: boolean;
      message: string | undefined;
      code: number | undefined;
    }>({
      error: false,
      message: undefined,
      code: undefined,
    });
    const setErrorMessage = (error: boolean, msg?: string, code?: number) => {
      errorOutOfLimitation.error = error;
      errorOutOfLimitation.message = MESSAGE_MAP[code as keyof typeof MESSAGE_MAP] || msg;
      errorOutOfLimitation.code = code;
    };

    // NOTE: orgModelIds 从详情获取
    const orgModelIds = computed(() => {
      const orgModelIdsQuery = (route?.query?.orgModelIds as string) ?? '';
      // 如果orgModelIds不存在(查看快照)
      if (!orgModelIdsQuery) {
        return riskInfo.value?.orgModelId ? [riskInfo.value?.orgModelId] : [];
      }
      const orgModelIdsList = compact(orgModelIdsQuery.split(','));
      return orgModelIdsList.length ? orgModelIdsList.map((id) => Number(id)) : undefined;
    });

    const { riskModels, riskModelDimensionStrategies, getRiskModels } = useRiskModels();
    const fetchRiskModels = async () => {
      const modelIds = allModelRiskInfoList.value.map(({ orgModelId }) => orgModelId);
      await getRiskModels(modelIds);
    };
    provide('getCompanyDetail', () => company.value);
    provide('riskModelDimensionStrategies', riskModelDimensionStrategies);

    /**
     * 获取风险排查详情
     * @param keyNo company keyNo
     * @param needRefreshSnapshot 是否刷新快照：刷新快照时不传 diligenceId 和 snapshotId
     * @param isDynamicDetails 是否是排查详情
     */
    const fetchData = async (keyNo: string, needRefreshSnapshot = false) => {
      try {
        setExpandKeys([]);
        setErrorMessage(false);
        loading.value = true;
        company.value = await companyService.getDetail({ keyNo });
        setWebTitle(company.value?.Name);
        if (!company.value?.Name) return;

        const riskInfoList = await getDiligence({
          keyNo,
          orgModelIds: orgModelIds.value,
          diligenceId: unref(cacheDiligenceId),
          isDynamicDetails: needRefreshSnapshot ? false : !!cacheDiligenceId.value,
          cacheHours: needRefreshSnapshot ? 0 : 24,
        });

        setAllModelRiskInfoList(riskInfoList.details);
        cacheDiligenceId.value = unref(riskInfo).id;
        // 仅在重新生成快照时更新 URL
        if (needRefreshSnapshot || unref(riskInfo).notMatch) {
          await router
            .replace({
              query: {
                ...router.currentRoute?.query, // 获取实时 query
                diligenceId: unref(riskInfo).id,
              },
            })
            .catch(noop);
        }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          setErrorMessage(true, error.response?.data.error, error.response?.data.code);
          if (error.response?.data.code === 700012) {
            loading.value = false;
            const selectIds = (await openRiskModelModal({})) as number[];
            if (selectIds?.length > 0) {
              await router.replace({
                path: route.path,
                query: {
                  from: route.query.from,
                  orgModelIds: selectIds.join(','),
                },
              });
              await fetchData(keyNo, true);
            }
          }
        } else {
          console.error(error);
        }
      } finally {
        loading.value = false;
      }
    };

    /** 生成报告 */
    const [genReportState, genReportPolling, resetGenReportState] = useGenerateReportFile(diligenceService.getReport);
    const handleGenerateReport = async () => {
      track(createTrackEvent(6204, '准入排查详情页', '生成报告'));

      // FIXME: 权限控制
      // if (!(await ability.check('stock', ['DiligenceReportQuantity']))) {
      //   return;
      // }
      if (!riskInfo.value?.id) {
        Message.warning('数据异常，请刷新重试');
        return;
      }
      try {
        genReportState.status = GenerateReportStatus.Pending;
        const { batchId } = await diligenceService.pollingGenerateReport(riskInfo.value?.id);
        Message.success('您的报告正在生成中，成功后我们将第一时间提醒您');
        genReportPolling(batchId);
      } catch (error: any) {
        genReportState.status = GenerateReportStatus.Idle;
        const errorMessage = error?.response?.data?.error ?? '报告生成失败，请稍后再试';
        Message.error(errorMessage);
      }
    };

    /** 生成快照 */
    const [refreshingSnapshot, setRefreshingSnapshot] = useToggle(false);
    const refreshSnapshot = async () => {
      if (refreshingSnapshot.value) {
        return;
      }
      setRefreshingSnapshot(true);
      try {
        // 更新排查数据
        await fetchData(company.value.KeyNo, true);
        resetGenReportState(); // 重置报告生成状态
      } catch (error) {
        console.error(error);
      } finally {
        setRefreshingSnapshot(false);
      }
    };

    const handleRouteChange = async (params, oldParams: any = {}) => {
      if (params.id !== oldParams.id) {
        await fetchData(params.id, false);
      }
      await fetchRiskModels();
    };
    watch(() => route.params, handleRouteChange, {
      immediate: true,
      deep: true,
    });

    const handleErrorRetry = () => {
      fetchData(company.value.KeyNo, false);
    };

    return {
      dimensionDetails,
      riskInfo,
      company,
      loading,
      riskLevel,
      refreshingSnapshot,
      snapshotDate,
      handleGenerateReport,
      errorOutOfLimitation,
      tc,
      refreshSnapshot,
      allModelRiskInfoList,
      riskModels,
      handleErrorRetry,

      genReportState,
      genReportPolling,

      isCurrentDDResultsDesc,

      route,
      router,
    };
  },
  render() {
    const { company, loading, route, router } = this;
    let routeList: any = [];
    // FIXME: 重构面包屑组件
    switch (route.query.from) {
      // 准入排查
      case 'investigation':
        routeList = [
          {
            id: 'home',
            route: {
              path: '/investigation/search',
            },
            name: '准入尽调',
          },
        ];
        break;
      // 企业尽调列表
      case 'list':
        routeList = [
          {
            id: 'assessment-batch',
            route: {
              path: `/investigation/search/list`,
              query: pick(route.query, ['keyword', 'type', 'orgModelIds']),
            },
            name: '搜索列表',
          },
        ];
        break;
      case 'record':
        routeList = [
          {
            id: 'investigation-history',
            route: {
              path: '/investigation/history',
              query: {
                useCacheQuery: 'true',
              },
            },
            name: '尽调记录',
          },
        ];
        break;
      default:
        routeList = [];
    }

    // 是否有面包屑
    const hasBreadcrumb = !this.isExternal && routeList.length > 0;
    const breadcrumbHeight = hasBreadcrumb ? 50 : 0;
    const bodyPaddingTop = breadcrumbHeight > 0 ? 0 : breadcrumbHeight; // 需要考虑容器中的 paddingTop
    // 布局样式
    const bodyStyle = getBodyStyle({
      paddingTop: `${bodyPaddingTop}px`,
    });
    // 风险排查内容 context
    const riskContentContext: any = {
      props: {
        loading,
        dimensions: this.riskInfo.details || [],
        company,
        riskInfo: this.riskInfo,
        riskLevel: this.riskInfo.details?.result || 0,
        allModelRiskInfoList: this.allModelRiskInfoList,
        riskModels: this.riskModels,
        offset: {
          y: 102,
          x: 110,
        },
        errorInfo: this.errorOutOfLimitation,
        breadcrumbHeight,
      },
      on: {
        retry: this.handleErrorRetry,
      },
    };

    // 渲染操作按钮
    const actionsRenderer = () => {
      // 重新排查
      const reScanAction = this.snapshotDate ? (
        <Tooltip title="点击将刷新页面重新排查">
          <RiskAction
            icon="icon-icon_sqq"
            loading={this.refreshingSnapshot}
            tid="refresh-snapshot"
            v-debounceclick={this.refreshSnapshot}
            theme="slight"
          >
            {this.tc('Screening time')}: {this.snapshotDate}
          </RiskAction>
        </Tooltip>
      ) : null;

      // 下载报告（轮询）
      const downloadReportAction = this.isCurrentDDResultsDesc
        ? []
        : [
            /** 轮询获取下载报告链接 */
            <div v-show={this.genReportState.status === GenerateReportStatus.Pending}>
              <RiskAction loading={true} v-permission={[Permission.INVESTIGATION_REPORT]}>
                <div class={styles.downloading}>
                  <span class={styles.icon}>
                    <img slot="indicator" width="14" height="14" src={LoadingIcon} />
                  </span>
                  <span>{this.tc('Report Generating')}</span>
                </div>
              </RiskAction>
            </div>,

            /** 下载报告 */
            <div v-show={this.genReportState.status === GenerateReportStatus.Success}>
              <RiskAction tag="a" v-permission={[Permission.INVESTIGATION_REPORT]} href={this.genReportState.downloadLink} icon="icon-a-wenjianxiazaimian">
                {this.tc('Download Report')}
              </RiskAction>
            </div>,

            /** 生成报告 */
            <div
              v-show={
                this.genReportState.status === GenerateReportStatus.Idle || this.genReportState.status === GenerateReportStatus.Failed
              }
            >
              <RiskAction
                v-permission={[Permission.INVESTIGATION_REPORT]}
                icon="icon-shengchengbaogao1"
                onClick={this.handleGenerateReport}
              >
                {this.tc('Report')}
              </RiskAction>
            </div>,
          ];

      // 添加至
      // const watchAction = <RiskWatch onInit={this.getOtherInfo} />;

      return [
        reScanAction, // 重新排查
        ...downloadReportAction, // 下载报告（轮询）
        // watchAction, // 添加至
      ];
    };

    return (
      <div class={styles.container}>
        {!this.isExternal && company?.Name && routeList?.length ? (
          <div class="flex justify-between items-center sticky-breadcrumb">
            <div>
              <Breadcrumb>
                {routeList.map((v: any, index: number) => {
                  return (
                    <Breadcrumb.Item key={v.id}>
                      <a
                        onClick={() => {
                          // 从搜索结果列表进入时，直接返回上一级
                          if (v.id === 'investigateList' && route.query.query === 'list') {
                            router.back();
                            return;
                          }
                          // 上级页面有window.open的行为，返回上一页不可用
                          if (v.route === 'back' || v.route?.path === 'back') {
                            router.back();
                          } else {
                            router.push(v.route);
                          }
                        }}
                      >
                        {index === 0 ? <q-icon type="icon-mianbaoxiefanhui"></q-icon> : null}
                        {v.name}
                      </a>
                    </Breadcrumb.Item>
                  );
                })}
                <Breadcrumb.Item>{company?.Name}</Breadcrumb.Item>
              </Breadcrumb>
            </div>
          </div>
        ) : null}
        <HeroicLayout
          innerStyle={{ minHeight: this.isExternal ? '100vh' : `calc(100vh - 52px - 60px)` }}
          bodyStyle={bodyStyle}
          loading={loading}
          gap={false}
        >
          {/* 工商信息 */}
          {!loading && company?.KeyNo ? (
            <QCard slot="hero" bodyStyle={{ padding: '16px' }}>
              <BasicInfo company={company}>
                <template slot="extra">{actionsRenderer()}</template>
              </BasicInfo>
            </QCard>
          ) : null}

          {/* 排查信息 */}
          {!loading && company?.KeyNo ? <RiskContentDefault {...riskContentContext}></RiskContentDefault> : null}
        </HeroicLayout>
      </div>
    );
  },
});

export default InvestigateDetailPage;
