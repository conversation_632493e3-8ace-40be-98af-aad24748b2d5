import { computed, defineComponent, ref } from 'vue';
import { <PERSON><PERSON>, Icon } from 'ant-design-vue';
import { escape } from 'lodash';
import { useRoute, useRouter } from 'vue-router/composables';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import QTag from '@/components/global/q-tag';
import SearchCount from '@/components/search-count';
import { getRiskLevelStyle, getTagStyle, MetricsTypeMap, RISK_LEVEL_MAP, TrendsStatus } from '@/config/risk.config';
import CompanyLogo from '@/components/company-logo';
import EmptyWrapper from '@/shared/components/empty-wrapper';
import TrendsContent from '@/shared/components/trends-content';
import { Permission } from '@/config/permissions.config';

import { openFollowUpDrawer } from '../add-follow-up-drawer';
import styles from './search-result.module.less';
import CompanyRelatedPop from '../../../targets/widgets/company-related-pop';
import { OpenRelatedCompanyModal } from '../../../targets/widgets/open-related-company-modal';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    searchKey: {
      type: String,
      default: '',
    },
    selectedItems: {
      type: Array,
      default: () => [],
    },
    totalCompany: {
      type: Number,
      default: 0,
    },
  },
  emits: ['removeItem', 'selectItems', 'changePage', 'changeSettings', 'refresh', 'openCourtNotice', 'sorterChange'],
  setup(props, { emit }) {
    const selectedIds = ref([]);

    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: selectedIds.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedIds.value = selectedRowKeys;
        // emit('selectItems', selectedRows);
      },
    }));

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const handleChangeSettings = () => {
      emit('changeSettings');
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    const router = useRouter();
    const route = useRoute();

    const handleGoToDetail = (record) => {
      router.push({
        path: `/risk-monitor/trends/detail/${record.companyId}`,
        params: {
          id: record.companyId,
        },
        query: {
          from: 'trends', // 面包屑导航
          name: route.meta?.title,
          groupId: record.monitorGroupId,
        },
      });
    };

    const handleFollowUp = async (record) => {
      try {
        await openFollowUpDrawer({
          record: { ...record },
          handleOk: () => {
            emit('refresh');
          },
        });
      } catch (error) {
        console.error(error);
      }
    };

    const handleExport = (key: string) => {
      console.log(key);
    };

    return {
      selectedIds,
      rowSelection,

      paginationProps,
      handleChangeSettings,

      handleFollowUp,
      handleGoToDetail,

      handleExport,
    };
  },
  render() {
    return (
      <QCard bodyStyle={{ padding: '15px' }} class={styles.container}>
        <div slot="title" class="flex items-center">
          <SearchCount
            showSelects={false}
            total={this.pagination.total}
            loading={this.isLoading}
            scopedSlots={{
              message: (content) => {
                return <div class="flex items-center">共找到{content}条相关动态，</div>;
              },
            }}
          />
          <SearchCount
            showSelects
            total={this.totalCompany}
            loading={this.isLoading}
            selectedIds={this.selectedIds}
            scopedSlots={{
              message: (content) => {
                return <div class="flex items-center">{content}家企业</div>;
              },
            }}
          />
        </div>
        <div slot="extra">
          {/* <DropdownButtonWrapper
            totalCount={this.pagination.total}
            btnText="导出列表"
            needPopConfirm={false}
            menuItems={EXPORTITEMS}
            onConfirm={this.handleExport}
          /> */}
        </div>
        <EmptyWrapper dataSource={this.dataSource} loading={this.isLoading}>
          <QRichTable
            showIndex={true}
            tableLayout="fixed"
            loading={this.isLoading}
            rowKey={this.rowKey}
            dataSource={this.dataSource}
            columns={this.columns}
            pagination={this.paginationProps}
            // rowSelection={this.rowSelection}
            scroll={{ y: 'calc(100vh - 146px - 215px)' }}
            onChange={({ sorter }) => {
              this.$emit('sorterChange', sorter);
            }}
            scopedSlots={{
              RiskLevel: (riskLevel) => {
                const tag = RISK_LEVEL_MAP[riskLevel];
                return <span style={getRiskLevelStyle(riskLevel, true)}>{tag}</span>;
              },
              MetricsContent: (item) => {
                return <TrendsContent record={item} onClick={() => this.$emit('openCourtNotice', item)} />;
              },
              MetricsType: (typeCode) => {
                const { label = '-', style = {} } = MetricsTypeMap[typeCode] || {};
                return (
                  <span
                    style={{
                      ...getTagStyle(style),
                    }}
                  >
                    {label}
                  </span>
                );
              },
              MonitorCompany: (item) => {
                const companyName = escape(item.companyName).replace(this.searchKey, `<em style="color: #f04040">${this.searchKey}</em>`);
                return (
                  <div class="flex items-center" style={{ gap: '10px' }}>
                    <CompanyLogo
                      src={item.ImageUrl}
                      id={item.companyId || ''}
                      name={item.ShortName || item.companyName}
                      hasimage={item.HasImage}
                    />
                    <div class="flex-1">
                      <span data-testid="company-name" class="mr-1" domPropsInnerHTML={companyName} />
                      <CompanyRelatedPop
                        class="mr-1 align-bottom"
                        typeCode={item.relatedCompany?.length > 0 ? 0 : 1}
                        totalCount={item.relatedPartyCount}
                        monitorGroupId={item.monitorGroupId}
                        companyInfo={{
                          companyId: item.companyId,
                          companyName: item.companyName,
                        }}
                        onOpenRelated={async (options) => {
                          OpenRelatedCompanyModal({
                            record: item,
                            options,
                          });
                        }}
                      />
                    </div>
                    <Button type="link" onClick={() => this.handleGoToDetail(item)}>
                      动态 <Icon type="right"></Icon>
                    </Button>
                  </div>
                );
              },
              MonitorStatus: (statusCode) => {
                const labelMap = TrendsStatus.reduce((acc, cur) => {
                  acc[cur.value] = cur.label;
                  return acc;
                }, {});
                const dotMap = {
                  '-1': { backgroundColor: '#BBB' },
                  '0': { backgroundColor: '#B3B3B3' },
                  '1': { backgroundColor: '#00A35B' },
                  '2': { backgroundColor: '#FFA000' },
                };
                return (
                  <span style={{ display: 'inline-flex', alignItems: 'center', gap: '5px' }}>
                    {/* <i
                      style={{
                        ...dotMap[statusCode],
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                      }}
                    ></i> */}
                    {labelMap[statusCode]}
                  </span>
                );
              },
              MonitorResult: (resultCode) => {
                const codeMap = {
                  '-1': '未排查',
                  '0': '通过',
                  '1': '风险较高',
                  '2': '慎重考虑',
                };
                const typeMap = {
                  '-1': 'default',
                  '0': 'success',
                  '1': 'warning',
                  '2': 'danger',
                };
                return <QTag type={typeMap[resultCode]}>{codeMap[resultCode]}</QTag>;
              },
              Action: (record) => {
                return (
                  <Button v-permission={[Permission.RISK_TRENDS_FOLLOW_UP]} type="link" onClick={() => this.handleFollowUp(record)}>
                    {record.status === 1 ? '查看' : '跟进'}
                  </Button>
                );
              },
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
