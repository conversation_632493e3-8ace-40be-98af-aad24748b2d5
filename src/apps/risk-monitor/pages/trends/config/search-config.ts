import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const SEARCH_RESULT_TABLE_COLUMNS = [
  {
    title: '企业名称',
    // fixed: 'left',
    scopedSlots: {
      customRender: 'MonitorCompany',
    },
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    width: 80,
    scopedSlots: {
      customRender: 'RiskLevel',
    },
  },

  // 风险指标
  {
    title: '风险指标',
    width: 120,
    dataIndex: 'metricsName',
  },

  {
    title: '风险标签',
    width: 80,
    dataIndex: 'metricsType',
    scopedSlots: {
      customRender: 'MetricsType',
    },
  },

  // 风险内容
  {
    title: '风险内容',
    scopedSlots: {
      customRender: 'MetricsContent',
    },
  },
  // 状态
  {
    title: '状态',
    dataIndex: 'status',
    width: 68,
    scopedSlots: {
      customRender: 'MonitorStatus',
    },
  },

  // 更新时间
  {
    title: '更新时间',
    width: 168,
    dataIndex: 'createDate',
    sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },

  {
    title: '操作',
    width: 100,
    // fixed: 'right',
    scopedSlots: {
      customRender: 'Action',
    },
  },
];

export const getSearchFilterConfig = ({
  groupId,
  riskLevels,
  metricsIds,
  metricType,
  dataStatus,
  primaryObjects,
}: {
  groupId: any[];
  riskLevels: any[];
  metricsIds: any[];
  metricType: any[];
  dataStatus: any[];
  primaryObjects: any[];
}) => {
  return [
    {
      field: 'groupId',
      type: 'button-multiple',
      label: '所属分组',
      options: groupId,
      meta: {
        maxLength: 15,
      },
    },
    {
      field: 'filters',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'primaryObjects',
          type: 'multiple',
          label: '企业角色',
          options: primaryObjects,
        },
        {
          field: 'riskLevels',
          type: 'multiple',
          label: '风险等级',
          options: riskLevels,
        },
        {
          field: 'metricsIds',
          type: 'multiple',
          label: '风险指标',
          options: metricsIds,
          meta: {
            showFilter: true,
          },
        },
        {
          field: 'metricTypes',
          type: 'multiple',
          label: '风险标签',
          options: metricType,
        },
        {
          field: 'dataStatus',
          type: 'multiple',
          label: '状态',
          options: dataStatus,
        },
        {
          field: 'createDate',
          type: 'single',
          label: '更新时间',
          options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
          custom: {
            type: 'date-range',
          },
        },
      ],
    },
  ];
};
