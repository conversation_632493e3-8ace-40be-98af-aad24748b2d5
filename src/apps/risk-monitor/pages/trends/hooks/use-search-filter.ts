import { ref, computed, onMounted, reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { isEqual, isNil, sortBy } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService } from '@/shared/services';
import { getSearchFilterConfig } from '@/apps/risk-monitor/pages/trends/config/search-config';
import { MetricsTypes, RISK_LEVEL, TrendsStatus } from '@/config/risk.config';
import { useCacheQuery } from '@/hooks/use-cache-query';

type FilterOptions = {
  label: string;
  value: string | number;
  count?: number;
};

export const useSearchFilter = () => {
  const route = useRoute();
  const router = useRouter();

  const monitorGroups = useRequest(monitorService.getAllGroups);
  const aggsOptionSearch = useRequest(monitorService.searchDynamics);
  const searchDynamic = useRequest(monitorService.searchDynamics);

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const isInit = ref(true);

  /** 公司数量汇总 */
  const totalCompany = computed(() => {
    const data = searchDynamic.data.value?.aggsResponse || {};
    return data['4_companyCount']?.value || 0;
  });

  /** 表格数据 */
  const dataSource = computed(() => {
    const data = searchDynamic.data.value || {};
    return data.data || [];
  });

  const { cached } = useCacheQuery(
    // Namespace
    'risk-monitor-trends',
    // Defaults
    {
      query: defaultFilterValues,
      pagination: {
        pageSize: 10,
        current: 1,
        total: 0,
      },
      sort: {},
    },
    // Deps
    { route, router }
  );

  const isFilterLoading = ref(false);
  const filterOptions = reactive<{
    groupId: FilterOptions[];
    riskLevels: FilterOptions[];
    metricsIds: FilterOptions[];
    metricType: FilterOptions[];
    dataStatus: FilterOptions[];
    primaryObjects: FilterOptions[];
  }>({
    groupId: [],
    riskLevels: [],
    metricsIds: [],
    metricType: [],
    dataStatus: [],
    primaryObjects: [],
  });

  const transformFilterOptions = (aggsData: any[] = [], dict: any[] = [], isNumber = false) => {
    return aggsData.map((item) => {
      const option = dict.find((v) => v.value.toString() === item.key.toString());
      return {
        label: option?.label || '未知',
        value: isNumber ? Number(item.key) : item.key,
        count: item.doc_count,
      };
    });
  };

  /** 更新搜索选项卡 */
  const getFilterOptions = (group?, showAll = false) => {
    if (showAll) {
      isFilterLoading.value = true;
      return;
    }
    const data = searchDynamic.data.value?.aggsResponse || {};
    filterOptions.metricsIds =
      data['4_metricsId']?.buckets?.map((v) => {
        const label = v.metricsName?.buckets?.[0]?.key;
        return { label, value: v.key, count: v.doc_count };
      }) || [];
    filterOptions.riskLevels = sortBy(transformFilterOptions(data['4_riskLevel']?.buckets, RISK_LEVEL), (o) => -o.value);
    filterOptions.metricType = transformFilterOptions(data['4_metricsType']?.buckets, MetricsTypes);
    filterOptions.dataStatus = transformFilterOptions(data['4_dataStatus']?.buckets, TrendsStatus, true);
    filterOptions.primaryObjects = [
      { label: '企业主体', value: 1, count: data['4_relatedCompany_exist_stats']?.buckets?.no_relatedCompany?.doc_count || 0 },
      { label: '关联方', value: 2, count: data['4_relatedCompany_exist_stats']?.buckets?.has_relatedCompany?.doc_count || 0 },
    ].filter((v) => v.count > 0);
  };
  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return getSearchFilterConfig(filterOptions);
  });

  const search = async (payload?: Record<string, any>) => {
    const res = await searchDynamic.execute({
      pageIndex: cached.pagination.value.current,
      pageSize: cached.pagination.value.pageSize,
      aggsField: [4],
      ...cached.query.value.filters,
      keywords: cached.query.value.keywords,
      createDate: cached.query.value.filters?.createDate ? [cached.query.value.filters.createDate] : undefined,
      ...cached.sort.value,
      ...payload,
    });
    cached.pagination.value.total = res?.total > 50000 ? 50000 : res?.total;
    cached.pagination.value.current = res?.pageIndex;
    cached.pagination.value.pageSize = res?.pageSize;
  };

  /** groupId改变时，重置其他筛选项 */
  const resetOtherFilters = (remainedKeys: string[], currentFilters = {}) => {
    const filters = remainedKeys.reduce((acc, key) => {
      acc[key] = currentFilters[key];
      return acc;
    }, {});
    return {
      ...defaultFilterValues,
      filters,
    };
  };

  /** 更新搜索过滤 */
  const handleFilterChange = async (values) => {
    const { filters } = values;
    if (!isEqual(filters?.groupId, cached.query.value.filters?.groupId)) {
      cached.query.value = resetOtherFilters(['groupId'], filters);
    } else {
      cached.query.value = values;
    }
    cached.pagination.value.current = 1;
    await search();
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    cached.query.value = {
      ...defaultFilterValues,
    };
  };

  const initFilter = async () => {
    const queryMap = {
      metricsIds: (val) => val.toString().split(','),
      metricTypes: (val) => [Number(val)],
      dataStatus: (val) => val.toString().split(',').map(Number),
      createDate: (val) => JSON.parse(val.toString()),
      groupId: (val) => [Number(val)],
    };

    const filters = { ...cached.query.value.filters };
    Object.keys(queryMap).forEach((k) => {
      const val = route.query?.[k];
      if (val && isNil(filters[k])) {
        filters[k] = queryMap[k](val);
      }
    });
    cached.query.value = {
      ...cached.query.value,
      filters,
    };

    await search();
    isInit.value = false;
  };

  const getAggsGroups = () => {
    const aggsData = aggsOptionSearch.data.value?.aggsResponse || {};
    const allGroups = monitorGroups.data.value?.data || [];
    filterOptions.groupId =
      aggsData['4_monitorGroupId']?.buckets?.map((v) => {
        const item = allGroups.find((g) => g.monitorGroupId === +v.key) || {};
        return {
          label: item.name,
          value: item.monitorGroupId,
          count: v.doc_count,
        };
      }) || [];
  };

  /**
   * 下拉筛选取消所有勾选后，需要重置筛选项
   * 筛选项是从列表接口里获取的，此处等待接口请求结束
   *
   */
  watch([isFilterLoading, searchDynamic.isLoading], () => {
    if (isFilterLoading.value && !searchDynamic.isLoading.value) {
      getFilterOptions();
      isFilterLoading.value = false;
    }
  });

  /** 初始化 */
  onMounted(async () => {
    try {
      await aggsOptionSearch.execute<any>({ aggsField: [4] });
      await monitorGroups.execute<any>({ pageIndex: 1, pageSize: 100 });
      getAggsGroups();
      await initFilter();
    } catch (error) {
      console.error(error);
    }
  });

  return {
    filterValues: cached.query,
    filterGroups,
    handleFilterChange,
    handleFilterReset,
    isFilterLoading,
    isLoading: searchDynamic.isLoading,
    getFilterOptions,
    totalCompany,
    dataSource,
    pagination: cached.pagination,
    search,
    isInit,
    sortInfo: cached.sort,
    cached,
  };
};
