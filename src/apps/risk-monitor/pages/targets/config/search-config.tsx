import { Tooltip } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';

export const RelatedTypeMap = {
  // 主要人员控制企业
  PrincipalControl: '主要人员控制企业',
  // 法定代表人控制企业
  LegalRepresentativeControl: '法定代表人控制企业',
  // 实际控制人控制企业
  ActualControllerControl: '实际控制人控制企业',
  // 受益人控制企业
  BeneficiaryControl: '受益人控制企业',
  // 分支机构
  Branch: '分支机构',
  // 母公司（股东信息(股比>50%)）
  MotherCompanyMajorityShareholder: '母公司（股东信息(股比>50%)）',
  // 母公司控制企业
  MotherCompanyControl: '母公司控制企业',
  // 子公司（对外投资(>50%的企业)）
  MajorityInvestment: '子公司（对外投资(>50%的企业)）',
  // 关联方成员包含以上所有
  RelatedMember: '关联方成员包含以上所有',
};

export const SEARCH_RESULT_TABLE_COLUMNS = [
  {
    title: '企业名称',
    width: 356,
    scopedSlots: {
      customRender: 'expanded',
    },
  },
  {
    title: '登记状态',
    dataIndex: 'companyStatus',
    width: 107,
    scopedSlots: {
      customRender: 'companyStatus',
    },
  },
  {
    title: '企业分组',
    width: 130,
    scopedSlots: {
      customRender: 'MonitorGroup',
    },
  },
  {
    title: () => {
      return (
        <div>
          <span>风险等级</span>
          <Tooltip title="监控企业在最近4小时内产生风险动态的最高等级。">
            <QIcon
              type="icon-zhushi"
              class={'drag-handle'}
              style="margin-left: 4px;"
              onMouseenter={() => {
                const tableSortPop = document.querySelector('.table-sorter-menu') as HTMLElement;
                if (tableSortPop) {
                  tableSortPop.style.display = 'none';
                }
              }}
              onMouseleave={() => {
                const tableSortPop = document.querySelector('.table-sorter-menu') as HTMLElement;
                if (tableSortPop) {
                  tableSortPop.style.display = 'block';
                }
              }}
            ></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: 98,
    sorter: true,
    dataIndex: 'riskLevel',
    scopedSlots: {
      customRender: 'MonitorResult',
    },
  },
  {
    title: '操作人',
    width: 107,
    dataIndex: 'createBy',
    scopedSlots: {
      customRender: 'MonitorCreator',
    },
  },

  {
    title: '监控时间',
    width: 142,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作',
    width: 71,
    scopedSlots: {
      customRender: 'Action',
    },
  },
];

export const RelatedColoumns = [
  {
    title: '关联方企业名称',
    scopedSlots: {
      customRender: 'expanded',
    },
  },
  {
    title: '登记状态',
    dataIndex: 'companyStatus',
    width: 100,
    scopedSlots: {
      customRender: 'companyStatus',
    },
  },
  {
    title: '关联方类型',
    dataIndex: 'relatedTypeDescList',
    width: 130,
    customRender: (data) => {
      return RelatedTypeMap[data] ?? data?.join('，') ?? '-';
    },
  },
  {
    title: '关联方状态',
    dataIndex: 'status',
    width: 86,
    customRender: (data) => {
      return data ? '有效' : '失效';
    },
  },
  {
    title: '风险等级',
    width: 96,
    sorter: true,
    dataIndex: 'riskLevel',
    scopedSlots: {
      customRender: 'MonitorResult',
    },
  },
  {
    title: '监控时间',
    width: 160,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: {
      customRender: 'InnerAction',
    },
  },
];
