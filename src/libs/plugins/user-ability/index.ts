import _ from 'lodash';
import { DirectiveOptions, DirectiveFunction, VueConstructor } from 'vue';
import { Ability } from './ability';
import { AbilityRule } from './ability-rule';
import { type UserInfo } from './types';

type Options<U> = {
  user: U;
  rules: AbilityRule<U>[];
};

let ability: Ability<UserInfo>;

const PRESET_ACTIONS = {
  visible: {
    bind: (hasAbility: boolean, el: HTMLElement) => {
      if (!hasAbility) {
        el.style.display = 'none';
      } else {
        el.style.display = 'block';
      }
    },
    unbind: _.noop,
  },
};

const createHandler =
  (lifeCycle: 'bind' | 'unbind'): DirectiveFunction =>
  async (el, binding, vnode, oldVnode) => {
    // 匹配规则: 例如: `permission`
    const rule = binding.arg;
    // 规则传参
    const info = binding.value;
    // 匹配行为：例如：`visible`
    const actions = Object.keys(binding.modifiers);

    if (!rule) {
      return;
    }

    let hasAbility: boolean;
    try {
      hasAbility = await ability.check(rule, info);
    } catch (error) {
      hasAbility = false;
    }
    actions.forEach((actionType) => {
      const preset = PRESET_ACTIONS[actionType];
      if (preset && typeof preset[lifeCycle] === 'function') {
        const fn = preset[lifeCycle];
        fn.call(null, hasAbility, el, binding, vnode, oldVnode);
      }
    });
  };

function install(app: VueConstructor, options: Options<UserInfo>) {
  ability = new Ability<UserInfo>(options.user, options.rules);

  const directive: DirectiveOptions = {
    async bind(el, binding, vnode, oldVnode) {
      const fn = createHandler('bind');
      fn(el, binding, vnode, oldVnode);
    },
    async update(el, binding, vnode, oldVnode) {
      const fn = createHandler('bind');
      fn(el, binding, vnode, oldVnode);
    },
    unbind(el, binding, vnode, oldVnode) {
      const fn = createHandler('unbind');
      fn(el, binding, vnode, oldVnode);
    },
  };

  app.directive('ability', directive);
  app.prototype.$ability = ability;
}

/**
 * Hook
 */
export function useAbility(): Ability<UserInfo> {
  return ability;
}

export * from './ability-rule';

export default {
  name: 'user-ability',
  version: '0.1.0',
  install,
};
