export type BundleInfo = {
  [key: string]: {
    enabled: boolean;
    level: number;
  };
};

type BundleUsageStat = {
  limitation: number; // 用量上限
  stock: number; // 剩余库存用量
  [x: string]: unknown;
};

export type BundleUsage = {
  [key: string]: BundleUsageStat;
};

export type UserInfo = {
  permissions: number[]; // 权限（code）列表
  usage: BundleUsage; // 用量
  bundle?: BundleInfo; // 套餐功能点，第三方中不需要

  [key: string]: unknown;
};
