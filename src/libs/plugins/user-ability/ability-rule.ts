import type { UserInfo } from './types';

export type UserAbilityRuleName = string | undefined;
type UserAbilityRulePattern<U> = (user: U, ...args) => Promise<boolean>;

export class AbilityRule<U = UserInfo> {
  private pattern: UserAbilityRulePattern<U>;

  name: UserAbilityRuleName;

  message: string | undefined;

  constructor({ name, pattern, message }: { pattern: UserAbilityRulePattern<U>; name?: UserAbilityRuleName; message?: string }) {
    this.name = name;
    this.pattern = pattern;
    this.message = message;
  }

  // 权限检查
  async check(user: U, ...args): Promise<boolean> {
    try {
      const hasAbility = await this.pattern.call(this, user, ...args);
      return hasAbility;
    } catch (error) {
      return false;
    }
  }
}
