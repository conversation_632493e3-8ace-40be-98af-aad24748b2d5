import { AbilityRuleError } from './ability-error';
import { AbilityRule, UserAbilityRuleName } from './ability-rule';
import type { UserInfo } from './types';

export class Ability<U = UserInfo> {
  private _user: U;

  rules: AbilityRule<U>[];

  constructor(user: U, rules: AbilityRule<U>[] = []) {
    this._user = user;
    this.rules = rules;
  }

  get user() {
    return this._user;
  }

  async check(rule: UserAbilityRuleName | AbilityRule<U> | Array<AbilityRule<U> | UserAbilityRuleName>, ...args): Promise<boolean> {
    if (!this.user) {
      return false;
    }
    // isRuleName
    if (typeof rule === 'string') {
      const matchedRule = this.findRuleByName(rule);
      if (!matchedRule) {
        return false;
      }
      return matchedRule.check(this.user, ...args);
    }
    // isRule
    if (rule instanceof AbilityRule) {
      return rule.check(this.user, ...args);
    }
    // isRuleList
    if (Array.isArray(rule)) {
      return rule.every(async (r) => this.check(r, ...args));
    }
    return false;
  }

  /**
   * 根据条件名称匹配规则
   * @param ruleName
   */
  findRuleByName(ruleName: string) {
    return this.findRule((r) => ruleName === r.name);
  }

  /**
   * 根据条件实例匹配规则
   * @param {AbilityRule} rule
   */
  findRuleByInstance(rule: AbilityRule<U>) {
    return this.findRule((r) => rule === r);
  }

  /**
   * 查找匹配条件
   * @param match 查找规则
   */
  findRule(match: (rule: AbilityRule<U>) => boolean): AbilityRule<U> | null {
    const rules = this.rules;
    let result: AbilityRule<U> | null = null;
    for (let i = 0, length = rules.length; i < length; i += 1) {
      if (match(rules[i])) {
        result = rules[i];
        break;
      }
    }
    return result;
  }

  /**
   * 添加条件
   * @param rule {AbilityRule}
   */
  addRule(rule: AbilityRule<U>) {
    if (this.findRuleByInstance(rule)) {
      throw new AbilityRuleError('Rule already exists', rule);
    }
    if (rule.name && this.findRuleByName(rule.name)) {
      throw new AbilityRuleError('Rule already exists', rule);
    }
    this.rules.push(rule);
  }

  /**
   * 批量添加条件
   * @param rules {AbilityRule[]}
   */
  addRules(rules: AbilityRule<U>[]) {
    rules.forEach((rule) => this.addRule(rule));
  }
}
