import { createLocalVue, mount } from '@vue/test-utils';
import UserAbilityPlugin, { AbilityRule, useAbility } from '..';
import { Ability } from '../ability';
import userAbility from '..';
import { defineComponent } from 'vue';
import { flushPromises } from '@/test-utils/flush-promises';

describe('UserAbility', () => {
  let localVue: ReturnType<typeof createLocalVue>;
  let abilityRule: AbilityRule;

  beforeEach(() => {
    abilityRule = new AbilityRule({
      name: 'TEST',
      pattern: (user, arg) => {
        // console.log({
        //   user,
        // });
        if (arg === undefined || arg === 1) {
          return Promise.resolve(true);
        }
        return Promise.resolve(false);
      },
      message: '测试',
    });
    localVue = createLocalVue();
    localVue.use(UserAbilityPlugin, {
      user: {
        permissions: [1, 2, 3],
        usage: {
          a: {
            limitation: 1,
            stock: 10,
          },
        },
      },
      rules: [abilityRule],
    });
  });

  describe('Vue plugin', () => {
    test('install', async () => {
      expect(await localVue.prototype.$ability.check(abilityRule)).toBe(true);
      expect(await localVue.prototype.$ability.check('TEST')).toBe(true);
      expect(await localVue.prototype.$ability.check(['TEST'])).toBe(true);

      expect(await localVue.prototype.$ability.check('X')).toBe(false);
      expect(await localVue.prototype.$ability.check()).toBe(false);

      expect(localVue.prototype.$ability).toBeInstanceOf(Ability);
      expect(localVue.directive('ability')).toMatchInlineSnapshot(`
        {
          "bind": [Function],
          "unbind": [Function],
          "update": [Function],
        }
      `);
    });

    test('add rule', async () => {
      const rule = new AbilityRule({
        name: 'X',
        pattern: () => {
          return Promise.resolve(true);
        },
        message: '测试',
      });
      localVue.prototype.$ability.addRule(rule);
      expect(await localVue.prototype.$ability.check('X')).toBe(true);
    });

    test('add rules', async () => {
      const rule = new AbilityRule({
        name: 'X',
        pattern: () => {
          return Promise.resolve(true);
        },
        message: '测试',
      });
      localVue.prototype.$ability.addRules([rule]);
      expect(await localVue.prototype.$ability.check('X')).toBe(true);
    });

    test('should throw error when add a rule that exists', () => {
      expect(() => {
        localVue.prototype.$ability.addRule(abilityRule);
      }).toThrow();

      expect(() => {
        localVue.prototype.$ability.addRule({ name: 'TEST' });
      }).toThrow();
    });
  });

  describe('Vue Directive', () => {
    test('should show content when user have ability', async () => {
      const TestComponent = defineComponent({
        template: `<div v-ability:TEST.visible='1'>test</div>`,
      });
      const wrapper = mount(TestComponent, {
        localVue,
      });
      await flushPromises();
      expect(wrapper.isVisible()).toBe(true);
    });

    test('should not show content when user does not have ability', async () => {
      const TestComponent = defineComponent({
        template: `<div v-ability:TEST.visible='0'>test</div>`,
      });
      const wrapper = mount(TestComponent, {
        localVue,
      });
      await flushPromises();
      expect(wrapper.isVisible()).toBe(false);
    });
  });

  describe('Composition API', () => {
    test('useAbility', () => {
      const ability = useAbility();
      expect(ability).toBeInstanceOf(Ability);
    });
  });
});
