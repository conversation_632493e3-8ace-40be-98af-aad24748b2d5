type MessageData = {
  appName: string;
  event: string;
  originUrl: string; // location.href
  ref?: string;
  entity?: Record<string, any>; // JSON.stringify
  uid?: string; // 用户id, 非 qcc.com 用户体系的情况下传
};

interface Messager {
  sdk: string;

  send(data: MessageData): void;

  normalizeData(data: MessageData): string;
}

class ImageMessager implements Messager {
  sdk: string;

  constructor(sdk: string) {
    this.sdk = `${sdk}/s.gif`;
  }

  normalizeData(data: MessageData): string {
    const params = Object.keys(data)
      .map((key) => {
        let value = data[key];
        if (value && key === 'entity') {
          value = JSON.stringify(data[key]);
        }
        return [key, encodeURIComponent(value)].join('=');
      })
      .join('&');

    return params;
  }

  send(data: MessageData) {
    const src = `${this.sdk}?${this.normalizeData(data)}`;
    const img = document.createElement('img');
    img.src = src;
  }
}

// class ScriptMessager implements Messager {}

// class IframeMessager implements Messager {}

export class Tracer {
  appName: string;

  applicationName?: string;

  userId?: string;

  beforeSend?: () => void;

  enable: boolean;

  private messager: Messager;

  constructor({
    appName,
    applicationName,
    messager,
    beforeSend,
    enable = true,
  }: {
    appName: string;
    applicationName?: string;
    messager: Messager;
    beforeSend?: () => void;
    enable: boolean;
  }) {
    this.appName = appName;
    this.applicationName = applicationName;
    this.messager = messager;
    this.beforeSend = beforeSend;
    this.enable = enable;
  }

  track<T extends Record<string, any>>({ event, entity }: { event: string; entity?: T }) {
    if (this.enable) {
      return;
    }

    if (typeof this.beforeSend === 'function') {
      this.beforeSend();
    }

    const payload: MessageData = {
      appName: this.appName,
      event,
      originUrl: document.location.href,
      entity: entity && this.userId ? { ...entity, uid: this.userId } : entity,
      // FIXME: application_name 通过传递过来
      // application_name: this.applicationName,
      // ref?: string;
      // uid?: string; // 用户id, 非 qcc.com 用户体系的情况下传
    };

    setTimeout(() => {
      this.messager.send(payload);
      if (window.__TRACER_DEBUG__) {
        this.log(payload);
      }
    }, 0);
  }

  log(data: MessageData) {
    console.group(`%cTRACER ${new Date().toLocaleString()}`, 'color:#CD6155');
    console.log(JSON.stringify(data, null, 2));
    console.groupEnd();
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  static create({
    appName,
    applicationName,
    sdk,
    beforeSend,
    enable,
  }: {
    appName: string;
    applicationName?: string;
    sdk: string;
    beforeSend?: () => void;
    enable: boolean;
  }) {
    const messager = new ImageMessager(sdk);
    return new Tracer({
      appName,
      applicationName,
      messager,
      beforeSend,
      enable,
    });
  }
}
