// function install(app: VueConstructor, options: Options<UserInfo>) {
//   ability = new Ability<UserInfo>(options.user, options.rules);

import { DirectiveOptions, VueConstructor } from 'vue';
import { Tracer } from './core';

//   const directive: DirectiveOptions = {
//     async bind(el, binding, vnode, oldVnode) {
//       const fn = createHandler('bind');
//       fn(el, binding, vnode, oldVnode);
//     },
//     async update(el, binding, vnode, oldVnode) {
//       const fn = createHandler('bind');
//       fn(el, binding, vnode, oldVnode);
//     },
//     unbind(el, binding, vnode, oldVnode) {
//       const fn = createHandler('unbind');
//       fn(el, binding, vnode, oldVnode);
//     },
//   };

//   app.directive('ability', directive);
//   app.prototype.$ability = ability;
// }

// /**
//  * Hook
//  */
// export function useAbility(): Ability<UserInfo> {
//   return ability;
// }

// export * from './ability-rule';

function install(app: VueConstructor, options: { appName: string; applicationName?: string; sdk: string; beforeSend?: () => void }) {
  if (!options) {
    throw new Error('options is required!');
  }

  const tracer = Tracer.create({
    appName: options.appName,
    applicationName: options.applicationName,
    sdk: options.sdk,
    beforeSend: options.beforeSend,
    enable: false,
  });

  app.prototype.$tracer = tracer;
  app.prototype.$track = tracer.track.bind(tracer);

  // app.directive('track', {
  //   inserted(el, binding) {
  //     const { eventName, ...data } = binding.value || {};
  //     el.addEventListener('click', () => {
  //       tracer.track(eventName, data);
  //     });
  //   },
  // });
}

export default {
  name: 'user-tracking',
  version: '0.1.0',
  install,
};

declare module 'vue/types/vue' {
  interface Vue {
    $tracer: InstanceType<typeof Tracer>;
    $track: Tracer['track'];
  }
}
