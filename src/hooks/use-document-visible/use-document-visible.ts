import { onMounted, onUnmounted, ref } from 'vue';

const isDocumentVisible = () => {
  return document.visibilityState === 'visible';
};

export const useDocumentVisible = () => {
  const documentVisibility = ref(isDocumentVisible());

  const handler = () => {
    documentVisibility.value = isDocumentVisible();
  };

  onMounted(() => {
    document.addEventListener('visibilitychange', handler);
  });

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handler);
  });

  return documentVisibility;
};
