import { shallowReactive } from 'vue';

export type Pagination = {
  current: number | undefined;
  pageSize: number | undefined;
};

export const usePagination = (options: Partial<Pagination> = {}) => {
  const defaults: Pagination = {
    current: 1,
    pageSize: 10,
  };
  const pagination = shallowReactive({
    ...defaults,
    ...options,
  });
  const setPagination = ({ current, pageSize }: Pagination) => {
    // pageSize 有变更时，需要重置 current 到第一页
    const needReset = pageSize !== undefined && pageSize !== pagination.pageSize;
    pagination.current = needReset ? 1 : current || pagination.current;
    pagination.pageSize = pageSize || pagination.pageSize;
  };

  return [pagination, setPagination] as const;
};
