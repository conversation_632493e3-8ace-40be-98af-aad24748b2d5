import { useStorage } from '@vueuse/core';
import { cloneDeep, has, omit } from 'lodash';
import { unref } from 'vue';

import { useUserStore } from '@/shared/composables/use-user-store';

const { profile } = useUserStore();

export const getTableColumns = (params) => {
  const { key, data } = params;
  const newColoum = cloneDeep(data);
  // 表格最新的排序map
  const newIndexMap = newColoum.reduce((indexMap, cur, index) => {
    indexMap[cur.title] = index;
    return indexMap;
  }, {});

  const localColoums = useStorage<any[]>(`${key}-${unref(profile)?.currentOrg}-${unref(profile)?.id}`, []);
  let newPart: any[] = [];
  // 通过本地的缓存的表格列进行匹配，
  // 如果新的列存在当前列，则保存下来，更新除了显隐之外的其他列；并删除对应的当前列
  if (unref(localColoums)?.length !== 0) {
    newPart = unref(localColoums).reduce<any[]>((columns, cur: any) => {
      const exsitIndex = newIndexMap[cur.title]; // 寻找新的设置中的对应的列
      if (exsitIndex > -1) {
        columns.push({
          ...cur,
          ...omit(newColoum[exsitIndex], ['show']),
        });
        newColoum.splice(exsitIndex, 1, undefined);
      }
      return columns;
    }, []);
    // 得到插入列的位置，如果大于表格棕列数，插入最后，不然就在自己的位置，如果是操作列，就在最后
    const getIndex = (item) => {
      const len = newPart.length;
      if (item.title === 'title') {
        return len;
      }
      if (newIndexMap[item.title] <= len - 1) {
        return newIndexMap[item.title];
      }
      return -1;
    };
    newColoum
      .filter((coloum) => coloum)
      .forEach((element) => {
        const insertIndex = getIndex(element);
        newPart.splice(insertIndex, 0, element);
      });
  } else {
    newPart = newColoum;
  }

  localColoums.value = [...newPart];
  // 遍历，如果没有show，增加对应的属性
  localColoums.value.forEach((item: Record<string, any>) => {
    if (!has(item, 'show')) {
      item.show = true;
    }
  });
  return localColoums;
};
