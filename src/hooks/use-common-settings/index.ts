import { message } from 'ant-design-vue';
import { differenceBy, uniqBy } from 'lodash';
import { computed, reactive, ref, shallowReactive, unref } from 'vue';

import { getTableColumns } from './use-store-table-columns';
import { useTableFixed } from '../use-table-fixed';

// key是数据的唯一标志符号
export const useCommonSettingStore = ({ idKey, key, columnData }) => {
  const init = ref(true); // FIXME: 在当前 store 中没有被引用过，没有在当前 store 存在的意义
  const showBatchData = ref(false);
  const selectRows = ref<any[]>([]); // 选中的行
  const dataSource = ref<any[]>([]); // 当前页的数据
  const columsOri = getTableColumns({ key, data: columnData });
  const columns = computed(() => columsOri.value.filter((item) => item.show));
  const { isFixed, dynamicColumns } = useTableFixed({ columns, dataSource });
  const selectedIds = computed(() => selectRows.value.map((item) => item[idKey])); // 选中的id
  // 选中的标签
  const selectedItemLabels = ref<any[]>([]);

  // 按钮的disabled
  const btnDisable = computed(() => !selectedIds.value.length);
  // 分页
  const pagination = reactive({
    pageSize: 10,
    current: 1,
    total: 0,
  });
  //  排序
  const sortInfo = shallowReactive<{ sortField?: string; isSortAsc?: boolean }>({
    sortField: undefined,
    isSortAsc: undefined,
  });

  const showBatchSelectAll = computed(() => unref(showBatchData) && pagination.total > unref(dataSource).length);

  // 选中的标签
  const generateLabels = (data = selectRows.value) => {
    selectedItemLabels.value = uniqBy(
      data.reduce((labelsArr, row) => [...labelsArr, ...(row.labels ?? [])], []),
      'labelId'
    );
  };
  // 生成选中的数据
  const generateSelectData = (currentSelects) => {
    if (selectRows.value.length > 5000) {
      message.warning('单次勾选最大数量不超过5000条！');
      return;
    }
    selectRows.value = uniqBy([...differenceBy(unref(selectRows), unref(dataSource), idKey), ...currentSelects], idKey);
    generateLabels();
  };

  return {
    init,
    selectRows,
    dataSource,
    selectedIds,
    selectedItemLabels,
    pagination,
    sortInfo,
    btnDisable,
    columsOri,
    columns,
    showBatchData,
    showBatchSelectAll,
    generateSelectData,
    dynamicColumns,
    isFixed,
  };
};
