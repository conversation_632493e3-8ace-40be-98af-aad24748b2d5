import { shallowReactive } from 'vue';

export type Sort = {
  order: string | undefined;
  field: string | undefined;
};

export const useSortOrder = (options: Partial<Sort> = {}) => {
  const defaults: Sort = {
    order: undefined,
    field: undefined,
  };
  const sort = shallowReactive({
    ...defaults,
    ...options,
  });
  const setSort = ({ order, field }: Partial<Sort>) => {
    sort.order = order;
    sort.field = field;
  };

  return [sort, setSort] as const;
};
