import { useUnionFilter } from '../index';

const dictionary = [
  { label: 'labelA', value: '1' },
  { label: 'labelB', value: '2' },
  { label: 'labelC', value: '3' },
];
const cascaderDictionary = [
  {
    label: '1',
    value: 1,
    children: [
      { label: '1-1', value: 11 },
      { label: '1-2', value: 12 },
      {
        label: '1-3',
        value: 13,
        children: [
          { label: '1-3-1', value: 131 },
          { label: '1-3-2', value: 132 },
          { label: '1-3-3', value: 133 },
        ],
      },
    ],
  },
  {
    label: '2',
    value: 2,
  },
  {
    label: '3',
    value: 3,
    children: [
      { label: '3-1', value: 31 },
      { label: '3-2', value: 32 },
    ],
  },
];

describe('useUnionFilter', () => {
  test('filterConfig中无key值不调用接口', async () => {
    const func = vi.fn();
    const { getUnionOptions, optionsMap } = useUnionFilter(func);
    await getUnionOptions({});
    expect(func).toHaveBeenCalledTimes(0);
    expect(optionsMap.value).toEqual({});
  });

  test('获取type为multiple时的选项', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() =>
      Promise.resolve([
        { fieldLabel: 'labelA', fieldValue: '1', count: 100 },
        { fieldLabel: 'labelB', fieldValue: '2', count: 10 },
        { fieldLabel: 'labelC', fieldValue: '3', count: 1 },
      ])
    );
    await getUnionOptions({
      key: 'test',
      type: 'multiple',
    });
    expect(optionsMap.value).toEqual({
      test: [
        { label: 'labelA(100)', value: '1' },
        { label: 'labelB(10)', value: '2' },
        { label: 'labelC(1)', value: '3' },
      ],
    });
  });

  test('获取type为multiple时的选项（接口无label需要前端映射）', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() =>
      Promise.resolve([
        { fieldLabel: 'labelC', fieldValue: '3', count: 1 },
        { fieldLabel: 'labelA', fieldValue: '1', count: 100 },
        { fieldLabel: 'labelB', fieldValue: '2', count: 10 },
      ])
    );
    await getUnionOptions({
      key: 'test',
      type: 'multiple',
      dict: dictionary,
    });
    expect(optionsMap.value).toEqual({
      test: [
        { label: 'labelA(100)', value: '1' },
        { label: 'labelB(10)', value: '2' },
        { label: 'labelC(1)', value: '3' },
      ],
    });
  });

  test('获取type为single时的选项', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() =>
      Promise.resolve([
        { fieldLabel: 'labelB', fieldValue: '2', count: 10 },
        { fieldLabel: 'labelA', fieldValue: '1', count: 100 },
        { fieldLabel: 'labelC', fieldValue: '3', count: 1 },
      ])
    );
    await getUnionOptions({
      key: 'test',
      type: 'single',
    });
    expect(optionsMap.value).toEqual({
      test: [
        { label: 'labelB(10)', value: '2' },
        { label: 'labelA(100)', value: '1' },
        { label: 'labelC(1)', value: '3' },
      ],
    });
  });

  test('获取type为cascader时的选项', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() =>
      Promise.resolve([
        { fieldLabel: '1', fieldValue: 1, count: 1 },
        { fieldLabel: '1-1', fieldValue: 11, count: 2 },
        { fieldLabel: '1-3', fieldValue: 13, count: 3 },
        { fieldLabel: '2', fieldValue: 2, count: 3 },
        { fieldLabel: '3', fieldValue: 3, count: 4 },
        { fieldLabel: '3-2', fieldValue: 32, count: 4 },
      ])
    );
    const key = 'cascadeKey';
    await getUnionOptions({
      key,
      type: 'cascader',
      dict: cascaderDictionary,
    });
    expect(optionsMap.value).toEqual({
      [key]: [
        {
          label: '1(1)',
          value: 1,
          children: [
            { label: '1-1(2)', value: 11 },
            { label: '1-3(3)', value: 13 },
          ],
        },
        {
          label: '2(3)',
          value: 2,
        },
        {
          label: '3(4)',
          value: 3,
          children: [{ label: '3-2(4)', value: 32 }],
        },
      ],
    });
  });
  test('获取type为cascader-multiple时的选项', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() =>
      Promise.resolve([
        { fieldLabel: '1', fieldValue: 1, count: 1 },
        { fieldLabel: '1-3', fieldValue: 13, count: 3 },
        { fieldLabel: '2', fieldValue: 2, count: 3 },
        { fieldLabel: '1-1', fieldValue: 11, count: 2 },
        { fieldLabel: '3', fieldValue: 3, count: 4 },
        { fieldLabel: '3-2', fieldValue: 32, count: 4 },
      ])
    );
    const key = 'cascadeKey';
    await getUnionOptions({
      key,
      type: 'cascader-multiple',
      dict: cascaderDictionary,
    });
    expect(optionsMap.value).toEqual({
      [key]: [
        {
          label: '1(1)',
          value: 1,
          children: [
            { label: '1-1(2)', value: 11 },
            { label: '1-3(3)', value: 13 },
          ],
        },
        {
          label: '2(3)',
          value: 2,
        },
        {
          label: '3(4)',
          value: 3,
          children: [{ label: '3-2(4)', value: 32 }],
        },
      ],
    });
  });

  test('接口返回值为空', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() => Promise.resolve([]));
    await getUnionOptions({
      key: 'test',
      type: 'cascader',
    });
    expect(optionsMap.value).toEqual({
      test: [],
    });
  });

  test('type不为以上四种时不进行处理', async () => {
    const { getUnionOptions, optionsMap } = useUnionFilter(() =>
      Promise.resolve([
        { fieldValue: '1', count: 100 },
        { fieldValue: '3', count: 1 },
        { fieldValue: '2', count: 10 },
      ])
    );
    const key = 'typeKey';
    await getUnionOptions({
      key,
      type: 'test',
    });
    expect(optionsMap.value).toEqual({});
  });

  test('filter缓存', async () => {
    const key = 'testKey';
    const config = {
      key,
      type: 'multiple',
    };
    const func = vi.fn(() => Promise.resolve([]));
    const { clearFilterStash, getUnionOptions } = useUnionFilter(func);
    await getUnionOptions(config);
    expect(func).toHaveBeenCalledTimes(1);
    await getUnionOptions(config);
    expect(func).toHaveBeenCalledTimes(1);
    await getUnionOptions(config);
    expect(func).toHaveBeenCalledTimes(1);
    clearFilterStash();
    await getUnionOptions(config);
    expect(func).toHaveBeenCalledTimes(2);
  });

  test('清除缓存', async () => {
    const func = vi.fn(() => Promise.resolve([]));
    const { clearFilterStash, getUnionOptions } = useUnionFilter(func);
    await getUnionOptions({
      key: 'key1',
      type: 'multiple',
    });
    expect(func).toHaveBeenCalledTimes(1);
    await getUnionOptions({
      key: 'key2',
      type: 'multiple',
    });
    expect(func).toHaveBeenCalledTimes(2);
    clearFilterStash('key2');
    await getUnionOptions({
      key: 'key1',
      type: 'multiple',
    });
    expect(func).toHaveBeenCalledTimes(2);
    await getUnionOptions({
      key: 'key2',
      type: 'multiple',
    });
    expect(func).toHaveBeenCalledTimes(3);
  });

  test('getGroups', async () => {
    const { getGroups, groups } = await useUnionFilter(() => Promise.resolve([{ fieldLabel: '1', fieldValue: 1, count: 1 }]));
    await getGroups();
    expect(groups.value).toEqual([{ label: '1', value: 1, count: 1, groupId: 1, name: '1' }]);
  });
  test('getGroups throw exception', async () => {
    const { getGroups, groups } = await useUnionFilter(() => Promise.reject());
    await getGroups();
    expect(groups.value).toEqual([]);
  });
});
