import { intersectionWith, isNull } from 'lodash';
import { type Ref, ref, set } from 'vue';

interface FilterOptions {
  label: string;
  value: string | number;
  count?: number;
}

const flatten = (list: any[] = [], res: any[] = [], parentId = null) => {
  for (let i = 0; i < list.length; i++) {
    const el = list[i];
    res.push({ ...el, children: undefined, parentId });
    if (el.children?.length) {
      flatten(el.children, res, el.value);
    }
  }
  return res;
};

const unflatten = (list: any[] = []) => {
  const res: any[] = [];
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (item.parentId) {
      const parent = list.find((el) => el.value === item.parentId);
      if (!parent.children) {
        parent.children = [];
      }
      delete item.parentId;
      parent.children.push(item);
    } else {
      delete item.parentId;
      res.push(item);
    }
  }
  return res;
};

export const useUnionFilter = (action) => {
  const isLoading = ref(false);
  const optionsMap = ref<Record<string, FilterOptions[]>>({});
  const stashList = ref<string[]>([]);
  const groups = ref([]);

  // 如果只悬浮，不筛选，不再调用接口(取消/勾选筛选条件时重新调用聚合接口)
  // 新增/移除时，重新调用
  const clearFilterStash = (key?: string) => {
    if (!key) {
      stashList.value = [];
      return;
    }
    const index = stashList.value.indexOf(key);
    if (index >= 0) {
      stashList.value.splice(index, 1);
    } else {
      stashList.value = [];
    }
  };

  const getGroups = async () => {
    try {
      const res = await action?.('group');
      groups.value = res.map((el) => ({
        name: el.fieldLabel,
        groupId: Number(el.fieldValue),
        label: el.fieldLabel,
        value: Number(el.fieldValue),
        count: el.count,
      }));
    } catch (error) {
      groups.value = [];
    }
  };

  const getCascaderOptions = (dict = [], options: any[] = []) => {
    if (!options.length) return [];

    const flattenDict = flatten(dict);
    const intersectionDict = flattenDict.filter((item) => {
      const hasOption = options.find((el: any) => el.fieldValue === item.value);
      if (hasOption) {
        item.label = `${item.label}(${hasOption.count})`;
      }
      return hasOption;
    });

    const res = unflatten(intersectionDict);
    return res;
  };
  const getOptions = (dict: any[] = [], options: any[] = []) => {
    if (dict.length) {
      // 按字典顺序展示下拉选项
      const list = intersectionWith(dict, options, (x, y) => x.value === y.fieldValue);
      return list.map((el) => ({
        value: el.value,
        label: `${el.label}(${options.find((v) => v.fieldValue === el.value).count})`,
      }));
    }
    // 部门、标签等由接口返回label无需转义
    return options.map((el) => ({
      label: `${el.fieldLabel}(${el.count})`,
      value: el.fieldValue,
    }));
  };

  const getUnionOptions = async (group) => {
    const { key, type } = group;
    if (!key || !action) return;

    if (stashList.value.includes(key)) {
      return;
    }
    try {
      isLoading.value = true;
      const res = await action(key);
      const data = res.filter((el) => !isNull(el.fieldValue) && el.fieldValue !== '');
      isLoading.value = false;
      switch (type) {
        case 'single':
        case 'multiple':
          set(optionsMap.value, key, getOptions(group.dict, data));
          break;
        case 'cascader':
        case 'cascader-multiple':
          set(optionsMap.value, key, getCascaderOptions(group.dict, data));
          break;
        default:
          throw new Error(`${type} is not defined`);
      }
      stashList.value.push(key);
    } catch (error) {
      isLoading.value = false;
    }
  };

  return {
    isLoading,
    optionsMap,
    getUnionOptions,
    groups,
    getGroups,
    clearFilterStash,
  };
};
