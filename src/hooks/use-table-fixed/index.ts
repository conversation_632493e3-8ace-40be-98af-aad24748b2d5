import { computed, ref, watch, nextTick } from 'vue';

/**
 * Table 动态隐藏/显示列时使用
 *  - 每一列都要声明width
 *  - scroll.x不写死（写死会导致列过少时width失效，列被拉宽，在固定列时会显示空列）
 * @param props
 * @returns
 */

export function useTableFixed({ columns, dataSource, loading = ref(false) }) {
  // 是否固定列（列过少时取消固定，剩余列会自适应宽度，不显示空列）
  const isFixed = ref(true);

  const updateFixed = () => {
    const header = document.querySelector('.ant-table-header');
    const clientWidth = header?.clientWidth;
    const scrollWidth = header?.scrollWidth;
    if (header && clientWidth === scrollWidth) {
      isFixed.value = false;
    } else {
      isFixed.value = true;
    }
  };

  watch(
    () => columns.value,
    () => {
      nextTick(() => {
        updateFixed();
      });
    }
  );

  // 用以解决loading时无法获取table的问题
  const unwatch = watch(
    () => loading.value,
    (val) => {
      if (!val && dataSource.value.length > 0) {
        updateFixed();
      }
      if (!val) {
        unwatch();
      }
    }
  );

  const dynamicColumns = computed(() => {
    if (!isFixed.value) {
      return columns.value.map((el) => {
        if (el.fixed && !el.oldFixed) {
          el.oldFixed = el.fixed;
        }
        return { ...el, fixed: undefined };
      });
    }
    return columns.value.map((el) => ({
      ...el,
      fixed: el.fixed || el.oldFixed,
    }));
  });

  return {
    isFixed,
    updateFixed,
    dynamicColumns,
  };
}
