import { defineComponent, reactive, ref } from 'vue';
import { createLocalVue, shallowMount } from '@vue/test-utils';

import { useTableFixed } from '../index';

describe('useTableFixed', () => {
  let mockHeader;

  const config = [
    {
      title: '人员编号',
      fixed: 'left',
      key: 'personNo',
      sorter: true,
      unChangeAble: true,
      show: true,
    },
    {
      title: '姓名',
      fixed: 'left',
      unChangeAble: true,
      show: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      show: true,
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
      show: true,
    },
  ];

  beforeEach(() => {
    // 清除 DOM 结构
    document.body.innerHTML = '';

    // 创建模拟的 .ant-table-header 元素
    mockHeader = document.createElement('div');
    mockHeader.classList.add('ant-table-header');
    document.body.appendChild(mockHeader);
  });

  test('does not fix table when scroll width is less than header width', () => {
    const loading = ref(false);
    const columns = ref([...config]);
    const props = {
      loading,
      columns,
      dataSource: [1],
    };
    const { isFixed, updateFixed, dynamicColumns } = useTableFixed(props);

    Object.defineProperty(mockHeader, 'clientWidth', { value: 100 });
    Object.defineProperty(mockHeader, 'scrollWidth', { value: 100 });
    updateFixed();
    expect(isFixed.value).toBe(false);
    expect(dynamicColumns.value).toEqual([
      {
        title: '人员编号',
        oldFixed: 'left',
        key: 'personNo',
        sorter: true,
        unChangeAble: true,
        show: true,
      },
      {
        title: '姓名',
        oldFixed: 'left',
        unChangeAble: true,
        show: true,
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        show: true,
      },
      {
        title: '电子邮箱',
        dataIndex: 'email',
        show: true,
      },
    ]);
  });

  test('fix table when scroll width is more than header width', () => {
    const loading = ref(false);
    const columns = ref([...config]);
    const props = {
      loading,
      columns,
      dataSource: ref([1]),
    };
    const { isFixed, updateFixed, dynamicColumns } = useTableFixed(props);

    Object.defineProperty(mockHeader, 'clientWidth', { value: 100 });
    Object.defineProperty(mockHeader, 'scrollWidth', { value: 110 });
    updateFixed();
    expect(isFixed.value).toBe(true);
    expect(dynamicColumns.value).toEqual([
      {
        title: '人员编号',
        fixed: 'left',
        oldFixed: 'left',
        key: 'personNo',
        sorter: true,
        unChangeAble: true,
        show: true,
      },
      {
        title: '姓名',
        fixed: 'left',
        oldFixed: 'left',
        unChangeAble: true,
        show: true,
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        show: true,
      },
      {
        title: '电子邮箱',
        dataIndex: 'email',
        show: true,
      },
    ]);
  });

  test('watch loading change to update fixed', async () => {
    const loading = ref(true);
    const props = {
      loading,
      columns: ref(config),
      dataSource: ref([1]),
    };
    Object.defineProperty(mockHeader, 'clientWidth', { value: 100 });
    Object.defineProperty(mockHeader, 'scrollWidth', { value: 100 });
    const { isFixed } = useTableFixed(props);
    expect(isFixed.value).toBe(true);
    loading.value = false;
    const func = () => new Promise((resolve) => setTimeout(resolve, 200));
    await func();
    expect(isFixed.value).toBe(false);
  });

  test('watch columns change to update fixed', async () => {
    const localVue = createLocalVue();
    const TestComponent = defineComponent({
      setup(props) {
        const columns = ref();
        const loading = ref(true);
        const dataSource = ref([]);
        const { updateFixed } = useTableFixed({ columns, loading, dataSource });
        return { updateFixed };
      },
      template: '<div>{{this.columns}}</div>',
    });
    const wrapper = shallowMount(TestComponent, {
      localVue,
    });
    const spy = vi.spyOn(wrapper.vm, 'updateFixed');
    expect(spy).not.toHaveBeenCalled();
    wrapper.setProps({
      columns: [
        {
          title: '人员编号',
          fixed: 'left',
          key: 'personNo',
          sorter: true,
          unChangeAble: true,
          show: true,
        },
        {
          title: '姓名',
          fixed: 'left',
          unChangeAble: true,
          show: true,
        },
      ],
    });
    expect(spy).not.toHaveBeenCalled();
  });
});
