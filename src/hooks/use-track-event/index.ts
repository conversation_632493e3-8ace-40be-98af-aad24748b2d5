import { createTrackEvent, useTrack } from '@/config/tracking-events';

export const useTrackEvent = (pageName?: string) => {
  const track = useTrack();

  // 关键字搜索
  const handleInputTrack = (code, searchKeywords, defaultPageName = pageName) => {
    if (!searchKeywords) return;
    track(
      createTrackEvent(code, {
        pageName: defaultPageName,
        searchType: '搜索',
        searchKeywords,
      })
    );
  };
  // 条件筛选
  const handleFilterTrack = (code, searchKeywords, defaultPageName = pageName) => {
    if (!searchKeywords) return;
    track(
      createTrackEvent(code, {
        pageName: defaultPageName,
        searchType: '筛选',
        searchKeywords,
      })
    );
  };

  const handleSearchTrack = (code, { keyword, filter }, defaultPageName = pageName) => {
    if (filter) {
      handleFilterTrack(code, filter, defaultPageName);
    } else {
      handleInputTrack(code, keyword, defaultPageName);
    }
  };

  return {
    handleSearchTrack,
  };
};
