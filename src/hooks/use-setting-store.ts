/**
 * FIXME: 需要重构，避免全局内存污染，全局状态统一通过 Vuex 管理
 */

import { computed, ref, unref } from 'vue';
import { get, omit, throttle } from 'lodash';
import { message } from 'ant-design-vue';

import { useFetchState } from '@/hooks/use-fetch-state';
import { setting } from '@/shared/services';

import { flattenAndAddProperties } from './helper/flatten-dimension-settings';

type Setting = {
  key: string;
  name: string;
  status: number;
  sort: number;
  level: number;
  parentKey: string;
  rootKey: string;
  [key: string]: any;
};

// 黑名单字段
export const BLACK_LIST_DIMENSION_KEYS = ['HitOuterBlackList', 'risk_outer_blacklist'];

const result = ref<Record<string, any>>({});

const isEdit = ref(false);
const visible = ref(false);
const next = ref(() => {
  //
});

export const useSettingStore = () => {
  const { execute, isLoading } = useFetchState(async () => {
    const res = await setting.history();
    result.value = res;
  });
  const getSettingInfo = throttle(execute, 100);

  // FIXME: 合并 `settings` 和 `extraContent`
  const settings = computed(() => omit(result.value?.content, ['version']));
  const extraSettings = computed(() => result.value?.extraContent);

  // 打平后的setting
  const flatSettings = computed<Setting[]>(() => {
    const items = Object.entries(unref(settings)).map(([key, value]: any) => {
      const subDimensionList = get(value, 'items', []);
      return {
        key,
        ...omit(value, 'items'),
        subDimensionList,
      };
    });
    return flattenAndAddProperties(items);
  });

  // 一级维度的setting
  const parentSettings = computed(() => flatSettings.value.filter((item) => !item.parentKey));

  // 获取其他打开的Setting
  // 主要用来判断当前是不是最后一个打开的维度，如果是的话，不允许关闭
  const getAnotherOpenSetting = (key: string) => {
    const current = unref(flatSettings).find((item) => item.key === key);
    if (!current) return [];
    const hasAncestorWithStatus = (item) => {
      if (!get(item, 'status', 0)) {
        return false;
      }
      if (item.key === key || item.parentKey === key) {
        return false;
      }
      if (!item.parentKey) {
        return item.status;
      }
      const parent = unref(flatSettings).find((v) => v.key === item.parentKey);
      if (parent && !hasAncestorWithStatus(parent)) {
        return false;
      }
      return item.status;
    };
    // 通过传入的key进行判断，如果parentKey和key没有交集，说明是其他打开的setting
    const anotherOpenSetting = unref(flatSettings)
      .filter((item) => item.key !== key && item.key !== (current.parentKey === 'HitOuterBlackList' ? current.rootKey : current.parentKey))
      .filter(hasAncestorWithStatus);
    return anotherOpenSetting;
  };

  const editSettingInfo = (data) => {
    return setting.update({ ...result.value, content: { ...unref(result)?.content, ...data } });
  };

  /**
   * 恢复默认
   */
  const recovery = async () => {
    const res = await setting.recoverydiligenceSetting(result?.value?.id);
    message.success('恢复成功');
    result.value = res;
    isEdit.value = false;
  };

  return {
    settings, // 维度设置
    extraSettings, // 额外设置（企查分）

    isEdit,
    getSettingInfo,
    result,
    flatSettings,
    parentSettings,
    getAnotherOpenSetting,
    editSettingInfo,
    isLoading,
    visible,
    next,
    recovery,
  };
};
