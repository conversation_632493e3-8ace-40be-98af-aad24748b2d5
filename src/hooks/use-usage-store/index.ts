import { computed, unref } from 'vue';

import { useStore } from '@/store';

const DEFAULT_USEAGE = {
  batchDiligenceDailyQuantity: {
    limitation: 0,
    stock: 0,
  },
  diligenceDailyQuantity: {
    limitation: 0,
    stock: 0,
  },
  diligenceExportDailyQuantity: {
    limitation: 0,
    stock: 0,
  },
  diligenceExportQuantity: {
    limitation: 0,
    stock: 0,
  },
  batchInspectionDailyQuantity: {
    limitation: 0,
    stock: 0,
  },
  batchDiligenceUploadQuantity: {
    limitation: 0,
    stock: 0,
  },
  batchThirdPartyUploadQuantity: {
    limitation: 0,
    stock: 0,
  },
  batchInnerBlacklistUploadQuantity: {
    limitation: 0,
    stock: 0,
  },
  batchPersonUploadQuantity: {
    limitation: 0,
    stock: 0,
  },
  monitorGroupQuantity: {
    limitation: 0,
    stock: 0,
  },
  monitorGroupCompanyQuantity: {
    limitation: 0,
    stock: 0,
  },
  batchInspectionQuantity: {
    limitation: 0,
    stock: 99,
  },
  diligenceCompanyQuantity: {
    limitation: 0,
    stock: 0,
  },
  diligenceHistoryQuantity: {
    limitation: 0,
    stock: 0,
  },
  diligenceReportQuantity: {
    limitation: 0,
    stock: 0,
  },
  innerBlacklistQuantity: {
    limitation: 0,
    stock: 0,
  },
  personQuantity: {
    limitation: 0,
    stock: 0,
  },
  thirdPartyQuantity: {
    limitation: 0,
    stock: 0,
  },
  bundleUsage: {
    batchInspectionQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceCompanyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceHistoryQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceReportQuantity: {
      limitation: 0,
      stock: 0,
    },
    innerBlacklistQuantity: {
      limitation: 0,
      stock: 0,
    },
    personQuantity: {
      limitation: 0,
      stock: 0,
    },
    thirdPartyQuantity: {
      limitation: 0,
      stock: 0,
    },
    memberLimit: {
      limitation: 0,
      stock: 0,
    },
  },
  limitationUsage: {
    batchDiligenceDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceExportDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceExportQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchInspectionDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchDiligenceUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchThirdPartyUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchInnerBlacklistUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchPersonUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    monitorGroupQuantity: {
      limitation: 0,
      stock: 0,
    },
    monitorGroupCompanyQuantity: {
      limitation: 0,
      stock: 0,
    },
  },
  orgLimitationUsage: {
    batchDiligenceDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceExportDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    diligenceExportQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchInspectionDailyQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchDiligenceUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchThirdPartyUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchInnerBlacklistUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    batchPersonUploadQuantity: {
      limitation: 0,
      stock: 0,
    },
    monitorGroupQuantity: {
      limitation: 0,
      stock: 0,
    },
    monitorGroupCompanyQuantity: {
      limitation: 0,
      stock: 0,
    },
  },
  packageUsage: [],
};

export const useUsageStore = () => {
  const store = useStore();
  const usage = computed<typeof DEFAULT_USEAGE>(() => {
    return store.getters['user/usage'];
  });

  // 判断当前的资源是否被使用过
  // true: 使用过；false: 未使用
  const hasUsage = (key: keyof typeof DEFAULT_USEAGE.bundleUsage, pageFlag = false) => {
    if (!unref(usage)?.bundleUsage?.[key]?.limitation) return false;
    return unref(usage)?.bundleUsage?.[key]?.limitation !== unref(usage)?.bundleUsage?.[key]?.stock || pageFlag;
  };
  return {
    usage,
    hasUsage,
  };
};
