import Vuex from 'vuex';
import { createLocalVue } from '@vue/test-utils';

import { useUsageStore } from '..';

const localVue = createLocalVue();
localVue.use(Vuex);
const mockStore = new Vuex.Store({
  modules: {
    user: {
      getters: {
        usage: () => ({
          bundleUsage: {
            batchInspectionQuantity: {
              limitation: 0,
              stock: 0,
            },
            diligenceCompanyQuantity: {
              limitation: 10,
              stock: 5,
            },
            thirdPartyQuantity: {
              limitation: 10,
              stock: 10,
            },
          },
        }),
      },
      namespaced: true,
    },
  },
});
vi.mock('@/store', () => {
  return {
    __esModule: true,
    useStore: vi.fn(() => {
      return mockStore;
    }),
  };
});

describe('usageStore', () => {
  it('should return false for hasUsage when the limitation is 0', () => {
    const { hasUsage } = useUsageStore();
    const key = 'batchInspectionQuantity';
    expect(hasUsage(key)).toBe(false);
  });

  it('should return true for hasUsage when the limitation is not 0 and stock is not equal to limitation', () => {
    const { hasUsage } = useUsageStore();
    const key = 'diligenceCompanyQuantity';
    expect(hasUsage(key)).toBe(true);
  });

  it('should return true for hasUsage when the pageFlag is true', () => {
    const { hasUsage } = useUsageStore();
    const key = 'thirdPartyQuantity';
    expect(hasUsage(key, true)).toBe(true);
  });
});
