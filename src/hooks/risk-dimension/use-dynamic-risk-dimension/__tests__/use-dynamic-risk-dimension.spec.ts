import { getCurrentInstance } from 'vue';

import { getDynamicRiskDimensionURL, dynamicDetailJudge, noDynamicRiskDimensionHandle, useDynamicRiskDimensionDetail } from '..';

describe('noDynamicRiskDimensionDetail', () => {
  test('should return true for keys that are in DYNAMIC_RISK_DIMENSION_NO_DETAIL', () => {
    const sourceKeys = [
      20, // 严重违法
      11, // 经营异常
      24, // 大股东变更
    ];
    sourceKeys.forEach((key) => {
      expect(dynamicDetailJudge({ Category: key, ChangeExtend: '', CreateDate: '', dimensionKey: '' })).toBe(false);
    });
  });

  test('should return false for keys that are not in DYNAMIC_RISK_DIMENSION_NO_DETAIL', () => {
    const sourceKeys = ['key1', 'key2', 'key3'];
    sourceKeys.forEach((key) => {
      expect(dynamicDetailJudge({ Category: key, ChangeExtend: '', CreateDate: '', dimensionKey: '' })).toBe(true);
    });
  });
});

describe('noDynamicRiskDimensionHandle', () => {
  test('should return true for keys that are in DYNAMIC_RISK_DIMENSION_NO_HANDEL', () => {
    const sourceKeys = [
      18, // 短期多起开庭公告
    ];
    sourceKeys.forEach((key) => {
      expect(noDynamicRiskDimensionHandle(key)).toBe(true);
    });
  });

  test('should return false for keys that are not in DYNAMIC_RISK_DIMENSION_NO_HANDEL', () => {
    const sourceKeys = ['key1', 'key2', 'key3'];
    sourceKeys.forEach((key) => {
      expect(noDynamicRiskDimensionHandle(key)).toBe(false);
    });
  });
});

describe('getDynamicRiskDimensionURL', () => {
  test('should return the correct URL for AdministrativePenalties', () => {
    const cagtegory = 121;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/adminpenaltydetail?id=123';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for FinancialRegulation', () => {
    const cagtegory = 107;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/adminpenaltydetail?id=123';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorRestrictedConsumption', () => {
    const cagtegory = 55;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/sumptuary?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorInquiryEvaluation', () => {
    const cagtegory = 59;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/inquiryEvaluation?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorJudicialAuction', () => {
    const cagtegory = 57;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/judicialSale?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for 司法拍卖', () => {
    const cagtegory = 57;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/judicialSale?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for 资产拍卖', () => {
    const cagtegory = 75;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/assetsaleDetail?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorAssetsAuction', () => {
    const cagtegory = 4;
    const item = { ObjectId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/judgementInfo?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorBillDispute', () => {
    const cagtegory = 86;
    const item = { Id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/patentDetail?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });

  test('should return the correct URL for unknown', () => {
    const cagtegory = 'UNKNOWN';
    const item = { CaseSearchId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/courtCaseDetail?caseId=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(cagtegory, item)).toBe(expectedURL);
  });
});

// Mock getCurrentInstance
vi.mock('vue', () => ({
  getCurrentInstance: vi.fn(),
}));

describe('useDynamicRiskDimensionDetail', () => {
  test('should open a new window with the correct URL when record.dimensionKey is not support', () => {
    const mockWindowOpen = vi.fn();
    Object.defineProperty(window, 'open', {
      value: mockWindowOpen,
      writable: false,
    });

    // Arrange
    const record = {
      dimensionKey: 'TestDimension',
      CaseSearchId: '123',
      companyName: 'Test Company',
    };
    const expectedURL = '/embed/courtCaseDetail?caseId=123&title=Test Company';
    // Act
    const [openDimensionDetail] = useDynamicRiskDimensionDetail();
    openDimensionDetail(record);
    // Assert
    expect(mockWindowOpen).toHaveBeenCalledWith(expectedURL);
  });

  test.each([
    [
      {
        category: 12,
        ObjectId: 'OBJECT_ID',
      },
      {
        KeyNo: 'COMPANY_ID',
        pledgeId: 'OBJECT_ID',
      },
    ],
    [
      {
        category: 101,
        dynamicId: 'DYNAMIC_ID',
      },
      {
        KeyNo: 'COMPANY_ID',
        riskId: 'DYNAMIC_ID',
      },
    ],
  ])(
    'should call $modal.showDimension with the correct parameters when record.dimensionKey is support - %s',
    (record, ...expectedParams) => {
      const mockShowDimension = vi.fn();
      vi.mocked<any>(getCurrentInstance).mockReturnValue({
        proxy: {
          $modal: {
            showDimension: mockShowDimension,
          },
        },
      });
      // Arrange
      const source = {
        companyId: 'COMPANY_ID',
        companyName: 'Test Company',
        ...record,
      };

      const [openDimensionDetail] = useDynamicRiskDimensionDetail();
      openDimensionDetail(source);
      // Assert
      expect(mockShowDimension).toHaveBeenCalled();
    }
  );
});
