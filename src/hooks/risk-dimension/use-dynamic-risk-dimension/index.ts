import { get, isString } from 'lodash';
import moment from 'moment';
import { getCurrentInstance } from 'vue';

/** 判断创建时间是否小于两年前 */
const judgeIsBeforeTwoYears = (createDate) => {
  /** 两年前 moment */
  const twoYearsAgo = moment().subtract(2, 'years');
  /** 创建时间 moment */
  const crDate = createDate ? moment.unix(Number(createDate)) : null;
  return crDate ? crDate < twoYearsAgo : false;
};

/**
 * 风险动态维度->风险维度详情映射
 */
const DYNAMIC_RISK_DIMENSION_MAP = {
  39: 'oper', // 法定代表人变更
  // 68: 'partnerDetail', // 持股
  68: 'personShareChange', // 持股
  46: 'mainPartnerChange', // 主要人员变更
  44: 'partnerChange', // 股东股份变更
  114: 'finalBeneficiary', // 受益所有人变更
  21: 'finalBeneficiay', // 最终受益人变更
  60: 'entNameChange', // 企业名称变更
  40: 'entAddress', // 企业地址变更
  58: 'bankruptcy', // 破产重整
  3: 'zhixing', // 被列入被执行人
  206: 'zhixing', // 被列入被执行人
  131: 'monitorTaxCall', // 税务催缴
  27: 'dnotice', // 新增送达公告
  217: 'dnotice', // 新增送达公告(人)
  12: 'pledge', // 股权出质
  213: 'pledge', // 股权出质
  // 17: 'outboundInvest', // 对外投资变更
  // 203: 'outboundInvest', // 对外投资(人员)
  101: 'riskGuarantor', // 担保信息
  53: 'riskGuarantor', // 担保信息
  56: 'endExecutionCase', // 终本案件
  26: 'assistance', // 股权冻结
  2: 'shixin', // 被列入失信被执行人
  205: 'shixin', // 失信被执行人(人员)
  130: 'monitorTaxReminder', // 税务催报
  7: 'gonggao', // 新增法院公告
  218: 'gonggao', // 新增法院公告(人)
  49: 'lian', // 新增立案信息
  220: 'lian', // 立案信息(人员)
  14: 'spotcheck', // 抽查检查
  41: 'manageScope', // 经营范围变更
  42: 'entType', // 企业类型
  37: 'registeredCapital', // 注册资本变更
  79: 'foodSafetyDetail', // 食品安全
  90: 'lian', // 诉前调解
  232: 'lian', // 诉前调解（人）
  78: 'productRecallDetail', // 产品召回
  31: 'owenotice', // 欠税公告
  29: 'taxIllegal', // 税收违法
  98: 'notallowedentry', // 未准入境
  61: 'enliqDetail', // 注销备案
  63: 'doubleRandomSampling', // 双随机抽查
  23: 'jyzx', // 简易注销
  108: 'billDefaults', // 票据违约
  15: 'mPledge', // 动产抵押
  30: 'landmortgage', // 土地抵押
  50: 'spledge', // 股权质押
  214: 'spledge', // 股权质押
  91: 'passportlimit', // 限制出境
  109: 'publicSecurity', // 公安通告
  77: 'govProcurementIllegal', // 黑名单
  18: 'ktnotice', // 开庭公告
  219: 'ktnotice', // 开庭公告（人）
  123: 'decreaseCapiNotice', // 减资公告
  132: 'decreaseCapiNotice', // 减资公告
  // 22: 'env', // 环保处罚
  51: 'pn', // 公示催告
  76: 'inquiryEvaluation', // 询价评估
  38: 'businessStatus', // 经营状态变更
  110: 'bond', // 债券违约
  72: 'memberChange',
  25: 'actualController', // 实际控制人变更
  243: 'riskGuarantor', // 担保信息

  // FIXME：没有category的情况只能先造一个category
  111111: 'assetsFreezing', // 资产查冻
  111112: 'zdwPledgeDetail', // 资产查冻
  111113: 'tradeDetail', // 财报披露关联方交易风险
  111114: 'supplierOrCustomer', // 供应商和客户交易金额同比变化
  111115: 'overseasListing', // 境内企业境外上市
};

/** 无详情维度 */
const DYNAMIC_RISK_DIMENSION_NO_DETAIL = [
  20, // 严重违法
  11, // 经营异常
  24, // 大股东变更
  139, // 企业经营地址变更
  117, // 列入税收非正常户
  17, // 对外投资
  203, // 对外投资
];
/**
/** 无需处理维度, 用于展示跟进按钮 */
const DYNAMIC_RISK_DIMENSION_NO_HANDEL = [
  18, // 短期多起开庭公告
  219, // 短期多起开庭公告（人）
];

/** 特殊维度 */
const DIMENSION_WITHOUT_CATEGORY = [
  // 资产查冻
  'AssetInvestigationAndFreezing',
  'PledgeMerger',
  'PatentInfo',
  'TradeDetail',
  'SupplierOrCustomer',
  'OverseasListing',
];
/**
 * 详情是否为弹窗
 * @param category
 * @returns
 */
const hasDynamicRiskDimensionDetailModal = (category?: string | number): boolean => {
  if (!category) {
    return false;
  }
  return DYNAMIC_RISK_DIMENSION_MAP[category] !== undefined;
};

// 获取extend内容
const getExtrendInfo = (changeExtend) => {
  if (!changeExtend) {
    return {};
  }
  return isString(changeExtend) ? JSON.parse(changeExtend) : changeExtend;
};

/**
 * 是否有详情
 * @returns
 */
export const dynamicDetailJudge = ({ Category, CreateDate, ChangeExtend, dimensionKey }) => {
  if (!Category) {
    return DIMENSION_WITHOUT_CATEGORY.includes(dimensionKey);
  }
  const cate = Number(Category);
  const ce = getExtrendInfo(ChangeExtend);
  /** 判断融资动态（Category:129）是否展示详情按钮 */
  const judgeIpoHasDetail = () => {
    let ipoHasDetial = false;

    /**
     *  ipoCode mapping
     *  [1001, '上市辅导进行中'],
        [1002, '上市辅导完成'],
        [1003, '上市辅导终止备案'],
        [1004, '上市辅导撤回备案'],
        [1005, '上市辅导'],
        [2000, 'IPO申报未上会'],
        [2006, 'IPO申报上会未通过'],
        [2007, 'IPO申报取消审核'],
        [2008, 'IPO申报不予注册'],
        [2001, 'IPO申报已受理'],
        [2002, 'IPO申报已问询'],
        [2003, 'IPO申报上会通过'],
        [2004, 'IPO申报提交注册'],
        [2005, 'IPO申报注册生效'],
        [2009, 'IPO申报暂缓审议'],
        [2099, 'IPO申报中止'],
        [2999, 'IPO申报终止'],
        [3001, '待上市'],
        [3002, '终止发行'],
        [4001, '上市'],
        [4002, '暂停上市'],
        [5001, '退市']
     */
    const ipoCode = Number(ce?.Code);
    /** 债券内码 */
    const relatedSec = ce?.RelatedSec;
    /**
     * 申报期终止、终止发行、暂停发行、退市 超两年隐藏详情
     */
    const isHideIpoDetail =
      ([2999].includes(ipoCode) || ([3002, 4002, 5001].includes(ipoCode) && relatedSec)) && judgeIsBeforeTwoYears(CreateDate);
    if (cate === 129 && (([2, 3, 4, 5].includes(Number(ce?.Stage)) && !isHideIpoDetail) || (Number(ce?.Stage) === 1 && ce?.ReportUrl))) {
      ipoHasDetial = true;
    }
    return ipoHasDetial;
  };
  /**
   * 抽查检查展示详情逻辑：changeExtend.G 为 1
   * 行政许可数据来源不是'工商''工商自主公示'显示详情入口
   */
  let hasDetail = true;
  // 无详情的直接返回false
  if (DYNAMIC_RISK_DIMENSION_NO_DETAIL.includes(cate)) {
    return false;
  }

  // 有详情的维度的时候，还要考虑其他的情况
  if (cate === 113) {
    hasDetail = [1, 2, 3].includes(Number(ce?.IsDetail)) || (Number(ce?.IsDetail) === 4 && ce?.url);
  }
  if (cate === 14) {
    hasDetail = Number(ce?.G) === 1;
  }

  if (cate === 95) {
    hasDetail = ce?.I || ![2, 9].includes(Number(ce?.H));
  }

  if (cate === 55 || cate === 208) {
    hasDetail = Number(ce.I) === 1;
  }

  if (cate === 125) {
    hasDetail = ce?.F || (ce?.J && ce?.K);
  }

  if (cate === 129) {
    hasDetail = judgeIpoHasDetail();
  }
  return hasDetail;
};
/**
 * 是否有处理按钮
 * @returns
 */
export const noDynamicRiskDimensionHandle = (category) => {
  return DYNAMIC_RISK_DIMENSION_NO_HANDEL.includes(category);
};

/**
 * 获取风险维度跳转链接
 * @param cagtegory
 * @param item
 * @returns
 * 参考   README_EMBED_PAGES.md
 */
export const getDynamicRiskDimensionURL = (cagtegory, item) => {
  let url;
  const title = item.companyName;
  // const isShangbiao = item?.Content?.includes('出质知产类型：商标');
  switch (cagtegory) {
    case 107: // 行政处罚
    case 238: // 行政处罚(人员)
    case 121: // 金融监管
    case 22: // 环保处罚
      url = `/embed/adminpenaltydetail?id=${item.ObjectId}`;
      break;
    // 限制高消费
    case 55:
    case 208: // 限制高消费(人员)
      url = `/embed/sumptuary?id=${item.ObjectId}&title=${title}`;
      break;
    case 59: // 询价评估
      url = `/embed/inquiryEvaluation?id=${item.ObjectId}&title=${title}`;
      break;
    // // 公安通告
    // case 109:
    //   url = `/embed/news-detail-page?keyNo=${item.KeyNo}&title=${title}`;
    //   break;
    // 司法拍卖
    case 57:
      url = `/embed/judicialSale?id=${item.ObjectId}&title=${title}`;
      break;
    // 资产拍卖
    case 75:
      url = `/embed/assetsaleDetail?id=${item.ObjectId}&title=${title}`;
      break;
    // 裁判文书
    case 4:
    case 221: // 裁判文书(人员)
      url = `/embed/judgementInfo?id=${item.ObjectId}&title=${title}`;
      break;
    case 86: // 知识产权出质
      // url = `/embed/${isShangbiao ? 'tmDetail' : 'patentDetail'}?id=${item.IPRPledgeId ?? item.Id}&title=${title}`;
      url = `/embed/patentDetail?id=${item.Id}&title=${title}`;
      break;
    case 28: // 知识产权出质
      url = `/embed/product-info?id=${item.OtherHighlight[0]?.Id}&title=${item.OtherHighlight[0]?.Name}`;
      break;
    case 67: // 新闻舆情
    case 66:
    case 62:
      url = `/embed/post-news?newsId=${item.ObjectId}&title=${title}`;
      break;
    default:
      url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId || item.Id || item.ObjectId}&title=${title}`;
      break;
  }
  return url;
};

const DIMENSION_KEY_MAP = {
  // 资产查冻
  AssetInvestigationAndFreezing: 111111,
  // 动产抵押（source: 中登网）
  PledgeMerger: 111112,
  // 财报披露关联方交易风险
  TradeDetail: 111113,
  // 供应商和客户交易金额同比变化
  SupplierOrCustomer: 111114,
  // 境内企业境外上市
  OverseasListing: 111115,
  // 专利出质
  PatentInfo: 86,
};

/**
 * 获取风险动态维度跳转方法
 */
export const useDynamicRiskDimensionDetail = () => {
  const vm = getCurrentInstance()?.proxy;

  const openDimensionDetail = (record) => {
    let rest = {};
    let category = record.category || record.Category;
    const dimensionKey = record.dimensionKey;
    const changeExtend = record.ChangeExtend ? JSON.parse(record.ChangeExtend) : {};
    record.ChangeInfo = record.ChangeInfo ?? changeExtend.PartInfo;
    record.ChangeExtend = changeExtend;
    let extra = {};

    // 如果是没有category的维度，先对category进行判断赋值
    if (DIMENSION_WITHOUT_CATEGORY.includes(dimensionKey)) {
      category = DIMENSION_KEY_MAP[dimensionKey];
      if (!category) {
        return;
      }
    }

    let dimensionType = DYNAMIC_RISK_DIMENSION_MAP[category];

    if (!hasDynamicRiskDimensionDetailModal(category)) {
      let url;
      //  113 ok  65,
      // 企业公告
      if ([65, 113].includes(category)) {
        url = record.URL;
      } else if ([129].includes(category)) {
        // 上市公司进程 多类型处理
        const stage = get(changeExtend, 'Stage');
        const ipoCode = get(changeExtend, 'Code');
        const relatedSec = get(changeExtend, 'RelatedSec');
        const createDate = get(record, 'CreateDate');
        if (stage === 1) {
          // 辅导期直接打开公告链接
          const reportUrl = get(changeExtend, 'ReportUrl');
          if (reportUrl) {
            window.open(reportUrl);
            return;
          }
        } else if (stage === 2) {
          if ([2999].includes(Number(ipoCode)) && !judgeIsBeforeTwoYears(createDate)) {
            window.open(`/embed/companyDetail?keyNo=${record.companyId}&redirect=上市信息&subRediect=sbProgressAreaId`);
            return;
          }
          // 申报期打开申报信息弹窗
          dimensionType = 'ipoReportPeriod';
          extra = { type: get(changeExtend, 'IpoType'), keyNo: record.companyId };
        } else if ([3, 4, 5].includes(stage)) {
          if ([3002, 4002, 5001].includes(Number(ipoCode)) && !judgeIsBeforeTwoYears(createDate) && relatedSec) {
            window.open(`/embed/companyDetail?keyNo=${record.companyId}&redirect=上市信息&subRediect=sbProgressAreaId`);
            return;
          }
          extra = { keyNo: record.companyId, type: 0 };
          // 发行期 已上市 已退市 打开基本信息首发信息弹窗
          dimensionType = 'ipoOthersPeriod';
        }
      } else {
        url = getDynamicRiskDimensionURL(category, record);
      }
      if (url) {
        window.open(url);
        return;
      }
    }

    // url的方法设置了dimensionType，所以当没有的dimensionType，return掉
    if (!dimensionType) {
      return;
    }

    const params = {
      KeyNo: record.companyId,
    };

    switch (category) {
      case 12:
      case 213:
        extra = { pledgeId: record.ObjectId };
        break;
      case 131:
        extra = { id: record.ObjectId, title: '税务催缴详情' };
        break;
      case 123:
      case 132:
        extra = {
          id: record.ObjectId,
          type: 'deceaseCapiNotice',
          keyNo: record.companyId,
        };
        break;
      case 14:
      case 60:
      case 40:
      case 17:
      case 203:
      case 41:
      case 42:
      case 37:
      case 15:
      case 98:
      case 101:
      case 68:
      case 21:
      case 79:
      case 109:
      case 91:
      case 114:
      case 25:
        extra = { riskId: record.Id, title: record.Subtitle };
        break;
      case 38:
      case 243:
        extra = { riskId: record.Id };
        break;
      case 76:
        extra = { riskId: record.Id, title: '询价评估-选定评估机构' };
        break;
      case 90:
      case 232:
        extra = { id: record.ObjectId, title: '诉前调解详情' };
        break;
      case 63:
      case 61:
        extra = { ids: record.ObjectId, keyNo: record.companyId };
        break;
      case 44:
      case 46:
      case 72:
        extra = {
          ids: record.ObjectId,
          ...record.ChangeInfo,
          Name: record.companyName,
          ...record,
          type: 'com',
          title: record.Subtitle,
        };
        break;
      case 130:
      case 23:
      case 26:
      case 58:
      case 2:
      case 205:
      case 3:
      case 206:
      case 56:
      case 31:
      case 77:
      case 108:
      case 50:
      case 214:
      case 110:
      case 27:
      case 217:
      case 30:
      case 51:
      case 78:
      case 18:
      case 219:
      case 49:
      case 220:
        extra = { id: record.ObjectId };
        break;
      case 129:
        break;
      case 111111:
      case 111115:
        extra = { ...record };
        break;
      case 111112:
        rest = {
          Detail: record,
        };
        break;
      case 111114: {
        const queryParam = record.calculationResult?.QueryParam ? JSON.parse(record.calculationResult.QueryParam) : {};
        extra = {
          title: queryParam.dataType === 0 ? '供应商' : '客户',
          ...queryParam,
        };
        break;
      }
      case 111113:
        extra = record.calculationResult?.QueryParam ? JSON.parse(record.calculationResult.QueryParam) : {};
        break;
      default:
        extra = { riskId: record.ObjectId };
        break;
    }

    vm?.$modal.showDimension(dimensionType, Object.assign(params, extra), rest);
  };

  return [openDimensionDetail];
};
