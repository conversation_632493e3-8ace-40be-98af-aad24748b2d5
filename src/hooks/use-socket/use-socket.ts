import { io } from 'socket.io-client';
import type { Socket, SocketOptions, ManagerOptions } from 'socket.io-client';

const CONNECTION_CACHE = new Map<string, Socket>();

/**
 * 使用 websocket, 基于 socket.io
 */
export const useSocket = (path: string, settings: Partial<ManagerOptions & SocketOptions> = {}) => {
  let socket: Socket;

  if (CONNECTION_CACHE.has(path)) {
    socket = CONNECTION_CACHE.get(path) as Socket; // Read cache
  } else {
    const defaults: Partial<ManagerOptions & SocketOptions> = {
      withCredentials: true,
      transports: ['websocket'],
    };

    const options = {
      ...defaults,
      ...settings,
      path,
    };

    socket = io(options);
    CONNECTION_CACHE.set(path, socket); // Write cache
  }

  /**
   * 加入房间
   */
  const joinRoom = (profile: Record<string, any>, room: string, event = 'JoinRoom') => {
    socket.on('connect', () => {
      if (profile.currentOrg) {
        socket.emit(event, {
          orgId: profile.currentOrg,
          roomType: room,
        });
      }
    });
  };

  /**
   * 退出房间
   */
  const leaveRoom = (profile: Record<string, any>, room: string, event = 'LeaveRoom') => {
    if (profile.currentOrg) {
      socket.emit(event, {
        orgId: profile.currentOrg,
        roomType: room,
      });
    }
  };

  return {
    socket,

    joinRoom,
    leaveRoom,
  };
};
