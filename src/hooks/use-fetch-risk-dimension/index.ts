import { ref, watch } from 'vue';
import { cloneDeep, isEmpty, isNil } from 'lodash';

import { diligence as diligenceService } from '@/shared/services';

import { useFetchState } from '../use-fetch-state';
import { usePagination } from '../use-pagination';
import { useSortOrder } from '../use-shallow-state';

export const useFetchRiskDimension = (meta: Record<string, any> = {}, needAggs = false) => {
  const filterParams = ref<{ filters: Record<string, any>; keywords: string } | null>(null);
  const initialAggs = ref({});
  const fetchData = (params) => {
    const from = window.location.search.split('from=')[1]?.split('&')[0] || 'record';
    if (meta.isInnerDetail) {
      return diligenceService
        .getRelatedCompanyDetails({
          sortField: params.field,
          sortOrder: params.order,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
          keyNo: meta.keyNo,
          ids: meta.ids,
          key: meta.key,
        })
        .then((res) => {
          const data = res?.Result || [];
          const paginationInfo = {
            total: res?.Paging?.TotalRecords > 5000 ? 5000 : res?.Paging?.TotalRecords,
            current: res?.Paging?.PageIndex || params.pageIndex || 1,
            pageSize: res?.Paging?.PageSize || params.pageSize || 5,
          };
          return {
            pagination: paginationInfo,
            data,
            displayKey: res?.displayKey,
            aggs: undefined,
          };
        });
    }
    return diligenceService.detail(params.key, { ...params, from }).then((res) => {
      const data = res?.Result || [];
      const aggs = res?.aggs || {};
      const paginationInfo = {
        total: res?.Paging?.TotalRecords > 5000 ? 5000 : res?.Paging?.TotalRecords,
        current: res?.Paging?.PageIndex || params.pageIndex || 1,
        pageSize: res?.Paging?.PageSize || params.pageSize || 5,
      };
      if (params.esFilter.aggs === 1 && isEmpty(params.esFilter.filter)) {
        initialAggs.value = cloneDeep(aggs);
      }
      return {
        data,
        aggs,
        displayKey: res?.displayKey,
        pagination: paginationInfo,
      };
    });
  };
  const [sort, setSort] = useSortOrder({
    field: undefined,
    field2: undefined,
    order: undefined,
  });
  const [pagination, setPagination] = usePagination({
    current: 1,
    pageSize: meta.pageSize || 5,
  });
  const { execute, result, isLoading, isLoaded } = useFetchState(fetchData);

  const valToArray = (obj) => {
    if (isEmpty(obj)) {
      return {};
    }
    const res = {};
    Object.keys(obj).forEach((key) => {
      res[key] = obj[key];
      if (!isNil(obj[key]) && !Array.isArray(obj[key])) {
        res[key] = [obj[key]];
      }
    });
    return res;
  };

  /**
   * 搜索
   */
  const search = async () => {
    await execute({
      ...meta,
      esFilter: {
        filter: valToArray(filterParams.value?.filters),
        aggs: needAggs ? 1 : 0,
      },
      field: sort.field,
      order: sort.order,
      field2: sort.field2,
      pageIndex: pagination.current,
      pageSize: meta.key === 'CompanyShell' ? 10 : pagination.pageSize,
    });
  };

  /**
   * 分页或排序变化时，重新请求数据
   */
  watch([pagination, sort, filterParams], search, {
    // immediate: true,
    deep: true,
  });

  return {
    pagination,
    setPagination,
    sort,
    setSort,
    execute,
    result,
    isLoading,
    isLoaded,
    search,
    initialAggs,
    filterParams,
  };
};
