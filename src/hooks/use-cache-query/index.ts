import { RemovableRef, useSessionStorage } from '@vueuse/core';

/**
 * 基于 url 缓存查询条件
 * @param namespace 命名空间
 * @param defaults 默认值
 * @param options 依赖 route, router
 * @returns
 */
export function useCacheQuery<D extends object>(namespace: string, defaults: D, { route, router }: { route: any; router: any }) {
  const cached = {} as { [K in keyof D]: RemovableRef<D[keyof D] extends any ? any : D[keyof D]> };

  /**
   * 从缓存中恢复
   */
  const restore = () => {
    Object.entries(defaults).forEach(([key, value]) => {
      cached[key] = useSessionStorage<typeof value>(`${namespace}.${key}`, value);
    });
  };

  /**
   * 重置为默认数据
   */
  const reset = () => {
    Object.keys(cached).forEach((key) => {
      cached[key].value = defaults[key];
    });
  };

  restore();

  // TODO: 使用可配置缓存条件定义
  const isCached = route?.query?.useCacheQuery === 'true';

  if (!isCached) {
    // 重置缓存
    reset();
    // 重定向
    router.replace({
      name: String(route.name),
      query: {
        ...route?.query,
        useCacheQuery: 'true',
      },
    });
  }

  return {
    cached,
    restore,
    reset,
  };
}
