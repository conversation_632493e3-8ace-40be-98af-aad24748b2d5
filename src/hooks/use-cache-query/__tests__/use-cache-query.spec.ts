import { useSessionStorage } from '@vueuse/core';

import { useCacheQuery } from '..';

vi.mock('@vueuse/core', () => ({
  useSessionStorage: vi.fn(),
}));

describe('useCacheQuery', () => {
  const mockRoute = {
    query: {},
    name: 'testRoute',
  };
  const mockRouter = {
    replace: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该从缓存中恢复数据', () => {
    vi.mocked<any>(useSessionStorage).mockImplementation((key, defaultValue) => ({
      value: defaultValue,
    }));

    const { cached } = useCacheQuery('testNamespace', { key1: 'value1', key2: 'value2' }, { route: mockRoute, router: mockRouter });

    expect(cached.key1.value).toBe('value1');
    expect(cached.key2.value).toBe('value2');
  });

  it('应该重置为默认数据', () => {
    vi.mocked<any>(useSessionStorage).mockImplementation((key, defaultValue) => ({
      value: 'cachedValue',
    }));

    const { cached, reset } = useCacheQuery('testNamespace', { key1: 'value1', key2: 'value2' }, { route: mockRoute, router: mockRouter });

    cached.key1.value = 'newValue1';
    cached.key2.value = 'newValue2';

    reset();

    expect(cached.key1.value).toBe('value1');
    expect(cached.key2.value).toBe('value2');
  });

  it('应该在未缓存时重置缓存并重定向', () => {
    vi.mocked<any>(useSessionStorage).mockImplementation((key, defaultValue) => ({
      value: defaultValue,
    }));

    const { cached } = useCacheQuery('testNamespace', { key1: 'value1', key2: 'value2' }, { route: mockRoute, router: mockRouter });

    expect(cached.key1.value).toBe('value1');
    expect(cached.key2.value).toBe('value2');
    expect(mockRouter.replace).toHaveBeenCalledWith({
      name: 'testRoute',
      query: {
        useCacheQuery: 'true',
      },
    });
  });

  it('不应该在已缓存时重置缓存并重定向', () => {
    vi.mocked<any>(useSessionStorage).mockImplementation((key, defaultValue) => ({
      value: defaultValue,
    }));

    const cachedRoute = {
      query: { useCacheQuery: 'true' },
      name: 'testRoute',
    };

    useCacheQuery('testNamespace', { key1: 'value1', key2: 'value2' }, { route: cachedRoute, router: mockRouter });

    expect(mockRouter.replace).not.toHaveBeenCalled();
  });
});
