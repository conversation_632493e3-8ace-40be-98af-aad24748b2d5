import { useToggle } from '..';

describe('useToggle', () => {
  test('should initialize with default state of false', () => {
    const [state] = useToggle();
    expect(state.value).toBe(false);
  });

  test('should initialize with custom default state', () => {
    const [state] = useToggle(true);
    expect(state.value).toBe(true);
  });

  test('should toggle state to true when setState is called without argument', () => {
    const [state, setState] = useToggle(false);
    setState();
    expect(state.value).toBe(true);
  });

  test('should toggle state to false when setState is called without argument', () => {
    const [state, setState] = useToggle(true);
    setState();
    expect(state.value).toBe(false);
  });

  test('should set state to true when setState is called with true argument', () => {
    const [state, setState] = useToggle(false);
    setState(true);
    expect(state.value).toBe(true);
  });

  test('should set state to false when setState is called with false argument', () => {
    const [state, setState] = useToggle(true);
    setState(false);
    expect(state.value).toBe(false);
  });
});
