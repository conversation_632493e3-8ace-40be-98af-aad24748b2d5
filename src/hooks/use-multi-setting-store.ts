import { computed, ref, unref } from 'vue';
import { isEmpty } from 'lodash';

import { setting } from '@/shared/services';

import { useFetchState } from './use-fetch-state';
import { useUserStore } from '../shared/composables/use-user-store';

const list = ref([]);

const defaultModel = ref<Record<string, any>>({});
const selectedModel = ref<Record<string, any>>({});

const { execute, result, isLoading } = useFetchState(async () => {
  selectedModel.value = {};
  defaultModel.value = {};
  const res = await setting.list();
  const defaultRes = await setting.getDefaultModel();
  res?.data?.forEach((item) => {
    if (item.version === defaultRes.settingVersion) {
      item.default = true;
      defaultModel.value = item;
    } else {
      item.default = false;
    }
  });
  list.value = res?.data;

  // 接口如果没返回默认模型，选第一个
  if (isEmpty(unref(defaultModel))) {
    defaultModel.value = list.value[0];
  }
  selectedModel.value = defaultModel.value;

  return res;
});

const canCreate = computed(() => {
  const { usage } = useUserStore();
  return unref(list).length < (unref(usage)?.diligenceModelQuantity?.limitation || 0);
});

export const useMultiSettingStore = () => {
  return { list, execute, isLoading, result, defaultModel, selectedModel, canCreate };
};
