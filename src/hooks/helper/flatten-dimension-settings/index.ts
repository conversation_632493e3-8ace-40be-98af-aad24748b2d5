import { flatMapDeep, omit, isArray } from 'lodash';

export const flattenAndAddProperties = (arr, { rootKey = null, parentKey = null, level = 0 } = {}) => {
  return flatMapDeep(arr, (item) => {
    const newItem = {
      ...omit(item, ['subDimensionList']),
      rootKey,
      parentKey,
      level,
    };
    if (isArray(item.subDimensionList)) {
      return [
        newItem,
        ...flattenAndAddProperties(item.subDimensionList, {
          rootKey: rootKey || item.key,
          parentKey: item.key,
          level: level + 1,
        }),
      ];
    }
    return newItem;
  });
};
