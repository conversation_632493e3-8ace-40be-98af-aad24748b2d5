import { shallowReactive } from 'vue';

export const useSortOrder = <T extends object = Record<string | number, any>>(options: T = {} as T) => {
  const defaultValue = {
    field: undefined,
    order: undefined,
  };
  const state = shallowReactive({
    ...defaultValue,
    ...options,
  });
  const setSort = (nextState: Partial<T>) => {
    Object.keys(nextState).forEach((k) => {
      state[k] = nextState[k];
    });
  };

  return [state, setSort] as const;
};
