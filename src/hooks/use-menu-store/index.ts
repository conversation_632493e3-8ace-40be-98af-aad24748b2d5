import { computed, ref } from 'vue';

import { IMenuItem } from '@/components/common/app-side-menu/types';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useI18n } from '@/shared/composables/use-i18n';

const isCollapse = ref(false);
const currentKey = ref('');
const menus = ref<IMenuItem[]>([]);

export const useMenuStore = () => {
  const flatMenu = computed(() => {
    return menus.value.reduce((pre, cur) => {
      if (cur.children) {
        pre.push(...(cur.children as any));
      } else {
        pre.push(cur);
      }
      return pre;
    }, [] as IMenuItem[]);
  });
  const currentMenu = computed(() => {
    return flatMenu.value.find((item) => item.key === currentKey.value);
  });
  const currentTitle = computed(() => {
    const { tc } = useI18n();
    const { isZeiss } = useUserStore();
    if (!currentMenu.value) return '';
    return tc(currentMenu.value.label, isZeiss.value + 1);
  });

  return {
    currentMenu,
    currentTitle,
    currentKey,
    isCollapse,
    menus,
  };
};
