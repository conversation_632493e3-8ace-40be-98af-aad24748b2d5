import { ref } from 'vue';

import { useMenuStore } from '@/hooks/use-menu-store';
import { IMenuItem } from '@/components/common/app-side-menu/types';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useI18n } from '@/shared/composables/use-i18n';

vi.mock('@/shared/composables/use-user-store', () => ({
  useUserStore: vi.fn(),
}));

vi.mock('@/shared/composables/use-i18n', () => ({
  useI18n: vi.fn(),
}));

describe('useMenuStore', () => {
  beforeEach(() => {
    vi.mocked<any>(useUserStore).mockReturnValue({ isZeiss: ref(false) });
    vi.mocked<any>(useI18n).mockReturnValue({ tc: vi.fn((label, count) => `${label}-${count}`) });
  });

  it('should return the correct currentMenu', () => {
    const { currentMenu, currentKey, menus } = useMenuStore();
    const menuItem: IMenuItem = { key: '1', label: 'Menu 1' };
    menus.value = [menuItem];
    currentKey.value = '1';
    expect(currentMenu.value).toEqual(menuItem);
  });

  it('should return the correct currentTitle', () => {
    const { currentTitle, currentKey, menus } = useMenuStore();
    const menuItem: IMenuItem = { key: '1', label: 'Menu 1' };
    menus.value = [menuItem];
    currentKey.value = '1';
    expect(currentTitle.value).toBe('Menu 1-1');
  });

  it('should return empty string for currentTitle if currentMenu is not found', () => {
    const { currentTitle, currentKey } = useMenuStore();
    currentKey.value = '2';
    expect(currentTitle.value).toBe('');
  });

  it('should flatten menus with children', () => {
    const { currentMenu, currentKey, menus } = useMenuStore();
    const menuItem: IMenuItem = {
      key: '1',
      label: 'Menu 1',
      children: [{ key: '1-1', children: [{ key: '1-1-1', label: 'SubMenu 1' }] }],
    };
    menus.value = [menuItem];
    currentKey.value = '1-1';
    expect(currentMenu.value).toEqual({
      key: '1-1',
      children: [{ key: '1-1-1', label: 'SubMenu 1' }],
    });
  });
});
