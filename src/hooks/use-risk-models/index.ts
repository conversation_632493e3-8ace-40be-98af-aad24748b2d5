import { computed, ref, set } from 'vue';

import { setting as settingService } from '@/shared/services';

export const useRiskModels = () => {
  const riskModels = ref<Record<number, any>>({});

  const getRiskModels = async (modelIds: number[]) => {
    const modelResults = await Promise.all(modelIds.map((modelId) => settingService.getModelDetail(modelId)));
    modelIds.forEach((modelId, i) => {
      set(riskModels.value, modelId, modelResults[i]);
    });
  };

  const riskModelDimensionStrategies = computed(() => {
    const result = {};
    Object.entries(riskModels.value).forEach(([modelId, model]) => {
      let dimensionStrategies = [];
      // NOTE 只取胜用到的维度策略: 从 `hitStrategy.must/should/must_not` 中获取id再取
      model?.groups.forEach((group) => {
        group?.groupMetrics.forEach((groupMetric) => {
          const hitStrategyList = groupMetric?.metricEntity.dimensionHitStrategies;
          dimensionStrategies = dimensionStrategies.concat(hitStrategyList);
        });
      });
      set(result, modelId, { strategies: dimensionStrategies });
    });

    return result;
  });

  return {
    riskModels,
    riskModelDimensionStrategies,
    getRiskModels,
  };
};
