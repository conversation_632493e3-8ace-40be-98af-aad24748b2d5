import { Ref, ref, unref } from 'vue';
import { message } from 'ant-design-vue';

import { downloadScreenshot } from '@/utils/screenshot';

export function useDOMScreenshot(targetRef?: Ref<HTMLElement | null>, filename?: string, format?: string) {
  const elementRef = targetRef || ref(null);
  const download = async () => {
    const el = unref(elementRef);
    if (!el) {
      return;
    }
    try {
      el.classList.add('screenshot');
      await downloadScreenshot(el, filename, format);
    } catch (error) {
      console.error(error);
      message.warn({
        content: '导出失败, 请稍后再试',
      });
    } finally {
      el.classList.remove('screenshot');
    }
  };
  return [elementRef, download] as const;
}
