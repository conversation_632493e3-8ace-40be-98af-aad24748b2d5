import { omit } from 'lodash';

import { useStore } from '@/store';

const store = useStore();

// 通过前后usage的stock数量，手动消耗额度，防止uasge返回的stock数量不及时
export const syncUsage = async (dimension, count) => {
  const beforeUsage = store.getters['user/usage'];
  const beforeCount = beforeUsage?.bundleUsage?.[dimension]?.stock || 0;
  await store.dispatch('user/getUsage');
  const afterUsage = store.getters['user/usage'];
  const afterCount = afterUsage?.bundleUsage?.[dimension]?.stock || 0;
  if (beforeCount === afterCount) {
    store.commit('user/SET_USAGE', {
      ...omit(beforeUsage, ['bundleUsage']),
      bundleUsage: {
        ...beforeUsage.bundleUsage,
        [dimension]: {
          ...beforeUsage.bundleUsage[dimension],
          stock: afterCount - count,
        },
      },
    });

    // todo: 后续需要优化，不确定3s后是否一定已经更新了
    setTimeout(() => {
      store.dispatch('user/getUsage');
    }, 3000);
  }
};
