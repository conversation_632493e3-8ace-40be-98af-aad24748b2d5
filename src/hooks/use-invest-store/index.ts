import { computed, ref, unref } from 'vue';
import { isEmpty } from 'lodash';

import { diligence } from '@/shared/services';

const RISK_REVIEW_CODE = -10;

interface IRiskInfoDetail {
  id: number;
  result: number;
  orgModelId: number;
  riskModelName: string;
  details: {
    levelGroup: {
      '0': any[];
      '1': any[];
      '2': any[];
    };
    dimensionScoreDetails: any[];
  };
  createDate: string;
}

// 当前选择的模型id
const currentSelectModelID = ref<number>(RISK_REVIEW_CODE);

export const uesInvestStore = () => {
  const companyInfo = ref<Record<string, any>>({});

  // 多模型排查结果列表
  const allModelRiskInfoList = ref<IRiskInfoDetail[]>([]);

  const riskInfo = computed<Record<string, any>>(() => {
    return allModelRiskInfoList.value?.find((v) => v.id === currentSelectModelID.value) || {};
  });

  // 设置所有模型排查结果列表
  const setAllModelRiskInfoList = (list: IRiskInfoDetail[]) => {
    allModelRiskInfoList.value = list;
    currentSelectModelID.value = list.length > 1 ? RISK_REVIEW_CODE : list[0].id;
  };

  // 风险等级
  const riskLevel = computed(() => unref(riskInfo)?.details?.result || 0);

  // 当前是否命中尽调综述
  const isCurrentDDResultsDesc = computed(() => {
    return currentSelectModelID.value === RISK_REVIEW_CODE;
  });

  const loading = ref(true);

  const snapshotId = computed(() => {
    return unref(riskInfo)?.snapshotId || undefined;
  });

  const dimensionDetails = computed(() => {
    return unref(riskInfo)?.details?.groupMetricScores || [];
  });

  // 风险筛查
  const getDiligence = async (params: {
    keyNo: string;
    orgModelIds?: number[];
    cacheHours?: number;
    diligenceId?: string;
    snapshotId?: string;
    isDynamicDetails?: boolean;
    settingId?: number;
    ambiguousSettingId?: number;
  }) => {
    let request: Promise<any>;
    if (params.isDynamicDetails) {
      request = diligence
        .scanRiskDetail({
          companyName: companyInfo.value.Name,
          companyId: params.keyNo,
          orgModelIds: params.orgModelIds,
          cacheHours: params.cacheHours,
          diligenceId: params.diligenceId ? Number(params.diligenceId) : params.diligenceId,
        })
        .then((res) => (isEmpty(res) ? [] : [res]));
    } else {
      request = diligence.scanRisk({
        companyName: companyInfo.value.Name,
        companyId: params.keyNo,
        orgModelIds: params.orgModelIds,
        cacheHours: params.cacheHours,
      });
    }
    const res = await request;

    if (res?.length) {
      res.forEach((detail) => {
        detail.companyId = params.keyNo;
        detail.companyName = companyInfo.value.Name;
      });
    }
    return {
      companyId: params.keyNo,
      companyName: companyInfo.value.Name,
      snapshotId: params.snapshotId,
      details: res,
    };
  };
  return {
    companyInfo,
    riskInfo,
    allModelRiskInfoList,
    currentSelectModelID,
    setAllModelRiskInfoList,
    riskLevel,
    loading,
    snapshotId,
    dimensionDetails,
    getDiligence,
    isCurrentDDResultsDesc,
  };
};
