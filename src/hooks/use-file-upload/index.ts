import { ref } from 'vue';
import { message } from 'ant-design-vue';
import type { UploadFile } from 'ant-design-vue/types/upload';
import type { Ref } from 'vue';

import { useToggle } from '../use-toggle';

export const FEEDBACK_MESSAGE = {
  UPLOAD_SUCCESS: '文件上传成功！后台正在处理中，导入完成后我们将第一时间提醒您！',
  UPLOAD_FAILED: '文件上传失败！上传文件不符合模板要求，请下载最新模板后重新上传！',
  INCORRECT_FILE_FORMAT: '文件上传失败！该功能仅支持Excel（xls、xlsx）格式文件！',
};

const API_FEEDBACK_ERROR_CODE = [
  400001, // 文件上传失败！仅支持Excel（xls、xlsx）文件
  400002, // 文件上传失败！请检查上传文件是否符合模板要求，下载最新模板后重新上传
  400003, // 您导入的文件中无有效数据，请检查后重新上传！
  400004, // 您导入的文件中数据量超出上限，请检查后重新上传！
  400007, // 文件上传失败！上传文件大小不能超过2M，请检查后重新上传！
  400006, // 您导入的文件名称长度过长，请控制在100个字符以内！
  600002, // 当前已有离线任务正在进行，请勿重复进行操作
  900105, // 创建数量已达到最大限制
  401101, // 超出限制
];
export const useFileUpload = (emit, { isMultiple = false, messages = FEEDBACK_MESSAGE, needMessage = true } = {}) => {
  const [isUploading, setUploading] = useToggle(false);
  const fileList: Ref<UploadFile[]> = ref([]); // 文件列表
  const beforeUpload = (file: UploadFile): boolean | Promise<boolean> => {
    if (isMultiple) {
      fileList.value.push(file);
    } else {
      fileList.value = [file];
    }
    return false;
  };
  const uploadChange = (info: { file: UploadFile; fileList: UploadFile[]; event: Event }) => {
    let errorMessage = messages.UPLOAD_FAILED;

    switch (info.file.status) {
      case 'done':
      case 'success':
        errorMessage = messages.UPLOAD_SUCCESS;
        if (needMessage) {
          message.success(errorMessage);
        }
        if (typeof emit === 'function') {
          emit('success', { batchId: info.file?.response?.id }); // 发送事件
        }
        setUploading(false);
        break;
      case 'uploading':
        setUploading(true);
        break;
      case 'error':
        // NOTE: 特殊处理上传错误消息
        if (API_FEEDBACK_ERROR_CODE.includes(info.file?.response?.code)) {
          errorMessage = info.file?.response?.error || messages.UPLOAD_FAILED;
        }
        if (typeof emit === 'function') {
          emit('reject');
        }
        message.error(info.file?.response?.error || errorMessage);
        setUploading(false);
        break;
      case 'removed':
        fileList.value = fileList.value.filter((f) => f !== info.file);
        break;

      default:
        break;
    }
  };
  const uploadReject = () => {
    message.error(messages.INCORRECT_FILE_FORMAT);
  };
  return {
    isUploading,
    fileList,
    beforeUpload,
    uploadChange,
    uploadReject,
  };
};
