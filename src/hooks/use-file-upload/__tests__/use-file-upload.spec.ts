import { message } from 'ant-design-vue';
import type { UploadFile } from 'ant-design-vue/types/upload';

import { useFileUpload, FEEDBACK_MESSAGE } from '..';

vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('useFileUpload', () => {
  const fileInfo = {
    uid: '1',
    size: 100,
    name: 'file_name.jpg',
    type: 'image/jpeg',
  };
  it('应该正确处理文件上传成功', () => {
    const emit = vi.fn();
    const { uploadChange } = useFileUpload(emit);
    const file: UploadFile = { status: 'done', response: { id: '123' }, ...fileInfo };
    uploadChange({ file, fileList: [file], event: new Event('change') });

    expect(message.success).toHaveBeenCalledWith(FEEDBACK_MESSAGE.UPLOAD_SUCCESS);
    expect(emit).toHaveBeenCalledWith('success', { batchId: '123' });
  });

  it('应该正确处理文件上传失败', () => {
    const emit = vi.fn();
    const { uploadChange } = useFileUpload(emit);
    const file: UploadFile = { status: 'error', response: { code: 400001, error: '文件格式错误' }, ...fileInfo };
    uploadChange({ file, fileList: [file], event: new Event('change') });

    expect(message.error).toHaveBeenCalledWith('文件格式错误');
  });

  it('文件上传失败无错误信息时使用默认提示信息', () => {
    const emit = vi.fn();
    const { uploadChange } = useFileUpload(emit);
    const file: UploadFile = { status: 'error', response: { code: 400001 }, ...fileInfo };
    uploadChange({ file, fileList: [file], event: new Event('change') });

    expect(message.error).toHaveBeenCalledWith(FEEDBACK_MESSAGE.UPLOAD_FAILED);
  });

  it('应该正确处理文件上传中', () => {
    const emit = vi.fn();
    const { uploadChange, isUploading } = useFileUpload(emit);
    const file: UploadFile = { status: 'uploading', ...fileInfo };
    uploadChange({ file, fileList: [file], event: new Event('change') });

    expect(isUploading.value).toBe(true);
  });

  it('应该正确处理文件移除', () => {
    const emit = vi.fn();
    const { uploadChange, fileList } = useFileUpload(emit);
    const file: UploadFile = { status: 'removed', ...fileInfo };
    fileList.value = [
      { ...fileInfo, name: 'file1' },
      { ...fileInfo, name: 'file2' },
    ];
    uploadChange({ file, fileList: [file], event: new Event('change') });

    expect(fileList.value).toEqual([
      {
        name: 'file1',
        size: 100,
        type: 'image/jpeg',
        uid: '1',
      },
      {
        name: 'file2',
        size: 100,
        type: 'image/jpeg',
        uid: '1',
      },
    ]);
  });

  it('应该正确处理文件格式错误', () => {
    const emit = vi.fn();
    const { uploadReject } = useFileUpload(emit);
    uploadReject();

    expect(message.error).toHaveBeenCalledWith(FEEDBACK_MESSAGE.INCORRECT_FILE_FORMAT);
  });

  it('应该正确处理单个文件上传', () => {
    const emit = vi.fn();
    const { beforeUpload, fileList } = useFileUpload(emit);
    const file: UploadFile = { name: 'file1', ...fileInfo };
    beforeUpload(file);

    expect(fileList.value).toEqual([file]);
  });

  it('应该正确处理多个文件上传', () => {
    const emit = vi.fn();
    const { beforeUpload, fileList } = useFileUpload(emit, { isMultiple: true });
    const file1: UploadFile = { name: 'file1', ...fileInfo };
    const file2: UploadFile = { name: 'file2', ...fileInfo };
    beforeUpload(file1);
    beforeUpload(file2);

    expect(fileList.value).toEqual([file1, file2]);
  });
});
