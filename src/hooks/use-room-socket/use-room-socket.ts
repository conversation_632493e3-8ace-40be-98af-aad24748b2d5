import { onBeforeUnmount, onMounted } from 'vue';

import { useSocket } from '../use-socket';

interface MessageData {
  roomType: string;
  data: Record<string, any>;
}

export const useRoomSocket = (
  path: string,
  {
    filter,
    refresh,
    eventType = 'RoomMessage',
  }: {
    filter: (messageData: MessageData) => boolean;
    update?: (data: MessageData['data']) => void;
    refresh?: (data: MessageData['data']) => void;
    eventType?: string;
  }
) => {
  const { socket } = useSocket(path);

  const roomMessageListener = (messageData: MessageData) => {
    try {
      if (filter(messageData)) {
        // 任务完成或失败, 刷新列表获取文件链接 (socket 数据中不包含文件链接)
        refresh?.(messageData);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const turnOffSocket = () => {
    socket.off(eventType, roomMessageListener); // 取消监听批量任务状态
  };

  onMounted(() => {
    socket.on(eventType, roomMessageListener); // 获取批量任务状态
  });

  onBeforeUnmount(() => {
    turnOffSocket();
  });

  return {
    socket,
    turnOffSocket,
  };
};
