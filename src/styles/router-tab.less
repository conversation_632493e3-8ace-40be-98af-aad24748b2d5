/* stylelint-disable property-no-vendor-prefix */
@import './vars.less';

// 变量
@font-size: 12px;
@color-primary: #333;
@border-color: #eaecef;
@tab-text-color: #666;
@bg-color: #f3f3f3;
@border: 1px solid @border-color;
@tab-trans: all 0.3s ease-in-out;
@hd-height: 26px;
@tab-padding: 8px;
@close-icon-margin: 4px;
@close-icon-size: 13px;

// 页签
.router-tab {
  &__slot-end {
    margin-left: 4px;
    color: #bbb;
    display: flex;
    align-items: center;
    justify-content: center;
    height: @hd-height;
    font-size: 9px;

    a {
      color: inherit;
    }
  }

  // 页签头部
  &__header {
    position: fixed;
    top: @header-height;
    left: @navigation-width;
    right: 0;
    z-index: 9;
    display: flex;
    flex: none;
    align-items: flex-end;
    box-sizing: border-box;
    background-color: @bg-color;
    height: @tabs-height;
  }

  // 滚动区域
  &__scroll {
    position: relative;
    // flex: 1 1 0px;
    height: @hd-height;
    overflow: hidden;

    &-container {
      width: 100%;
      height: 100%;
      overflow: hidden;
      // 移动设备原生滚动
      &.is-mobile {
        overflow: auto hidden;
      }
    }
  }

  // 滚动条
  &__scrollbar {
    @h: 3px;

    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: @h;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: @h;
    opacity: 0;

    .router-tab__scroll:hover &,
    &.is-dragging {
      opacity: 1;
    }

    &-thumb {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: @h;

      &:hover,
      .router-tab__scrollbar.is-dragging & {
        background-color: rgba(@color-primary, 0.8);
      }
    }
  }

  // 页签导航
  &__nav {
    position: relative;
    display: inline-flex;
    flex-wrap: nowrap;
    height: 100%;
    margin: 0;
    padding: 0 6px 0 0;
    list-style: none;
  }

  // 页签项
  &__item {
    background: @bg-color;
    position: relative;
    display: flex;
    flex: none;
    align-items: center;
    padding: 0 5px 0 12px;
    color: @tab-text-color;
    font-size: @font-size;
    cursor: pointer;
    user-select: none;
    width: 160px;

    &-icon {
      color: #999;
      transition: color 0.3s ease-in-out;
    }

    &:first-child {
      padding-right: 12px;
      width: auto;
    }

    &-divider {
      width: 1px;
      background-color: #e5e5e5;
      position: absolute;
      top: 50%;
      right: -1px;
      transform: translateY(-50%);
      height: 14px;
      z-index: 1;
    }

    &:hover,
    &.is-active {
      z-index: 20;
      color: #128bed;
      border-radius: 6px 6px 0 0;
      background-color: rgba(255, 255, 255, 0.5);

      &::before,
      &::after {
        border-color: rgba(255, 255, 255, 0.5);
        content: '';
        display: block;
        width: 8px;
        height: 22px;
        bottom: -2px;
        background: transparent;
        position: absolute;
        border-style: solid;
      }

      &::before {
        left: -6px;
        border-radius: 0 0 8px;
        border-width: 0 2px 2px 0;
      }

      &::after {
        right: -6px;
        border-radius: 0 0 0 8px;
        border-width: 0 0 2px 2px;
      }

      .router-tab__item-title {
        background: linear-gradient(to left, rgba(255, 255, 255, 0.67) 0%, @primary-color 5%);
        -webkit-background-clip: text;
      }

      .router-tab__item-icon {
        color: #128bed !important;
      }

      .router-tab__item-divider {
        visibility: hidden;
      }

      .router-tab__item-close {
        width: @close-icon-size;

        &::before,
        &::after {
          border-color: #bbb;
        }
      }
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.5);

      &::before,
      &::after {
        border-color: rgba(255, 255, 255, 0.5);
      }
    }

    // 激活的页签
    &.is-active {
      background-color: #fff;

      &:hover {
        &::before,
        &::after {
          border-color: #fff;
        }
      }
    }

    // 拖拽经过
    &.is-drag-over {
      background: rgba(0, 0, 0, 0.05);
      transition: background 0.15s ease;
    }

    // 页签标题
    &-title {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      background: linear-gradient(to left, rgba(255, 255, 255, 0.67) 0%, @tab-text-color 5%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 12px;
    }

    // 页签图标
    &-icon {
      margin-right: 4px;
      font-size: 14px;
    }

    // 页签关闭按钮
    &-close {
      @inner: 9px;

      position: relative;
      display: block;
      width: 0;
      height: @close-icon-size;
      margin-left: 3px;
      overflow: hidden;
      border-radius: 50%;
      cursor: pointer;

      &::before,
      &::after {
        position: absolute;
        top: 6.5px;
        left: 50%;
        width: @inner;
        margin-left: @inner / 2 * -1;
        display: block;
        height: 1px;
        background-color: #bbb;
        transition: background-color 0.2s ease-in-out;
        content: '';
      }

      &::before {
        transform: rotate(-45deg);
      }

      &::after {
        transform: rotate(45deg);
      }

      &:hover {
        background-color: mix(@tab-text-color, #fff, 50%);

        &::before,
        &::after {
          background-color: #fff;
        }
      }
    }
  }

  // 页面容器
  &__container {
    height: 100%;
    position: relative;
    flex: 1;

    > .router-alive {
      height: 100%;
    }
  }

  // iframe 页面
  &__iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  // 右键菜单
  &__contextmenu {
    position: fixed;
    z-index: 999;
    min-width: 120px;
    padding: 8px 0;
    font-size: @font-size;
    background: #fff;
    border: @border;
    box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, 0.1);
    transform-origin: left top;

    &-item {
      position: relative;
      display: block;
      padding: 0 8px;
      color: @tab-text-color;
      line-height: 30px;
      cursor: pointer;
      transition: color 0.2s ease-in-out;
      user-select: none;

      &:hover,
      &:active {
        color: @color-primary;
      }

      &[disabled] {
        color: #aaa;
        background: none;
        cursor: default;
        pointer-events: none;
      }

      // 拥有图标
      .has-icon & {
        padding-left: 30px;
      }
    }

    &-icon {
      position: absolute;
      top: 0;
      left: 8px;
      display: none;
      line-height: 30px;

      // 拥有图标
      .has-icon & {
        display: block;
      }
    }
  }
}
