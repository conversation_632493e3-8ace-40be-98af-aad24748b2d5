// 用于 iframe 中的样式重置
@import '../token.less';
@import (less) '~normalize.css'; // 不走 css-loader，让 less 来处理依赖

html {
  font-size: 16px;
  line-height: 1.6;
  font-family: @rover-font-family;
}

h1 {
  font-size: 20px;
}

h2 {
  font-size: 18px;
}

h3,
h4,
h5 {
  font-size: 16px;
}

h6 {
  font-size: 14px;
}

a {
  transition: color 0.2s;
  color: @primary-color;
  text-decoration: none;

  &:hover {
    color: #3cabfa;
  }
}

img {
  max-width: 100%;
}

table {
  min-width: 500px;
  width: 100%;
  word-wrap: break-word;
  border-collapse: collapse;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 14px;

  th,
  td {
    border: 1px solid #ddd;
    min-width: 70px;
    padding: 5px;
  }

  thead {
    th {
      background-color: #f6f6f6;
      color: #666;
    }
  }

  p {
    margin: 0;
  }
}

p {
  margin: 4px 0;
}

.qcc-highlight {
  color: #f04040;
  font-style: normal;
}
