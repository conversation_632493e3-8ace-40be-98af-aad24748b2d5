.kzz-list-item {
  position: relative;
  padding: 15px 20px 18px;
  background-color: #fff;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f9fd;

    .kzz-list-title a {
      color: @primary-color;
    }

    .newestTender {
      background-color: #fff;
    }
  }

  &::after {
    position: absolute;
    right: 20px;
    bottom: 0;
    left: 20px;
    height: 1px;
    background-color: #eee;
    content: '';
  }

  &--selectable {
    padding-left: 26px;
  }

  .ant-checkbox-wrapper {
    position: absolute;
    top: 18px;
    left: 0;
  }
}

.kzz-list-actions {
  position: relative;
  margin-bottom: 0;
  padding: 18px 0;
  line-height: 22px;
  background-color: #f3f9fd;

  &::after {
    position: absolute;
    right: 20px;
    bottom: 0;
    left: 20px;
    height: 1px;
    background-color: #eee;
    content: '';
  }

  .ant-checkbox-wrapper,
  .ant-checkbox {
    color: inherit;
  }

  .ant-checkbox + span {
    padding-right: 0;
  }

  li {
    display: inline-block;
    user-select: none;
  }
}

.kzz-list-title {
  color: #333;
  font-weight: bold;
  font-size: 18px;
  line-height: 28px;

  a {
    color: inherit;
  }

  &__actions {
    display: flex;
    justify-content: space-between;
  }
}

.kzz-list-title-actions {
  color: #999;
  font-weight: normal;
  font-size: 14px;
}

.kzz-list-tags {
  margin-top: 10px;
}

.kzz-list-row {
  display: flex;
  margin: 0;
  color: #333;

  li {
    display: inline-block;
    margin-top: 10px;

    &:not(:last-child) {
      margin-right: 30px;
    }
  }

  label {
    color: #999;
  }

  &__space_between {
    & > * {
      flex: none;
    }

    & > :first-child {
      flex: 1;
    }
  }
}

.kzz-list-row--single {
  li {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.kzz-list-custom-pagination {
  overflow: hidden;
}

.kzz-list-hit-icon {
  margin-right: 10px;
}
