.q-text-gray {
  color: #999;
}

.q-icon-gray {
  color: @single-icon-color;
}

.q-text-left {
  text-align: left;
}

.q-text-center {
  text-align: center;
}

.q-text-right {
  text-align: right;
}

.q-m-b-xs {
  margin-bottom: 5px;
}

.q-m-xxs {
  margin: 2px 4px;
}

.q-m-xs {
  margin: 5px;
}

.q-m-sm {
  margin: 10px;
}

.q-m {
  margin: 15px;
}

.q-m-md {
  margin: 20px;
}

.q-m-lg {
  margin: 30px;
}

.q-m-xl {
  margin: 50px;
}

.q-m-n {
  margin: 0 !important;
}

.q-m-l-none {
  margin-left: 0;
}

.q-m-l-xs {
  margin-left: 5px;
}

.q-m-l-sm {
  margin-left: 10px;
}

.q-m-l {
  margin-left: 15px;
}

.q-m-l-md {
  margin-left: 20px;
}

.q-m-l-lg {
  margin-left: 30px;
}

.q-m-l-xl {
  margin-left: 40px;
}

.q-m-l-n-xxs {
  margin-left: -1px;
}

.q-m-l-n-xs {
  margin-left: -5px;
}

.q-m-l-n-sm {
  margin-left: -10px;
}

.q-m-l-n {
  margin-left: -15px;
}

.q-m-l-n-md {
  margin-left: -20px;
}

.q-m-l-n-lg {
  margin-left: -30px;
}

.q-m-l-n-xl {
  margin-left: -40px;
}

.q-m-t-none {
  margin-top: 0;
}

.q-m-t-xxs {
  margin-top: 1px;
}

.q-m-t-xs {
  margin-top: 5px;
}

.q-m-t-sm {
  margin-top: 10px;
}

.q-m-t {
  margin-top: 15px;
}

.q-m-t-md {
  margin-top: 20px;
}

.q-m-t-lg {
  margin-top: 30px;
}

.q-m-t-xl {
  margin-top: 40px;
}

.q-m-t-n-xxs {
  margin-top: -1px;
}

.q-m-t-n-xs {
  margin-top: -5px;
}

.q-m-t-n-sm {
  margin-top: -10px;
}

.q-m-t-n {
  margin-top: -15px;
}

.q-m-t-n-md {
  margin-top: -20px;
}

.q-m-t-n-lg {
  margin-top: -30px;
}

.q-m-t-n-xl {
  margin-top: -40px;
}

.q-m-r-none {
  margin-right: 0;
}

.q-m-r-xxs {
  margin-right: 1px;
}

.q-m-r-xs {
  margin-right: 5px;
}

.q-m-r-sm {
  margin-right: 10px;
}

.q-m-r {
  margin-right: 15px;
}

.q-m-r-md {
  margin-right: 20px;
}

.q-m-r-lg {
  margin-right: 30px;
}

.q-m-r-xl {
  margin-right: 40px;
}

.q-m-r-n-xxs {
  margin-right: -1px;
}

.q-m-r-n-xs {
  margin-right: -5px;
}

.q-m-r-n-sm {
  margin-right: -10px;
}

.q-m-r-n {
  margin-right: -15px;
}

.q-m-r-n-md {
  margin-right: -20px;
}

.q-m-r-n-lg {
  margin-right: -30px;
}

.q-m-r-n-xl {
  margin-right: -40px;
}

.q-m-b-none {
  margin-bottom: 0;
}

.q-m-b-xxs {
  margin-bottom: 2px;
}

.q-m-b-sm {
  margin-bottom: 10px;
}

.q-m-b {
  margin-bottom: 15px;
}

.q-m-b-md {
  margin-bottom: 20px;
}

.q-m-b-lg {
  margin-bottom: 30px;
}

.q-m-b-xl {
  margin-bottom: 40px;
}

.q-m-b-n-xxs {
  margin-bottom: -1px;
}

.q-m-b-n-xs {
  margin-bottom: -5px;
}

.q-m-b-n-sm {
  margin-bottom: -10px;
}

.q-m-b-n {
  margin-bottom: -15px;
}

.q-m-b-n-md {
  margin-bottom: -20px;
}

.q-m-b-n-lg {
  margin-bottom: -30px;
}

.q-m-b-n-xl {
  margin-bottom: -40px;
}

.q-icon {
  position: relative;
  top: 1px;

  &__medium {
    font-size: 16px;
  }
}

.q-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.q-btn {
  & + .q-btn {
    margin-left: 20px;
  }
}

.q-btn-icon {
  margin-right: 2px;
}

.ntag {
  height: 22px;
  font-weight: normal;
  display: inline-block;
  line-height: 14px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 2px;
}

.ntag:not(:last-child) {
  margin-right: 6px;
}

.ntag.text-primary {
  color: #128bed;
  background: #e7f4ff;
}

.ntag.text-danger {
  color: #F04040;
  background: #fff0f2;
}

.ntag.text-warning {
  color: #f9ad14;
  background: #fef3dc;
}

.ntag.text-success {
  color: #00ad65;
  background: #e3f6ee;
}

.ntag.text-gray {
  color: #666;
  background: #f6f6f6;
}

.ntag.text-pl {
  color: #6f77d1;
  background: #edeef9;
}

.ntag.text-list {
  color: #ec9662;
  background: #fff4ed;
}

.ntag.text-info {
  color: #31708f;
  background: #e6f2fa;
}

.text-red-500 {
  color: #F04040;
}
