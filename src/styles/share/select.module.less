@import '../token.less';

.menuItem {
  color: #333;
  position: relative;
  line-height: 24px;
  font-size: 14px;
  padding: 0;
  user-select: none;

  & > :first-child {
    display: block;
    padding: 5px 10px;
  }

  &.active,
  &.checked {
    color: @primary-color;
    background-color: #f2f8fe;

    small {
      color: inherit;
    }
  }

  &.disabled {
    color: #bbb;
    cursor: not-allowed;

    &:hover {
      background-color: #fff;
    }

    small {
      color: inherit;
    }
  }

  &:hover {
    background-color: #f2f8fe;
  }

  .icon {
    position: absolute;
    right: 5px;
    top: 50%;
    font-size: 15px;
    color: inherit;
    transform: translateY(-50%);
  }

  :global(.ant-checkbox-wrapper) {
    color: inherit;
    display: block;
  }

  :global(.ant-checkbox-wrapper:hover .ant-checkbox-inner) {
    border-color: #d9d9d9;
  }

  small {
    font-size: 12px;
    margin-left: 2px;
    color: #999;
  }

  .arrowIcon {
    color: #bbb;
  }

  .tipIcon {
    margin-left: 5px;
    color: #d6d6d6;
  }

  .dateRange {
    display: inline-block;
    padding-right: 5px;
  }
}

.menuItemPath {
  background-color: #f2f8fe;
  color: #128bed;

  :global {
    .ant-checkbox-inner {
      border: 1px solid #128bed;
    }
  }
}

.withCheckbox {
  .menuItem > :first-child {
    padding-right: 30px;
  }

  .checkIcon {
    right: 10px;
    font-size: 12px;
  }

  .menuItem:not(.checked) .checkIcon {
    display: none;
  }
}

.withChildren {
  & > :first-child {
    padding-right: 18px;
  }
}
