@import 'reset.less';
@import 'token.less';

// Button
.ant-btn {
  padding: 0 12px;
  transition: unset;

  > i,
  > span {
    transition: unset;
  }

  > .anticon {
    vertical-align: middle;
  }

  &:not(.ant-btn-link) {
    min-width: 54px;
  }

  > .anticon + span,
  > span + .anticon {
    margin-left: 5px;
  }

  &:hover {
    color: @qcc-color-blue-500;
    border-color: currentcolor;
  }

  &-primary {
    text-shadow: none;

    &:hover,
    &:focus {
      color: @qcc-color-white;
      background-color: @qcc-color-blue-600;
      border-color: @qcc-color-blue-600;
    }

    &.ant-btn-background-ghost {
      &:hover {
        color: @qcc-color-white;
        background-color: @qcc-color-blue-500 !important;
        border-color: @qcc-color-blue-500;
      }
    }
  }

  &-danger {
    &:hover {
      color: @qcc-color-white;
      border-color: @danger-color;
    }
  }

  &-link {
    &:hover {
      color: @qcc-color-blue-600;
      border-color: transparent;
    }
  }
}

// DropdownMenu
.ant-dropdown-menu {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .ant-dropdown-menu-item-active {
    color: #128bed;
  }
}

.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
  font-weight: normal;
}

.ant-tabs-nav .ant-tabs-tab:not(.ant-tabs-tab-active) {
  color: #999;
}

.ant-card {
  &-head {
    border: none;

    &-wrapper {
      border-bottom: 1px solid @border-color-base;
    }
  }

  &-contain-tabs &-head-wrapper {
    border: none;
  }
}

@modal-header-border-color-split: @qcc-color-gray-500;
@modal-footer-border-color-split: @qcc-color-gray-500;
@modal-body-padding: 15px;
@modal-base-gap: 15px;
@modal-body-gap-vertical: 15px;
@drawer-header-padding: 15px 20px;
@drawer-body-padding: 15px;

.ant-modal,
.ant-drawer {
  &-title {
    font-size: 15px;
    font-weight: @qcc-font-bold;
  }

  &-header {
    margin: 0 @modal-base-gap;
    padding: @modal-base-gap 0;
    border-bottom: 1px solid @modal-footer-border-color-split;

    &-inner {
      padding-right: 32px;
    }
  }

  &-header &-header-inner,
  &-footer &-footer-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-body {
    padding: @modal-body-gap-vertical @modal-base-gap;
  }

  &-close {
    width: 18px;
    height: 18px;
    padding: 1px;
    top: @modal-base-gap;
    right: @modal-base-gap;
    // &:hover {
    //   opacity: 0.75;
    // }
    &-x {
      width: 18px;
      height: 18px;
      font-size: 18px;
      line-height: 1;
      display: block;
      // color: @qcc-color-black-200;
    }
  }

  &-footer {
    margin: 0 @modal-base-gap;
    padding: @modal-base-gap 0;
    border-top: 1px solid @modal-footer-border-color-split;

    button {
      min-width: 80px !important;
    }

    button + button {
      margin-bottom: 0;
      margin-left: 15px;
    }

    &-inner {
      height: 100%;
    }
  }
}

.ant-modal {
  top: 50px;

  &-content {
    border-radius: 4px;
    overflow: hidden;
  }
}

.ant-drawer {
  &-header {
    position: relative;
    border: none;

    &::after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1px;
      background-color: #eee;
      content: '';
    }
  }

  &-body {
    padding: 0;
  }

  &-body-content {
    padding: 0 @drawer-body-padding;
  }

  &-title {
    font-weight: bold;
    line-height: 24px;
  }

  &-close {
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    color: #d6d6d6;
    font-size: 24px;

    &:hover {
      color: #b7b7b7;
    }
  }
}

.ant-table {
  color: @text-color;
  font-size: 13px;
  border-color: @table-border-color;

  .ant-btn-link {
    font-size: 13px;
    // font-weight: bold;
  }

  .ant-table-tbody {
    .ant-table-row-cell-break-word {
      div {
        span {
          font-size: 13px;
        }
      }
    }
  }

  .ant-table-tbody > tr > td {
    div {
      font-size: 13px;
    }
  }

  & colgroup > col&-selection-col {
    width: 34px; // 选择模式列宽度
  }

  &-thead > tr > th {
    text-align: left;
    line-height: 20px;
    // border-color: @table-border-color;
    font-weight: normal;
    padding: @table-th-padding;
    // Sorter icon size
    .ant-table-column-sorter .ant-table-column-sorter-inner {
      .ant-table-column-sorter-up,
      .ant-table-column-sorter-down {
        font-size: 10px !important;
      }
    }

    .anticon-filter,
    .ant-table-filter-icon {
      font-size: 10px !important;
    }
  }

  &-tbody > tr > td {
    padding: @table-td-padding;
  }

  &-body {
    margin: 0 !important;
  }

  &-row td .ant-btn-link {
    padding: 0;
    height: auto;
  }

  &-content {
    border-right-color: @table-border-color !important;
  }

  &-placeholder {
    padding: 16px;
    border-color: @table-border-color !important;
  }
  // 嵌套表格
  &-expanded-row {
    > td {
      padding: 0 !important;
    }
  }
  // 固定列
  &-fixed-left {
    box-shadow: 4px 0 6px -4px @shadow-color;
  }

  &-fixed-right {
    box-shadow: -4px 0 6px -4px @shadow-color;
  }
  // 空表格
  &-empty &-body {
    overflow-x: hidden !important; // 当表格内容为空时, 不显示滚动条
  }

  .ant-table-row-cell-ellipsis {
    &::before {
      display: block;
      content: '';
    }
  }
}

.ant-table-tbody > tr.ant-table-row-selected td {
  background: #fff;
  // &:hover {
  //   background: #fff !important;
  // }
}

.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row, .ant-table-row-selected) > td,
.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row, .ant-table-row-selected) > td,
.ant-table-thead > tr:hover:not(.ant-table-expanded-row, .ant-table-row-selected) > td,
.ant-table-tbody > tr:hover:not(.ant-table-expanded-row, .ant-table-row-selected) > td {
  .clamp-btn {
    background: linear-gradient(to right, rgba(242, 248, 254, 0), rgba(242, 248, 254, 1) 35%);
  }
}

// .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
//   background: #2e3cd6;
// }

// 表格边框
.ant-table-bordered {
  .ant-table-header > table,
  .ant-table-body > table,
  .ant-table-fixed-left table,
  .ant-table-fixed-right table {
    border-color: @table-border-color !important;
  }

  // Border: 表头
  .ant-table-thead > tr > th {
    padding: @table-th-padding;
    background-color: @table-header-bg;
    // 隐藏头部内容边框
    border-right-color: @table-border-color;

    &:last-child {
      border-right-color: @table-border-color;
    }
    // 激活的排序
    &.ant-table-column-sort {
      background-color: #e4eef6;
    }

    .ant-table-column-sorters:hover::before {
      background-color: #e4eef6;
    }
  }
  // Border: 表体
  .ant-table-tbody > tr > td {
    line-height: 20px;
    border-color: @table-border-color;
    // 激活的排序
    &.ant-table-column-sort {
      background: #f6fbfe;
    }
  }

  // 移除嵌套表格边框
  & & table {
    border: 0;

    .ant-table-tbody > tr {
      > td {
        border-right: 0;
      }

      &:last-child {
        > td {
          border-bottom: 0;
        }
      }
    }
  }
}

tr.ant-table-expanded-row {
  > td {
    padding: 0;
  }
}

// Alert
.ant-alert {
  color: #666;

  &-banner {
    border-radius: @border-radius-base;
  }

  &-with-description &-message {
    color: #666;
  }

  &-message {
    color: #666;
  }
}

.ant-alert-close-icon .anticon-close {
  color: #d6d6d6;
  font-size: 16px;

  &:hover {
    color: #a9a9a9;
  }
}

.ant-pagination-options {
  .ant-select-arrow {
    .ant-select-arrow-icon {
      transform: scaleX(1) !important;
    }
  }
}

// Select
.ant-select-selection,
.ant-cascader-picker {
  box-shadow: none !important;

  &:not(:disabled) {
    color: #333;

    .ant-select-arrow {
      .ant-select-arrow-icon {
        transform: scaleX(1.34);
      }
    }

    .anticon-down {
      transform: scaleX(1.34);
    }

    &:hover {
      color: @primary-color;
      border-color: #3cabfa;

      .ant-select-arrow {
        color: @primary-color;
      }
    }
  }
}

// Popover & tooltips
.ant-popover {
  &-placement {
    &-top,
    &-topRight,
    &-topLeft {
      padding-bottom: 0;
    }
  }

  &-inner-content {
    padding: 15px;

    .ant-btn {
      height: 32px;
      // padding: 5px 12px;
    }

    .ant-btn-primary {
      &:hover {
        background: @qcc-color-blue-600;
        border-color: @qcc-color-blue-600;
      }
    }
  }

  &-buttons {
    margin-bottom: 0;

    button {
      margin-left: 15px;
    }
  }

  &-message {
    padding: 0 0 15px;
    line-height: 22px;

    > .anticon {
      font-size: 16px;
      top: 6px;
    }
  }

  &-message-title {
    padding-left: 26px;
  }

  &-placement-bottom,
  &-placement-bottomLeft,
  &-placement-bottomRight {
    padding-top: 0;
  }

  &-arrow {
    display: none;
  }
}

.ant-checkbox {
  &-indeterminate &-inner {
    background-color: @qcc-color-blue-500;
    border-color: @qcc-color-blue-500;
  }

  &-indeterminate &-inner::after {
    background-color: #fff;
    height: 2px;
  }
}

// Pagination
// ---
.ant-pagination {
  display: flex;
  align-items: center;
  color: @qcc-color-black-600;
  font-size: @qcc-text-md;

  &-item {
    border-color: @qcc-color-gray-500;
    margin: 0;

    a {
      color: @qcc-color-blue-500;
    }

    &-active {
      border-color: @qcc-color-blue-500;
      color: @qcc-color-white;

      a {
        color: @qcc-color-white;
      }

      &:hover,
      &:focus {
        a {
          color: @qcc-color-white;
        }
      }
    }
  }

  &-item + &-item {
    margin-left: 8px;
  }

  &-prev {
    margin-right: 8px;
  }

  &-next {
    margin-left: 8px;
  }

  &-prev,
  &-next {
    a {
      color: @qcc-color-blue-500;
    }
  }

  // 上一页、下一页
  &-prev &-item-link,
  &-next &-item-link {
    border-color: @qcc-color-gray-500;
  }
  // .anticon {
  //   // color: @qcc-color-black-200;
  // }
  .ant-select-selection {
    border-color: @qcc-color-gray-500;
  }

  .ant-pagination-options-quick-jumper input {
    border-color: @qcc-color-gray-500;
  }
}

.ant-list-loading .ant-spin-nested-loading {
  min-height: 160px;
}

.ant-spin-nested-loading {
  div {
    .ant-spin {
      max-height: 500px;
    }
  }
}

.ant-upload-list-item-card-actions .anticon {
  color: inherit;
}

.ant-btn-link:hover {
  color: @qcc-color-blue-600;
}

.ant-input-clear-icon {
  font-size: @qcc-text-md; // 统一清除输入框值为 14
  color: #bbb;
}

.ant-input-clear-icon:hover {
  color: #999;
}

.ant-select-selection__clear {
  font-size: @qcc-text-md; // 统一清除输入框值为 14
  width: @qcc-text-md;
  height: @qcc-text-md;
  line-height: @qcc-text-md;
  opacity: 1;
}

.ant-cascader-picker-clear {
  font-size: @qcc-text-md; // 级联菜单统一清除输入框值为 14
}

.ant-form-item-label > label {
  font-weight: normal;

  &.ant-form-item-no-colon::after {
    content: ''; // 不显示冒号时，右侧不保留空格
    margin: 0 10px 0 0;
  }
}

.ant-form-item {
  margin-bottom: 6px;

  .has-error .ant-form-explain {
    font-size: 12px;
    line-height: 18px;
    min-height: 18px;
    margin-top: 4px;
  }
}



.ant-table-row {
  &:hover {
    .btn-expand {
      transition: all 0.3s;
      background: linear-gradient(to right, rgba(255, 255, 255, 0), #f2f8fe 25%) !important;
    }
  }
}

.ant-btn {
  box-shadow: none;
}

.ant-checkbox-inner {
  border: 1px solid #d8d8d8;
}

.ant-modal-close {
  color: #bbb;
  padding: 0;

  &:hover {
    color: #128bed;
  }
}

.ant-tooltip {
  &-inner {
    padding: 8px 12px;
  }
}

// message
.ant-message {
  border-radius: 4px;

  .ant-message-notice-content {
    border-radius: 4px;
    padding: 0;

    .ant-message-custom-content {
      border-radius: 4px;
      padding: 10px 15px;
      color: #333;
    }

    .ant-message-success {
      background-color: #e0f5ec;

      .anticon {
        color: #13c261;
      }
    }

    .ant-message-info {
      background-color: #e2f1fd;

      .anticon {
        color: #128bed;
      }
    }

    .ant-message-warning {
      background-color: #fff4e0;

      .anticon {
        color: #fa0;
      }
    }

    .ant-message-error {
      background-color: #ffecec;

      .anticon {
        color: #f04040;
      }
    }
  }
}

.ant-popover-message {
  & > .anticon {
    top: 3px;
  }
}

.ant-breadcrumb {
  a {
    color: @qcc-color-blue-500;

    &:hover {
      color: @qcc-color-blue-600;
    }
  }
}

.ant-input-number-handler {
  &-wrap:hover & {
    height: 50% !important;
  }

  &-up:hover,
  &-down:hover {
    height: 50% !important;
  }
}

.ant-radio-wrapper {
  display: inline-flex;
  align-items: center;

  &:hover {
    .ant-radio-inner {
      border-color: #128bed;
    }

    color: #128bed;
  }

  &.ant-radio-wrapper-checked {
    color: #128bed;
  }
}

.ant-input {
  &:focus {
    box-shadow: none;
  }

  &:hover {
    border-color: #128bed;
  }

  &-lg {
    font-size: 14px; // 全局覆盖 size="lg" 时的字体大小
  }

  &[disabled] {
    color: #999;
    background: #f3f3f3;
  }
}

// 密码控件等特殊控件样式
.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: #128bed;
}

.ant-select {
  &-lg {
    font-size: 14px; // 全局覆盖 size="lg" 时的字体大小
  }
}

// 表单中的 textarea 默认有 4px 的 margin-bottom
form .ant-mentions,
form textarea.ant-input {
  margin-bottom: 0;
}

.anticon-close-circle {
  color: #d8d8d8;
}

// 解决 Safari 15.6: 设置中心会有不明阴影出现
.ant-switch::after {
  box-shadow: none;
}

.ant-select-dropdown {
  .ant-select-dropdown-menu-item {
    &:hover {
      background: #f2f8fe;
      color: #128bed;

      .anticon {
        color: #128bed;
      }
    }
  }

  .ant-select-dropdown-menu-item-active {
    color: #128bed;
    background: #fff;
  }

  .ant-select-dropdown-menu-item-selected {
    background: #f2f8fe;
    color: #128bed;
    font-weight: 400;
  }

  .ant-select-selected-icon {
    display: none !important;
  }
}
