/**
 * <AUTHOR> xue song
 * @email [<EMAIL>]
 * @create date 2021-11-20 17:15:18
 * @modify date 2021-12-03 10:44:36
 * @desc [description]
 */

const getIdByHref = (href) => {
  let str = href;
  let id = '';
  // 老版本格式
  if (str.indexOf('firm_') > -1 || str.indexOf('pl_') > -1 || str.indexOf('investor_') > -1) {
    str = href.split('_');
    str = str[str.length - 1];
    id = str.substr(0, 32);
  } else {
    let spliter = '';
    // 新版公司URL格式
    if (str.indexOf('firm/') > -1) {
      spliter = 'firm/';
    }
    // 新版人员URL格式
    if (str.indexOf('pl/') > -1) {
      spliter = 'pl/';
    }
    // 新版投资机构格式
    if (str.indexOf('investor/') > -1) {
      spliter = 'investor/';
    }

    str = href.split(spliter);
    str = str[str.length - 1];
    id = str.substr(0, 32);
  }
  const isEnt = href.indexOf('firm_') > -1 || href.indexOf('firm/') > -1;
  const isPerson = href.indexOf('pl_') > -1 || href.indexOf('pl/') > -1;
  return { id, isEnt, isPerson };
};

const handleClick = (Vue) => (el) => {
  $(el)
    .find('a')
    .each(function replaceUrl() {
      const href = $(this).attr('href');
      const { id, isEnt, isPerson } = getIdByHref(href);
      let parsedHref = '';
      if (isEnt) {
        parsedHref = `/embed/companyDetail?keyNo=${id}`;
      } else if (isPerson) {
        parsedHref = `/embed/beneficaryDetail?personId=${id}`;
      } else {
        parsedHref = href;
      }
      $(this).unbind('click');
      return $(this).on('click', (e) => {
        e.preventDefault();
        if (isEnt || isPerson) {
          // Vue.prototype.$tabs.open(parsedHref);
          window.open(`${parsedHref}`);
        } else {
          window.open(parsedHref);
          // Vue.prototype.$tabs.open(parsedHref, $(this).html());
        }
      });
    });
};

export default {
  install(Vue) {
    Vue.directive('entity-click', {
      inserted: handleClick(Vue),
      componentUpdated: handleClick(Vue),
    });
  },
};
