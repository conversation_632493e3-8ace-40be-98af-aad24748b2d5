import { Directive } from 'vue';

export const VDisableTip: Directive = {
  bind(el, { value }) {
    if (!value) return;
    const tooltip = document.createElement('div');
    tooltip.setAttribute('class', 'ant-tooltip');
    const inner = document.createElement('div');
    inner.setAttribute('class', 'ant-tooltip-inner');
    inner.textContent = value;
    tooltip.appendChild(inner);
    tooltip.style.display = 'none';
    document.body.appendChild(tooltip);

    el.addEventListener('mouseenter', (e) => {
      if (el.disabled) {
        const offset = 5;
        const { x, y, height, width } = e.target.getBoundingClientRect();
        tooltip.style.left = `${x}px`;
        tooltip.style.top = `${y + offset + height}px`;
        tooltip.style.display = 'block';
        tooltip.style.transform = `translateX(calc( -50% + ${width / 2}px ))`;
      }
    });

    el.addEventListener('mouseleave', () => {
      tooltip.style.display = 'none';
    });

    el._tooltip = tooltip;
  },
  unbind(el) {
    if (el._tooltip) {
      document.body.removeChild(el._tooltip);
      delete el._tooltip;
    }
  },
};
