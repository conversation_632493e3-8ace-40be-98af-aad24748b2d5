/**
 * <AUTHOR> xue song
 * @email [<EMAIL>]
 * @create date 2021-11-20 17:15:13
 * @modify date 2021-11-23 18:52:39
 * @desc [description]
 */

import VEntityClick from './entity-click/entity-click';
import VIgnoreReferrer from './ignore-referrer';
import { VPermission } from './permission';
import { VDebounceclick } from './debounce-click';
import { VDisableTip } from './disable-tip';

export default (Vue) => {
  Vue.use(VEntityClick);
  Vue.use(VIgnoreReferrer);
  Vue.directive('permission', VPermission);
  Vue.directive('debounceclick', VDebounceclick);
  Vue.directive('disabletip', VDisableTip);
};
