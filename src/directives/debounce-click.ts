import { Directive } from 'vue';

let onClick: any = null;
export const VDebounceclick: Directive = {
  bind(el, binding) {
    onClick = async (event) => {
      const isDisabled = el.disabled || el.getAttribute('aria-disabled') === 'true';
      const setDisabled = (flag: boolean) => {
        el.disabled = flag;
        el.setAttribute('aria-disabled', flag.toString());
      };
      if (!isDisabled) {
        setDisabled(true);
        // 触发按钮原有的点击事件
        if (typeof binding.value === 'function') {
          if (binding.value instanceof Promise) {
            await binding.value(event);
            setDisabled(false);
          } else {
            binding.value(event);
            setTimeout(() => {
              setDisabled(false);
            }, 2000);
          }
        }
      } else {
        event.preventDefault();
      }
    };

    el.addEventListener('click', onClick);

    // 在元素被卸载时移除事件监听器
    el._debounceClickEventHandler = onClick;
  },
  unbind(el) {
    el.removeEventListener('click', el._debounceClickEventHandler);
    clearTimeout(el._debounceTimeout);
    delete el._debounceClickEventHandler;
    delete el._debounceTimeout;
  },
};
