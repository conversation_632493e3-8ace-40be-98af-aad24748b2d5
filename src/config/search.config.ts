type OptionsMap = Record<string, string>;

const toMap = (options: Array<{ label: string; value: string }>): OptionsMap =>
  options.reduce((map, { value, label }) => ({ ...map, [value]: label }), {} as OptionsMap);

export const EMPTY = '__EMPTY__';
export const COMPANY_SORT_OPTIONS = [
  { label: '默认排序', value: null },
  { label: '成立日期从晚到早', value: { sortField: 'startdate', isSortAsc: 'false' } },
  { label: '成立日期从早到晚', value: { sortField: 'startdate', isSortAsc: 'true' } },
  { label: '注册资本从高到低', value: { sortField: 'registcapi', isSortAsc: 'false' } },
  { label: '注册资本从低到高', value: { sortField: 'registcapi', isSortAsc: 'true' } },
];

export const ACHIEVEMENT_TYPE = [
  // { value: '0', label: '未知' },
  { value: '100', label: '房屋建筑工程' },
  { value: '200', label: '市政基础设施工程' },
  { value: '1', label: '其他' },
];

export const ACHIEVEMENT_TYPE_MAP = toMap(ACHIEVEMENT_TYPE);

export const DATE_OPTIONS = [
  {
    label: '近1年',
    value: { currently: true, flag: 1, number: 1, unit: 'year' },
  },
  {
    label: '近2年',
    value: { currently: true, flag: 1, number: 2, unit: 'year' },
  },
  {
    label: '近3年',
    value: { currently: true, flag: 1, number: 3, unit: 'year' },
  },
  {
    label: '近5年',
    value: { currently: true, flag: 1, number: 5, unit: 'year' },
  },
];

export const RISK_RESULT = [
  { value: 3, label: '良好' },
  { value: 4, label: '提示' },
  { value: 0, label: '低风险' },
  { value: 1, label: '中风险' },
  { value: 2, label: '高风险' },
];
