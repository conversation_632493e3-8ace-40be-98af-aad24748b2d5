export const PERMISSION_VALIDATE_MESSAGES = {
  DiligenceCompanyQuantity: {
    // 尽调公司数量(同一家公司不重复计费)
    path: 'diligenceCompanyQuantity',
    message: '您的风险排查企业额度已用完，请联系管理员增加。',
  },
  // 每日排查次数
  DiligenceDailyQuantity: {
    path: 'diligenceDailyQuantity',
    message: '您今日排查企业数已达到当日限制！',
  },
  // 每日批量排查次数
  BatchDiligenceDailyQuantity: {
    path: 'batchDiligenceDailyQuantity',
    message: '您今日批量年检次数已达到当日限制！',
  },
  // 每日全量风险排查年检
  BatchInspectionDailyQuantity: {
    path: 'batchInspectionDailyQuantity',
    message: '您今日批量年检次数已达到当日限制！',
  },

  // 全量风险排查年检
  DiligenceHistoryQuantity: {
    path: 'diligenceHistoryQuantity',
    message: '您的风险排查可用额度已用完，请联系管理员添加。',
  },
  DiligenceReportQuantity: {
    path: 'diligenceReportQuantity',
    message: '您的风险排查报告下载额度已用完，请联系管理员增加。',
  },
  // 全量风险排查年检次数
  BatchInspectionQuantity: {
    path: 'batchInspectionQuantity',
    // 批量年检总的次数
    message: '您的批量年检额度已用完，请联系管理员增加。',
  },

  ThirdPartyQuantity: {
    // 第三方的数量
    path: 'thirdPartyQuantity',
    message: '您可添加的企业额度已达到上限，请联系管理员增加。',
  },
  InnerBlacklistQuantity: {
    // 内部黑名单数量
    path: 'innerBlacklistQuantity',
    message: '您可添加的黑名单额度已达到上限，请联系管理员增加。',
  },
  PersonQuantity: {
    // 人员数量
    path: 'personQuantity',
    message: '您可添加的人员额度已达到上限，请联系管理员增加。',
  },
  // 监控企业数量
  MonitorCompanyQuantity: {
    path: 'monitorCompanyQuantity',
    message: '您可添加的监控企业额度已达到上限，请联系管理员增加。',
  },
};

export enum Permission {
  // 准入尽调
  INVESTIGATION_VIEW = 20050,
  INVESTIGATION_REPORT = 20104,

  // 尽调记录
  INVESTIGATION_HISTORY_VIEW = 20040,

  // 监控动态
  RISK_TRENDS_DASHBOARD = 20000,
  RISK_TRENDS_FOLLOW_UP = 20002,
  RISK_TRENDS_VIEW = 20001,

  // 监控企业
  MONITOR_ENTERPRISE_VIEW = 20010,
  MONITOR_ENTERPRISE_DELETE = 20011,
  MONITOR_ENTERPRISE_GROUP_MANAGE = 20012,
  MONITOR_ENTERPRISE_ADD = 20013,
  MONITOR_ENTERPRISE_ADD_RELATED = 20015,
  MONITOR_ENTERPRISE_MOVE_GROUP = 20014,

  // 监控模型
  MONITOR_MODEL_VIEW = 20020,
  MONITOR_MODEL_EDIT = 20021,
  MONITOR_MODEL_DEPRECATE = 20022,
  MONITOR_MODEL_PUBLISH = 20023,

  // 尽调模型
  INVESTIGATION_MODEL_VIEW = 20030,
  INVESTIGATION_MODEL_EDIT = 20031,
  INVESTIGATION_MODEL_DEPRECATE = 20032,
  INVESTIGATION_MODEL_PUBLISH = 20033,

  // 人企核验
  IDENTITY_VERIFICATION_EXPORT = 20081,
  IDENTITY_VERIFICATION_BATCH = 20080,
  IDENTITY_VERIFICATION_VIEW = 20079,
  IDENTITY_VERIFICATION_CHECK = 20078,
}
