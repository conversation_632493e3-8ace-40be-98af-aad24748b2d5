import { computed } from 'vue';

import { useUserStore } from '@/shared/composables/use-user-store';

export const MapColor = computed(() => {
  const { isZeiss } = useUserStore();
  if (isZeiss.value) {
    return ['#E6EFFF', '#6171FF', '#001D70'];
  }
  return ['#E2F1FD', '#128BED', '#1D3C8F'];
});

export const DashboardColor = {
  low: '#9ADFC3',
  middle: '#FFD761',
  hight: '#FC7C7C',
};

// 卡尔蔡司仪表盘配色，同步分析看板
export const DashboardColorZeiss = {
  low: '#C6DAF2',
  middle: '#0090EF',
  hight: '#0F2DB3',
};

// 风险颜色
export const RiskColor = computed(() => {
  const { isZeiss } = useUserStore();
  if (isZeiss.value) {
    return DashboardColorZeiss;
  }
  return DashboardColor;
});
