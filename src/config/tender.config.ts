import { getOptionsByMap } from '@/utils';

export const SearchFieldsOptions = [
  { label: '标题', value: 'title' },
  { label: '招采单位', value: 'ifbunit' },
  { label: '代理单位', value: 'agent' },
  { label: '中标单位', value: 'wtbunit' },
];

export const DEFAULT_DATE_RANGE = [
  { value: { currently: true, flag: 1, number: 1, unit: 'day' }, label: '今日' },
  { value: { currently: true, flag: 1, number: 7, unit: 'day' }, label: '近7天' },
  { value: { currently: true, flag: 1, number: 30, unit: 'day' }, label: '近30天' },
  { value: { currently: true, flag: 1, number: 90, unit: 'day' }, label: '近3个月' },
  { value: { currently: true, flag: 1, number: 180, unit: 'day' }, label: '近6个月' },
  { value: { currently: true, flag: 1, number: 365, unit: 'day' }, label: '1年以内' },
];

// 成立年限
export const DEFAULT_DATE_RANGE_LIFE = [
  { value: { currently: true, flag: 4, min: 0, max: 5, unit: 'year' }, label: '5年内' },
  { value: { currently: true, flag: 4, min: 5, max: 10, unit: 'year' }, label: '5-10年' },
  { value: { currently: true, flag: 4, min: 10, max: 20, unit: 'year' }, label: '10-20年' },
  { value: { currently: true, flag: 4, min: 20, max: 30, unit: 'year' }, label: '20-30年' },
  { value: { currently: true, flag: 4, min: 30, max: 999, unit: 'year' }, label: '30年以上' },
];

export const REGISTERED_CAPITAL_RANGE = [
  { value: { min: 0, max: 100 }, label: '100万以内' },
  { value: { min: 100, max: 500 }, label: '100-500万' },
  { value: { min: 500, max: 1000 }, label: '500-1000万' },
  { value: { min: 1000, max: 5000 }, label: '1000-5000万' },
  { value: { min: 5000 }, label: '5000万元以上' },
];

export const DEFAULT_AMOUNT_RANGE = [
  { value: { min: 0, max: 100 }, label: '100万元以下' },
  { value: { min: 100, max: 200 }, label: '100-200万元' },
  { value: { min: 200, max: 500 }, label: '200-500万元' },
  { value: { min: 500, max: 1000 }, label: '500-1000万元' },
  { value: { min: 1000 }, label: '1000万元以上' },
];
export const UNIT_WTB = 2019.1014;

export const WTBAMT = [
  {
    label: '单价中标',
    value: { min: UNIT_WTB, max: UNIT_WTB },
  },
  {
    label: '无中标金额',
    value: { min: 0, max: 0 },
  },
  {
    label: '20万内',
    value: { min: 0, max: 20 },
  },
  {
    label: '20万-50万',
    value: { min: 20, max: 50 },
  },
  {
    label: '50万-100万',
    value: { min: 50, max: 100 },
  },
  {
    label: '100万-300万',
    value: { min: 100, max: 300 },
  },
  {
    label: '300万以上',
    value: { min: 300 },
  },
];

export const BUDGET = [
  {
    label: '无预算金额',
    value: { min: 0, max: 0 },
  },
  {
    label: '20万内',
    value: { min: 0, max: 20 },
  },
  {
    label: '20万-50万',
    value: { min: 20, max: 50 },
  },
  {
    label: '50万-100万',
    value: { min: 50, max: 100 },
  },
  {
    label: '100万-300万',
    value: { min: 100, max: 300 },
  },
  {
    label: '300万以上',
    value: { min: 300 },
  },
];

export const COMPANY_STATUS = [
  { value: '10,20', label: '在业/存续' },
  // { value: '30', label: '筹建' },
  { value: '40', label: '清算' },
  { value: '50', label: '迁入' },
  { value: '60', label: '迁出' },
  { value: '70', label: '停业' },
  { value: '75', label: '歇业' },
  { value: '80', label: '撤销' },
  { value: '85', label: '责令关闭' },
  { value: '90', label: '吊销' },
  { value: '99', label: '注销' },
];

// C端最新 "登记状态"
export const COMPANY_STATUS_C = [
  // '正常状态'
  {
    label: '存续',
    value: '20',
  },
  {
    label: '在业',
    value: '10',
  },
  {
    label: '迁入',
    value: '50',
  },
  {
    label: '迁出',
    value: '60',
  },
  {
    label: '设立',
    value: '117',
  },

  // '异常状态'

  {
    label: '注销',
    value: '99',
  },
  {
    label: '吊销',
    value: '90',
  },

  // '其他状态' value: '2',

  {
    label: '设立失败',
    value: '142',
  },
  {
    label: '撤销',
    value: '80',
  },
  {
    label: '破产',
    value: '124',
  },
  {
    label: '重整',
    value: '129',
  },
  {
    label: '清算',
    value: '40',
  },
  {
    label: '清理',
    value: '122',
  },
  {
    label: '废止',
    value: '100',
  },
  {
    label: '撤回',
    value: '143',
  },
  {
    label: '停业',
    value: '70',
  },
  {
    label: '歇业',
    value: '75',
  },
  {
    label: '责令关闭',
    value: '85',
  },
];

export const COMPANY_SCALE = [
  { label: '大型', value: 'SCALE_1' },
  { label: '中型', value: 'SCALE_2' },
  { label: '小型', value: 'SCALE_3' },
  { label: '微型', value: 'SCALE_4' },
];

/**
 * 企业规模
 */
export const COMPANY_SCALE_C = [
  { label: '大型', value: '大型企业' },
  { label: '中型', value: '中型企业' },
  { label: '小型', value: '小型企业' },
  { label: '微型', value: '微型企业' },
];

export const EconKindCode = {
  // 0: '其他',
  10: '有限责任公司',
  20: '股份有限公司',
  30: '国企',
  40: '外商投资企业',
  50: '独资企业',
  51: '法人独资',
  52: '港澳台独资',
  53: '个人独资企业',
  54: '国有独资',
  55: '外商独资',
  56: '自然人独资',
  70: '个体工商户',
  80: '联营企业',
  90: '集体所有制',
  100: '有限合伙',
  110: '普通合伙',
  120: '股份合作制',
};

/**
 * 企业性质
 */
export const EconTypeMap = {
  '*********': '国有企业',
  '*********001': '央企',
  '*********002': '央企子公司',
  '*********003': '省管国企',
  '*********004': '市管国企',
  '*********005': '国有全资企业',
  '*********006': '国有独资企业',
  '*********007': '国有控股企业',
  '*********': '集体所有制',
  '*********': '联营企业',
  '002006': '民营企业',
  '002002': '港澳台投资企业',
  '002003': '外商投资企业',
  '0': '其他',
};

export const EconType = [
  {
    label: '国有企业',
    value: '*********',
    children: [
      {
        label: '央企',
        value: '*********001',
      },
      {
        label: '央企子公司',
        value: '*********002',
      },
      {
        label: '省管国企',
        value: '*********003',
      },
      {
        label: '市管国企',
        value: '*********004',
      },
      {
        label: '国有全资企业',
        value: '*********005',
      },
      {
        label: '国有独资企业',
        value: '*********006',
      },
      {
        label: '国有控股企业',
        value: '*********007',
      },
    ],
  },
  {
    label: '集体所有制',
    value: '*********',
  },
  {
    label: '联营企业',
    value: '*********',
  },
  {
    label: '民营企业',
    value: '002006',
  },
  {
    label: '港澳台投资企业',
    value: '002002',
  },
  {
    label: '外商投资企业',
    value: '002003',
  },
  {
    label: '其他',
    value: '0',
  },
];

export const COMPANY_TYPES = getOptionsByMap(EconKindCode);
/**
 * 机构类型（数组）
 */
export const ENTERPRISE_TYPES = [
  {
    value: '*********',
    label: '有限责任公司',
  },
  {
    value: '*********',
    label: '股份有限公司',
  },
  {
    value: '001006',
    label: '个人独资企业',
  },
  {
    value: '*********',
    label: '普通合伙',
  },
  {
    value: '*********',
    label: '有限合伙',
  },
  {
    value: '*********',
    label: '股份合作企业',
  },
  {
    value: '001009',
    label: '个体工商户',
  },
  {
    value: '001003',
    label: '机关单位',
  },
  {
    value: '001004',
    label: '事业单位',
  },
  {
    value: '001005',
    label: '社会组织',
  },
  {
    value: '001011',
    label: '律师事务所',
  },
  {
    value: '001016',
    label: '学校',
  },
  {
    value: '001008',
    label: '农民专业合作社（联合社）',
  },
  {
    value: '001015',
    label: '医疗机构',
  },
  {
    value: '0',
    label: '其他',
  },
];

/**
 * 机构类型（对象）
 */
export const EnterpriseType = ENTERPRISE_TYPES.reduce((acc, curr) => {
  acc[curr.value] = curr.label;
  return acc;
}, {});

// 机构类型：C端工商搜索（忽略子级）
export const COMPANY_TYPES_C = [
  {
    label: '有限责任公司',
    value: '*********',
  },
  {
    label: '股份有限公司',
    value: '*********',
  },
  {
    label: '个体工商户',
    value: '001009',
  },
  {
    label: '合伙企业',
    value: '001007',
  },
  {
    label: '全民所有制',
    value: '*********',
  },
  {
    label: '集体所有制',
    value: '*********',
  },
  {
    label: '联营企业',
    value: '*********',
  },
  {
    label: '股份合作公司',
    value: '*********',
  },
  {
    label: '个人独资企业',
    value: '001006',
  },
  {
    label: '机关单位',
    value: '001003',
  },
  {
    label: '事业单位',
    value: '001004',
  },
  {
    label: '社会组织',
    value: '001005',
  },
  {
    label: '律师事务所',
    value: '001011',
  },
  {
    label: '学校',
    value: '001016',
  },
  {
    label: '医疗机构',
    value: '001015',
  },
  {
    label: '农民专业合作社（联合社）',
    value: '001008',
  },
  {
    label: '中国香港企业',
    value: '001013',
  },
  {
    label: '中国台湾企业',
    value: '001014',
  },
];

export const ORGANIZATION_TYPES = [
  { label: '大陆企业', value: '0,4,10,12' },
  { label: '社会组织', value: '1' },
  { label: '香港企业', value: '3' },
  { label: '台湾企业', value: '5' },
  // { label: '基金会', value: '10' },
  { label: '事业单位', value: '11' },
  { label: '律师事务所', value: '12' },
  // { label: '美股企业', value: '21' },
];

export const REGISTERED_UNIT = [
  { value: 'CNY', label: '人民币' },
  // { value: 'TWD', label: '新台币' },
  { value: 'USD', label: '美元' },
  // { value: 'HKD', label: '港元' },
  // { value: 'EUR', label: '欧元' },
  // { value: 'JPY', label: '日元' },
  { value: 'OTHER', label: '其他' },
];

export const ASSET_OPTIONS = [
  { label: '100万以内', value: 'P0,P1,P2,P3,P4,P5,P6,P7' },
  { label: '100-300万', value: 'P8,P9,P10' },
  { label: '300-500万', value: 'P11' },
  { label: '500-1000万', value: 'P12' },
  { label: '1000-5000万', value: 'P13,P14' },
  { label: '5000万以上', value: 'P15,P16,P17,P18,P19,P20,P21,P22,P23,P24' },
];

export const TENDER_INDUSTRY = [
  { label: '工程建筑', value: '1' },
  { label: '办公文教', value: '2' },
  { label: '医疗卫生', value: '3' },
  { label: '服务采购', value: '4' },
  { label: '机械设备', value: '5' },
  { label: '水利水电', value: '6' },
  { label: '能源化工', value: '7' },
  { label: '弱电安防', value: '8' },
  { label: '信息技术', value: '9' },
  { label: '交通运输', value: '10' },
  { label: '市政基建', value: '11' },
  { label: '农林牧渔', value: '12' },
  { label: '政府部门', value: '13' },
  { label: '日用百货', value: '14' },
  { label: '材料配件', value: '16' },
  { label: '通讯电子', value: '17' },
  { label: '仪器仪表', value: '18' },
  { label: '环保绿化', value: '19' },
  { label: '服装布料', value: '20' },
  { label: '制造生产', value: '21' },
  { label: '家居建材', value: '22' },
  { label: '食品饮品', value: '23' },
  { label: '债券发行', value: '24' },
  { label: '其他', value: '15' },
];

export const TENDER_CONTACTS_TYPE = [
  {
    label: '有联系方式',
    value: [1, 2, 3],
  },
  {
    label: '有招标/采购联系方式',
    value: [1],
  },
  {
    label: '有中标联系方式',
    value: [2],
  },
  {
    label: '有代理联系方式',
    value: [3],
  },
];

export const TENDER_OWNER_TYPES = [
  { value: 7, label: '党政机关' },
  { value: 6, label: '事业单位' },
  { value: 3, label: '医院' },
  { value: 4, label: '学校' },
  { value: 8, label: '国有企业' },
  { value: 2, label: '其他一般企业' },
  // { value: 1, label: '政府' },
  // { value: 5, label: '无类型' },
];

export const QUALIFICATION_CERTIFICATE = [
  {
    label: '电信设备进网许可证',
    value: 'C_211_A',
  },
  {
    label: '电信业务经营许可证',
    value: 'TV001',
  },
  {
    label: '建筑资质',
    value: 'BM',
  },
  {
    label: '安全生产许可证',
    value: 'BM014',
  },
  {
    label: '排污许可证',
    value: 'EM001',
  },
  {
    label: '办学许可证',
    value: 'IN001',
  },
  {
    label: '采矿许可证',
    value: 'MN001',
  },
  {
    label: '金融许可证',
    value: 'JR001',
  },
  {
    label: '食品生产许可证',
    value: 'C_997_91',
  },
  {
    label: '食品经营许可证',
    value: 'FB001',
  },
  {
    label: '医疗器械生产许可证',
    value: 'EQ002',
  },
  {
    label: '医疗器械经营许可证',
    value: 'EQ004',
  },
  {
    label: '国产非特殊用途化妆品备案',
    value: 'CO004',
  },
  // {
  //   label: '商业特许经营备案',
  //   value: 'CF001',
  // },
  {
    label: '质量管理体系认证',
    value: 'CNCA082',
  },
  {
    label: '环境管理体系(ISO14001)',
    value: 'CNCA009',
  },
  {
    label: '职业健康安全管理体系(ISO45001)',
    value: 'CNCA025',
  },
  {
    label: '知识产权管理体系',
    value: 'CNCA058062',
  },
];

export const INDUSTRYMAP = TENDER_INDUSTRY.reduce((map, ind) => {
  map[ind.value] = ind.label;
  return map;
}, {});

// 经济类型
export const ECONOMICS_TYPES = [
  {
    label: '国有企业',
    value: '*********',
    children: [
      {
        label: '央企',
        value: '*********001',
      },
      {
        label: '央企子公司',
        value: '*********002',
      },
      {
        label: '省管国企',
        value: '*********003',
      },
      {
        label: '市管国企',
        value: '*********004',
      },
      {
        label: '国有全资企业',
        value: '*********005',
      },
      {
        label: '国有独资企业',
        value: '*********006',
      },
      {
        label: '国有控股企业',
        value: '*********007',
      },
    ],
  },
  {
    label: '民营企业',
    value: '002006',
  },
  {
    label: '港澳台投资企业',
    value: '002002',
  },
  {
    label: '外商投资企业',
    value: '002003',
    children: [
      {
        label: '中外合资经营企业',
        value: '002003001',
      },
      {
        label: '中外合作经营企业',
        value: '002003002',
      },
      {
        label: '外资企业',
        value: '002003003',
      },
      {
        label: '外商投资股份有限公司',
        value: '002003005',
      },
      {
        label: '其他外商投资企业',
        value: '002003004',
      },
    ],
  },
];

// 证书类型，用于RA-7947 招标排查时都对资质证书进行排查 取值来自准入排查的资质证书维度
export const TENDER_CERTIFICATELIST = [
  {
    label: '质量管理体系认证',
    value: '000002001',
  },
  {
    label: '环境管理体系认证',
    value: '000002002',
  },
  {
    label: '食品安全管理体系认证',
    value: '000002004',
  },
  {
    label: '服务认证',
    value: '000003',
  },
  {
    label: '自愿性产品认证',
    value: '000004',
  },
  {
    label: '食品农产品认证',
    value: '000005',
  },
  {
    label: '电信业务经营许可证',
    value: '001',
  },
  {
    label: '建筑业企业资质证书',
    value: '004004',
  },
  {
    label: '安全生产许可证',
    value: '011',
  },
  {
    label: '医疗器械生产许可证',
    value: '012001001',
  },
  {
    label: '医疗器械生产备案凭证',
    value: '012001002',
  },
  {
    label: '医疗器械经营许可证',
    value: '012002001',
  },
  {
    label: '医疗器械经营备案凭证',
    value: '012002002',
  },
  {
    label: '医疗器械注册证',
    value: '012003001',
  },
  {
    label: '医疗器械备案凭证',
    value: '012003002',
  },
  {
    label: '排污许可证',
    value: '027001',
  },
  {
    label: 'CCC工厂认证',
    value: '900',
  },
];
