import { keyBy } from 'lodash';

/**
 * 与设置中心的选项保持一致
 */
export const RelationTypeOptions = [
  // 投资任职关联（直接关系）
  { label: '法定代表人', value: 'Legal', type: 0 },
  { label: '历史法定代表人', value: 'HisLegal', type: 0 },
  { label: '持股/投资关联', value: 'Invest', type: 0 },
  { label: '董监高', value: 'Employ', type: 0 },
  { label: '历史董监高', value: 'HisEmploy', type: 0 },
  { label: '历史持股/投资关联', value: 'HisInvest', type: 0 },
  { label: '分支机构', value: 'Branch', type: 0 },
  { label: '实际控制人', value: 'ActualController', type: 0 },
  { label: '控制关系', value: 'Hold', type: 0 },
  { label: '控制关系', value: 'ControlRelation', type: 0 },

  // 潜在关联/利益关联方（疑似关系）
  { label: '相同电话号码', value: 'ContactNumber', type: 1 },
  { label: '相同邮箱', value: 'Mail', type: 1 },
  { label: '相同经营地址', value: 'Address', type: 1 },
  { label: '相互担保关联', value: 'Guarantor', type: 1 },
  { label: '股权出质关联', value: 'EquityPledge', type: 1 },
  { label: '动产抵押关联', value: 'ChattelMortgage', type: 1 },
  { label: '上下游关联', value: 'UpAndDownRelation', type: 1 },
  { label: '围串标关联', value: 'BidCollusive', type: 1 },
  { label: '相同域名信息', value: 'Website', type: 1 },
  { label: '相同专利信息', value: 'Patent', type: 1 },
  { label: '相同国际专利信息', value: 'IntPatent', type: 1 },
  { label: '相同软件著作权', value: 'SoftwareCopyright', type: 1 },
  { label: '相同司法案件', value: 'Case', type: 1 },
  { label: '疑似同名主要人员', value: 'SameNameEmployee', type: 1 },
];

export const RelationTypeMap = keyBy(
  RelationTypeOptions.map((item) => ({ ...item, value: String(item.value).toUpperCase() })),
  'value'
);

export const DIlIGENCETYPES = [
  {
    key: 'ShareholdingRelationship',
    status: 0,
    keyName: '持股关联',
  },
  {
    key: 'InvestorsRelationship',
    status: 0,
    keyName: '投资关联',
  },
  {
    key: 'EmploymentRelationship',
    status: 0,
    keyName: '董监高/法人关联',
  },
  {
    key: 'HisShareholdingRelationship',
    status: 0,
    keyName: '持股关联（历史）',
  },
  {
    key: 'HisInvestorsRelationship',
    status: 0,
    keyName: '投资关联（历史）',
  },
  {
    key: 'HisLegalAndEmploy',
    status: 0,
    keyName: '董监高/法人（历史）',
  },
  {
    key: 'ActualController',
    status: 0,
    keyName: '相同实际控制人',
  },
  {
    key: 'MainInfoUpdateBeneficiary',
    status: 1,
    keyName: '相同受益所有人',
  },
  {
    key: 'FinalBenefit',
    status: 1,
    keyName: '最终受益所有人',
  },
  {
    key: 'Branch',
    status: 0,
    keyName: '分支机构',
  },
  {
    key: 'ShareholdingRatio',
    value: 0.01,
    status: 0,
    keyName: '持股/投资股权比例（含历史）',
  },
];

export const SUSPECTYPES = [
  {
    key: 'Guarantee',
    status: 1,
    keyName: '相互担保关联',
  },
  {
    key: 'Guarantor',
    status: 1,
    keyName: '相互担保关联',
  },
  {
    key: 'EquityPledgeRelation',
    status: 1,
    keyName: '股权出质关联',
  },
  {
    key: 'EquityPledge',
    status: 1,
    keyName: '股权出质关联',
  },
  {
    key: 'Pledge',
    status: 1,
    keyName: '股权出质关联',
  },
  {
    key: 'HasPledgee',
    status: 1,
    keyName: '股权出质关联',
  },
  {
    key: 'HasPhone',
    status: 1,
    keyName: '相同电话号码',
  },
  {
    key: 'HasAddress',
    status: 1,
    keyName: '相同经营地址',
  },
  {
    key: 'HasEmail',
    status: 1,
    keyName: '相同邮箱',
  },
];

// 图谱和接口查询的keymap映射
export const RalationKeyMap = [...RelationTypeOptions, ...DIlIGENCETYPES, ...SUSPECTYPES].reduce((map: any, item: any) => {
  const key = (item.key || item.value).toUpperCase();
  const name = item.keyName || item.label;
  return {
    ...map,
    [key]: name,
  };
}, {});
