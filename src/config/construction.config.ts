import Tree from '@/utils/tree';

type OptionsMap = Record<string, string>;
const toMap = (options: Array<{ label: string; value: string }>): OptionsMap =>
  options.reduce((map, { value, label }) => ({ ...map, [value]: label }), {} as OptionsMap);

export const PROJECT_PURPOSE = [
  {
    label: '房屋建筑',
    value: 'PJ001',
    children: [
      {
        label: '居住建筑',
        value: 'PJ001001',
        children: [
          {
            label: '居住建筑',
            value: 'PJ001001001',
          },
          {
            label: '居住建筑配套工程',
            value: 'PJ001001002',
          },
          {
            label: '公共租赁住房',
            value: 'PJ001001003',
          },
          {
            label: '共有产权住房',
            value: 'PJ001001004',
          },
          {
            label: '商品住房',
            value: 'PJ001001005',
          },
          {
            label: '棚户区改造住房',
            value: 'PJ001001006',
          },
          {
            label: '其他居住建筑',
            value: 'PJ001001007',
          },
        ],
      },
      {
        label: '公共建筑',
        value: 'PJ001002',
        children: [
          {
            label: '公共建筑',
            value: 'PJ001002001',
          },
          {
            label: '科教文卫建筑',
            value: 'PJ001002002',
          },
          {
            label: '商业建筑',
            value: 'PJ001002003',
          },
          {
            label: '旅游建筑',
            value: 'PJ001002004',
          },
          {
            label: '通信建筑',
            value: 'PJ001002005',
          },
          {
            label: '交通运输类',
            value: 'PJ001002006',
          },
          {
            label: '办公建筑',
            value: 'PJ001002007',
          },
          {
            label: '公共建筑配套工程',
            value: 'PJ001002008',
          },
        ],
      },
      {
        label: '农业建筑',
        value: 'PJ001003',
        children: [
          {
            label: '农业建筑',
            value: 'PJ001003001',
          },
          {
            label: '农业建筑配套工程',
            value: 'PJ001003002',
          },
        ],
      },
      {
        label: '工业建筑',
        value: 'PJ001004',
        children: [
          {
            label: '工业建筑',
            value: 'PJ001004001',
          },
          {
            label: '工业建筑配套工程',
            value: 'PJ001004002',
          },
        ],
      },
      {
        label: '商住楼',
        value: 'PJ001005',
      },
    ],
  },
  {
    label: '市政基础设施',
    value: 'PJ002',
    children: [
      {
        label: '道路',
        value: 'PJ002001',
      },
      {
        label: '隧道',
        value: 'PJ002002',
      },
      {
        label: '公共交通',
        value: 'PJ002003',
      },
      {
        label: '轨道交通',
        value: 'PJ002004',
      },
      {
        label: '综合管廊',
        value: 'PJ002005',
      },
      {
        label: '桥隧',
        value: 'PJ002006',
      },
      {
        label: '桥梁',
        value: 'PJ002007',
      },
      {
        label: '防洪',
        value: 'PJ002008',
      },
      {
        label: '给水',
        value: 'PJ002009',
      },
      {
        label: '排水',
        value: 'PJ002010',
      },
      {
        label: '燃气',
        value: 'PJ002011',
      },
      {
        label: '热力',
        value: 'PJ002012',
      },
      {
        label: '园林绿化',
        value: 'PJ002013',
      },
      {
        label: '环境园林',
        value: 'PJ002014',
      },
      {
        label: '风景园林',
        value: 'PJ002015',
      },
      {
        label: '环境卫生',
        value: 'PJ002016',
      },
      {
        label: '污水处理',
        value: 'PJ002017',
      },
      {
        label: '垃圾处理',
        value: 'PJ002018',
      },
    ],
  },
  {
    label: '其他',
    value: 'PJ003',
  },
];
export const HONER_TYPE = [
  // { value: '0', label: '未知' },
  { value: '1', label: '工程荣誉' },
  { value: '2', label: '企业荣誉' },
  { value: '3', label: '人员荣誉' },
];

export const HONER_LEVEL = [
  // { value: '0', label: '未知' },
  { value: '1', label: '国家级' },
  { value: '2', label: '省部级' },
  { value: '3', label: '市级' },
  // { value: '4', label: '区县级' },
];

export const ACHIEVEMENT_TYPE = [
  // { value: '0', label: '未知' },
  { value: '100', label: '房屋建筑工程' },
  { value: '200', label: '市政基础设施工程' },
  { value: '1', label: '其他' },
];
export const HONER_LEVEL_MAP = toMap(HONER_LEVEL);
export const HONER_TYPE_MAP = toMap(HONER_TYPE);

[PROJECT_PURPOSE, HONER_LEVEL, HONER_LEVEL_MAP, HONER_TYPE, HONER_TYPE_MAP].forEach(Object.freeze);

export const ProjectPurposeTree = new Tree(PROJECT_PURPOSE);
