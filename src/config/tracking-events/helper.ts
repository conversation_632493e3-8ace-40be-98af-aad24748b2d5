type EventData = {
  event: 'page_view' | 'page_button_click' | 'search_words_click';
  entity?: {
    [key: string]: string | number | object | null | undefined;
    // page_name: string;
    // page_button_click: string;
    // search_type: string;
    // object_type: any,
    // object_detail: object,
    // searchwords_detail: string;
  };
};

/**
 * 页面访问事件
 */
export const createPageEvent =
  (applicationName: string) =>
  (pageName: string): EventData => {
    return {
      event: 'page_view',
      entity: {
        application_name: applicationName,
        page_name: pageName,
      },
    };
  };

/**
 * 点击事件
 */
export const createClickEvent =
  (applicationName: string) =>
  (pageName: string, buttonName: string, pageModule?: string): EventData => {
    return {
      event: 'page_button_click',
      entity: {
        application_name: applicationName,
        page_name: pageName,
        button_name: buttonName,
        page_module: pageModule,
      },
    };
  };

/**
 * 搜索事件
 */
export const createSearchEvent =
  (applicationName: string) =>
  ({
    pageName,
    searchType,
    searchKeywords,
    objectType,
    objectDetail,
  }: {
    pageName: string;
    searchType: string;
    searchKeywords?: string;
    objectType?: string;
    objectDetail?: any;
  }): EventData => {
    return {
      event: 'search_words_click',
      entity: {
        application_name: applicationName,
        page_name: pageName,
        search_type: searchType,
        searchwords_detail: searchKeywords,
        object_type: objectType,
        object_detail: objectDetail,
      },
    };
  };
