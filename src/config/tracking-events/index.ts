import { getCurrentInstance } from 'vue';

import { createPageEvent, createClickEvent, createSearchEvent } from './helper';

export type EventName = 'page_button_click' | 'search_words_click' | 'page_view';

// 页面访问埋点
const APPLICATION_NAME = '客商风险排查';

const pageEvent = createPageEvent(APPLICATION_NAME);
const clickEvent = createClickEvent(APPLICATION_NAME);
const searchEvent = createSearchEvent(APPLICATION_NAME);

export const TRAKING_EVENTS = {
  // 页面访问
  '6202': pageEvent,
  '6234': pageEvent, // 外部黑名单-页面浏览埋点
  '6240': pageEvent, // 分析看板页面-页面浏览埋点
  '6899': pageEvent, // 工作台

  // 点击事件
  '6204': clickEvent,
  '6212': clickEvent,
  '6206': clickEvent,
  '6208': clickEvent, // 批量排查
  '6224': clickEvent,
  '6242': clickEvent, // 分析看板页面-点击埋点
  '6903': clickEvent, // 分析看板-页面模块
  '6238': clickEvent, // 人员管理页面--点击埋点
  '6921': clickEvent, // 人员管理页面 - 新增人员弹框
  '6232': clickEvent, // 内部黑名单页面--点击埋点
  '6923': clickEvent, // 内部黑名单页面 - 新增黑名单弹框
  '6226': clickEvent, // 第三方列表页面--点击埋点
  '6925': clickEvent, // 第三方列表 - 新增第三方弹框
  '6214': clickEvent, // 排查记录--导出列表
  '6891': clickEvent, // 三方风险左侧导航
  '6895': clickEvent, // 三方风险顶部菜单
  '6901': clickEvent, // 工作台
  '6911': clickEvent, // 工作台 - 数据概览
  '6913': clickEvent, // 工作台 - 常用功能
  '6915': clickEvent, // 工作台 - 严重风险事件
  '6917': clickEvent, // 工作台 - 工商变更动态
  '6919': clickEvent, // 工作台 - 企业负面新闻
  '7647': clickEvent, // 工作台 - 企业风险分布
  '6927': clickEvent, // 风险年检
  '6931': clickEvent, // 风险年检/批量排查结果页 - 结果列表
  '7677': clickEvent, // 年检记录
  '6933': clickEvent, // 招标排查详情
  '6935': clickEvent, // 招标排查详情 - 排查结果
  '6937': clickEvent, // 招标排查详情 - 排查企业明细
  '6939': clickEvent, // 招标排查详情 - 深度关系排查图谱
  '6943': clickEvent, // 招标排查历史记录
  '6947': clickEvent, // 批量招标排查结果页
  '6949': clickEvent, // 批量招标排查结果页 - 结果列表
  '6951': clickEvent, // 批量招标排查
  '6953': clickEvent, // 招标排查/批量排查 - 批量粘贴弹框
  '6955': clickEvent, // 招标排查 - 从第三方列表选择弹框
  '6957': clickEvent, // 风险动态/舆情动态
  '6975': clickEvent, // 监控列表
  '6979': clickEvent, // 准入排查
  '6981': clickEvent, // 准入排查/批量排查
  '6983': clickEvent, // 准入排查详情页 - 排查详情
  '6985': clickEvent, // 准入排查详情页 - 排查结果
  '6987': clickEvent, // 准入排查详情页 - 公司概况
  '6989': clickEvent, // 准入排查详情页 - 资质信息
  '6991': clickEvent, // 准入排查详情页 - 信用评价
  '7665': clickEvent, // 批量排查结果页 - 排序
  '7667': clickEvent, // 批量排查结果页
  '7641': clickEvent, // 投标预警
  '7645': clickEvent, // 投标详情
  '6959': clickEvent, // 企业核实（第三方列表/内部黑名单/监控列表/批量排查的上传文件二次确认页面）
  '7675': clickEvent, // 维度侧滑框
  '7687': clickEvent, // 个人中心 - 账号设置
  '7705': clickEvent, // 任务中心
  '7709': clickEvent, // 消息中心
  '7717': clickEvent, // 搜索结果
  '7721': clickEvent, // 准入排查设置
  '7723': clickEvent, // 风险年检设置
  '7725': clickEvent, // 招标排查设置
  '7727': clickEvent, // 合作监控设置 - 风险动态设置
  '7729': clickEvent, // 合作监控设置 - 舆情动态设置
  '7731': clickEvent, // 预警方案设置
  '7733': clickEvent, // 分组管理
  '7735': clickEvent, // 标签管理
  '8113': clickEvent, // 特定利益关系排查
  '8115': clickEvent, // 批量特定利益关系排查
  '8119': clickEvent, // 资质筛查
  '8121': clickEvent, // 特定利益关系排查详情
  '8125': clickEvent, // 特定利益关系排查详情 - 排查结果
  '8169': clickEvent, // 批量特定利益排查结果

  // 搜索事件
  '6210': searchEvent,
  '6236': searchEvent, // 人员管理页面--搜索埋点
  '6230': searchEvent, // 内部黑名单页面--搜索埋点
  '6228': searchEvent, // 第三方列表页面--搜索埋点
  '6893': searchEvent, // 三方风险顶部菜单--企业信息查询
  '6929': searchEvent, // 风险年检/批量排查结果页
  '6941': searchEvent, // 招标排查历史记录
  '6945': searchEvent, // 批量招标排查结果页
  '6961': searchEvent, // 风险动态/舆情动态
  '6973': searchEvent, // 监控列表
  '6977': searchEvent, // 准入排查
  '7643': searchEvent, // 投标预警 - 搜索
  '7679': searchEvent, // 年检记录 - 搜索
  '7707': searchEvent, // 任务中心 - 搜索
  '7719': searchEvent, // 搜索结果 - 搜索
  '8171': searchEvent, // 批量特定利益排查结果页 - 搜索
} as const;

export function createTrackEvent(eventID: number, ...args: Parameters<any>) {
  if (typeof TRAKING_EVENTS[eventID] !== 'function') {
    throw new Error(`埋点事件: ${eventID}不存在`);
  }
  const eventData = TRAKING_EVENTS[eventID].apply(null, args);
  return eventData;
}

export function useTrack() {
  const vm = getCurrentInstance();
  const instance = vm?.proxy as any;
  return instance?.$track;
}
