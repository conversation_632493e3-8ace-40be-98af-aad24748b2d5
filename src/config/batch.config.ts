export enum BatchBusinessTypeEnums {
  DiligenceID = 11, // 批量排查-传入
  DiligenceContinuous = 14, // 风险监控(持续尽调)
  DiligencePDFExport = 34, // 洞察 pdf 生成报告(PDF导出)
  MonitorRelatedCompany = 35,
  VerificationImport = 36, // 人企核验-批量导入核验
  VerificationResultRegularExport = 37, // 人企核验-常规核验-核验结果-导出
  VerificationResultDeepExport = 38, // 人企核验-深度核验-核验结果-导出
  VerificationRecordExport = 39, // 人企核验-核验记录-导出
  VerificationRegularImport = 40, // 人企核验-常规核验-批量导入核验
  VerificationDeepImport = 41, // 人企核验-深度核验-批量导入核验
  VerificationPackageExport = 42, // 人企核验-套餐消费-导出
  MonitorImport = 43, // 监控-批量导入
  MonitorFailedExport = 44, // 监控-批量导出失败数据
}
