import AREA_OPTIONS from '@/shared/constants/area.constant';

// 未采集省份编码
export const UNCOLLECTED_CODE = ['GS', 'HK', 'MO', 'TW'];

export const RECORD_OPTIONS = AREA_OPTIONS.map((one) => {
  if (UNCOLLECTED_CODE.includes(String(one.value))) {
    return { ...one, disabled: true };
  }
  return one;
});

export enum COMPANY_TYPES {
  '省内和进省企业',
  '省内企业',
  '进省企业',
}

export const COMPANY_TYPES_OPTIONS = [
  {
    label: COMPANY_TYPES['1'],
    value: '1',
  },
  {
    label: COMPANY_TYPES['2'],
    value: '2',
  },
  {
    label: COMPANY_TYPES['0'],
    value: '0',
  },
];

export enum AccountConfig {
  Info = 'info',
  Nickname = 'nickname',
  Phone = 'phone',
  // Email = 'email',
  Password = 'password',
}

export const AccountConfigEnum = {
  [AccountConfig.Info]: {
    label: '组织信息',
    icon: 'icon-a-qiyexian',
  },
  [AccountConfig.Nickname]: {
    label: '成员昵称',
    icon: 'icon-a-renyuan',
  },
  [AccountConfig.Phone]: {
    label: '手机号码',
    icon: 'icon-a-dianhuaxian',
  },
  // [AccountConfig.Email]: '邮箱',
  [AccountConfig.Password]: {
    label: '登录密码',
    icon: 'icon-shangsuo',
  },
};

// 导出的设置
export const EXPORTITEMS = [
  {
    label: '选中导出',
    key: 'exportByIds',
  },
  {
    label: '全部导出',
    key: 'export',
  },
];
