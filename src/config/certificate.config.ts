import Tree from '@/utils/tree';

const CERTIFICATE = [
  {
    label: '注册建筑师',
    value: 'JZ001',
    children: [
      {
        label: '一级注册建筑师',
        value: 'JZ001001',
      },
      {
        label: '二级注册建筑师',
        value: 'JZ001002',
      },
    ],
  },
  {
    label: '勘察设计注册工程师',
    value: 'JZ002',
    children: [
      {
        label: '一级注册结构工程师',
        value: 'JZ002001001',
      },
      {
        label: '二级注册结构师',
        value: 'JZ002001002',
        alias: '二级注册结构工程师',
      },
      {
        label: '注册土木工程师（岩土）',
        value: 'JZ002002001',
      },
      {
        label: '注册电气工程师（发输变电）',
        value: 'JZ002003001',
      },
      {
        label: '注册电气工程师（供配电）',
        value: 'JZ002003002',
      },
      {
        label: '注册公用设备工程师（给水排水）',
        value: 'JZ002004001',
      },
      {
        label: '注册公用设备工程师（暖通空调）',
        value: 'JZ002004002',
      },
      {
        label: '注册公用设备工程师（动力）',
        value: 'JZ002004003',
      },
      {
        label: '注册化工工程师',
        value: 'JZ002005',
      },
    ],
  },
  {
    label: '注册监理工程师',
    value: 'JZ003',
    children: [
      {
        label: '不限专业',
        value: 'JZ003',
      },
      {
        label: '水利水电工程',
        value: 'ZY1200',
      },
      {
        label: '公路工程',
        value: 'ZY1201',
      },
      {
        label: '通信工程',
        value: 'ZY1202',
      },
      {
        label: '港口与航道工程',
        value: 'ZY1203',
      },
      {
        label: '市政公用工程',
        value: 'ZY1204',
      },
      {
        label: '矿山工程',
        value: 'ZY1205',
      },
      {
        label: '化工石油工程',
        value: 'ZY1206',
      },
      {
        label: '农林工程',
        value: 'ZY1207',
      },
      {
        label: '机电安装工程',
        value: 'ZY1208',
      },
      {
        label: '冶炼工程',
        value: 'ZY1209',
      },
      {
        label: '房屋建筑工程',
        value: 'ZY1210',
      },
      {
        label: '铁路工程',
        value: 'ZY1211',
      },
      {
        label: '航天航空工程',
        value: 'ZY1212',
      },
      {
        label: '电力工程',
        value: 'ZY1213',
      },
    ],
  },
  {
    label: '注册建造师',
    value: 'JZ004',
    children: [
      {
        label: '一级注册建造师',
        value: 'JZ004001',
        children: [
          {
            label: '不限专业',
            value: 'JZ004001',
          },
          {
            label: '通信与广电工程',
            value: 'ZY101',
          },
          {
            label: '铁路工程',
            value: 'ZY102',
          },
          {
            label: '水利水电工程',
            value: 'ZY103',
          },
          {
            label: '机电工程',
            value: 'ZY104',
          },
          {
            label: '市政公用工程',
            value: 'ZY105',
          },
          {
            label: '民航机场工程',
            value: 'ZY106',
          },
          {
            label: '矿业工程',
            value: 'ZY107',
          },
          {
            label: '公路工程',
            value: 'ZY108',
          },
          {
            label: '建筑工程',
            value: 'ZY109',
          },
          {
            label: '港口与航道工程',
            value: 'ZY110',
          },
        ],
      },
      {
        label: '二级注册建造师',
        value: 'JZ004003',
        children: [
          {
            label: '不限专业',
            value: 'JZ004003',
          },
          {
            label: '公路工程',
            value: 'ZY301',
          },
          {
            label: '建筑工程',
            value: 'ZY302',
          },
          {
            label: '机电工程',
            value: 'ZY303',
          },
          {
            label: '水利水电工程',
            value: 'ZY304',
          },
          {
            label: '市政公用工程',
            value: 'ZY305',
          },
          {
            label: '矿业工程',
            value: 'ZY306',
          },
        ],
      },
    ],
  },
  {
    label: '注册造价工程师',
    value: 'JZ005',
    children: [
      {
        label: '不限专业',
        value: 'JZ005',
      },
      {
        label: '土建',
        value: 'ZY1214',
      },
      {
        label: '安装',
        value: 'ZY1215',
      },
    ],
  },
];

Object.freeze(CERTIFICATE);

export const tree = new Tree(CERTIFICATE);

export default CERTIFICATE;
