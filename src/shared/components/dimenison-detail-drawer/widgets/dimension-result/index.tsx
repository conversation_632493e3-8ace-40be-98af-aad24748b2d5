import { PropType, defineComponent, ref } from 'vue';
import { <PERSON><PERSON>, Spin } from 'ant-design-vue';

import QRichTable from '@/components/global/q-rich-table';
import { getContent } from '@/utils/content-helper';
import { dynamicDetailJudge } from '@/hooks/risk-dimension/use-dynamic-risk-dimension';
import ClampContent from '@/components/clamp-content';
import CompanyStatus from '@/components/global/q-company-status';
import RiskHitReasonWrapper from '@/shared/components/risk-hit-reason-wrapper';
import formatDate from '@/utils/format/date';

import styles from './dimension-result.module.less';

const DimensionResult = defineComponent({
  name: 'DimensionResult',
  props: {
    strategy: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    columns: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    dataSource: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    dimensionKey: {
      type: String,
      default: '',
    },
    hitDetail: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    total: {
      type: Number,
      default: 0,
    },
    strategyName: {
      type: String,
      default: '',
    },
    hitTime: {
      type: String,
      default: undefined,
    },
    basicParams: {
      type: Object,
      default: () => ({
        companyId: '03a350311543bd8fbd6d23a4efeafcac',
        dimensionKey: 'RelatedCompanies',
        strategyId: 101212,
        diligenceId: 50177543,
        batchId: 50002631,
        preBatchId: 50002616,
      }),
    },
    extra: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const pagigation = ref({
      current: 1,
      pageSize: 10,
      pageSizeOptions: ['10', '20'],
      total: 0,
    });
    const pageChange = (current, pageSize) => {
      pagigation.value.current = current;
      pagigation.value.pageSize = pageSize;
      emit('change', { pageIndex: current, pageSize });
    };
    return {
      pagigation,
      pageChange,
    };
  },
  render() {
    // loading
    if (this.loading) {
      return <Spin class={styles.container} spinning={true} />;
    }
    const pagigation = {
      ...this.pagigation,
      total: this.total,
      onChange: this.pageChange,
      onShowSizeChange: this.pageChange,
    };
    return (
      <div class={styles.container}>
        <div class={styles.title}>
          <span class={styles.titleText}>
            【<span domPropsInnerHTML={this.strategyName}></span>】{this.total}
            条记录
          </span>
          <RiskHitReasonWrapper placement="right" hitDetails={this.hitDetail} dimensionStrategies={this.dimensionStrategies as any} />
        </div>
        <QRichTable
          rowKey={'dimensionId'}
          showIndex={true}
          columns={this.columns}
          dataSource={this.dataSource}
          loading={this.loading}
          pagination={pagigation}
          scopedSlots={{
            companyNameRelated: (record, index, rowIndex, rowData) => {
              const nameKeyList = rowData.nameKeyList || {
                Name: 'Name',
                KeyNo: 'KeyNo',
              };
              const name = record[nameKeyList.Name];
              const keyNo = record[nameKeyList.KeyNo];
              return (
                <a href={`/embed/companyDetail?keyNo=${keyNo}&title=${name}`} target="_blank">
                  {name}
                </a>
              );
            },
            relatedTypeDesc: (record) => {
              return record?.relatedTypeDescList?.join('，') ?? '-';
            },
            riskTypeDescList: (record) => {
              const list = record?.riskTypeDescList ?? [record.shortStatus || record.Status];
              return (
                <div class="flex" style={{ gap: '4px' }}>
                  {list.map((tag) => {
                    if (!tag) {
                      return '-';
                    }
                    return <CompanyStatus style={{ margin: 0 }} status={tag}></CompanyStatus>;
                  })}
                </div>
              );
            },
            MonitorContent: (item) => {
              return (
                <ClampContent class={'trends-content'} clampKey={item.recordId} line={Infinity}>
                  <span domPropsInnerHTML={getContent(item, this.dimensionKey)}></span>
                </ClampContent>
              );
            },
            hitTime: () => {
              return this.hitTime;
            },
            updateTime: (item) => {
              const { Publishdate, CreateDate } = item;
              return formatDate(Publishdate || CreateDate, { x1000: true, pattern: 'YYYY-MM-DD' });
            },
            Action: (record) => {
              if (!dynamicDetailJudge({ ...record, dimensionKey: this.dimensionKey })) {
                return '-';
              }
              return (
                <Button
                  type="link"
                  onClick={() => {
                    this.$emit('openDimensionDetail', record);
                  }}
                >
                  详情
                </Button>
              );
            },
          }}
        ></QRichTable>
      </div>
    );
  },
});

export default DimensionResult;
