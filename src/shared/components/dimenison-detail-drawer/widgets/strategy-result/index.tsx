import { PropType, computed, defineComponent, ref, onMounted } from 'vue';
import { cloneDeep, isNil, isObject } from 'lodash';

import { monitor } from '@/shared/services';
import { useDynamicRiskDimensionDetail } from '@/hooks/risk-dimension/use-dynamic-risk-dimension';
import { useSearchCompanies } from '@/apps/risk-monitor/pages/targets/hooks/use-search-companies';

import DimensionResult from '../dimension-result';
import { TCCONFIG } from '../../config';

const StrategyResult = defineComponent({
  name: 'StrategyResult',
  props: {
    strategy: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    hitDetail: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    hitTime: {
      type: String,
      default: undefined,
    },
    basicParams: {
      type: Object,
      default: () => ({
        companyId: '03a350311543bd8fbd6d23a4efeafcac',
        dimensionKey: 'RelatedCompanies',
        strategyId: 101212,
        diligenceId: 50177543,
        batchId: 50002631,
        preBatchId: 50002616,
      }),
    },
    extra: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  setup(props) {
    // 为了使用useSearchCompanies
    const filterValues = ref({
      filters: {
        ...props.basicParams,
        strategyId: props.strategy.strategyId,
        dimensionKey: props.strategy.dimensionKey,
      },
    });

    const searchCompanies = useSearchCompanies(filterValues, monitor.getDynamicContentList);

    const [openDimensionDetail] = useDynamicRiskDimensionDetail();
    // 维度类型，根据维度类型展示不同的表格
    const dimensionKey = computed(() => {
      return props.dimensionStrategies[0]?.dimensionHitStrategyEntity?.extendJson?.displayKey || props.strategy.dimensionKey;
    });

    const getColumns = computed(() => {
      return cloneDeep(TCCONFIG[dimensionKey.value] || TCCONFIG.defaultColumns);
    });

    // 是否是同时包含增加、减少对数据的
    const isResultContainsAddAndMinus = computed(() => {
      return ['OutwardInvestmentChange'].includes(props.strategy.dimensionKey);
    });

    const dataArr = ref<any[]>([]);

    // 用于存放对于新增、减少都存于一起的数据特殊处理，原因是该数据是由后端对比快照获得的
    const preDealedData = ref<any[]>([]);

    // 预处理数据，后期直接截取就行
    const predealData = () => {
      const { added, removed } = searchCompanies.data.value?.data?.[0] || {};
      const changeDate = searchCompanies.data.value?.data?.[0].changeDate;
      preDealedData.value = [added, removed]
        .map((datas: any, index) => {
          if (isObject(datas as any)) {
            const totalData =
              datas?.data?.map((item) => ({
                ...item,
                ...item.dimensionContent,
                changeDate,
              })) || [];
            return {
              total: totalData.length,
              data: totalData,
              isAdd: index === 0,
            };
          }
          return undefined;
        })
        .filter(Boolean);
      dataArr.value = preDealedData.value.map((item) => {
        const { data, total, isAdd } = item;
        return {
          total,
          dataSource: data.slice(0, 10),
          isAdd,
        };
      });
    };
    const getDatasource = (params?: any) => {
      if (!isResultContainsAddAndMinus.value) {
        dataArr.value = [
          {
            dataSource: searchCompanies.data.value.Result,
            total: searchCompanies.pagination.value.total,
          },
        ];
        return;
      }
      const { pageIndex = 1, pageSize = 10, index } = params || {};
      if (!isNil(index)) {
        const calcData = preDealedData.value[index];
        const start = (pageIndex - 1) * pageSize;
        const end = Math.min(start + pageSize, calcData.total);
        const { data, total, isAdd } = calcData;
        const newData = {
          dataSource: data.slice(start, end),
          total: total,
          isAdd,
        };
        dataArr.value = dataArr.value.map((v, idx) => {
          return idx === index ? newData : v;
        });
      }
    };
    onMounted(async () => {
      await searchCompanies.search();
      if (isResultContainsAddAndMinus.value) {
        predealData();
      }
      getDatasource();
    });

    return {
      searchCompanies,
      getColumns,
      dimensionKey,
      loading: searchCompanies.isLoading,
      dataArr,
      isResultContainsAddAndMinus,
      openDimensionDetail,
      getDatasource,
    };
  },
  render() {
    return (
      <div>
        {/* 基本的维度表格 */}
        {this.dataArr.map((data, index) => {
          const { total, dataSource, isAdd } = data;
          const strategyName = this.strategy.strategyName;
          const title = this.isResultContainsAddAndMinus ? `${isAdd ? '新增' : '减少'} - ${this.strategy.strategyName}` : strategyName;
          return (
            <DimensionResult
              loading={this.loading}
              hitTime={this.hitTime}
              dataSource={dataSource}
              total={total}
              strategyName={title}
              strategy={this.strategy}
              columns={this.getColumns}
              dimensionStrategies={this.dimensionStrategies}
              dimensionKey={this.dimensionKey}
              hitDetail={this.hitDetail}
              onChange={async (data) => {
                if (!this.isResultContainsAddAndMinus) {
                  await this.searchCompanies.search(data);
                }
                this.getDatasource({
                  index,
                  ...data,
                });
              }}
              onOpenDimensionDetail={(data) => {
                this.openDimensionDetail({
                  ...data,
                  dimensionKey: this.dimensionKey,
                  companyId: this.basicParams.companyId,
                  companyName: this.basicParams.companyName,
                });
              }}
            />
          );
        })}
      </div>
    );
  },
});

export default StrategyResult;
