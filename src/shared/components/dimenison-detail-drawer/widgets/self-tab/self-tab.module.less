.container {
  display: flex;
  align-items: center;
  gap: 30px;
  border-bottom: 1px solid #e8e8e8;

  .tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    cursor: pointer;

    .tab {
      display: flex;
      height: 50px;
      align-items: center;
      gap: 4px;
      position: relative;

      .title {
        font-size: 15px;
        font-weight: bold;
      }

      &:hover {
        .title {
          color: #128bed;
        }
      }
    }

    .diligenceWarning {
      cursor: pointer !important;
    }

    .active {
      position: relative;
      color: #128bed;

      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #128bed;
      }
    }
  }

  .action {
    cursor: pointer;
    font-size: 15px;

    &:hover {
      color: #189bed;
    }
  }

  .active {
    color: #128bed;
  }

  :global {
    .ant-dropdown-open {
      .anticon {
        transform: rotate(180deg);
      }
    }
  }
}

.actionMenu {
  :global {
    .ant-dropdown-menu-item .ant-btn-link {
      color: #333 !important;
    }
  }

  .diligenceWarning {
    cursor: pointer !important;
    margin-left: 4px;
  }

  .active {
    color: #128bed;
  }
}
