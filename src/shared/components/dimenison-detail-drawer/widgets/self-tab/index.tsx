import { defineComponent, ref, PropType } from 'vue';
import { Dropdown, Menu } from 'ant-design-vue';

import DiligenceWarningPop from '@/components/diligence-warning-pop';
import QIcon from '@/components/global/q-icon';

import styles from './self-tab.module.less';

const SelfTab = defineComponent({
  name: 'SelfTab',
  props: {
    tabData: {
      type: Array as PropType<any[]>,
      required: true,
    },
    // 可见的tab数量
    visibleMaxCount: {
      type: Number,
      default: 6,
    },
  },
  setup(props, { emit }) {
    const activeTab = ref(0);

    const handleTabClick = (index: number) => {
      activeTab.value = index;
      emit('tabClick', index);
    };

    return {
      activeTab,
      handleTabClick,
    };
  },
  render() {
    // 可见的tab
    const visibelTabs = this.tabData.slice(0, this.visibleMaxCount);
    // 更多的tab
    const hiddenTabs = this.tabData.slice(this.visibleMaxCount);
    return (
      <div class={styles.container}>
        <div class={styles.tabs}>
          {visibelTabs.map((tab, index) => {
            const riskLevel = tab.hitStrategy.scoreSettings.riskLevel;
            return (
              <div
                class={{ [styles.tab]: true, [styles.active]: index === this.activeTab }}
                onClick={() => {
                  this.handleTabClick(index);
                }}
              >
                <span class={styles.title}>策略{tab.hitStrategy.order + 1}</span>
                <DiligenceWarningPop score={riskLevel} isMonitor={true} class={styles.diligenceWarning} />
              </div>
            );
          })}
        </div>
        {hiddenTabs.length > 0 && (
          <Dropdown trigger={['click']} onClick={(e) => e.preventDefault()} placement="bottomRight" overlayClassName={styles.actionMenu}>
            <div class={{ [styles.action]: true, [styles.active]: this.activeTab >= this.visibleMaxCount }}>
              更多
              <QIcon type="icon-a-shixinxia1x" />
            </div>
            <Menu slot="overlay">
              {hiddenTabs.map((tab, index) => {
                const riskLevel = tab.hitStrategy.scoreSettings.riskLevel;
                return (
                  <Menu.Item
                    key={index + this.visibleMaxCount}
                    class={{ [styles.active]: index + this.visibleMaxCount === this.activeTab }}
                    onClick={() => {
                      this.handleTabClick(index + this.visibleMaxCount);
                    }}
                  >
                    <span class={styles.title}>策略{index + this.visibleMaxCount + 1}</span>
                    <DiligenceWarningPop score={riskLevel} isMonitor={true} class={styles.diligenceWarning} />
                  </Menu.Item>
                );
              })}
            </Menu>
          </Dropdown>
        )}
      </div>
    );
  },
});
export default SelfTab;
