const RelatedCompanies = [
  {
    title: '关联企业名称',
    width: 230,
    nameKeyList: {
      Name: 'companyNameRelated',
      KeyNo: 'companyKeynoRelated',
    },
    scopedSlots: { customRender: 'companyNameRelated' },
  },
  {
    title: '关联方类型',
    width: 200,
    scopedSlots: { customRender: 'relatedTypeDesc' },
  },
  {
    title: '企业经营状态',
    scopedSlots: { customRender: 'riskTypeDescList' },
  },
  {
    title: '命中时间',
    width: 160,
    scopedSlots: { customRender: 'hitTime' },
  },
];

const defaultColumns = [
  {
    title: '风险内容',
    scopedSlots: { customRender: 'MonitorContent' },
  },
  {
    title: '更新日期',
    width: 180,
    dataIndex: 'CreateDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: {
      customRender: 'Action',
    },
  },
];

const AssetInvestigationAndFreezingColumns = [
  {
    title: '风险内容',
    scopedSlots: { customRender: 'MonitorContent' },
  },
  {
    title: '更新日期',
    width: 180,
    scopedSlots: {
      customRender: 'updateTime',
    },
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: {
      customRender: 'Action',
    },
  },
];

const TradeDetailColumns = [
  {
    title: '风险内容',
    scopedSlots: { customRender: 'MonitorContent' },
  },
  {
    title: '更新日期',
    width: 180,
    dataIndex: 'publishTime',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: {
      customRender: 'Action',
    },
  },
];

const OutwardInvestmentColumns = [
  {
    title: '企业名称',
    nameKeyList: {
      Name: 'Name',
      KeyNo: 'KeyNo',
    },
    scopedSlots: { customRender: 'companyNameRelated' },
  },
  {
    title: '关联方类型',
    width: 150,
    customRender: () => '对外投资（直接持股）大于25%的子公司（一层）',
  },
  {
    title: '企业经营状态',
    scopedSlots: { customRender: 'riskTypeDescList' },
  },
  {
    title: '更新时间',
    width: 160,
    dataIndex: 'changeDate',
  },
];

const ShareholderChangeColumns = [
  {
    title: '企业名称',
    nameKeyList: {
      Name: 'Name',
      KeyNo: 'KeyNo',
    },
    scopedSlots: { customRender: 'companyNameRelated' },
  },
  {
    title: '企业经营状态',
    width: 120,
    scopedSlots: { customRender: 'riskTypeDescList' },
  },
  {
    title: '更新时间',
    width: 160,
    dataIndex: 'changeDate',
  },
];

export const TCCONFIG = {
  RelatedCompanyChange: RelatedCompanies,
  RelatedCompanies: RelatedCompanies,
  OutwardInvestmentChange: OutwardInvestmentColumns, // 关联方吊注销
  ShareholderChange: ShareholderChangeColumns, // 控股股东变化
  AssetInvestigationAndFreezing: AssetInvestigationAndFreezingColumns, // 资产查冻
  PledgeMerger: AssetInvestigationAndFreezingColumns, // 动产抵押
  PatentInfo: AssetInvestigationAndFreezingColumns, // 专利转让出质
  ControllerCompany: AssetInvestigationAndFreezingColumns, // 集中注册无实缴资本
  OverseasListing: AssetInvestigationAndFreezingColumns, // 境内企业境外上市
  TradeDetail: TradeDetailColumns, // 财报披露关联方交易风险
  SupplierOrCustomer: TradeDetailColumns, // 供应商和客户交易金额同比变化
  defaultColumns,
};
