import { computed, defineComponent, onMounted, ref } from 'vue';
import { Drawer, Spin } from 'ant-design-vue';
import moment from 'moment';

import { sortBy } from 'lodash';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { setting } from '@/shared/services';

import SelfTab from './widgets/self-tab';
import styles from './dimension-detail-drawer.module.less';
import StrategyResult from './widgets/strategy-result';

const TargetsTrends = defineComponent({
  name: 'TargetsTrends',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const visible = ref(false);
    const init = ref(false);
    // 当前监控公司的id
    const companyId = ref(props.params?.record?.companyId);
    const record = props.params?.record;

    const riskModelDimensionStrategies = ref<Record<string, any>[]>([]);
    // 通过strategyIds就获取对应指标详情
    const getStrategyDetails = async (strategyIds) => {
      const params = {
        strategyIds,
        modelId: record.riskModelId,
      };
      try {
        const res = await setting.getDimensionHitStrategies(params);
        riskModelDimensionStrategies.value = res;
      } catch (error) {
        console.log(error);
      }
    };

    const queryParams = ref({
      companyId: record.companyId,
      companyName: record.companyName,
      dimensionKey: record.companyId,
      strategyId: record.companyId,
      diligenceId: record.diligenceId,
      batchId: record.batchId,
      preBatchId: record.preBatchId || 0,
      monitorGroupId: record.monitorGroupId,
      uniqueHashkey: record.uniqueHashkey,
      sort: {
        field: 'publishTime',
        order: 'DESC',
      },
    });

    const hitDetailsTab = ref<any[]>([]);

    const activeData = ref({} as Record<string, any>);

    // 获取指标下的hitStrategyIds
    const getStrategyIds = (activeData) => {
      const { must = [], should = [], must_not = [] } = activeData;
      return [...must, ...should, ...must_not];
    };

    const dealStatics = async (data) => {
      const { metricsContent } = data;
      const { metricScorePO } = metricsContent;
      const { hitDetails, otherHitDetails } = metricScorePO;
      hitDetailsTab.value = sortBy([hitDetails, ...otherHitDetails], (detail) => {
        return detail.hitStrategy.order;
      });
      activeData.value = hitDetailsTab.value[0];
      const strategyIds = hitDetailsTab.value.reduce((arr, cur) => {
        const curIds = getStrategyIds(cur.hitStrategy);
        arr = [...arr, ...curIds];
        return arr;
      }, []);
      await getStrategyDetails(strategyIds);
    };

    const renderTableData = computed(() => {
      return getStrategyIds(activeData.value);
    });

    onMounted(async () => {
      visible.value = true;
      await dealStatics(record);
      init.value = true;
    });

    return {
      init,
      visible,
      activeData,
      renderTableData,
      companyId,
      record,
      queryParams,
      hitDetailsTab,
      riskModelDimensionStrategies,
    };
  },
  render() {
    return (
      <Drawer
        wrapClassName={styles.container}
        visible={this.visible}
        footer={false}
        width={'800'}
        closable={false}
        onClose={() => {
          this.visible = false;
        }}
      >
        <div slot="title" class="drawer-title">
          <div>
            <q-entity-link coyObj={{ Name: this.params.record.companyName, KeyNo: this.params.record.companyId }} />-
            {`${this.params.record.metricsName} 命中详情`}
          </div>
          <q-icon
            class="icon-close"
            type="icon-tanchuangguanbi"
            onClick={() => {
              this.visible = false;
            }}
          ></q-icon>
        </div>
        <SelfTab
          style="margin: 0 15px"
          tabData={this.hitDetailsTab}
          onTabClick={(idx) => {
            this.activeData = this.hitDetailsTab[idx];
          }}
        />

        {!this.init ? (
          <Spin class={styles.dimensionDetailSpining} spinning={true} />
        ) : (
          <div class={styles.dimensionDetail}>
            {this.init && <div class={styles.dimensionDetailTitle}>共找到{this.activeData.totalHits}条动态</div>}
            <div class={styles.dimensionDetailContent}>
              {this.renderTableData.map((item, index) => {
                const strategies = this.riskModelDimensionStrategies?.filter((st) => st.dimensionStrategyId === item.strategyId) || [];
                return (
                  <StrategyResult
                    style={{ marginTop: index ? '15px' : 0 }}
                    key={item.strategyId + this.activeData}
                    dimensionStrategies={strategies}
                    basicParams={{
                      ...this.queryParams,
                    }}
                    hitDetail={this.activeData}
                    strategy={item}
                    hitTime={moment(this.params.record.createDate).format('yyyy-MM-DD HH:mm:ss')}
                  />
                );
              })}
            </div>
          </div>
        )}
      </Drawer>
    );
  },
});
export const openDimensionDetailDrawer = createPromiseDialog(TargetsTrends);
