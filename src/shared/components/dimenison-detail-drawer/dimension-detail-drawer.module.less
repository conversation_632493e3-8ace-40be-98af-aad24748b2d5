.container {
  .dimensionDetail {
    .dimensionDetailTitle {
      height: 50px;
      line-height: 50px;
      padding: 0 15px;
      font-size: 15px;
      font-weight: bold;
    }

    .dimensionDetailContent {
      max-height: calc(100vh - 165px);
      padding: 0 15px;
      overflow-y: auto;
    }

    :global {
      .ant-spin-spinning {
        height: calc(100vh - 165px)
      }
    }
  }

  .dimensionDetailSpining {
    width: 100%;
    height: calc(100vh - 105px);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
