import { DefineComponent, defineComponent } from 'vue';
import { mount, shallowMount } from '@vue/test-utils';

import CollapseNodes from '..';

describe('CollapseNodes', () => {
  let TestA: DefineComponent;
  let TestB: DefineComponent;

  beforeEach(() => {
    TestA = defineComponent({
      functional: true,
      render(h) {
        return h('span', {}, 'A');
      },
    });
    TestB = defineComponent({
      functional: true,
      render(h) {
        return h('span', {}, 'B');
      },
    });
  });

  test('props: limit', () => {
    const wrapper1 = shallowMount(CollapseNodes, {
      propsData: {
        limit: 1,
      },
      slots: {
        default: [TestA, TestB],
      },
    });
    expect(wrapper1).toMatchSnapshot();

    const wrapper2 = shallowMount(CollapseNodes, {
      propsData: {
        limit: 2,
      },
      slots: {
        default: [TestA, TestB],
      },
    });
    expect(wrapper2).toMatchSnapshot();
  });

  test('props: locale', async () => {
    const wrapper = mount(CollapseNodes, {
      propsData: {
        limit: 1,
        locale: {
          more: 'MORE',
          less: 'LESS',
        },
      },
      slots: {
        default: [TestA, TestB],
      },
    });
    expect(wrapper.text()).toContain('MORE');
    await wrapper.find('[data-testid="collapse-nodes-action"]').trigger('click');
    expect(wrapper.text()).toContain('LESS');
  });
});
