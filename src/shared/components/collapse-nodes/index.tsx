import { computed, defineComponent, PropType, ref } from 'vue';

import styles from './collapse-nodes.module.less';

const CollapseNodes = defineComponent({
  name: 'CollapseNodes',
  props: {
    /**
     * 默认显示条数
     */
    limit: {
      type: Number,
      default: 1,
    },

    /**
     * 文案
     */
    locale: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({
        more: '查看更多',
        less: '收起',
      }),
    },

    /**
     * 显示装饰线
     */
    dashed: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { slots }) {
    const children = slots.default?.() || [];

    // 是否展示"查看更多"
    const hasMore = ref(children.length > props.limit);

    // 显示更多
    const isCollapsed = ref(true);
    const toggleCollapsed = () => {
      isCollapsed.value = !isCollapsed.value;
    };

    // 渲染内容
    const content = computed(() => {
      if (!isCollapsed.value) {
        return children;
      }
      return children.slice(0, props.limit);
    });

    const collapsedText = computed(() => (isCollapsed.value ? props.locale.more : props.locale.less));

    return {
      content,
      hasMore,

      collapsedText,

      isCollapsed,
      toggleCollapsed,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div>{this.content}</div>
        <div v-show={this.hasMore && this.collapsedText}>
          <span data-testid="collapse-nodes-action" class={styles.action} onClick={this.toggleCollapsed}>
            <i v-show={this.dashed} class={styles.dash} />
            <span>{this.collapsedText}</span>
          </span>
        </div>
      </div>
    );
  },
});

export default CollapseNodes;
