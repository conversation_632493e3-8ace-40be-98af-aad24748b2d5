import { mount } from '@vue/test-utils';

import { RISK_LEVEL_LABEL_MAP, RiskLevel } from '@/shared/constants/risk-level-code-map.constant';

import RiskDimensionHeading from '../index';

describe('RiskDimensionHeading', () => {
  test('render', () => {
    const wrapper = mount(RiskDimensionHeading, {
      propsData: {
        level: RiskLevel.High,
        content: 'TEST',
        showTag: false,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: content', () => {
    const expected = '<em>TEST</em>';
    const wrapper = mount(RiskDimensionHeading, {
      propsData: {
        content: expected,
      },
    });
    expect(wrapper.html()).toContain(expected);
  });

  test('props: level', () => {
    const wrapper = mount(RiskDimensionHeading, {
      propsData: {
        level: RiskLevel.High,
        content: 'TEST',
        showTag: true,
      },
    });
    expect(wrapper.text()).toContain(RISK_LEVEL_LABEL_MAP[RiskLevel.High]);
  });

  test('props: showTag', () => {
    const wrapper = mount(RiskDimensionHeading, {
      propsData: {
        level: RiskLevel.High,
        content: 'TEST',
        showTag: false,
      },
    });
    expect(wrapper.text()).not.toContain(RISK_LEVEL_LABEL_MAP[RiskLevel.High]);
    expect(wrapper.text()).toContain('TEST');
  });
});
