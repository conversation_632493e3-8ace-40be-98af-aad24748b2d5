import { defineComponent } from 'vue';

import { RISK_LEVEL_CODE_MAP, RISK_LEVEL_LABEL_MAP } from '@/shared/constants/risk-level-code-map.constant';

import styles from './risk-dimension-heading.module.less';

const RiskDimensionHeading = defineComponent({
  functional: true,
  props: {
    content: {
      type: String,
      default: '',
    },
    level: {
      type: Number,
      default: 0,
    },
    showTag: {
      type: Boolean,
      default: true,
    },
  },
  render(h, { props }) {
    return (
      <div class={[[styles.container], styles[RISK_LEVEL_CODE_MAP[props.level]]]}>
        {props.showTag ? <i class={styles.tag}>{RISK_LEVEL_LABEL_MAP[props.level]}</i> : null}
        <span domPropsInnerHTML={props.content} />
      </div>
    );
  },
});

export default RiskDimensionHeading;
