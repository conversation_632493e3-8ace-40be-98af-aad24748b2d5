@import '@/styles/token.less';

@colors: blue, red, green, yellow, orange, red, violet, zaffer, scifi, cyan,
  gold, gray, white;

@background-colors: @qcc-color-blue-300, @qcc-color-red-300,
  @qcc-color-green-300, @qcc-color-yellow-300, @qcc-color-orange-300,
  @qcc-color-red-300, @qcc-color-violet-300, @qcc-color-zaffer-300,
  @qcc-color-scifi-300, @qcc-color-cyan-300, @qcc-color-gold-300,
  @qcc-color-gray-500, @qcc-color-white;

@text-colors: @qcc-color-blue-500, @qcc-color-red-500, @qcc-color-green-500,
  @qcc-color-yellow-500, @qcc-color-orange-500, @qcc-color-red-500,
  @qcc-color-violet-500, @qcc-color-zaffer-500, @qcc-color-scifi-500,
  @qcc-color-cyan-500, @qcc-color-gold-500, @qcc-color-black-500,
  @qcc-color-black-500;

@line-height: 16px;
@vertical-gap: 2px;
@horizontal-gap: 6px;
@special-corner: (@line-height + (@vertical-gap * 2)) / 2;

@line-height-sm: 12px;
@vertical-gap-sm: 1px;
@horizontal-gap-sm: 4px;
@special-corner-sm: (@line-height-sm + (@vertical-gap-sm * 2)) / 2;

.theme-style(@background, @text) {
  background-color: @background;
  border-color: @background;
  color: @text;

  &.clickable {
    cursor: pointer;

    &:hover,
    &:focus {
      background-color: darken(@background, 8%);
      border-color: darken(@background, 8%);
    }
  }

  &.flag {
    &::before,
    &::after {
      border-color: currentcolor;
    }
  }

  &.bubble {
    &::after {
      border-color: currentcolor;
    }

    &.default::after {
      border-color: @background;
    }

    &.filled::after {
      border-color: @text;
    }
  }

  &.filled {
    background-color: @text;
    border-color: @text;
    color: @qcc-color-white;

    &.clickable {
      &:hover,
      &:focus {
        background-color: darken(@text, 9%);
        border-color: darken(@text, 9%);

        &::before,
        &::after {
          border-color: darken(@text, 9%);
        }
      }
    }

    &.flag {
      &::before,
      &::after {
        border-color: @text;
      }
    }
  }

  &.outlined {
    &.clickable {
      &:hover,
      &:focus {
        border-color: currentcolor;
      }
    }
  }
}

.container {
  display: inline-flex;
  align-items: center;
  line-height: @line-height;
  padding: @vertical-gap @horizontal-gap;
  border-radius: @qcc-border-radius-base;
  border: 1px solid transparent;
  font-size: 12px;
  white-space: nowrap;
  // transition: background 0.15s ease-in-out, border-color 0.15s ease-in-out;
  .icon {
    font-size: 10px;
  }

  > *:not(:last-child) {
    margin-right: 3px;
  }
  each(@colors, {
    &.@{value} {
      .theme-style(extract(@background-colors, @index), extract(@text-colors, @index));
    }
  });
  // Size
  // -----------------------------------------------
  &.small {
    padding: @vertical-gap-sm @horizontal-gap-sm;
    line-height: @line-height-sm;
  }
  // Type
  // -----------------------------------------------
  &.ghost {
    border-color: currentcolor;
    background-color: transparent;
  }

  &.outlined {
    border-color: currentcolor;
  }
  // Shape
  // -----------------------------------------------
  &.rounded {
    border-radius: @special-corner;

    &.small {
      border-radius: @special-corner-sm;
    }
  }

  &.leaf {
    border-radius: @special-corner 0;

    &.small {
      border-radius: @special-corner-sm 0;
    }
  }

  &.pie {
    border-radius: @special-corner @special-corner @special-corner 0;

    &.small {
      border-radius: @special-corner-sm @special-corner-sm @special-corner-sm 0;
    }
  }

  &.bubble,
  &.flag {
    position: relative;

    &::before,
    &::after {
      position: absolute;
      width: 0;
      height: 0;
    }
  }

  &.flag {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    &::before,
    &::after {
      content: '';
      right: (@special-corner + 2px) * -1;
      border-style: solid;
    }

    &::before {
      top: -1px; // 外部有 border-top
      border-width: @special-corner + 1px @special-corner + 1px 0 0;
      border-color: currentcolor transparent transparent;
    }

    &::after {
      bottom: -1px; // 外部有 border-top
      border-width: @special-corner + 1px 0 0 @special-corner + 1px;
      border-color: transparent transparent transparent currentcolor !important;
    }

    &.small {
      &::before,
      &::after {
        right: (@special-corner-sm + 2px) * -1;
      }

      &::before {
        border-width: @special-corner-sm + 1px @special-corner-sm + 1px 0 0;
      }

      &::after {
        border-width: @special-corner-sm + 1px 0 0 @special-corner-sm + 1px;
      }
    }
  }

  &.bubble {
    &::after {
      content: '';
      // gap - borderWidth - arrowSize
      bottom: @vertical-gap * -1 - 1px - 2px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 4px solid transparent !important;
      border-right: 4px solid transparent !important;
      border-top: 4px solid currentcolor;
    }
  }

  &.transparent {
    background: transparent;
    border-color: transparent;
  }
}
