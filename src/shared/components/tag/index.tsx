import { defineComponent, type PropType } from 'vue';

import Icon from '../icon';
import styles from './tag.module.less';

export type TagColor = 'blue' | 'red' | 'green' | 'yellow' | 'orange' | 'gray' | 'violet' | 'zaffer' | 'scifi' | 'cyan' | 'gold' | 'white';

type TagIconType = 'up' | 'down' | 'left' | 'right' | 'close';
type TagType = 'default' | 'ghost' | 'outlined';
type TagShape = 'rectangle' | 'rounded' | 'leaf' | 'flag' | 'bubble';
type TagSize = 'middle' | 'small';

const mapping: Record<string, string> = {
  up: 'caret-up',
  down: 'caret-down',
  left: 'caret-left',
  right: 'caret-right',
  close: 'close',
};

const Tag = defineComponent({
  functional: true,
  props: {
    color: {
      type: String as PropType<TagColor>,
      default: 'blue',
    },
    icon: {
      type: [Boolean, String] as PropType<boolean | TagIconType>,
      default: false,
    },
    /**
     * ghost: 透明背景 |
     * outlined: 增加描边, 背景保持 |
     * filled: 更改填充背景, 前景色为白色
     */
    type: {
      type: String as PropType<TagType>,
      default: 'default',
    },
    /**
     * 支持 `middle` 和 `small`
     */
    size: {
      type: String as PropType<TagSize>,
      default: 'middle',
    },
    /**
     * rectangle: 长方形 |
     * rounded: 圆形 |
     * leaf: 叶形 |
     * pie: 介于圆形与叶形之间 |
     * flag: 旗形 (注意: flag 仅支持 filled 填充类型) |
     * bubble: 气泡
     */
    shape: {
      type: String as PropType<TagShape>,
      default: 'rectangle',
    },
    /**
     * 背景透明
     */
    transparent: {
      type: Boolean,
      default: false,
    },
  },
  /**
   * @slot prefix
   */
  render(h, ctx) {
    const { props, listeners } = ctx;
    const slots = ctx.slots();
    const handler = listeners.click;
    const handleClick = (ev: Event) => {
      if (typeof handler === 'function') {
        handler.call(null, ev);
      }
    };
    const classes = {
      [styles.container]: true,
      [styles[props.color]]: true,
      [styles[props.type]]: true,
      [styles[props.shape]]: true,
      [styles.clickable]: !!handler,
      [styles.small]: props.size === 'small',
      [styles.transparent]: props.transparent,
    };
    return (
      <div class={classes} onClick={handleClick}>
        {slots.prefix}
        <span>{slots.default}</span>
        {props.icon ? <Icon class={styles.icon} type={mapping[props.icon === true ? 'icon-a-xianduanyou' : props.icon]} /> : null}
      </div>
    );
  },
});

// Tag.emits = ['click'];

export default Tag;
