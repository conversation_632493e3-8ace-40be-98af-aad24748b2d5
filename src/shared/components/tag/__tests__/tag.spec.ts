import { mount } from '@vue/test-utils';

import Tag from '..';
import Icon from '../../icon';

describe('Tag Component', () => {
  test('renders correctly with default props', () => {
    const wrapper = mount(Tag);
    expect(wrapper.findComponent(Icon).exists()).toBe(false);
  });

  test('renders correctly with custom props', () => {
    const wrapper = mount(Tag, {
      propsData: {
        color: 'red',
        icon: 'up',
        type: 'ghost',
        size: 'small',
        shape: 'rounded',
        transparent: true,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('does not render icon when icon prop is true', () => {
    const wrapper = mount(Tag, {
      propsData: {
        icon: true,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('does not render icon when icon prop is false', () => {
    const wrapper = mount(Tag, {
      propsData: {
        icon: false,
      },
    });
    expect(wrapper.findComponent(Icon).exists()).toBe(false);
  });

  test('calls click handler when clicked', () => {
    const onClick = vi.fn();
    const wrapper = mount(Tag, {
      listeners: {
        click: onClick,
      },
    });
    wrapper.trigger('click');
    expect(onClick).toHaveBeenCalled();
  });

  test('does not call click handler when not clickable', () => {
    const onClick = vi.fn();
    const wrapper = mount(Tag);
    wrapper.trigger('click');
    expect(onClick).not.toHaveBeenCalled();
  });

  test('renders slot content correctly', () => {
    const wrapper = mount(Tag, {
      slots: {
        default: '<span>Default Slot</span>',
        prefix: '<span>Prefix Slot</span>',
      },
    });
    expect(wrapper.text()).toContain('Default Slot');
    expect(wrapper.find('span:first-child').text()).toBe('Prefix Slot');
  });
});
