@import '@/styles/token.less';

@exlarge-height: 50px;

.container {
  display: block;
  // 支持超大尺寸
  .control {
    display: flex;
    align-items: center;
  }

  &.exlarge {
    :global {
      .ant-select-selection__rendered {
        line-height: @exlarge-height;
      }

      .ant-input-lg {
        font-size: @qcc-text-lg;
        height: @exlarge-height;
        padding: 6px 16px;
      }

      .ant-btn-lg {
        height: @exlarge-height;
        padding-left: 25px;
        padding-right: 25px;
        font-size: @qcc-text-2xl;
        background-color: @qcc-color-orange-500;
        border-color: @qcc-color-orange-500;
      }
    }
  }
}
