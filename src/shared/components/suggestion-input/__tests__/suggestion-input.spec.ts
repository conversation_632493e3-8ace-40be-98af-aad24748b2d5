import { mount } from '@vue/test-utils';

import SuggestionInput from '..';

vi.mock('@/shared/services');

describe('SuggestionInput 组件单元测试', () => {
  it('正常输入选择', async () => {
    const mockRemote = vi.fn().mockResolvedValue([
      { label: '选项1', value: '1' },
      { label: '选项2', value: '2' },
    ]);
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: '',
        remote: mockRemote,
      },
    });

    await wrapper.vm.handleSearch('选项');
    expect(mockRemote).toHaveBeenCalled();
    expect(wrapper.vm.options).toEqual([
      { label: '选项1', value: '1' },
      { label: '选项2', value: '2' },
    ]);

    wrapper.vm.handleSelect('1');
    expect(wrapper.emitted().change).toBeTruthy();
    expect(wrapper.emitted().change?.[0]).toEqual(['1', { label: '选项1', value: '1' }]);
  });

  it('搜索字符长度小于2', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        remote: vi.fn(),
      },
    });

    await wrapper.vm.handleSearch('a');
    expect(wrapper.vm.options).toEqual([]);

    await wrapper.vm.handleSearch('b');
    expect(wrapper.vm.options).toEqual([]);
  });

  it('输入但未选择', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: '',
      },
    });

    wrapper.vm.handleChange('测试输入');
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input?.[0]).toEqual(['测试输入']);
  });

  it('处理远程请求错误', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        remote: vi.fn().mockRejectedValue(new Error('请求错误')),
      },
    });

    await wrapper.vm.handleSearch('错误');
    expect(wrapper.vm.options).toEqual([]);
  });

  it('选项更新后选择', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        remote: vi.fn().mockResolvedValue([{ label: '选项A', value: 'A' }]),
      },
    });

    await wrapper.vm.handleSearch('选项');
    expect(wrapper.vm.options).toEqual([{ label: '选项A', value: 'A' }]);

    wrapper.vm.handleSelect('A');
    expect(wrapper.emitted().change).toBeTruthy();
  });
});
