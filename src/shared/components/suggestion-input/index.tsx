import { AutoComplete, Button, Input, Select } from 'ant-design-vue';
import _ from 'lodash';
import { defineComponent, ref, type PropType, type Ref } from 'vue';

import styles from './suggestion-input.module.less';

// 确定精确录入
function isExact(options, currentValue) {
  return options.map(({ value }) => value).includes(currentValue);
}

type Option = {
  label: string;
  value: string | number;
  [key: string]: unknown;
};

const SuggestionInput = defineComponent({
  name: 'SuggestionInput',
  props: {
    value: {
      type: String,
      required: false,
    },
    /**
     * 尺寸
     */
    size: {
      type: String as PropType<'default' | 'small' | 'large' | 'exlarge'>,
      default: 'default',
    },
    /**
     * 提交按钮
     */
    enterButton: {
      type: [String, Boolean] as PropType<string | false>,
      default: false,
    },
    /**
     * 接口请求
     */
    remote: {
      type: Function as PropType<(keywords: string) => Promise<any>>,
    },
    /**
     * Placeholder
     */
    placeholder: {
      type: String,
      required: false,
    },
  },
  emits: ['change', 'input', 'search'],
  model: {
    prop: 'value',
    event: 'input',
  },
  setup(props, { emit }) {
    // 搜索选项
    const options: Ref<Option[]> = ref([]);

    const setOptions = (values: Option[]) => {
      options.value = values;
    };

    const handleSelect = (value: string) => {
      if (value.trim()) {
        emit('change', value, _.find(options.value, { value }));
      }
    };
    const handleChange = (value: string) => {
      if (!isExact(options.value, value)) {
        // console.error('请从结果中选择对应的值');
        emit('input', value);
      }
    };

    const request = props.remote
      ? _.debounce(props.remote, 300, {
          leading: true,
          trailing: true,
        })
      : null;

    const handleSearch = async (keywords: string) => {
      if (request && keywords.length >= 2) {
        try {
          const result = await request(keywords);
          setOptions(result);
        } catch (err) {
          setOptions([]);
        }
      } else {
        setOptions([]);
      }
    };

    const emitSearch = (value: string) => {
      emit('search', value);
    };

    return {
      options,
      setOptions,
      handleSearch,
      handleSelect,
      handleChange,
      emitSearch,
    };
  },
  render() {
    const { remote, ...props } = this.$props;
    const on = {
      change: this.handleChange,
      select: this.handleSelect,
      search: this.handleSearch,
      focus: () => {
        console.log('focus');
      },
    };
    const size = props.size === 'exlarge' ? 'large' : props.size;

    return (
      <AutoComplete
        {...{
          class: [styles.container, styles[props.size]],
          props: {
            ...props,
            size,
          },
          on,
        }}
      >
        <template slot="dataSource">
          {this.options.map((option: any) => (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          ))}
        </template>
        {props.enterButton ? (
          <Input.Search
            size={size}
            placeholder={props.placeholder}
            onSearch={this.emitSearch}
            enterButton={props.enterButton}
            loading={false}
          />
        ) : null}
      </AutoComplete>
    );
  },
});

export default SuggestionInput;
