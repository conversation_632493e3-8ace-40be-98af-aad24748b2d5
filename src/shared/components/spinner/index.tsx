import { defineComponent } from 'vue';
import type { PropType } from 'vue';

import DefaultIcon from '../assets/images/icon_default_spinner.gif';
import styles from './spinner.module.less';

const Spinner = defineComponent({
  functional: true,
  props: {
    size: {
      type: String as PropType<'default' | 'small' | 'large'>,
      default: 'default',
    },
    description: {
      type: String,
      // default: '正在加载中，请稍后…',
      required: false,
    },
  },
  render(h, { props }) {
    return (
      <div class={[styles.container, styles[props.size]]}>
        <img class={styles.icon} src={DefaultIcon} width={60} height={60} />
        {props.description ? <span class={styles.description}>{props.description}</span> : null}
      </div>
    );
  },
});

export default Spinner;
