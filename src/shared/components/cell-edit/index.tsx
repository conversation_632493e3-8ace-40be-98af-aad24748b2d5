import { defineComponent, ref, watch } from 'vue';
import { Input } from 'ant-design-vue';
import { debounce, isEqual, pick } from 'lodash';

import CompanySelect from '@/components/modal/supplier/company-select';
import { creditCodePattern, residentIdentityPattern } from '@/utils/validator';
import QIcon from '@/components/global/q-icon';

const rules = {
  personIdcard: {
    pattern: residentIdentityPattern,
    message: '请输入正确的身份证号码',
  },
  compCreditCode: {
    pattern: creditCodePattern,
    message: '请输入正确的统一社会信用代码',
  },
};

const CellEdit = defineComponent({
  name: 'CellEdit',
  props: {
    editType: {
      type: String,
      default: 'companyName',
    },
    rowData: Object,
  },
  setup(props, { emit }) {
    const data = ref<Record<string, any>>({ ...props.rowData, isEdit: false, key: 1 });
    const validateError = ref('');

    const modelValue = ref(data.value[props.editType]);

    const resetModelValue = () => {
      modelValue.value = data.value[props.editType];
      validateError.value = '';
    };

    const handleInputChange = () => {
      if (validateError.value) {
        data.value.isEdit = false;
        resetModelValue();
        return;
      }
      data.value.isEdit = false;
      // 输入框变化时更新数据
      if (modelValue.value && modelValue.value !== data.value[props.editType]) {
        emit('update', {
          ...data.value,
          itemId: (data.value as any).id,
          [props.editType]: modelValue.value,
        });
      }
      // 输入框为空时，还原原值
      if (!modelValue.value) {
        resetModelValue();
      }
    };

    const handleInputBlur = (e) => {
      if (e.relatedTarget?.classList?.contains('ant-input-clear-icon')) {
        // 点击清除按钮时，输入框会失焦
        return;
      }
      handleInputChange();
    };

    const handleSelectChange = (value, option) => {
      data.value.isEdit = false;
      modelValue.value = value;
      emit('select', {
        ...data.value,
        itemId: (data.value as any).id,
        companyName: option.value,
        companyId: option.KeyNo,
      });
    };

    const _validate = () => {
      const currentVal = rules[props.editType];
      if (!currentVal || !currentVal.pattern || !modelValue.value) return;
      const res = currentVal.pattern.test(modelValue.value);
      if (!res) {
        validateError.value = currentVal.message;
      } else {
        validateError.value = '';
      }
    };
    const validate = debounce(_validate, 100);

    watch(
      () => props.rowData,
      (newVal, oldVal) => {
        if (!isEqual(newVal, oldVal)) {
          data.value = { ...newVal, ...pick(data.value, ['isEdit', 'key']) };
        }
        resetModelValue();
      },
      { deep: true }
    );

    return {
      data,
      modelValue,
      handleInputChange,
      handleInputBlur,
      handleSelectChange,
      validateError,
      validate,
    };
  },
  render() {
    const renderEditInput = () => {
      if (this.editType === 'companyName') {
        return (
          <CompanySelect
            style="width: 100%; max-width: 600px;"
            placeholder="请选择企业名称"
            value={this.modelValue}
            showWarning={true}
            allowClear={false}
            autoFocus={true}
            onChange={this.handleSelectChange}
            onBlur={() => {
              this.data.isEdit = false;
            }}
          />
        );
      }
      return (
        this.$slots.default ?? (
          <div>
            <Input
              style="max-width: 600px;"
              ref={'inputRef'}
              v-model={this.modelValue}
              allowClear
              maxLength={50}
              onBlur={this.handleInputBlur}
              onPressEnter={this.handleInputChange}
              onChange={() => {
                this.modelValue = this.modelValue?.trim();
                if (!this.modelValue) {
                  this.$nextTick(() => {
                    (this.$refs.inputRef as any)?.focus();
                  });
                }
                this.validate();
              }}
            />
            <em style="margin-top: 4px;font-size: 12px;line-height: 18px;" v-show={this.validateError}>
              {this.validateError}
            </em>
          </div>
        )
      );
    };
    return (
      <div>
        {this.data.isEdit ? (
          renderEditInput()
        ) : (
          <div>
            <div>
              {(this.data as any).companyId && this.editType === 'companyName' ? (
                <a
                  style={{ paddingRight: '4px' }}
                  target="_blank"
                  href={`/embed/companyDetail?keyNo=${(this.data as any).companyId}&title=${this.modelValue}`}
                >
                  {this.modelValue}
                </a>
              ) : (
                <span style={{ paddingRight: '4px' }}>{this.modelValue || '-'}</span>
              )}
              <QIcon
                class="wrong-edit-btn"
                type="icon-a-bianjigenjin1x"
                style="color: #bbb"
                onClick={() => {
                  this.data.isEdit = true;
                  this.$nextTick(() => {
                    (this.$refs.inputRef as any)?.focus();
                  });
                }}
              ></QIcon>
            </div>
            <em v-show={this.editType === (this.data as any).parsedItem?.errorCode && (this.data as any).flag === 0}>
              {(this.data as any).parsedItem?.errorMsg || '匹配失败'}
            </em>
          </div>
        )}
      </div>
    );
  },
});
export default CellEdit;
