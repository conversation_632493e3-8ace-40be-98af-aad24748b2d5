import { mount } from '@vue/test-utils';
import CellEdit from '../index';

describe('CellEdit 组件', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  it('默认情况下渲染文本', () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, personIdcard: '11010519491231002X' },
        editType: 'personIdcard',
      },
    });
    expect(wrapper.html()).toContain('11010519491231002X');
  });

  it('editType 为 companyName 时渲染超链接', () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, companyName: '示例公司', companyId: '123456' },
        editType: 'companyName',
      },
    });
    expect(wrapper.html()).toContain('</a>');
    expect(wrapper.html()).toContain('示例公司');
  });

  it('isEdit 为 true 时正确渲染输入框', async () => {
    const rowData = { id: 1, companyName: '示例公司' };
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData,
        editType: 'companyName',
      },
    });
    await wrapper.setData({
      data: {
        ...rowData,
        isEdit: true,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'CompanySelect' }).exists()).toBe(true);
  });

  it('身份证号码输入正确时正确验证', async () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, personIdcard: '' },
        editType: 'personIdcard',
      },
    });
    await wrapper.setData({ modelValue: '11010519491231002X' });
    wrapper.vm.validate();
    await vi.advanceTimersByTimeAsync(150);
    expect(wrapper.vm.validateError).toBe('');
  });

  it('身份证号码输入错误时正确验证', async () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, personIdcard: '' },
        editType: 'personIdcard',
      },
    });
    await wrapper.setData({ modelValue: '1234567890123456789' });
    wrapper.vm.validate();
    await vi.advanceTimersByTimeAsync(150);
    expect(wrapper.vm.validateError).toBe('请输入正确的身份证号码');
  });

  it('统一社会信用代码输入正确时正确验证', async () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, compCreditCode: '' },
        editType: 'compCreditCode',
      },
    });
    await wrapper.setData({ modelValue: '123456789012345678' });
    wrapper.vm.validate();
    await vi.advanceTimersByTimeAsync(150);
    expect(wrapper.vm.validateError).toBe('');
  });

  it('统一社会信用代码输入错误时正确验证', async () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, compCreditCode: '' },
        editType: 'compCreditCode',
      },
    });
    await wrapper.setData({ modelValue: '12345678901234567' });
    wrapper.vm.validate();
    await vi.advanceTimersByTimeAsync(150);
    expect(wrapper.vm.validateError).toBe('请输入正确的统一社会信用代码');
  });

  it('输入校验失败后还原原值', async () => {
    const rowData = { id: 1, personIdcard: '11010519491231002X' };
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData,
        editType: 'personIdcard',
      },
    });
    await wrapper.setData({ data: { ...rowData, isEdit: true }, modelValue: '11010519491231002' });
    const inputWrapper = wrapper.findComponent({ name: 'AInput' });
    inputWrapper.vm.$emit('change', {});
    await vi.advanceTimersByTimeAsync(150);
    expect(wrapper.vm.validateError).toBe('请输入正确的身份证号码');
    inputWrapper.vm.$emit('pressEnter', {});
    expect(wrapper.vm.modelValue).toBe('11010519491231002X');
  });

  it('输入框正确时更新数据', async () => {
    const rowData = { id: 1, personIdcard: '11010519491231002X' };
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: rowData,
        editType: 'personIdcard',
      },
    });
    await wrapper.setData({
      data: {
        ...rowData,
        isEdit: true,
      },
      modelValue: '11010519491231002Y',
    });
    await wrapper.vm.$nextTick();
    const inputWrapper = wrapper.findComponent({ name: 'AInput' });
    inputWrapper.vm.$emit('pressEnter', {});
    expect(wrapper.emitted('update')).toBeTruthy();
    expect(wrapper.emitted('update')).toEqual([
      [
        {
          id: 1,
          isEdit: false,
          itemId: 1,
          key: 1,
          personIdcard: '11010519491231002Y',
        },
      ],
    ]);
  });

  it('输入框变化为空时还原原值', async () => {
    const rowData = { id: 1, personIdcard: '11010519491231002X' };
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData,
        editType: 'personIdcard',
      },
    });
    await wrapper.setData({ data: { ...rowData, isEdit: true }, modelValue: '' });
    const inputWrapper = wrapper.findComponent({ name: 'AInput' });
    inputWrapper.vm.$emit('blur', {});
    expect(wrapper.vm.modelValue).toBe('11010519491231002X');
  });

  it('企业名称选择变化时正确触发选择事件', async () => {
    const rowData = { id: 1, companyName: '' };
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData,
        editType: 'companyName',
      },
    });
    await wrapper.setData({
      data: { ...rowData, isEdit: true },
    });
    const selectWrapper = wrapper.findComponent({ name: 'CompanySelect' });
    selectWrapper.vm.$emit('change', '选择的公司', { value: '选择的公司', KeyNo: '123456' });
    expect(wrapper.vm.modelValue).toBe('选择的公司');
    expect(wrapper.vm.data.isEdit).toBe(false);
    expect(wrapper.emitted('select')).toBeTruthy();
    expect(wrapper.emitted('select')).toEqual([
      [
        {
          companyId: '123456',
          companyName: '选择的公司',
          id: 1,
          isEdit: false,
          itemId: 1,
          key: 1,
        },
      ],
    ]);
  });

  it('rowData 更新时正确重置 modelValue', async () => {
    const wrapper = mount(CellEdit, {
      propsData: {
        rowData: { id: 1, personIdcard: '11010519491231002X' },
        editType: 'personIdcard',
      },
    });
    await wrapper.setData({ modelValue: '11010519491231002Y', validateError: '错误信息' });
    await wrapper.setProps({ rowData: { id: 1, personIdcard: '11010519491231002Z' } });
    expect(wrapper.vm.modelValue).toBe('11010519491231002Z');
    expect(wrapper.vm.validateError).toBe('');
  });
});
