import { defineComponent } from 'vue';

import styles from './risk-action.module.less';
import QIcon from '@/components/global/q-icon';

const RiskAction = defineComponent({
  functional: true,
  props: {
    icon: {
      type: String,
      required: false,
    },
    iconStyle: {
      type: [String, Object],
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    spin: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: 'default',
    },
    component: {
      type: [Object, Boolean],
      default: false,
    },
    tag: {
      type: String,
      default: 'div',
    },
    // data-testid
    tid: {
      type: String,
      default: '',
    },
  },
  render(h, { props, children, data }) {
    return h(
      props.tag,
      {
        ...data,
        class: {
          [styles.container]: true,
          [styles.loading]: props.loading,
          [styles.container]: true,
          [styles.withIcon]: !!props.icon,
          [styles[props.theme]]: true,
          'risk-action-wrapper': true,
        },
      },
      [
        <span class={styles.icon} style={props.iconStyle} v-show={props.icon || props.component} data-testid={props.tid}>
          <QIcon component={props.component} type={props.icon} spin={props.spin && props.loading} />
        </span>,
        <span>{children}</span>,
      ]
    );
  },
});

export default RiskAction;
