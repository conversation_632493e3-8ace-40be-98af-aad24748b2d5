import { shallowMount } from '@vue/test-utils';

import RiskAction from '..';

describe('RiskAction', () => {
  test('render', () => {
    const wrapper = shallowMount(RiskAction, {
      propsData: {
        tag: 'div',
        icon: 'icon-a-wenjianxiazai<PERSON>',
      },
      slots: {
        default: 'CHILDREN',
      },
    });
    expect(wrapper.html()).toContain('CHILDREN');
    expect(wrapper).toMatchSnapshot();
  });

  test('render: no icon', () => {
    const wrapper = shallowMount(RiskAction, {
      propsData: {
        tag: 'div',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
