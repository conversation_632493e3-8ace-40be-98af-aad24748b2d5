@import '@/styles/token.less';

.container {
  display: flex;
  align-items: center;
  line-height: 22px;
  font-size: 14px;
  padding: 0 5px;
  border-radius: 2px;
  cursor: pointer;
  color: @qcc-color-black-600;

  &[disabled] {
    color: #bbb;
    cursor: not-allowed;

    &:hover {
      background-color: inherit !important;
      color: #bbb !important;

      .icon {
        color: #999 !important;
      }
    }
  }

  .icon {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2px;
    font-size: 14px;
    color: @qcc-color-blue-500;
    border-radius: 2px;
  }

  &.loading {
    cursor: default;
    pointer-events: none;
  }

  &.with-icon {
    padding-left: 1px;
  }

  &.default {
    &:hover {
      background-color: #e2f1fd;
      color: @qcc-color-blue-500;
    }
  }

  &.text {
    &:hover {
      background-color: #e2f1fd;
      color: @qcc-color-blue-500;
    }

    .icon {
      color: #999;
    }

    &:hover {
      .icon {
        color: @qcc-color-blue-500;
      }
    }
  }
  // 仅有 icon 高亮
  &.slight {
    &:hover {
      .icon {
        background-color: #e2f1fd;
      }
    }
  }
}
