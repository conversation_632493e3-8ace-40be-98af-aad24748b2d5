import { shallowMount } from '@vue/test-utils';

import BatchUploadFrame from '@/shared/components/batch-upload-frame';

describe('BatchUploadIframe', () => {
  it('render', () => {
    const wrapper = shallowMount(BatchUploadFrame);
    expect(wrapper).toMatchSnapshot();
  });

  it('props: title', () => {
    const expected = {
      title: 'TITLE',
    };
    const wrapper = shallowMount(BatchUploadFrame, {
      propsData: {
        title: expected.title,
      },
    });
    expect(wrapper.text()).toMatch(expected.title);
  });
});
