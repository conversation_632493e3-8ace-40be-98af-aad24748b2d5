import { defineComponent } from 'vue';

import styles from './batch-upload-frame.module.less';

const BatchUploadFrame = defineComponent({
  functional: true,
  props: {
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      required: false,
    },
    innerClass: {
      type: String,
      required: false,
    },
  },
  render(h, { props, slots }) {
    const { default: children, description } = slots();
    return (
      <div
        class={[styles.container, props.innerClass]}
        style={{
          flex: props.width ? `0 0 ${props.width}` : 1,
        }}
      >
        <div class={styles.box}>
          <div class={styles.title} v-show={props.title}>
            {props.title}
          </div>
          {children}
        </div>
        <div class={styles.description}>{description}</div>
      </div>
    );
  },
});

export default BatchUploadFrame;
