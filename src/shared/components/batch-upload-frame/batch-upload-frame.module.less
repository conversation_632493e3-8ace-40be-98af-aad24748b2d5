@import '@/styles/token';

.container {
  background: @qcc-color-blue-200;
  border-radius: @qcc-border-radius-middle;
  padding: 15px;

  .box {
    position: relative;
    background-color: #fff;
  }

  .title {
    z-index: 1;
    position: absolute;
    left: -5px;
    top: 0;
    background: url('./images/bg_flag.png') no-repeat 0 0;
    background-size: contain;
    width: 70px;
    height: 27px;
    color: #fff;
    font-size: @qcc-text-xs;
    text-indent: 8px;
    line-height: 25px;
    font-weight: @qcc-font-bold;
  }

  .description {
    color: @qcc-color-black-300;
    line-height: 20px;
    font-size: 12px;

    > * {
      margin-top: 8px;
    }

    li {
      position: relative;
      padding-left: 11px;

      a + span {
        margin-left: 5px;
      }

      em {
        color: @qcc-color-red-500;
      }

      &::before {
        content: '';
        width: 6px;
        height: 6px;
        display: inline-block;
        background: @qcc-color-black-200;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }
    }
  }
}
