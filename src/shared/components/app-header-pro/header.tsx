import { defineComponent } from 'vue';

import styles from './app-header-pro.module.less';

const AppHeader = defineComponent({
  functional: true,
  props: {
    translucent: {
      type: Boolean,
      default: false,
    },
  },
  render(h, ctx) {
    const slots = ctx.slots();
    const props = ctx.props;
    return (
      <div class={{ [styles.container]: true, [styles.translucent]: props.translucent }}>
        <div class={styles.wing}>
          {slots.logo}
          {slots.navigation}
        </div>
        <div class={styles.wing}>
          <div class={styles.extra}>{slots.extra}</div>
          <div class={styles.menu}>{slots.quick}</div>
          <div class={styles.user}>{slots.user}</div>
        </div>
      </div>
    );
  },
});

export default AppHeader;
