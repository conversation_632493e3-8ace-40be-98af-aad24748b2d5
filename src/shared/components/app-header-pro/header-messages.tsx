import { defineComponent, PropType } from 'vue';

import { useI18n } from '@/shared/composables/use-i18n';

import Empty from '../empty';
import styles from './app-header-pro.module.less';

type MessageType = {
  id: number;
  msgType: 1 | 2; // 1-任务提醒; 2-下载提醒;
  title: string;
  content: string;
  createDate: string;
  updateDate: string;
};

const MessagesBody = defineComponent({
  name: 'MessagesBody',
  props: {
    messages: {
      type: Array as PropType<MessageType[]>,
      default: () => [],
    },
    rowKey: {
      type: String,
      default: 'id',
    },
  },
  render() {
    const { messages, rowKey } = this;
    if (!this.messages.length) {
      return (
        <div class={styles.empty}>
          <Empty type="search" description="暂无未读提醒" />
        </div>
      );
    }

    return (
      <div class={styles.list}>
        {messages.map((message) => {
          return (
            <div class={styles.item} key={message[rowKey]}>
              <div class={styles.inner}>{this.$scopedSlots?.message?.(message)}</div>
            </div>
          );
        })}
      </div>
    );
  },
});

const Messages = defineComponent({
  functional: true,
  props: {
    width: {
      type: String,
      default: '500px',
    },
    messages: {
      type: Array as PropType<MessageType[]>,
      default: () => [],
    },
    count: {
      type: Number,
      default: 0,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
  },
  render(h, { props, slots, scopedSlots, parent }) {
    const { footer, title, extra } = slots();
    const { t } = useI18n();
    return (
      <div
        class={styles.messages}
        style={{
          width: props.width,
        }}
      >
        <header class={styles.header}>
          <div class={styles.title}>
            {title || (
              <div>
                {t('Message Center')}
                <span v-show={props.count > 0}>
                  （<em>{props.count}</em> {'条未读'}）
                </span>
              </div>
            )}
          </div>
          <div class={styles.extra}>{extra}</div>
        </header>
        <div class={styles.body}>
          <MessagesBody messages={props.messages} rowKey={props.rowKey} {...{ scopedSlots }} />
        </div>
        <div class={styles.footer}>{footer}</div>
      </div>
    );
  },
});

export default Messages;
