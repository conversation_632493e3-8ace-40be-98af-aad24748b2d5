export type ItemKey = string | number;

export type NavigationItem = {
  key: ItemKey;
  label: string;
  link?: string;
  external?: boolean; // 是否为外部链接
  exact?: boolean; // 是否为绝对匹配 (router-link)
};

export type NavigationItems = NavigationItem[];

type UserMenuDivider = null;
type UserMenuItem = NavigationItem & {
  icon?: string;
  permission?: unknown;
};

export type UserMenuItems = Array<UserMenuDivider | UserMenuItem>;
