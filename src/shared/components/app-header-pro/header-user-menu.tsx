import { Button, Dropdown, Menu, Icon } from 'ant-design-vue';
import { defineComponent } from 'vue';
import { isFunction, isString } from 'lodash';
import type { PropType } from 'vue';

import { getCurrentUserPermission, hasPermission } from '@/shared/composables/use-permission';

import type { UserMenuItems } from './types';
import { createFunctionalEventEmitter } from '../_helpers/functional-component';
import QIcon from '../icon';
import DefaultUserAvatar from './assets/icon_pro_user_avatar.svg';
import LinkItem from './header-link-item';
import styles from './app-header-pro.module.less';

const UserMenu = defineComponent({
  functional: true,
  props: {
    user: {
      type: Object as PropType<{ [key: string]: unknown } | null>,
      required: false,
    },
    items: {
      type: Array as PropType<UserMenuItems>,
      default: () => [],
    },
    loginPath: {
      type: String,
      required: false,
    },
    logoutPath: {
      type: String,
      required: false,
    },
  },
  render(h, { props, children, listeners }) {
    const emitters = createFunctionalEventEmitter(listeners);
    const handleClick = (data) => {
      const menuItem = props.items.find((el) => el?.key === data.key);
      emitters('change')({ ...data, menuItem });
    };

    let avatar = props.user?.faceimg;
    if (!avatar || avatar === '0') {
      // 未上传头像可能出现 faceimg 为 '0' 的情况
      avatar = DefaultUserAvatar;
    }

    const filterItems = props.items.filter((item) => {
      if (!item) return true;
      if (isFunction(item?.permission)) {
        const permission = getCurrentUserPermission();
        return item.permission(permission);
      }
      if (item?.permission) {
        return hasPermission(item.permission as number[] | number);
      }
      return true;
    });

    return (
      <nav class={styles.userMenu}>
        {!props.user ? (
          <Button type="link" href={props.loginPath}>
            登录/注册
          </Button>
        ) : (
          <Dropdown overlayClassName={styles.userMenuDropdown}>
            <Menu slot="overlay" onClick={handleClick}>
              {children ? <Menu.Item class={styles.userMenuInfo}>{children}</Menu.Item> : null}

              {filterItems.map((item: any, index: number) =>
                item === null ? (
                  <Menu.Divider class={styles.userMenuDivider} key={`divider-${index}`} />
                ) : (
                  <Menu.Item class={styles.userMenuItem} key={item.key}>
                    <LinkItem class={styles.item} active={false} href={item.link} external={item.external}>
                      {item.iconType === 'qicon' ? <q-icon class={styles.icon} type={item.icon}></q-icon> : null}
                      {item.icon && isString(item.icon) && item.iconType !== 'qicon' ? <Icon class={styles.icon} type={item.icon} /> : null}
                      <span>{item.label}</span>
                    </LinkItem>
                  </Menu.Item>
                )
              )}

              <Menu.Divider class={styles.userMenuDivider} />

              <Menu.Item class={styles.userMenuItem} style={{ marginBottom: '0px', paddingBottom: '15px' }} key="logout">
                <LinkItem class={[styles.item, styles.logout]} active={false} href={props.logoutPath} external={false} raw>
                  <q-icon type="icon-anquantuichu" class={[styles.icon, styles.iconLogout]} />
                  <span>安全退出</span>
                </LinkItem>
              </Menu.Item>
            </Menu>
            <div class={styles.info}>
              <div class={styles.avatar}>
                <span class={styles.mask}>
                  <QIcon type="icon-a-gengduohengpai" />
                </span>
                <img class={styles.img} src={avatar as string} />
              </div>
            </div>
          </Dropdown>
        )}
      </nav>
    );
  },
});

export default UserMenu;
