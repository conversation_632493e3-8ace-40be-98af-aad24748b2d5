import { defineComponent } from 'vue';

import styles from './app-header-pro.module.less';

const Logo = defineComponent({
  functional: true,
  props: {
    src: {
      type: String,
      required: false,
    },
    href: {
      type: String,
      default: '/',
    },
  },
  render(h, { props, slots }) {
    const { default: children } = slots();
    const goHome = (ev) => {
      ev.preventDefault();
      ev.stopPropagation();

      if (typeof props.href === 'string') {
        window.location.href = props.href;
      }

      if (props.href === null) {
        window.close();
      }
    };
    return (
      <a
        class={{
          [styles.brand]: true,
          [styles.disabled]: props.href === undefined,
        }}
        onClick={goHome}
      >
        {props.src ? <img slot="logo" src={props.src} height="22" /> : children}
      </a>
    );
  },
});

export default Logo;
