import { computed, defineComponent } from 'vue';

import { useStore } from '@/store';
import QIcon from '@/components/global/q-icon';

import styles from './org-logo.module.less';

const OrgLogo = defineComponent({
  name: 'OrgLogo',
  props: {
    disable: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const store = useStore();
    const profile = computed(() => store.getters['user/profile']);
    const orgName = computed(() => profile.value.orgName);

    return {
      orgName,
    };
  },
  render() {
    if (this.disable || !this.orgName) {
      return null;
    }
    return (
      <div class={styles.orgLogo}>
        <QIcon class={styles.logo} type="icon-icon_zuzhimingc"></QIcon>
        <span title={this.orgName} class={styles.orgName}>
          {this.orgName}
        </span>
      </div>
    );
  },
});

export default OrgLogo;
