import { defineComponent } from 'vue';
import type { PropType } from 'vue';

import type { ItemKey } from './types';

function isURL(urlLike: string) {
  const pattern = /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_+.~#?&/=]*)$/;
  return pattern.test(urlLike);
}

const LinkItem = defineComponent({
  functional: true,
  props: {
    activeKey: {
      type: [String, Number] as PropType<ItemKey>,
      required: false,
    },
    activeClass: {
      type: String,
      required: false,
    },
    href: {
      type: String,
      default: '',
    },
    external: {
      type: Boolean,
      default: false,
    },
    exact: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否跳过 vue-router 解析
     */
    raw: {
      type: Boolean,
      default: false,
    },
  },
  render(h, { props, listeners, children, data }) {
    const handleClick = (event: Event) => {
      if (typeof listeners.click === 'function') {
        listeners.click.call(null, event);
      }
    };

    if (!props.href) {
      return (
        <div class={data.class}>
          <span onClick={handleClick}>{children}</span>
        </div>
      );
    }

    if (props.raw || isURL(props.href)) {
      return (
        <div class={data.class}>
          <a href={props.href} target={props.external ? '_blank' : '_self'} onClick={handleClick}>
            {children}
          </a>
        </div>
      );
    }

    return (
      <router-link
        custom
        to={props.href}
        scopedSlots={{
          default: ({ href, navigate, isExactActive, isActive }) => {
            return (
              <div
                class={{
                  [data.class]: true,
                  [props.activeClass || '']: props.exact ? isExactActive : isActive,
                }}
              >
                <a href={href} target={props.external ? '_blank' : '_self'} onClick={navigate}>
                  {children}
                </a>
              </div>
            );
          },
        }}
      />
    );
  },
});

export default LinkItem;
