@import '@/styles/token';

@header-height: 52px;

.container {
  min-height: @header-height;
  padding: 0 15px;
  background: @qcc-color-white;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .wing {
    display: flex;
    align-items: center;

    > div:not(:last-child) {
      margin-right: 20px;
    }
  }

  .brand {
    //margin-right: 60px !important;
    color: #222;
    display: flex;

    &.disabled {
      pointer-events: none;
    }

    &:hover {
      color: currentcolor !important;
    }
  }

  .nav {
    color: @qcc-color-black-500;
    font-size: @qcc-text-lg;

    .item {
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      transition: color 0.15s ease;

      &:not(:first-child) {
        margin-left: 30px;
      }

      &.active,
      a:hover {
        color: @qcc-color-blue-500;
      }

      a {
        color: currentcolor;
      }

      .icon {
        font-size: @qcc-text-xs;
      }

      // .iconLogout {
      //   color: #bbb;
      // }
    }
  }
}

/**
 * 任务栏透明
 */
.translucent {
  background: transparent;
  color: #FFF;

  .quick-menu li > *,
  .nav {
    color: #FFF;
  }
}

.user-menu {
  .info {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: @header-height;
  }

  > *:not(:first-child) {
    margin-left: 8px;
  }

  .avatar {
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;

    &:hover {
      .mask {
        opacity: 1;
      }
    }

    .img {
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      position: absolute;
      z-index: 0;
      width: 30px;
      height: 30px;
    }

    .mask {
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      border-radius: 999px;
      overflow: hidden;
      transition: opacity 0.3s ease;
      color: #fff;
      font-size: 14px;
      position: absolute;
      background: rgba(0, 0, 0, 0.4);
      text-align: center;
      opacity: 0;
      z-index: 1;
    }
  }
}

.user-menu-dropdown {
  :global {
    .ant-dropdown-menu {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }

    .ant-dropdown-menu-item-active {
      background-color: #fff;
    }
  }
}

.user-menu-item {
  min-width: 250px;
  line-height: 22px;
  padding: 0 15px;
  margin-bottom: 15px;
  color: @qcc-color-black-600;
  font-size: 14px;

  .icon {
    margin-right: 10px;
    color: @qcc-color-blue-500;
  }

  .item {
    a {
      display: block;
      color: currentcolor;

      &:hover {
        color: @qcc-color-blue-500;
      }
    }
  }

  .logout {
    a {
      color: #999;

      .icon {
        color: @qcc-color-black-200;
      }

      &:hover {
        color: @qcc-color-blue-500;

        .icon {
          color: currentcolor;
        }
      }
    }
  }
}

.user-menu-info {
  padding: 15px;
  margin-bottom: 15px;
  background: @qcc-color-blue-200 !important;
}

.user-menu-divider {
  margin: 0 15px;
  margin-bottom: 15px;
}

.quick-menu {
  height: @header-height;
  display: flex;
  align-items: center;

  > * + * {
    margin-left: 20px;
  }

  ul {
    height: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  li {
    display: flex;
    align-items: center;
    height: 100%;

    > * {
      color: @qcc-color-black-500;
      line-height: 1;
      min-width: 28px;
      font-size: 18px;
      padding: 5px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.15s ease, color 0.15s ease;
      display: flex;
      align-items: center;

      > span {
        font-size: 14px;
      }

      &:hover,
      &:focus,
      &.active {
        color: @qcc-color-blue-500;
        background-color: @qcc-color-blue-200;

        > span {
          color: currentcolor;
        }
      }
    }

    :global(.ant-badge-count) {
      line-height: 16px;
      min-width: 16px;
      height: 16px;
      padding: 0 2px;
      top: 3px;
      right: 3px;
      box-shadow: 0 0 0 2px @qcc-color-white;
    }
  }
}

.messages {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background: @qcc-color-white;
  border-radius: 2px;

  .body {
    max-height: 397px;
    overflow-y: auto;
  }

  .header,
  .footer {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 0 15px;
    border-color: @qcc-color-gray-500;
  }

  .footer {
    justify-content: center;
    border-top-width: 1px;
    border-top-style: solid;
  }

  .header {
    justify-content: space-between;
    border-bottom-width: 1px;
    border-bottom-style: solid;

    .title {
      font-size: 15px;
      color: @qcc-color-black-600;
      font-weight: @qcc-font-bold;

      span {
        color: @qcc-color-black-300;
        font-weight: @qcc-font-normal;
      }

      em {
        color: @qcc-color-red-500;
      }
    }
  }

  .empty {
    padding: 45px 0;
  }

  .item {
    .inner {
      padding: 10px 15px;
    }

    &:hover {
      background: #f5f9ff;
    }

    &:not(:last-child) {
      border-bottom: 1px solid @qcc-color-gray-500;
    }
  }
}
