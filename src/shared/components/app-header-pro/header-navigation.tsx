import { Icon } from 'ant-design-vue';
import { defineComponent, type PropType } from 'vue';

import type { ItemKey, NavigationItems } from './types';
import styles from './app-header-pro.module.less';
import LinkItem from './header-link-item';

const Navigation = defineComponent({
  functional: true,
  props: {
    value: {
      type: [String, Number] as PropType<ItemKey>,
      required: false,
    },
    items: {
      type: Array as PropType<NavigationItems>,
      default: () => [],
    },
    max: {
      type: Number,
      default: 4,
    },
  },
  render(h, { props }) {
    return (
      <nav class={styles.nav}>
        {props.items.map((item) => {
          return (
            <LinkItem
              key={item.key}
              href={item.link}
              exact={item.exact}
              external={item.external}
              class={styles.item}
              activeKey={props.value}
              activeClass={styles.active}
            >
              {item.label}
            </LinkItem>
          );
        })}
        <div class={styles.item} v-show={props.items.length > props.max}>
          <span>更多</span>
          <Icon class={styles.icon} type="right" />
        </div>
      </nav>
    );
  },
});

export default Navigation;
