import Vue, { defineComponent } from 'vue';
import { <PERSON>ton, Modal } from 'ant-design-vue';
import AntPortalDirective from 'ant-design-vue/es/_util/portalDirective';
import type { PropType } from 'vue';

import { createFunctionalEventEmitter } from '../_helpers/functional-component';
import DefaultBrand from '../assets/images/icon_default_brand.svg';
import Icon from '../icon';

// NOTE: 弹窗类组件需要 `ant-portal` 指令支持
// if (isVue2 && Vue2) {
//   Vue2.use(AntPortalDirective);
// }
Vue.use(AntPortalDirective);

type ModalConfirmType = 'primary' | 'default' | 'danger';

const BaseModal = defineComponent({
  functional: true,
  props: {
    /**
     * 弹窗标题
     */
    title: {
      type: String,
      required: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [Number, String] as PropType<string | number>,
      default: 600,
    },
    /**
     * 确定按钮文案
     */
    okText: {
      type: String,
      default: '确定',
    },
    /**
     * 取消按钮文案
     */
    cancelText: {
      type: String,
      default: '取消',
    },
    /**
     * 确定按钮加载态
     */
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    /**
     * 确定按钮加载态
     */
    confirmType: {
      type: String as PropType<ModalConfirmType>,
      default: 'primary',
    },
    /**
     * 是否显示默认 "确定"，"取消" 按钮
     */
    hasFooter: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示蒙板
     */
    hasMask: {
      type: Boolean,
      default: true,
    },
    /**
     * 点击蒙层时是否关闭
     */
    maskClosable: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示 Logo (默认为企查查)
     */
    brand: {
      type: Boolean,
      default: false,
    },
    /**
     * 脚注
     */
    footnote: {
      type: String,
      required: false,
    },
    /**
     * Modal body 样式
     */
    bodyStyle: {
      type: Object as PropType<Record<string, unknown>>,
      required: false,
    },
    /**
     * 指定 Modal 挂载的 HTML 节点
     */
    getContainer: {
      type: Function,
      required: false,
    },
    /**
     * 是否居中
     */
    centered: {
      type: Boolean,
      required: false,
    },
    /**
     * 关闭后是否销毁
     */
    destroyOnClose: {
      type: Boolean,
      default: true,
    },
  },
  /**
   * @slot default
   */

  /**
   * 操作栏脚注插槽
   * @slot footnote
   */

  /**
   * 自定义操作栏
   * @slot footer
   */
  render(h, ctx) {
    const { props, listeners } = ctx;
    const slots = ctx.slots();
    const emitters = createFunctionalEventEmitter(listeners);
    const handleChange = emitters('change');
    const handleClose = () => {
      handleChange(false);
      emitters('close');
    };
    const handleCancel = () => {
      handleChange(false);
      emitters('cancel');
    };
    const handleFullyClose = emitters('fullyClose');
    const handleOk = emitters('ok');
    return (
      <Modal
        visible={props.visible}
        footer={props.hasFooter ? undefined : null}
        width={props.width}
        mask={props.hasMask}
        maskClosable={props.maskClosable}
        bodyStyle={props.bodyStyle}
        centered={props.centered}
        getContainer={props.getContainer}
        destroyOnClose={props.destroyOnClose}
        onCancel={handleClose}
        afterClose={handleFullyClose}
      >
        {slots?.title || props.title ? (
          <template slot="title">
            <div class="ant-modal-header-inner">
              <div>{slots?.title || props.title}</div>
              <div v-show={props.brand}>
                <img src={DefaultBrand} height="24" />
              </div>
            </div>
          </template>
        ) : null}
        {/* Modal body */}
        {slots.default}
        {/* Footer */}
        {/* NOTE: 外部组件如果是函数式组件，slots 无法正常传递 */}
        <template slot="footer">
          <div class="ant-modal-footer-inner">
            {slots.footnote ? <div>{slots.footnote}</div> : <div domPropsInnerHTML={props.footnote}></div>}
            {slots.footer || (
              <div>
                <Button onClick={handleCancel}>{props.cancelText}</Button>
                <Button onClick={handleOk} type={props.confirmType} loading={props.confirmLoading}>
                  {props.okText}
                </Button>
              </div>
            )}
          </div>
        </template>
        {/* Close button */}
        <Icon type="icon-tanchuangguanbi" slot="closeIcon" />
      </Modal>
    );
  },
});

// BaseModal.emits = ['change', 'close', 'cancel', 'ok', 'fullyClose'];
BaseModal.model = {
  prop: 'visible',
  event: 'change',
};

export default BaseModal;
