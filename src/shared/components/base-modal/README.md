# @qcc-ui/base-modal

## API

包装组件

```ts
import { executor } from '../../common/base-modal';

const { show } = executor<typeof UploadModal.props>(UploadModal, {
  title: '弹窗标题',
  width: 600,
});

UploadModal.show = show;

// 手动扩展类型，以确保调用时能够拿到正确的TS类型
export default UploadModal as typeof UploadModal & { show: typeof show };
```

子组件控制弹窗组件的加载状态

```ts
// `this.submit` 如果返回的是 Promise，弹窗组件则开始自行控制加载状态
this.$emit('modal:submit', this.submit);
```
