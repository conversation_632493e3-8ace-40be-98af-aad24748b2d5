import { defineComponent } from 'vue';

import styles from './card.module.less';

const Card = defineComponent({
  functional: true,
  props: {
    title: {
      type: String,
      required: false,
    },
    titleBorder: {
      type: Boolean,
      default: true,
    },
    mode: {
      type: String,
      required: false,
    },
    /**
     * 是否支持高亮
     */
    titleHighlight: {
      type: Boolean,
      default: false,
    },
    rootStyle: {
      type: Object,
      default: () => ({}),
    },
    bodyStyle: {
      type: Object,
      default: () => ({}),
    },
    headerStyle: {
      type: Object,
      default: () => ({}),
    },
  },
  render(h, { props, slots }) {
    const { rootStyle, bodyStyle, headerStyle, titleBorder, titleHighlight } = props;
    const { extra, title: titleSlot, body: bodySlot, default: defaultSlot } = slots();
    const title = titleSlot || props.title;
    const body = defaultSlot || bodySlot;
    return (
      <div
        class={{
          [styles.container]: true,
        }}
        style={rootStyle}
      >
        {title ? (
          <div class={styles.header} style={headerStyle}>
            <div
              class={{
                [styles.wrapper]: true,
                [styles.border]: titleBorder,
              }}
            >
              <div
                class={{
                  [styles.title]: true,
                  [styles.highlight]: titleHighlight,
                }}
              >
                {title}
              </div>
              {extra && <div class={styles.extra}>{extra}</div>}
            </div>
          </div>
        ) : null}

        {body ? (
          <div class={styles.body} style={bodyStyle}>
            {body}
          </div>
        ) : null}
      </div>
    );
  },
});

export default Card;
