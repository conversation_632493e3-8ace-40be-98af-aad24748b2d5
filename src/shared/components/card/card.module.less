@import '@/styles/token.less';

@card-gap: 15px;

.container {
  background-color: @qcc-color-white;
  border-radius: @qcc-border-radius-middle;

  & + & {
    margin-top: 10px;
  }

  .header {
    padding: 0 @card-gap;
    color: @qcc-color-black-600;

    .wrapper {
      padding: 9px 0;
      min-height: 50px;
      box-shadow: inset 0 -1px 0 0 @qcc-color-gray-500;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .title {
    font-size: 15px;
    font-weight: bold;

    &.highlight {
      em {
        color: @qcc-color-red-600;
      }
    }
  }

  .body {
    padding: @card-gap;
  }
}
