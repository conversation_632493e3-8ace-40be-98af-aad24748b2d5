@levels: high, middle, low;
@risk-level-bg: #ffcccc, #ffeecc, #c4f5e0;
@risk-level-fg: #666, #666, #666;

.theme-style(@background, @text) {
  background: @background;
  color: @text;
}

.container {
  display: inline-block;
  font-size: 12px;
  line-height: 18px;
  padding: 1px 4px;
  border-radius: 2px;

  each(@levels, {
    &.@{value} {
      .theme-style(extract(@risk-level-bg, @index), extract(@risk-level-fg, @index));
    }
  });
}