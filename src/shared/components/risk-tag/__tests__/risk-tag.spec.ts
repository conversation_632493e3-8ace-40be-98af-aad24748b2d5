import { mount } from '@vue/test-utils';

import RiskTag from '..';

describe('RiskTag', () => {
  it('renders correctly with high risk level', () => {
    const wrapper = mount(RiskTag, {
      propsData: {
        level: 2,
      },
    });
    expect(wrapper.text()).toBe('警示风险');
  });

  it('renders correctly with middle risk level', () => {
    const wrapper = mount(RiskTag, {
      propsData: {
        level: 1,
      },
    });
    expect(wrapper.text()).toBe('关注风险');
  });

  it('renders correctly with low risk level', () => {
    const wrapper = mount(RiskTag, {
      propsData: {
        level: 0,
      },
    });
    expect(wrapper.text()).toBe('提示风险');
  });

  it('renders null when level is undefined', () => {
    const wrapper = mount(RiskTag, {
      propsData: {
        level: undefined,
      },
    });
    expect(wrapper.html()).toBe('');
  });

  it('renders children when provided', () => {
    const wrapper = mount(RiskTag, {
      propsData: {
        level: 2,
      },
      slots: {
        default: {
          template: '<span>Custom Label</span>',
        },
      },
    });
    expect(wrapper.text()).toBe('Custom Label');
  });

  it('uses custom mapping when provided', () => {
    const customMapping = {
      2: {
        code: 'very-high',
        label: '非常高风险',
      },
    };
    const wrapper = mount(RiskTag, {
      propsData: {
        level: 2,
        mapping: customMapping,
      },
    });
    expect(wrapper.text()).toBe('非常高风险');
  });
});
