import { PropType, defineComponent } from 'vue';

import styles from './risk-tag.module.less';

type DimensionLevelMapping = {
  [key: number]: { code: string; label: string };
};

const riskLevelToLabel = (mapping: DimensionLevelMapping, dimensionLevel: number) => {
  return mapping[dimensionLevel];
};

const RiskTag = defineComponent({
  functional: true,
  props: {
    level: {
      type: Number as PropType<number>,
      required: false,
    },
    mapping: {
      type: Object as PropType<DimensionLevelMapping>,
      default: () => ({
        2: {
          code: 'high',
          label: '警示风险',
        },
        1: {
          code: 'middle',
          label: '关注风险',
        },
        0: {
          code: 'low',
          label: '提示风险',
        },
      }),
    },
  },
  render(h, { props, children }) {
    if (props.level === undefined) {
      return null;
    }
    const riskLevel = riskLevelToLabel(props.mapping, props.level);
    return (
      <div
        class={{
          [styles.container]: true,
          [styles[riskLevel.code]]: true,
        }}
      >
        {children || riskLevel.label}
      </div>
    );
  },
});

export default RiskTag;
