import { computed, defineComponent, PropType } from 'vue';
import { <PERSON><PERSON>, Too<PERSON><PERSON> } from 'ant-design-vue';
import { debounce } from 'lodash';

import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import QRichTable, { IQRichTablePagination } from '@/components/global/q-rich-table';
import CellEdit from '@/shared/components/cell-edit';

import styles from './batch-confirm-result.module.less';
import CompanyStatus from '@/components/global/q-company-status';

const BatchConfirmResult = defineComponent({
  name: 'BatchConfirmResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
      default: () => [],
    },
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    selectedIds: {
      type: Array,
      required: false,
    },
    pagination: {
      type: [Object, Boolean] as PropType<Partial<IQRichTablePagination> | boolean>,
      required: false,
      default: false,
    },
    loading: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: props.selectedIds,
      onChange: (selectedRowKeys, selectedRows) => {
        emit('select', selectedRows);
      },
      getCheckboxProps: (record) => ({
        props: {
          disabled: !record?.flag,
        },
      }),
    }));
    const handleRemove = (ids) => {
      emit('delete', ids);
    };

    const _handleDataChange = (data) => {
      emit('change', data);
    };

    const handleDataChange = debounce(_handleDataChange, 200);

    return {
      rowSelection,
      handleRemove,
      handleDataChange,
    };
  },
  render() {
    const { dataSource, pagination, loading, rowSelection } = this;
    if (!dataSource.length && !loading) {
      return (
        <QRichTableEmpty size={'100px'} minHeight={'calc(100vh - 315px)'}>
          <span class={styles.empty}>
            <div>暂时没有找到相关数据</div>
          </span>
        </QRichTableEmpty>
      );
    }

    return (
      <QRichTable
        class={[styles.tableData, 'modal-table']}
        rowKey={'id'}
        showIndex={true}
        columns={this.columns}
        loading={loading}
        dataSource={dataSource}
        // rowSelection={rowSelection}
        pagination={pagination}
        paddingDistance={110}
        customScroll={{ x: false, y: 'calc(100vh - 115px - 260px)' }}
        onChange={(...arg) => this.$emit('change', ...arg)}
        scopedSlots={{
          action: (text, record) => {
            return (
              <Button type="link" onClick={() => this.handleRemove([record.id])}>
                移除
              </Button>
            );
          },
          company: (item) => {
            return (
              <div>
                <CellEdit rowData={item} onSelect={this.handleDataChange} />
                <p class={styles.originalname} v-show={item.matchBy === 3}>
                  曾用名：
                  <Tooltip title={item.originalname?.join(', ')}>{item.originalname?.join(', ')}</Tooltip>
                </p>
              </div>
            );
          },
          creditCode: (val, item) => {
            return <CellEdit editType={'compCreditCode'} rowData={item} onUpdate={this.handleDataChange} />;
          },
          personName: (val, item) => {
            return <CellEdit editType={'personName'} rowData={item} onUpdate={this.handleDataChange} />;
          },
          idNumber: (val, item) => {
            return <CellEdit editType={'personIdcard'} rowData={item} onUpdate={this.handleDataChange} />;
          },
          companyStatus: (item) => {
            if (!item.companyStatus) return '-';
            return <CompanyStatus status={item.companyStatus} />;
          },
        }}
      />
    );
  },
});

export default BatchConfirmResult;
