import { mount } from '@vue/test-utils';
import BatchConfirmResult from '../index';

describe('BatchConfirmResult 组件', () => {
  const dataSource = [
    {
      id: 1,
      flag: 1,
      matchBy: 3,
      originalname: ['公司C', '公司D'],
      companyName: '公司A',
      companyId: 1,
      compCreditCode: '12345',
      personName: '张三',
      personIdcard: '123456789012345678',
    },
    {
      id: 2,
      flag: 0,
      matchBy: 2,
      originalname: [],
      companyName: '公司B',
      companyId: 2,
      compCreditCode: '67890',
      personName: '李四',
      personIdcard: '876543210987654321',
    },
  ];
  it('应该正确渲染表格数据', async () => {
    const wrapper = mount(BatchConfirmResult, {
      propsData: {
        dataSource,
        columns: [
          { title: '公司', scopedSlots: { customRender: 'company' } },
          { title: '信用代码', dataIndex: 'creditCode', scopedSlots: { customRender: 'creditCode' } },
          { title: '姓名', dataIndex: 'personName', scopedSlots: { customRender: 'personName' } },
          { title: '身份证号', dataIndex: 'personIdcard', scopedSlots: { customRender: 'idNumber' } },
          { title: '操作', scopedSlots: { customRender: 'action' } },
        ],
        loading: false,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('当没有数据时，应该显示空数据提示', async () => {
    const wrapper = mount(BatchConfirmResult, {
      propsData: {
        dataSource: [],
        columns: [
          { title: '公司', dataIndex: 'company', scopedSlots: { customRender: 'company' } },
          { title: '信用代码', dataIndex: 'creditCode', scopedSlots: { customRender: 'creditCode' } },
          { title: '姓名', dataIndex: 'personName', scopedSlots: { customRender: 'personName' } },
          { title: '身份证号', dataIndex: 'idNumber', scopedSlots: { customRender: 'idNumber' } },
          { title: '操作', scopedSlots: { customRender: 'action' } },
        ],
        loading: false,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.empty div').text()).toBe('暂时没有找到相关数据');
  });

  it('点击移除按钮时，应该触发 delete 事件', async () => {
    const wrapper = mount(BatchConfirmResult, {
      propsData: {
        dataSource,
        columns: [
          { title: '公司', scopedSlots: { customRender: 'company' } },
          { title: '操作', scopedSlots: { customRender: 'action' } },
        ],
        loading: false,
      },
    });

    await wrapper.findComponent({ name: 'AButton' }).trigger('click');
    expect(wrapper.emitted()).toHaveProperty('delete');
    expect(wrapper.emitted('delete')).toEqual([[[1]]]);
  });
});
