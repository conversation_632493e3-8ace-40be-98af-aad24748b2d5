// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`BatchConfirmResult 组件 > 应该正确渲染表格数据 1`] = `
<div class="container tableData modal-table">
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="ant-table-wrapper table scrollable scrollContentSet" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table ant-table-fixed-header ant-table-scroll-position-left ant-table-default ant-table-bordered">
              <div class="ant-table-content">
                <div class="ant-table-scroll">
                  <div class="ant-table-header">
                    <table class="">
                      <colgroup>
                        <col style="width: 58px; min-width: 58px;">
                        <col>
                        <col>
                        <col>
                        <col>
                        <col style="width: 50px; min-width: 50px;">
                      </colgroup>
                      <thead class="ant-table-thead">
                        <tr>
                          <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="1" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">公司</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="creditCode" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">信用代码</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              <th key="personName" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">姓名</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            <th key="personIdcard" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">身份证号</span><span class="ant-table-column-sorter"></span>
          </div></span></th>
          <th key="5" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作</span><span class="ant-table-column-sorter"></span>
        </div></span></th>
        </tr>
        </thead>
        </table>
      </div>
      <div tabindex="-1" class="ant-table-body" style="max-height: calc(100vh - 115px - 260px); overflow-y: scroll;">
        <table class="">
          <colgroup>
            <col style="width: 58px; min-width: 58px;">
            <col>
            <col>
            <col>
            <col>
            <col style="width: 50px; min-width: 50px;">
          </colgroup>
          <tbody class="ant-table-tbody">
            <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
              <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
              <td class="">
                <div>
                  <div>
                    <div>
                      <div><a target="_blank" href="/embed/companyDetail?keyNo=1&amp;title=公司A" style="padding-right: 4px;">公司A</a>
                        <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                      </div><em style="display: none;">匹配失败</em>
                    </div>
                  </div>
                  <p class="originalname">曾用名：<span class="">公司C, 公司D</span></p>
                </div>
              </td>
              <td class="">
                <div>
                  <div>
                    <div><span style="padding-right: 4px;">12345</span>
                      <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                    </div><em style="display: none;">匹配失败</em>
                  </div>
                </div>
              </td>
              <td class="">
                <div>
                  <div>
                    <div><span style="padding-right: 4px;">张三</span>
                      <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                    </div><em style="display: none;">匹配失败</em>
                  </div>
                </div>
              </td>
              <td class="">
                <div>
                  <div>
                    <div><span style="padding-right: 4px;">123456789012345678</span>
                      <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                    </div><em style="display: none;">匹配失败</em>
                  </div>
                </div>
              </td>
              <td class="ant-table-row-cell-break-word"><button type="button" class="ant-btn ant-btn-link"><span>移除</span></button></td>
            </tr>
            <tr class="ant-table-row ant-table-row-level-0" data-row-key="2">
              <td rowspan="1" style="position: relative; text-align: left;" dataindex="1" class="ant-table-row-cell-break-word"><span>2</span></td>
              <td class="">
                <div>
                  <div>
                    <div>
                      <div><a target="_blank" href="/embed/companyDetail?keyNo=2&amp;title=公司B" style="padding-right: 4px;">公司B</a>
                        <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                      </div><em style="display: none;">匹配失败</em>
                    </div>
                  </div>
                  <p class="originalname" style="display: none;">曾用名：<span class=""></span></p>
                </div>
              </td>
              <td class="">
                <div>
                  <div>
                    <div><span style="padding-right: 4px;">67890</span>
                      <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                    </div><em style="display: none;">匹配失败</em>
                  </div>
                </div>
              </td>
              <td class="">
                <div>
                  <div>
                    <div><span style="padding-right: 4px;">李四</span>
                      <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                    </div><em style="display: none;">匹配失败</em>
                  </div>
                </div>
              </td>
              <td class="">
                <div>
                  <div>
                    <div><span style="padding-right: 4px;">876543210987654321</span>
                      <q-icon-stub type="icon-a-bianjigenjin1x"></q-icon-stub>
                    </div><em style="display: none;">匹配失败</em>
                  </div>
                </div>
              </td>
              <td class="ant-table-row-cell-break-word"><button type="button" class="ant-btn ant-btn-link"><span>移除</span></button></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
`;
