import { mount } from '@vue/test-utils';

import TestUpload from '..';

describe('test-upload', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(TestUpload, {
      propsData: {
        placeholder: 'test-placeholder',
        hit: 'test-hint',
      },
    });
  });

  test('render', () => {
    expect(wrapper).toMatchSnapshot();
  });

  // test 点击
  test('clickModel', async () => {
    const spyOnOpen = vi.spyOn(wrapper.vm, 'showTextImportModal');
    const textInput = wrapper.find('[data-testid="text-input"]');
    await textInput.trigger('click');
    expect(spyOnOpen).toBeCalled();
  });
});
