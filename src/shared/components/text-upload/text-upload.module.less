@import '@/styles/token';

.themed(@bg, @border, @border-hover) {
  background: @bg;
  border-color: @border;

  &:hover {
    border-color: @border-hover;
  }
}

.container {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 20px 0;
  border: 1px dashed @qcc-color-blue-500;
  .themed(@qcc-color-blue-200, @qcc-color-blue-500, @qcc-color-blue-400);

  .icon + .placeholder {
    margin-top: 5px;
  }

  .icon {
    font-size: 42px;
    color: @qcc-color-blue-500;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .placeholder {
    .text {
      font-size: 14px;
      line-height: 24px;
      color: @qcc-color-black-600;
    }

    .hint {
      font-size: 14px;
      line-height: 22px;
      color: @qcc-color-black-300;
    }
  }

  &.lighter {
    .themed(@qcc-color-white, #c2c2c2, @qcc-color-blue-400);

    .icon {
      color: #c6e5f9;
    }

    .text {
      color: @qcc-color-black-300;
    }
  }
}
