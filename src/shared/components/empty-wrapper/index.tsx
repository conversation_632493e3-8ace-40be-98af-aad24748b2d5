import { defineComponent, ref, watch } from 'vue';
import { Button } from 'ant-design-vue';

import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import QIcon from '@/components/global/q-icon';

const EmptyWrapper = defineComponent({
  name: 'EmptyWrapper',
  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    emptyMinHeight: {
      type: String,
      default: 'calc(100vh - 315px)',
    },
    emptyText: {
      type: String,
      required: false,
    },
    enableResetBtn: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const hasFilter = ref(false);
    const getResetBtn = () => {
      const btn = document.querySelector('#reset-filter-btn');
      if (btn) {
        const display = window.getComputedStyle(btn).display;
        hasFilter.value = display !== 'none';
      }
    };

    const handleResetFilter = () => {
      const btn: HTMLElement | null = document.querySelector('#reset-filter-btn .ant-btn');
      btn?.click();
    };
    watch(
      () => props.dataSource,
      (data) => {
        if (props.enableResetBtn && !data.length) {
          getResetBtn();
        }
      }
    );
    return {
      hasFilter,
      handleResetFilter,
    };
  },
  render() {
    const { dataSource, loading, emptyMinHeight, $slots } = this as any;
    if (!dataSource.length && !loading) {
      return (
        $slots.empty ?? (
          <QRichTableEmpty size={'100px'} minHeight={emptyMinHeight}>
            <div v-show={!this.hasFilter}>{this.emptyText || '暂时没有找到相关数据'}</div>
            <div v-show={this.hasFilter} class="text-center">
              <p style={{ marginBottom: '30px' }}>当前筛选条件下无数据，请点击重置筛选，查看全部数据。</p>
              <Button onClick={this.handleResetFilter} type="primary">
                <QIcon type="icon-chexiaozhongzhi" />
                <span>重置筛选</span>
              </Button>
            </div>
          </QRichTableEmpty>
        )
      );
    }
    return $slots.default;
  },
});

export default EmptyWrapper;
