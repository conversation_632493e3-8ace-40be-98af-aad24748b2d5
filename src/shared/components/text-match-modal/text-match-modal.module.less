@import '@/styles/token';
@import '@/styles/risk.less';

.wrapperSpin {
  :global {
    .ant-spin {
      background-color: #fff;
      max-height: none !important;
    }
  }
}

.themed(@color) {
  &.item {
    color: @color;
  }

  &.legend::before {
    background-color: @color;
  }
}

.container2,
.container {
  display: flex;
  height: calc(80vh - 50px);

  :global {
    textarea {
      resize: none;
    }
  }

  .column {
    position: relative;
    flex: 1;
    width: 418px;
    height: 100%;

    .count {
      position: absolute;
      bottom: 5px;
      right: 15px;
      color: #666;
    }

    &.hasAlert {
      :global {
        .ant-input {
          padding-bottom: 61px;
        }
      }
    }

    .tipsWrapper {
      position: absolute;
      z-index: 100;
      padding: 10px;
      left: 1px;
      bottom: 1px;
      right: 1px;
      background-color: #fff;

      .tips {
        font-size: 14px;
        color: #ff898f;
        background-color: #faebee;
        padding: 10px;
      }
    }
  }

  .action {
    flex: 0 0 48px;
    margin: 0 20px;
    display: flex;
    align-items: center;

    .button {
      width: 60px;
      height: 40px;
      padding: 0;
      min-width: 48px;
      display: flex;
      align-items: center;
      justify-content: center;

      .buttonText {
        font-size: 14px;
      }

      .icon {
        font-size: 14px;
        display: inline-block;
        margin-left: 0;
      }

      &:disabled {
        color: @qcc-color-white;
        background: #88c5f6;
        border-color: #88c5f6;
      }
    }
  }

  .text,
  .result {
    height: 100%;
  }

  .loading {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .text {
    line-height: 2;
    font-size: 14px;
    padding: 8px 15px;
    border-radius: 0;
    border-color: #d8d8d8;

    &:focus {
      border-color: #d8d8d8;
      box-shadow: none;
    }
  }

  .result {
    display: flex;
    flex-direction: column;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    overflow: hidden;
    position: relative;

    .text {
      border: none;
    }

    .header {
      padding: 0 15px;
      background-color: #f7f7fa;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-shrink: 0;

      .title {
        font-size: 14px;

        em {
          color: @qcc-color-blue-500;
        }
      }
    }

    .footer {
      border-top: 1px solid #d8d8d8;
      // height: 40px;
      padding: 8px 10px;
      display: flex;
      align-items: center;
    }

    .empty {
      flex: 1;
    }

    .clear {
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      line-height: 20px;
      color: @qcc-color-black-600;
      transition: color 0.2s ease;

      .icon {
        transition: color 0.2s ease;
        color: @qcc-color-black-200;
        font-size: 16px;
        margin-right: 3px;
      }

      &:hover,
      &:hover .icon {
        color: @qcc-color-blue-500;
      }
    }

    .content {
      padding: 5px 10px;
      flex: 1;
      overflow: auto;
    }

    .item {
      position: relative;
      padding: 5px 7px;
      line-height: 22px;
      height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      cursor: default;

      .tag {
        font-size: 12px;
        color: #666;
        background-color: #f3f3f3;
        margin-left: 2px;
        border-radius: 2px;
        display: inline-block;
        padding: 0 2px;
        height: 18px;
        line-height: 18px;
      }

      .itemText {
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.linked {
          color: #ff8900;
        }
      }

      &:hover {
        .itemText {
          background: @qcc-color-gray-500;
        }

        .remove {
          visibility: visible;
        }
      }

      .edit {
        cursor: pointer;
        font-size: 14px;
        color: #ccc;
      }
      // position: relative;
      .remove {
        visibility: hidden;
        cursor: pointer;
        font-size: 12px;
        color: #ccc;
        display: block;
      }
    }

    .legend {
      display: flex;
      align-items: center;
      font-size: 12px;

      &:not(:last-child) {
        margin-right: 12px;
      }

      .matchCount {
        margin-left: 2px;
        color: #128bed;
      }

      &::before {
        content: '';
        margin-right: 5px;
        width: 11px;
        height: 11px;
        display: inline-block;
        background-color: #333;
        border-radius: 2px;
      }
    }

    .exact,
    .company {
      .themed(@qcc-color-black-600);
    }

    .fuzzy,
    .untag {
      .themed(#F04040);
    }

    .used,
    .product,
    .stock {
      .themed(#808cff);
    }

    .linked {
      .themed(#6371FF);
    }

    .company {
      color: #333;
    }

    .untag,
    .tw {
      color: #6371FF;
    }

    .product,
    .stock {
      color: #808cff;
    }
  }

  :global(.ant-checkbox-wrapper) {
    background: #fff;
    border: 1px solid #eee;
    padding: 1px 0 0 5px;

    &:hover {
      color: #128bed;
    }
  }
}

.container2 {
  height: 400px;
}

.footnote {
  color: @qcc-color-black-300;
}

.table {
  td {
    padding: 10px;
    border: 1px solid #e4eef6;
  }
}

.footerButton {
  margin-right: 20px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(18, 139, 237, 0.1);
  color: #128bed;
  background-color: #f2f8fe;
  padding: 3px 8px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 2px;

  &:hover {
    border-color: #128bed;
    color: #128bed;
  }
}

.clist-item-danger{
  background-color: #fff7f7;
  height: 30px;
  padding: 3px 5px;
}

.clist-item-action {
  color: #808080;
 
  :global{
    .ant-checkbox-wrapper{
      margin-left: 5px;
      color: #808080;
    }
  }
}
