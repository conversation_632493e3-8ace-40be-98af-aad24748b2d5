import { defineComponent, type PropType } from 'vue';

import QIcon from '@/components/global/q-icon';

import styles from './switch-button.module.less';

const SwitchButton = defineComponent({
  name: 'SwitchButton',
  props: {
    value: {
      type: [String, Number] as PropType<string | number | undefined>,
      required: false,
    },
    options: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    size: {
      type: String as PropType<'small' | 'medium' | 'large'>,
      default: 'medium',
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  render() {
    return (
      <div class={[styles.container, styles[this.size]]}>
        {this.options.map(({ value, label, icon }, index) => {
          return (
            <div
              key={value}
              class={{
                [styles.item]: true,
                [styles.active]: value === this.value,
              }}
              onClick={() => this.$emit('change', value, index)}
            >
              {icon ? <QIcon style={{ fontSize: '14px' }} type={icon} /> : null}
              <span>{label}</span>
            </div>
          );
        })}
      </div>
    );
  },
});

export default SwitchButton;
