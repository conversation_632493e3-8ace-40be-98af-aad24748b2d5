import { mount } from '@vue/test-utils';

import SwitchButton from '..';

describe('SwitchButton', () => {
  it('正常渲染', () => {
    const options = [
      { value: '1', label: '选项1' },
      { value: '2', label: '选项2' },
    ];
    const wrapper = mount(SwitchButton, {
      propsData: {
        options,
        value: '1',
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  it('没有选项时应正常渲染', () => {
    const wrapper = mount(SwitchButton);

    expect(wrapper.findAll('span').length).toBe(0);
  });

  it('点击选项应触发 change 事件', async () => {
    const options = [
      { value: '1', label: '选项1' },
      { value: '2', label: '选项2' },
    ];
    const wrapper = mount(SwitchButton, {
      propsData: {
        options,
        value: '1',
      },
    });

    const item = wrapper.findAll('span');
    await item.at(1).trigger('click');

    expect(wrapper.emitted().change[0]).toEqual(['2', 1]);
  });
});
