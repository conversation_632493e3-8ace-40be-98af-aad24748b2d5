@import '@/styles/token.less';

.container {
  padding: 4px;
  border: 1px solid @qcc-color-gray-700;
  border-radius: 2px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: @qcc-color-blue-500;
  }

  .item {
    padding: 1px 8px;
    font-size: 12px;
    line-height: 22px;
    white-space: nowrap;
    cursor: pointer;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;

    &:hover {
      color: @qcc-color-blue-500;
    }

    &.active {
      background: @qcc-color-blue-500;
      color: @qcc-color-white;
    }
  }
}

.small {
  padding: 3px;

  .item {
    font-size: 12px;
    line-height: 18px;
    padding: 0 4px;
  }
}
