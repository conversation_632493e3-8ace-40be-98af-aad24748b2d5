import { defineComponent, PropType, VNode } from 'vue';
import { sortBy } from 'lodash';
import { Button } from 'ant-design-vue';

import ClampContent from '@/components/clamp-content';
import { getContent } from '@/utils/content-helper';
import { calcTotalHits } from '@/apps/risk-monitor/pages/targets-detail/config/basic-info-config';

import { openDimensionDetailDrawer } from '../dimenison-detail-drawer';
import styles from './trends-content.module.less';

const TrendsContent = defineComponent({
  name: 'TrendsContent',
  props: {
    record: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    isRelatedTrends: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = (record: any) => {
      emit('click', record);
    };

    return {
      handleClick,
    };
  },
  render() {
    const { record } = this as any;

    const { metricsContent } = record;
    const { displayContent, metricScorePO = {} } = metricsContent || {};

    if (!displayContent?.length) {
      return null;
    }
    const totalHits = calcTotalHits(record);

    let displayContentBlock: VNode | null = null;
    if (metricScorePO.name?.includes('潜在关联方')) {
      const trendsList = sortBy(displayContent, 'operate').reduce((acc, cur) => {
        acc.push({
          operate: cur.operate,
          operateName: cur.operate === 1 ? '减少' : '新增',
          count: cur.count,
        });
        return acc;
      }, []);
      displayContentBlock = (
        <div>
          {trendsList.map((item) => (
            <div class="flex items-center">
              <span>关联方{item.operateName}：</span>
              <span>{item.count} 个</span>
            </div>
          ))}
        </div>
      );
    } else {
      const riskContent = displayContent[0];
      const isNoticeRecent = riskContent.dimensionKey === 'NoticeInTimePeriod';
      displayContentBlock = (
        <ClampContent
          clampKey={record.uniqueHashkey ?? record.companyMetricsHashkey}
          line={isNoticeRecent ? Infinity : 5}
          nativeOnClick={() => {
            //  if (Content?.includes('资质名称') && Content?.includes('证书编号') && CertificationId) {
            //    openDetailModal({ ...Highlight[0], CertificateCodeDesc: CertificationName });
            //    this.$emit('action', { type: 'qualification' });
            //  }
          }}
        >
          <span domPropsInnerHTML={getContent(riskContent?.dimensionContent[0], riskContent.dimensionKey)}></span>
        </ClampContent>
      );
    }

    return (
      <div class={styles.trendsContent}>
        {displayContentBlock}
        <Button
          class={[styles.viewAllButton, 'flex items-center']}
          type="link"
          onClick={() => {
            openDimensionDetailDrawer({
              record,
            });
          }}
        >
          查看全部({totalHits}) <q-icon type="icon-a-shixinyou1x" />
        </Button>
      </div>
    );
  },
});

export default TrendsContent;
