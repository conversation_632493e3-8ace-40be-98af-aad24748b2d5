import { defineComponent } from 'vue';
import { omit } from 'lodash';

import QTag from '@/components/global/q-tag';

import styles from './partner-tag.module.less';

const PartnerTag = defineComponent({
  functional: true,
  props: {
    color: {
      type: String,
      default: '#666666',
    },
    width: {
      type: [String, Number],
      required: false,
    },
  },
  render(h, { props, children, listeners, data }) {
    return (
      <QTag
        class={styles.container}
        style={{
          maxWidth: props.width || 'auto',
          ...(data.style as Record<string, any>),
        }}
        {...{ props, on: listeners, ...omit(data, ['style']) }}
      >
        <i class={[styles.dot, 'tag-select-input__dot']} style={{ backgroundColor: props.color }} />
        <span
          class="flex items-center"
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            flex: '1',
          }}
        >
          {children}
        </span>
      </QTag>
    );
  },
});

export default PartnerTag;
