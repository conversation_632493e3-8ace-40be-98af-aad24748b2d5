import { inject, PropType, defineComponent } from 'vue';
import { Icon as AIcon, Upload } from 'ant-design-vue';

import { useFileUpload } from '@/hooks/use-file-upload';
import { createTrackEvent } from '@/config/tracking-events';

import Icon from '../icon';
import styles from './file-upload.module.less';

const FileUpload = defineComponent({
  name: 'FileUpload',
  props: {
    /**
     * 上传地址
     */
    action: {
      type: [String, Function],
      required: true,
    },
    /**
     * 是否支持多文件上传
     */
    multiple: {
      type: Boolean,
      default: false,
    },
    /**
     * Placeholder text
     */
    placeholder: {
      type: String,
      default: '点击或将文件拖拽到这里上传',
    },
    /**
     * Placeholder hint
     */
    hint: {
      type: String,
      required: false,
    },
    icon: {
      type: String,
      default: 'icon-shangchuanwendang',
    },
    height: {
      type: String,
      default: 'auto',
    },
    theme: {
      type: String as PropType<'default' | 'lighter'>,
      default: 'default',
    },
    showUploadList: {
      type: Boolean,
      required: false,
    },
    beforeFileUpload: {
      type: Function as PropType<(file: File) => Promise<boolean>>,
      required: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const needMessage = inject('needMessage', true);
    const { isUploading, uploadChange, uploadReject } = useFileUpload(emit, { needMessage });
    const handleBeforeUpload = (file: any) => {
      if (!props.beforeFileUpload) {
        return true;
      }
      return new Promise((resolve, reject) => {
        return props.beforeFileUpload?.(file).then((result) => {
          if (result) {
            resolve(file);
          } else {
            reject(file);
          }
        });
      });
    };
    return {
      isUploading,
      uploadChange,
      uploadReject,
      handleBeforeUpload,
    };
  },
  render() {
    return (
      <Upload.Dragger
        class={[styles.container, styles[this.theme]]}
        style={{
          height: this.height,
        }}
        name="file"
        action={this.action}
        showUploadList={this.showUploadList}
        multiple={this.multiple}
        accept=".xls,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        onChange={this.uploadChange}
        beforeUpload={(file) => {
          this.$track(createTrackEvent(6208, this.$route.meta?.title, '上传文档'));
          return this.handleBeforeUpload(file);
        }}
        onReject={this.uploadReject}
        disabled={this.isUploading || this.loading}
      >
        <div class={styles.icon}>
          <AIcon v-show={this.isUploading || this.loading} type="loading" />
          <Icon v-show={!this.isUploading && !this.loading} type={this.icon} />
        </div>
        <div class={styles.placeholder}>
          <p class={styles.text}>{this.placeholder}</p>
          <p v-show={this.hint} class={styles.hint}>
            {this.hint}
          </p>
        </div>
      </Upload.Dragger>
    );
  },
});

export default FileUpload;
