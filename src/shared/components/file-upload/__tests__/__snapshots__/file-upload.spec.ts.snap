// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FileUpload > renders correctly with default props 1`] = `<span style="height: auto;" class="container default"><div class="ant-upload ant-upload-drag"><span role="button" tabindex="0" class="ant-upload ant-upload-btn"><input type="file" accept=".xls,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" style="display: none;"><div class="ant-upload-drag-container"><div class="icon"><i aria-label="icon: loading" class="anticon anticon-loading" style="display: none;"><svg viewBox="0 0 1024 1024" data-icon="loading" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class="anticon-spin"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg></i><q-icon-stub type="icon-shangchuanwendang"></q-icon-stub></div><div class="placeholder"><p class="text">点击或将文件拖拽到这里上传</p><p class="hint" style="display: none;"></p></div></div></span></div></span>`;
