import { mount } from '@vue/test-utils';

import { useFileUpload } from '@/hooks/use-file-upload';

import FileUpload from '..';

vi.mock('@/hooks/use-file-upload', () => ({
  useFileUpload: vi.fn(() => ({
    isUploading: false,
    uploadChange: vi.fn(),
    uploadReject: vi.fn(),
  })),
}));

vi.mock('@/config/tracking-events', () => ({
  useTrack: vi.fn(() => vi.fn()),
  createTrackEvent: vi.fn(() => 'trackEvent'),
}));

describe('FileUpload', () => {
  const action = 'https://example.com/upload';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders correctly with default props', async () => {
    const wrapper = mount(FileUpload, {
      propsData: {
        action,
      },
      provide: {
        needMessage: true,
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper).toMatchSnapshot();
    expect(wrapper.html()).toContain('点击或将文件拖拽到这里上传');
  });

  test('renders with custom placeholder', async () => {
    const wrapper = mount(FileUpload, {
      propsData: {
        action,
        placeholder: 'Custom Placeholder',
      },
      provide: {
        needMessage: true,
      },
    });
    await wrapper.vm.$nextTick();

    expect(wrapper.html()).toContain('Custom Placeholder');
  });

  test('renders with custom hint', async () => {
    const wrapper = mount(FileUpload, {
      propsData: {
        action,
        hint: 'Custom Hint',
      },
      provide: {
        needMessage: true,
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.html()).toContain('Custom Hint');
  });

  test('handles before upload correctly when check pass', async () => {
    const beforeFileUpload = vi.fn(() => Promise.resolve(true));
    const wrapper = mount(FileUpload, {
      propsData: {
        action,
        beforeFileUpload,
      },
      provide: {
        needMessage: true,
      },
    });

    const file = new File(['file content'], 'filename.xls', { type: 'application/vnd.ms-excel' });
    const handleBeforeUpload = wrapper.vm.handleBeforeUpload;

    await handleBeforeUpload(file);

    expect(beforeFileUpload).toHaveBeenCalled();
  });

  test('handles before upload correctly when check fail', async () => {
    const beforeFileUpload = vi.fn(() => Promise.resolve(false));

    const wrapper = mount(FileUpload, {
      propsData: {
        action,
        beforeFileUpload,
      },
      provide: {
        needMessage: true,
      },
    });

    const file = new File(['file content'], 'filename.xls', { type: 'application/vnd.ms-excel' });
    const handleBeforeUpload = wrapper.vm.handleBeforeUpload;

    try {
      await handleBeforeUpload(file);
    } catch (e) {
      expect(beforeFileUpload).toHaveBeenCalled();
    }
  });

  test('handles upload correctly when no check', async () => {
    const wrapper = mount(FileUpload, {
      propsData: {
        action,
      },
      provide: {
        needMessage: true,
      },
    });
    const file = new File(['file content'], 'filename.xls', { type: 'application/vnd.ms-excel' });
    const handleBeforeUpload = wrapper.vm.handleBeforeUpload;
    const res = await handleBeforeUpload(file);
    expect(res).toBeTruthy();
  });

  test('renders with loading icon when uploading', () => {
    vi.mocked<any>(useFileUpload).mockImplementationOnce(() => ({
      isUploading: true,
      uploadChange: vi.fn(),
      uploadReject: vi.fn(),
    }));

    const wrapper = mount(FileUpload, {
      propsData: {
        action,
      },
      provide: {
        needMessage: true,
      },
    });

    expect(wrapper.vm.isUploading).toBe(true);
  });
});
