@import '@/styles/token';

.themed(@bg, @border, @border-hover) {
  :global {
    .ant-upload.ant-upload-drag {
      background: @bg;
      border-color: @border;

      &:not(.ant-upload-disabled):hover {
        border-color: @border-hover;
      }

      .ant-upload {
        // padding: 4px 0 16px;
        padding: 20px 0;
      }
    }
  }
}

.container {
  display: block;
  .themed(@qcc-color-blue-200, @qcc-color-blue-500, @qcc-color-blue-400);

  .icon + .placeholder {
    margin-bottom: 5px;
  }

  .icon {
    font-size: 50px;
    color: @qcc-color-blue-500;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .placeholder {
    .text {
      font-size: 14px;
      line-height: 24px;
      color: @qcc-color-black-600;
    }

    .hint {
      font-size: 14px;
      line-height: 22px;
      color: @qcc-color-black-300;
    }
  }

  &.lighter {
    background-color: @qcc-color-white;

    .icon {
      color: #c6e5f9;
    }

    .text {
      color: @qcc-color-black-300;
    }
    .themed(@qcc-color-white, #c2c2c2, @qcc-color-blue-400);
  }
}
