import { defineComponent } from 'vue';

import IconBulb from './icon-bulb.svg';
import styles from './warning-info.module.less';

const WarningInfo = defineComponent({
  name: 'WarningInfo',
  props: {
    text: {
      type: String,
      required: true,
    },
  },
  render() {
    return (
      <div class={styles.warning}>
        <img src={IconBulb} />
        <span>{this.text}</span>
      </div>
    );
  },
});

export default WarningInfo;
