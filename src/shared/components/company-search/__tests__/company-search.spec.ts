import { shallowMount } from '@vue/test-utils';
import { message } from 'ant-design-vue';

import CompanySearch from '..';

vi.mock('@/store', () => ({
  useStore: vi.fn().mockReturnValue({
    getters: {
      'user/profile': {
        userId: '1',
      },
    },
  }),
}));

vi.mock('vue-router/composables', () => ({
  useRouter: vi.fn().mockReturnValue({
    currentRoute: {
      params: {
        id: '6',
      },
    },
  }),
}));

describe('CompanySearch', () => {
  it('正常渲染', () => {
    const wrapper = shallowMount(CompanySearch);
    expect(wrapper.exists()).toBe(true);
  });

  it('正常搜索', async () => {
    const wrapper = shallowMount(CompanySearch);
    await wrapper.setData({ keywords: '测试公司' });
    await wrapper.vm.handleSearch();
    expect(wrapper.emitted().search).toBeTruthy();
  });

  it('关键词长度不足', async () => {
    const wrapper = shallowMount(CompanySearch);
    vi.spyOn(message, 'warning');
    await wrapper.setData({ keywords: '测' });
    await wrapper.vm.handleSearch();
    expect(message.warning).toHaveBeenCalledWith('关键词须至少包含2个字符');
    expect(wrapper.emitted().search).toBeFalsy();
  });

  it('无输入', async () => {
    const wrapper = shallowMount(CompanySearch);
    vi.spyOn(message, 'warning');
    await wrapper.setData({ keywords: '' });
    await wrapper.vm.handleSearch();
    expect(message.warning).toHaveBeenCalledWith('请输入企业名称');
    expect(wrapper.emitted().search).toBeFalsy();
  });

  it('选中公司后返回对应事件', async () => {
    const wrapper = shallowMount(CompanySearch);
    const mockCompany = { id: '1', name: '测试公司' };
    await wrapper.vm.handleSelect(mockCompany.name, mockCompany);
    expect(wrapper.emitted().select).toBeTruthy();
  });

  it('url相同执行刷新操作', async () => {
    const wrapper = shallowMount(CompanySearch, {
      propsData: { defaultKeyNo: '6', defaultKeywords: '测试公司' },
    });
    const mockCompany = { id: '6', name: '测试公司' };
    await wrapper.vm.handleSelect(mockCompany.name, mockCompany);
    expect(wrapper.emitted().refresh).toBeTruthy();
  });

  it('清除关键字', async () => {
    const wrapper = shallowMount(CompanySearch);
    const input = wrapper.findComponent({ name: 'SuggestionInput' });
    input.vm.$emit('clear');
    expect(wrapper.vm.keywords).toBe('');
  });
});
