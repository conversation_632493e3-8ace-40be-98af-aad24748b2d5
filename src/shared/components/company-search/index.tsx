import _ from 'lodash';
import { message } from 'ant-design-vue';
import { computed, defineComponent, ref, watch } from 'vue';
import { useRouter } from 'vue-router/composables';

import { ICompanyQcc } from '@/interfaces';
import { company as companyService } from '@/shared/services';
import SuggestionInput from '@/components/enhancement/suggestion-input';
import { removeHTMLTags } from '@/utils/format/remove-html-tags';
import { useStore } from '@/store';
import { routerTryPush } from '@/shared/composables/use-auth';
import { validateCompanyTypeWithWarning, validateCompanyWithCountInfo } from '@/utils/company/company-type';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import styles from './company-search.module.less';

/**
 * FIXME: 清理逻辑
 */
const CompanySearch = defineComponent({
  props: {
    // 是显示大的搜索框还是小的搜索框
    size: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      required: false,
    },
    placeholder: {
      type: String,
      default: '请输入企业名称或统一社会信用代码',
    },
    enterText: {
      type: String,
      default: '搜索',
    },
    storagePrefix: {
      type: String,
      default: 'SEARCH_HISTORY',
    },
    hasBulkSearch: {
      type: Boolean,
      default: true,
    },
    // 默认的keyNo和关键字
    defaultKeywords: {
      type: String,
      default: '',
    },
    defaultKeyNo: {
      type: String,
      default: '',
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    height: {
      type: String,
      default: '60px',
    },
  },
  name: 'CompanySearch',
  emits: ['search', 'select', 'refresh'],
  expose: ['focus'],
  setup(props, { emit }) {
    const input = ref<HTMLInputElement | null>(null);
    const focus = () => {
      const reference: any = _.get(input.value, '$refs.ele.$refs.input.$refs.input');
      setTimeout(() => {
        if (reference instanceof HTMLInputElement) {
          reference.focus(); // 重新聚焦
        }
      }, 80);
    };

    const router = useRouter();
    const route = router.currentRoute;
    const keywords = ref(props.defaultKeywords);
    const handleClear = () => {
      keywords.value = '';
    };

    const track = useTrack();
    const store = useStore();

    const profileId = computed(() => {
      const profile = store.getters['user/profile'];
      return profile?.id || profile?.userId;
    });

    const requestData = ref([]);

    const defaultKeySearch = ref(true); // 用于判断默认搜索是否进行过，可能并不需要

    const preDealData = (data) => {
      const { KeyNo, ImageUrl, HitReason, Name, Type, ...other } = data;
      const reason = _.get(HitReason, 'Field'); // 命中原因
      const value = removeHTMLTags(Name); // 移除 html 标签
      const label = Name;
      return {
        ...other,
        id: KeyNo,
        imageUrl: ImageUrl,
        type: Type,
        reason,
        value,
        label,
      };
    };
    const handleRequest = async (searchKey: string) => {
      requestData.value = [];
      const { Result = [] } = await companyService.getCompanyListQCC({
        searchKey,
        isHighlight: true,
        pageSize: 5,
      });

      requestData.value = Result;
      const result: ICompanyQcc[] = Result;
      return result.map(preDealData);
    };

    const handleSearch = async () => {
      if (!keywords.value) {
        message.warning('请输入企业名称');
        return;
      }
      const actualKeywords = _.trim(keywords.value).slice(0, 100);
      if (actualKeywords.length < 2) {
        message.warning('关键词须至少包含2个字符');
        return;
      }

      // 如果有keyNo和关键字，默认为三方跳转过来，需要进行匹配查询，搜索的时候直接触发选择事件
      if (props.defaultKeyNo && props.defaultKeywords === actualKeywords && defaultKeySearch.value) {
        await handleRequest(props.defaultKeywords);
        const selectCompany = requestData.value.find((item: any) => item.KeyNo === props.defaultKeyNo) as any;
        if (selectCompany) {
          defaultKeySearch.value = false;
          emit('select', preDealData(selectCompany));
        }
      } else {
        emit('search', keywords.value);
      }
    };
    const handleSelect = async (value, option) => {
      if (!value?.trim()) {
        return;
      }

      try {
        // 非大陆企业且风险排查的时候直接return
        if (props.hasBulkSearch) {
          if (!validateCompanyTypeWithWarning(option?.type)) {
            return;
          }
        }
        if (validateCompanyWithCountInfo(option)) {
          message.warning('当前企业不支持排查');
          return;
        }
        // id相同，说明url相同，执行刷新操作
        if (option.id && route?.params?.id === option.id) {
          emit('refresh');
        } else {
          emit('select', option);
        }
      } catch (error) {
        console.error(error);
      } finally {
        keywords.value = value;
      }
    };
    const handleGoToBatch = () => {
      track(createTrackEvent(6979, '准入排查', '批量排查'));

      routerTryPush({
        name: 'batch-investigation',
      });
    };

    // 监听路由变化，如果路由变化，就重置搜索框
    watch(
      () => props.defaultKeywords,
      (value) => {
        keywords.value = value;
      }
    );

    // 输入内容小于2，不显示边框
    const showBorder = computed<boolean>(() => {
      if (!keywords.value || keywords.value.length < 2) {
        return false;
      }
      if (!requestData.value?.length || requestData.value?.length === 0) {
        return false;
      }
      return true;
    });

    return {
      handleSearch,
      handleSelect,
      handleClear,
      handleRequest,
      handleGoToBatch,
      requestData,
      keywords,
      profileId,
      input,
      focus,
      showBorder,
    };
  },
  render() {
    if (!this.profileId) {
      return null;
    }

    const storageKey = `${this.storagePrefix}_${this.profileId}`;
    const suggestionInputContext = {
      ref: 'input',
      props: {
        storageKey,
        clearIcon: 'icon-shanchu3',
        enterButton: this.enterText,
        placeholder: this.placeholder,
        remote: this.handleRequest,
      },
      on: {
        select: this.handleSelect,
        search: this.handleSearch,
        clear: this.handleClear,
        suffixClick: this.handleGoToBatch,
      },
    };

    if (this.size) {
      return (
        <div class={styles.container}>
          <SuggestionInput
            size={this.size}
            {...suggestionInputContext}
            style={{ width: '370px' }}
            class={styles.minSearch}
            v-model={this.keywords}
            autoFocus={false}
          />
        </div>
      );
    }

    return (
      <div class={styles.container}>
        {this.title ? <div class={styles.title}>{this.title}</div> : null}
        <div class={styles.search}>
          <SuggestionInput
            {...suggestionInputContext}
            class={{
              [styles.input]: true,
              [styles.noBottomRadius]: this.showBorder,
              [styles.bottomRadius]: !this.showBorder,
              [styles.noBorder]: !this.bordered,
            }}
            style={{ '--input-height': this.height }}
            v-model={this.keywords}
            size="large"
            suffixIcon="icon-piliangqiyechaxun"
            hasBulkSearch={this.hasBulkSearch}
          />
        </div>
      </div>
    );
  },
});

export default CompanySearch;
