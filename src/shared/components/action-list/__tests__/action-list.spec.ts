import { Wrapper, createLocalVue, mount } from '@vue/test-utils';
import Vue from 'vue';

import ActionList from '..';

describe('ActionList', () => {
  let localVue: typeof Vue;
  let wrapper: Wrapper<any>;
  const mockUpdateFn = vi.fn();

  beforeEach(() => {
    localVue = createLocalVue();
    wrapper = mount(ActionList, {
      localVue,
      // Mock directives
      directives: {
        permission: {},
      },
      propsData: {
        updateFn: mockUpdateFn,
        deletePermissionCode: [2105],
        trackInfo: { trackCode: 6975, actionPage: '监控列表' },
        deletePopText: '此操作不可恢复，您确认移除该人员吗？',
      },
    });
  });

  test('snapshot', () => {
    expect(wrapper).toMatchSnapshot();
  });

  test('props: `updateFn` 编辑操作', () => {
    const triggers = wrapper.findAllComponents({
      name: 'AButton',
    });

    expect(triggers).toHaveLength(2);

    // 点击编辑按钮
    triggers.at(0).trigger('click');
    expect(mockUpdateFn).toHaveBeenCalledTimes(1);

    // 点击移除按钮
    // triggers.at(1).trigger('click');
    // expect(wrapper).toContain('此操作不可恢复，您确认移除该人员吗？');
    // expect(mockUpdateFn).toHaveBeenCalledTimes(2);
  });

  // test.todo('props: `deleteFn` 删除操作');
});
