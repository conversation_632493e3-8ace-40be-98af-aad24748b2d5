import { Button, Popconfirm } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import { createTrackEvent } from '@/config/tracking-events';

import styles from './action-list.module.less';

// 通用操作咧
const ActionList = defineComponent({
  name: 'ActionList',
  props: {
    editPermissionCode: {
      type: Array as PropType<number[]> | null,
      default: undefined,
    },
    deletePermissionCode: {
      type: Array as PropType<number[]> | null,
      default: null,
    },
    updateFn: Function,
    deleteFn: Function,
    trackInfo: {
      type: Object,
      default: () => ({}),
    },
    deletePopText: {
      type: String,
      default: '此操作不可恢复，您确认删除该企业吗？',
    },
    // 是否需要编辑按钮
    needEdit: {
      type: Boolean,
      default: true,
    },
    recordData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { editPermissionCode, deletePermissionCode, deletePopText, trackInfo, needEdit, recordData } = this;
    const { trackCode, actionPage } = trackInfo;
    return (
      <div style={{ display: 'flex', gap: '5px' }}>
        <Button
          v-show={needEdit}
          v-permission={editPermissionCode}
          type="link"
          onClick={() => {
            this.updateFn?.(recordData);
            this.$track(createTrackEvent(trackCode, actionPage, '编辑'));
          }}
        >
          编辑
        </Button>
        <Popconfirm
          v-permission={deletePermissionCode}
          placement="bottom"
          okText="确认"
          cancelText="取消"
          overlayStyle={{ width: '240px' }}
          onConfirm={() => {
            this.$emit('action', 'delete');
            this.deleteFn?.(recordData);
            this.$track(createTrackEvent(trackCode, actionPage, '移除'));
          }}
          scopedSlots={{
            title: () => <div class={styles.popconfirmContent}>{deletePopText}</div>,
          }}
        >
          <Button type="link">删除</Button>
        </Popconfirm>
      </div>
    );
  },
});

export default ActionList;
