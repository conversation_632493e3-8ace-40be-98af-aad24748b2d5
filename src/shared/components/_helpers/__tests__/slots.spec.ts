import { shallowMount } from '@vue/test-utils';
import { defineComponent } from 'vue';

import { passSlots } from '../slots';

describe('slots', () => {
  test('render', () => {
    const TestComponent = defineComponent({
      functional: true,
      render: (h, { slots }) => {
        return h('div', {}, [passSlots(h, slots())]);
      },
    });

    const wrapper = shallowMount(TestComponent, {
      slots: {
        header: 'header slot',
        footer: 'footer slot',
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
