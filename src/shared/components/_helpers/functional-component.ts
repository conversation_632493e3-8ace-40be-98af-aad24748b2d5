/* eslint-disable prefer-spread */
/* eslint-disable @typescript-eslint/ban-types */
export const createFunctionalEventEmitter =
  (listeners: { [key: string]: Function | Function[] }) =>
  (eventName: string) =>
  (...args: unknown[]) => {
    const handler = listeners[eventName];
    if (typeof handler === 'function') {
      handler.apply(null, args);
    }
  };

export function createMountNode() {
  return null;
}
