import { ExtractPropTypes, Ref, ref, defineComponent } from 'vue';

import { useToggle } from '@/hooks/use-toggle';

import { passSlots } from './slots';

type UnknownProps = { [key: string]: any };

export const createWrapper = <P extends UnknownProps>(WrapperComponent) => {
  return (InnerComponent, modalProps: Partial<ExtractPropTypes<P>> = {}) => {
    // FIXME: 函数式组件不支持 props 透传?
    const WithWrapper = defineComponent({
      props: {
        visible: {
          type: Boolean,
          default: false,
        },
        ...(InnerComponent.props || {}),
      },
      emits: ['ok', 'change'],
      setup(props, { emit }) {
        const [loading, setLoading] = useToggle(false);
        const innerRef: Ref<HTMLElement | null> = ref(null);
        const handleSubmit = () => {
          const innerRefValue = innerRef.value as any;
          if (innerRefValue && typeof innerRefValue.submit === 'function') {
            setLoading(true);
            // 支持 Promise 控制 loading 状态, 是否关闭窗口，由内部业务组件控制
            const result = innerRefValue.submit();
            if (result instanceof Promise) {
              result.finally(() => {
                emit('ok');
                setLoading(false);
              });
            } else {
              setLoading(false);
            }
          } else {
            emit('ok');
          }
        };
        const handleClose = () => {
          emit('change', false);
        };
        return {
          loading,
          innerRef,
          handleSubmit,
          handleClose,
        };
      },
      render(h) {
        const { visible, loading, ...props } = this.$props;
        const slots = this.$slots;
        const { handleClose, handleSubmit } = this;
        return (
          <WrapperComponent
            {...{
              props: {
                confirmLoading: loading,
                visible,
                ...modalProps,
              },
              on: {
                ...this.$listeners,
                ok: handleSubmit,
              },
            }}
          >
            <InnerComponent
              {...{
                ref: 'innerRef',
                props: {
                  visible,
                  ...props,
                },
                on: {
                  'modal:close': handleClose,
                  'modal:submit': handleClose,
                  // 'modal:loading': handleLoading,
                  // 'modal:loaded': handleLoading,
                },
              }}
            >
              {passSlots(h, slots)}
            </InnerComponent>
            {passSlots(h, slots)}
          </WrapperComponent>
        );
      },
    });
    // WithWrapper.emits = ['change', 'close', 'cancel', 'ok', 'fullyClose'];
    WithWrapper.model = {
      prop: 'visible',
      event: 'change',
    };
    return WithWrapper;
  };
};
