import { PropType, defineComponent, onMounted, ref } from 'vue';
import { Tabs } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';
import QModal from '@/components/global/q-modal/q-modal';
import { getContent } from '@/utils/content-helper';

const StrategyTypeMap = {
  must: '必须命中',
  should: '任意命中',
  must_not: '均不命中',
};
export const RelatedCourt = defineComponent({
  name: 'RelatedCourt',
  props: {
    params: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
  },
  setup(props) {
    const visible = ref(false);
    const record = props.params.data;

    const queryParams = ref({
      companyId: record.companyId,
      dimensionKey: record.companyId,
      strategyId: record.companyId,
      diligenceId: record.diligenceId,
      batchId: record.batchId,
      preBatchId: record.preBatchId || 0,
      monitorGroupId: record.monitorGroupId,
    });
    const hitDetailsTab = ref([]);

    const getDetail = (hitDetails: any[], displayContent: any[]) => {
      const tabsData = hitDetails.reduce((acc, cur) => {
        const hitd = ['must', 'should', 'must_not'].reduce<any[]>((acc1, key) => {
          const typeHits = cur[key]?.map((hitItem) => {
            queryParams.value.strategyId = hitItem.strategyId;
            const hitData = displayContent.find((item) => item.strategyId === hitItem.strategyId);
            return {
              ...hitData,
              ...hitItem,
            };
          });
          if (typeHits?.length) {
            acc1.push({ data: typeHits, label: key });
          }
          return acc1;
        }, []);
        if (hitd.length) {
          acc.push({
            data: hitd,
            label: hitd[0].data[0].strategyName,
          });
        }
        return acc;
      }, []);

      return tabsData;
    };
    const dealStatics = (data) => {
      const { metricsContent } = data;
      const { displayContent, metricScorePO } = metricsContent;
      const { hitDetails, otherHitDetails } = metricScorePO;
      hitDetailsTab.value = getDetail([hitDetails, ...otherHitDetails], displayContent);
    };
    onMounted(async () => {
      visible.value = true;
      await dealStatics(props.params.data);
    });
    return {
      visible,
      hitDetailsTab,
    };
  },
  render() {
    const columns = [
      {
        title: '企业名称',
        scopedSlots: { customRender: 'expandedTitleRender' },
      },
      {
        title: '风险等级',
        width: 120,
        dataIndex: 'riskLevel',
        scopedSlots: {
          customRender: 'MonitorResult',
        },
      },
      {
        title: '风险类型',
        width: 160,
        dataIndex: 'metricsName',
      },
      {
        title: '风险内容',
        scopedSlots: { customRender: 'MonitorContent' },
      },
      {
        title: '更新日期',
        width: 180,
        dataIndex: 'createDate',
        scopedSlots: {
          customRender: 'date',
        },
      },
      {
        title: '操作',
        width: 80,
        scopedSlots: {
          customRender: 'Action',
        },
      },
    ];
    return (
      <QModal
        title="更多开庭公告"
        visible={this.visible}
        footer={false}
        bodyStyle={{ padding: '0  0 15px 0' }}
        size={'huge-large'}
        viewportDistance={200}
        onCancel={() => {
          this.visible = false;
        }}
      >
        {/* 搜索结果 */}
        {/* <SearchResult
          style={{ padding: '0 15px' }}
          columns={columns}
          showRowSelection={false}
          dataSource={this.params.data}
          pagination={{
            total: this.params.data.length,
          }}
          on={{}}
        /> */}
        <Tabs style={{ padding: '0 15px' }}>
          {this.hitDetailsTab.map((item: any, index) => (
            // 策略层级
            <Tabs.TabPane key={index}>
              <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" slot="tab" title={item.label}>
                策略{index + 1}: <span domPropsInnerHTML={item.label}></span>
              </div>
              <Tabs>
                {item.data.map((item_1, index_1) => {
                  return (
                    // 策略类型
                    <Tabs.TabPane key={index_1}>
                      <div domPropsInnerHTML={StrategyTypeMap[item_1.label]} slot="tab"></div>
                      <Tabs>
                        {item_1.data.map((item_2, index_2) => {
                          return (
                            <Tabs.TabPane key={index_2}>
                              <div slot="tab">
                                指标{index_2 + 1}: <span domPropsInnerHTML={item_2.strategyName}></span>
                              </div>
                              {item_2.dimensionContent?.map((content, contentIndex) => (
                                <div style="margin-top: 10px">
                                  <div>{contentIndex + 1}</div>
                                  <div domPropsInnerHTML={getContent(content)} />
                                </div>
                              ))}
                            </Tabs.TabPane>
                          );
                        })}
                      </Tabs>
                    </Tabs.TabPane>
                  );
                })}
              </Tabs>
            </Tabs.TabPane>
          ))}
        </Tabs>
      </QModal>
    );
  },
});

export const openRelatinCourtModal = createPromiseDialog(RelatedCourt);
