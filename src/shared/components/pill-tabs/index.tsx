import { defineComponent } from 'vue';

import { createFunctionalEventEmitter } from '../_helpers/functional-component';
import styles from './pill-tabs.module.less';

type TabValue = number | string;
type TabColor = 'default' | 'red' | 'yellow';

type TabItem = {
  value: TabValue;
  label: string;
  count?: number | string;
  color?: TabColor | string;
};

function hasBeenSelected(selectedValue: TabValue[] | TabValue, currentValue: TabValue) {
  if (Array.isArray(selectedValue)) {
    return selectedValue.includes(currentValue);
  }
  return selectedValue === currentValue;
}

function xorValues(selectedValue: TabValue[] | TabValue, currentValue: TabValue) {
  if (Array.isArray(selectedValue)) {
    const index = selectedValue.indexOf(currentValue);
    if (index === -1) {
      return selectedValue.concat(currentValue);
    }
    return selectedValue.filter((v) => v !== currentValue);
  }
  return currentValue;
}

const PillTabs = defineComponent({
  functional: true,
  props: {
    width: {
      type: String,
      default: 'auto',
    },
    size: {
      type: String,
      default: 'default',
    },
    /**
     * Selected value
     */
    value: {
      type: [String, Number, Array],
      default: '',
    },
    /**
     * Tabs
     */
    tabs: {
      type: Array,
      required: true,
    },
    /**
     * 是否支持多选(目前根据 `value` 类型判断是否为多选)
     */
    // multiple: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  render(h, { props, listeners, slots }) {
    const emitters = createFunctionalEventEmitter(listeners);
    const handleChange = emitters('change');
    const { extra } = slots();
    return (
      <div class={[styles.container, styles[props.size]]}>
        {props.tabs.map((tab: any) => {
          return (
            <div
              key={tab.value}
              class={{
                [styles.tab]: true,
                [styles[tab.color ? tab.color : 'default']]: true,
                [styles.selected]: hasBeenSelected(props.value as TabValue[] | TabValue, tab.value),
              }}
              onClick={() => handleChange(xorValues(props.value as TabValue[] | TabValue, tab.value))}
            >
              {tab.label} <em v-show={tab.count}>{tab.count}</em>
            </div>
          );
        })}
        {extra}
      </div>
    );
  },
});
PillTabs.model = {
  prop: 'value',
  event: 'change',
};
// PillTabs.emits = ['change'];

export default PillTabs;
