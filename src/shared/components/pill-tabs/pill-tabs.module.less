@import '@/styles/token';

@colors: default, red, yellow;
@background-colors: #f7f7f7, @qcc-color-red-300, @qcc-color-yellow-300;
@text-colors: currentColor, @qcc-color-red-500, @qcc-color-yellow-500;

.themed(@background, @highlight) {
  background-color: @background;

  em {
    color: @highlight;
  }

  &:hover,
  &:focus,
  &.selected {
    background-color: darken(@background, 4%);
  }

  &.active {
    background-color: @highlight;
    color: @qcc-color-white;

    em {
      color: @qcc-color-white;
    }
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  padding: 7px 0;

  .tab {
    color: @qcc-color-black-600;
    border-radius: @qcc-border-radius-base;
    line-height: 20px;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: 4px 10px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-align: center;
    transition: color 0.2s ease, background-color 0.2s ease;

    &:not(:last-child) {
      margin-right: 10px;
    }
    // themes
    each(@colors, {
      &.@{value} {
        .themed(extract(@background-colors, @index), extract(@text-colors, @index));
      }
    });

    // default
    &.default {
      &:hover,
      &:focus,
      &.selected {
        background-color: #e2f1fd;
      }

      &.selected {
        color: @qcc-color-blue-500;
      }

      &.active {
        background-color: @qcc-color-blue-500;
        color: @qcc-color-white;
      }
    }
  }

  &.large {
    padding: 5px 0;

    .tab {
      width: 160px; // fixed width
      padding: 6px 10px;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  &.small {
    font-size: 12px;

    .tab {
      line-height: 16px;
      padding: 2px 4px;
    }
  }
}
