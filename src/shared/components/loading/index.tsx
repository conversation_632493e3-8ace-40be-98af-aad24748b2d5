import { defineComponent } from 'vue';

import Spinner from '../spinner';
import styles from './loading.module.less';

const Loading = defineComponent({
  functional: true,
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    height: {
      type: String,
      default: '100%',
    },
    minHeight: {
      type: String,
      default: '100%',
    },
    ...Spinner.props,
  },
  render(h, { props, children }) {
    return (
      <div
        class={styles.container}
        style={{
          height: props.height,
          minHeight: props.minHeight,
        }}
      >
        <div class={styles.loader} v-show={props.loading}>
          <Spinner size={props.size} description={props.description} />
        </div>

        <div
          class={{
            [styles.content]: true,
            [styles.blur]: props.loading,
          }}
        >
          {children}
        </div>
      </div>
    );
  },
});

export default Loading;
