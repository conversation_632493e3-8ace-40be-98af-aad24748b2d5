import { mount, shallowMount } from '@vue/test-utils';
import { ref } from 'vue';
import type { Mock } from 'vitest';

import GroupModal from '@/shared/components/add-group/index';
import { useTransformRules } from '@/shared/components/add-group/hooks/use-transform-rules';
import { setting, user as userService } from '@/shared/services';

vi.mock('@/shared/services');

vi.mock('@/shared/components/add-group/hooks/use-transform-rules', () => ({
  useTransformRules: vi.fn(),
}));

describe('GroupModal', () => {
  const defaultParams = {
    title: '分组管理',
    models: [],
    data: {
      name: '',
      monitorModelId: undefined,
      pushEnable: 0,
      pushRules: [],
      riskModelEntity: { modelName: 'Test Model' },
      monitorStatus: 1,
    },
  };

  beforeEach(() => {
    (setting.getModelLists as Mock).mockResolvedValue({ data: [{ modelId: 1, modelName: 'Model 1' }] });
    (userService.getUserList as <PERSON>ck).mockResolvedValue([
      { name: '张三', active: 0, userId: 1 },
      { name: '李四', active: 1, userId: 2 },
    ]);
    (useTransformRules as Mock).mockReturnValue({ pushAlertRef: ref(), list: ref([]) });
  });

  it('renders with default params and visible on mount', async () => {
    const wrapper = await mount(GroupModal, {
      propsData: defaultParams,
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.visible).toBe(false);
    expect(wrapper.findComponent({ name: 'QModal' }).exists()).toBe(true);
  });

  it('handles submit with valid form', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: defaultParams,
    });
    await wrapper.vm.$nextTick();

    const validate = vi.fn().mockResolvedValue({});
    wrapper.vm.formRef.validate = validate;

    const qModalWrapper = wrapper.findComponent({ name: 'QModal' });

    await qModalWrapper.vm.$emit('ok');
    await wrapper.vm.$nextTick();

    expect(validate).toHaveBeenCalled();
  });

  it('handles cancel', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: defaultParams,
    });

    await wrapper.vm.$nextTick();

    await wrapper.vm.handleCancel();

    expect(wrapper.vm.visible).toBe(false);
  });

  it('changes with popconfirm cancel', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: defaultParams,
    });

    wrapper.vm.form.monitorModelId = 1;
    wrapper.vm.form.monitorStatus = 1;
    wrapper.setData({ popVisible: true });
    await wrapper.vm.$nextTick();

    const popConfirmWrapper = wrapper.findComponent({ name: 'APopconfirm' });
    popConfirmWrapper.vm.$emit('cancel');

    expect(wrapper.vm.popVisible).toBe(false);
  });

  it('changes status with popconfirm', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: defaultParams,
    });

    wrapper.vm.form.monitorModelId = 1;
    wrapper.vm.form.monitorStatus = 1;
    wrapper.setData({ popVisible: true });
    await wrapper.vm.$nextTick();

    await wrapper.vm.changeStatus();

    expect(wrapper.vm.form.monitorStatus).toBe(2);
    expect(wrapper.vm.popVisible).toBe(false);
  });

  it('changes status without popconfirm', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: defaultParams,
      },
    });

    wrapper.vm.form.monitorModelId = 1;
    wrapper.vm.form.monitorStatus = 1;
    await wrapper.vm.$nextTick();
    await wrapper.vm.changeStatus();

    expect(wrapper.vm.form.monitorStatus).toBe(2);
    expect(wrapper.vm.popVisible).toBe(false);
  });

  it('sets monitorModelId to undefined if model is not found', async () => {
    (setting.getModelLists as Mock).mockResolvedValue({ data: [] });
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: {
          ...defaultParams,
          data: { ...defaultParams.data, monitorModelId: 999 },
        },
      },
    });
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.form.monitorModelId).toBe(999);
    expect(wrapper.vm.placeholder).toBe('请选择模型');
  });

  it('sets disabled input if groupType is 1', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: {
          ...defaultParams,
          data: { ...defaultParams.data, groupType: 1 },
        },
      },
    });
    await wrapper.vm.$nextTick();

    const inputWrapper = wrapper.findComponent({ name: 'AInput' });
    expect(inputWrapper.attributes().disabled).toBe('true');
    const selectWrapper = wrapper.findComponent({ name: 'ASelect' });
    selectWrapper.vm.$emit('change');
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.form.monitorStatus).toBe(1);
  });

  it('renders PushAlert if pushEnable is true', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: {
          ...defaultParams,
          data: { ...defaultParams.data, pushEnable: 1 },
        },
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'PushAlert' }).exists()).toBe(true);
  });

  it('does render PushAlert if pushEnable is false', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: defaultParams,
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'PushAlert' }).exists()).toBe(true);
  });

  it('monitorStatus handles change', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: defaultParams,
      },
    });

    await wrapper.vm.$nextTick();

    const changeStatusSpy = vi.spyOn(wrapper.vm, 'changeStatus');

    const switchWrapper = wrapper.findAllComponents({ name: 'QSwitch' }).at(0);
    switchWrapper.vm.$emit('change');

    expect(changeStatusSpy).not.toHaveBeenCalled();
  });

  it('monitorStatus handles change monitorStatus is 2', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: { defaultParams, data: { ...defaultParams.data, monitorStatus: 2 } },
      },
    });

    await wrapper.vm.$nextTick();

    const changeStatusSpy = vi.spyOn(wrapper.vm, 'changeStatus');

    const switchWrapper = wrapper.findAllComponents({ name: 'QSwitch' }).at(1);
    switchWrapper.vm.$emit('change');

    expect(changeStatusSpy).toHaveBeenCalled();
  });

  it('pushEnable handles change', async () => {
    const wrapper = shallowMount(GroupModal, {
      propsData: {
        params: defaultParams,
      },
    });

    await wrapper.vm.$nextTick();

    const switchWrapper = wrapper.findAllComponents({ name: 'QSwitch' }).at(2);
    switchWrapper.vm.$emit('change', true);

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.form.pushEnable).toBe(1);
    expect(wrapper.findComponent({ name: 'PushAlert' }).exists()).toBe(true);
  });
});
