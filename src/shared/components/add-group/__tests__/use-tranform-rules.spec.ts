import { useTransformRules } from '@/shared/components/add-group/hooks/use-transform-rules';
import { MessageTypeEnum } from '@/config/risk.config';

describe('useTransformRules', () => {
  let pushRules;

  beforeEach(() => {
    pushRules = [
      {
        pushRuleId: 1,
        ruleJson: {
          methods: [MessageTypeEnum.EMAIL, MessageTypeEnum.SMS],
          emails: ['<EMAIL>'],
          phones: ['1234567890'],
          pushScope: { riskLevels: ['high', 'medium'] },
          pushTime: '09:00',
        },
      },
      {
        pushRuleId: 2,
        ruleJson: {
          methods: [MessageTypeEnum.EMAIL],
          emails: ['<EMAIL>'],
          phones: [],
          pushScope: { riskLevels: ['low'] },
          pushTime: '10:00',
        },
      },
      {
        pushRuleId: 3,
        ruleJson: {
          methods: [MessageTypeEnum.SMS],
          emails: [],
          phones: ['0987654321'],
          pushScope: { riskLevels: ['high'] },
          pushTime: '11:00',
        },
      },
      {
        pushRuleId: 4,
        ruleJson: {
          methods: [],
          emails: [],
          phones: [],
          pushScope: { riskLevels: [] },
          pushTime: '12:00',
        },
      },
    ];
  });

  it('should handle empty pushRules array', () => {
    const result = useTransformRules([]);
    expect(result.list.value).toEqual([]);
  });

  it('should handle undefined pushRules', () => {
    const result = useTransformRules(undefined);
    expect(result.list.value).toEqual([]);
  });

  it('should handle null pushRules', () => {
    const result = useTransformRules(null);
    expect(result.list.value).toEqual([]);
  });
});
