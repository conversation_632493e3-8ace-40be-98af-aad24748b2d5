import { onMounted, ref } from 'vue';

import { MessageTypeEnum } from '@/config/risk.config';

export const useTransformRules = (pushRules) => {
  const pushAlertRef = ref();
  const list = ref([]);
  const mergeRules = (ruleList) => {
    const output = ruleList.map((rule) => {
      const { ruleJson, pushRuleId } = rule;
      return {
        pushRuleId,
        emailEnabled: ruleJson.methods.includes(MessageTypeEnum.EMAIL),
        smsEnabled: ruleJson.methods.includes(MessageTypeEnum.SMS),
        mailAddress: ruleJson.emails?.[0],
        mobileNumber: ruleJson.phones?.[0],
        riskLevels: ruleJson.pushScope.riskLevels,
        pushTime: ruleJson.pushTime,
      };
    });
    return output;
  };
  onMounted(() => {
    list.value = mergeRules(pushRules);
  });

  return {
    pushAlertRef,
    list,
  };
};
