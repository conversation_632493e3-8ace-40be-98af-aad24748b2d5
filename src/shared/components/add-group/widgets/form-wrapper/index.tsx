import { defineComponent, PropType } from 'vue';
import { Form, Input, Checkbox } from 'ant-design-vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import TimeSelector from '@/shared/components/add-group/widgets/time-selector';
import { emailRegex, phoneRegex } from '@/utils/validator';

import styles from './form-wrapper.module.less';

const SettingForm = defineComponent({
  name: 'SettingForm',
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    return (
      <Form form={this.form} layout="vertical" class={styles.formWrapper} hideRequiredMark>
        <div class={[styles.formItem, styles.pushWay]}>
          <div class={styles.title}>推送方式：</div>
          <div class="flex-1">
            <div class={styles.inline}>
              <Form.Item>
                <Checkbox
                  v-decorator={[
                    'emailEnabled',
                    {
                      valuePropName: 'checked',
                      initialValue: !!this.formData.emailEnabled,
                    },
                  ]}
                >
                  邮箱推送
                </Checkbox>
              </Form.Item>
              <Form.Item v-show={this.form?.getFieldValue('emailEnabled') ?? this.formData.emailEnabled}>
                <Input
                  type="text"
                  placeholder="请输入邮箱"
                  v-decorator={[
                    'mailAddress',
                    {
                      initialValue: this.formData.mailAddress,
                      rules: [
                        {
                          required: this.form?.getFieldValue('emailEnabled') ?? this.formData?.emailEnabled,
                          message: '请输入邮箱',
                        },
                        {
                          pattern: emailRegex,
                          message: '请检查邮箱是否正确',
                        },
                      ],
                    },
                  ]}
                />
              </Form.Item>
            </div>
            <div class={styles.inline}>
              <Form.Item>
                <Checkbox
                  v-decorator={[
                    'smsEnabled',
                    {
                      valuePropName: 'checked',
                      initialValue: !!this.formData.smsEnabled,
                      rules: [
                        {
                          message: '请选择推送方式',
                          validator: (rule, value, callback) => {
                            const hasChecked = this.form?.getFieldValue('emailEnabled') || this.form?.getFieldValue('smsEnabled');
                            return hasChecked ? callback() : callback(false);
                          },
                        },
                      ],
                    },
                  ]}
                >
                  短信推送
                </Checkbox>
              </Form.Item>
              <Form.Item v-show={this.form?.getFieldValue('smsEnabled') ?? this.formData.smsEnabled}>
                <Input
                  type="text"
                  placeholder="请输入手机号码"
                  v-decorator={[
                    'mobileNumber',
                    {
                      initialValue: this.formData.mobileNumber,
                      rules: [
                        {
                          required: this.form?.getFieldValue('smsEnabled') ?? this.formData.smsEnabled,
                          message: '请输入手机号码',
                        },
                        {
                          pattern: phoneRegex,
                          message: '请检查手机号是否正确',
                        },
                      ],
                    },
                  ]}
                />
              </Form.Item>
            </div>
          </div>
        </div>

        <Form.Item label="推送时间：" class={styles.formItem}>
          <TimeSelector
            v-decorator={[
              'pushTime',
              {
                initialValue: this.formData.pushTime,
              },
            ]}
            formData={this.formData.pushTime}
            onChange={(val) => {
              this.form?.setFieldsValue({ pushTime: val });
            }}
          />
        </Form.Item>
        <Form.Item label="推送内容：" class={styles.formItem}>
          <Checkbox.Group
            v-decorator={[
              'riskLevels',
              {
                initialValue: this.formData.riskLevels,
                rules: [{ required: true, message: '请选择风险等级' }],
              },
            ]}
            options={[
              { label: '高风险', value: 2 },
              { label: '中风险', value: 1 },
              { label: '低风险', value: 0 },
            ]}
          />
        </Form.Item>
      </Form>
    );
  },
});

const SettingFormWrapper = Form.create({})(SettingForm);

export default SettingFormWrapper;
