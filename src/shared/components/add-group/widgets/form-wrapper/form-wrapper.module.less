.formWrapper {
  :global {
    .ant-form-item {
      display: flex;
      align-items: flex-start;
      margin: 0;
      padding: 0;
    }

    .ant-form-item-control-wrapper {
      flex: 1;
    }

    .ant-form-item-label {
      height: 32px;
      line-height: 32px;
      width: 86px;
      margin-right: 8px;
      padding: 0;
    }

    .ant-form-item-children {
      height: 32px;
      display: flex;
      align-items: center;
      flex: 1;
    }

    .ant-checkbox-group-item .ant-checkbox + span {
      padding-left: 4px;
      padding-right: 0;
    }

    .ant-checkbox-group-item:not(:last-child) {
      margin-right: 24px;
    }
  }

  .push-way {
    display: flex;

    .title {
      width: 86px;
      font-size: 14px;
      line-height: 22px;
      color: #333;
      margin-top: 12px;
      margin-right: 8px;
    }
  }
}

.inline {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 8px;

  :global{
    .ant-form-item:last-child {
      flex: 1;
    }

    .ant-checkbox + span {
      color: #666;
    }
  }
}

.form-item:not(:first-child) {
  margin-top: 12px;
}