.container {
  border-radius: 4px;
  background: #FAFAFA;
  padding-bottom: 8px;
}

.title {
  height: 32px;
  font-size: 14px;
  line-height: 32px;
  color: #333;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #EBF6FF;
  border-radius: 4px;
  padding-left: 16px;
  padding-right: 8px;

  svg {
    color: #BBB;
    cursor: pointer;

    &:hover {
      color: #128BED;
    }
  }

  &::before {
    content: "";
    width: 4px;
    height: 12px;
    border-radius: 2px;
    background: #128BED;
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.form {
  padding: 8px 16px;
}

.setting-item + .setting-item {
  margin-top: 8px;
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 8px;
  border-top: 1px solid #EEE;
}