import { computed, defineComponent, onMounted, ref } from 'vue';
import { Button, message } from 'ant-design-vue';

import SettingFormWrapper from '@/shared/components/add-group/widgets/form-wrapper';
import QIcon from '@/components/global/q-icon';

import styles from './push-alert.module.less';
import { MessageTypeEnum } from '@/config/risk.config';
import { compact } from 'lodash';

const DEFAULT_SETTING_CONFIG = {
  mailAddress: '',
  mobileNumber: '',
  emailEnabled: 0,
  smsEnabled: 0,
  pushTime: {
    scheduleType: 'DAILY',
    hour: 6,
    isWorkdayOnly: 0,
    weekDays: 0,
    monthDays: 0,
  },
  riskLevels: [2],
};

const PushAlert = defineComponent({
  name: 'PushAlert',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  expose: ['getFormData'],
  setup(props) {
    const limit = 5;
    const settingList = ref<Array<(typeof DEFAULT_SETTING_CONFIG & { index: number }) | unknown>>([]);
    const formRefs = ref<any[]>([]);
    const currentIndex = ref(0);
    const isAddDisabled = computed(() => settingList.value.length >= limit);

    const handleAddSetting = () => {
      if (settingList.value.length >= limit) {
        message.warning('推送设置数量已达到上限');
        return;
      }
      settingList.value.push({ ...DEFAULT_SETTING_CONFIG, index: ++currentIndex.value });
    };

    const handleRemoveSetting = (index: number) => {
      settingList.value.splice(index, 1);
      formRefs.value.splice(index, 1);
    };

    const validateForm = (formRef, i) => {
      return new Promise((resolve, reject) => {
        if (!formRef) {
          resolve(null);
          return;
        }
        const extraData = formRef.formData;
        formRef.validateFields((err, values) => {
          if (err) reject(err);
          resolve({ extraData, values });
        });
      });
    };

    const transformFormData = (formData) => {
      if (!formData) return [];
      const { extraData, values } = formData;
      const ruleJson = {
        methods: compact([values.emailEnabled ? MessageTypeEnum.EMAIL : null, values.smsEnabled ? MessageTypeEnum.SMS : null]),
        emails: values.mailAddress ? [values.mailAddress] : [],
        phones: values.mobileNumber ? [values.mobileNumber] : [],
        pushScope: { riskLevels: values.riskLevels },
        pushTime: values.pushTime,
      };
      return [
        {
          pushRuleId: extraData.pushRuleId,
          ruleJson,
        },
      ];
    };

    const getFormData = async () => {
      const res = await Promise.all(formRefs.value.map(validateForm));
      return res.flatMap((item) => transformFormData(item));
    };

    onMounted(() => {
      if (props.list.length > 0) {
        settingList.value = props.list;
      } else if (settingList.value.length === 0) {
        handleAddSetting();
      }
    });

    return {
      formRefs,
      settingList,
      handleAddSetting,
      handleRemoveSetting,
      isAddDisabled,
      getFormData,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        {this.settingList.map((item: any, index, arr) => {
          return (
            <div class={styles.settingItem} key={item.index ?? item.pushRuleId}>
              <div class={styles.title}>
                <span>推送设置{index + 1}</span>
                <QIcon v-show={arr.length > 1} type="icon-icon_yichu" onClick={() => this.handleRemoveSetting(index)} />
              </div>
              <SettingFormWrapper
                class={styles.form}
                ref={(el) => {
                  if (el) {
                    this.formRefs[index] = el;
                  }
                }}
                formData={this.settingList[index]}
              />
            </div>
          );
        })}
        <div class={styles.addButton}>
          <Button icon="plus" disabled={this.isAddDisabled} onClick={this.handleAddSetting}>
            新增推送设置
          </Button>
        </div>
      </div>
    );
  },
});

export default PushAlert;
