export enum PushScheduleTypeEnum {
  REAL = 'REAL',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
}

// 6-23点
export const TimeOptions = Array.from({ length: 18 }, (_, i) => {
  const label = `${(i + 6).toString().padStart(2, '0')}:00`;
  return {
    label,
    value: i + 6,
  };
});

// 周一-周日
export const WeekOptions = Array.from('一二三四五六日').map((day, i) => {
  return {
    label: `周${day}`,
    value: i + 1,
  };
});
// 每月1号到28号
export const MonthOptions = [
  {
    label: '1日',
    value: 1,
  },
  {
    label: '15日',
    value: 15,
  },
  {
    label: '当月最后一天',
    value: 31,
  },
];
export const SendWayOptions = [
  {
    label: '实时推送',
    value: PushScheduleTypeEnum.REAL,
  },
  {
    label: '每日推送',
    value: PushScheduleTypeEnum.DAILY,
  },
  {
    label: '每周推送',
    value: PushScheduleTypeEnum.WEEKLY,
  },
  {
    label: '每月推送',
    value: PushScheduleTypeEnum.MONTHLY,
  },
];
