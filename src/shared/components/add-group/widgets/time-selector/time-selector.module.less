.timeSelector {
  display: flex;
  align-items: center;
  flex: 1;
}

.selector1 {
  width: 100px !important;

  :global {
    .ant-select-selection {
      border-radius: 2px 0 0 2px;
      background: #F3F7FD;
      border: 1px solid #D8D8D8;
      font-size: 14px;
      line-height: 22px;
      color: #666;
      border-right: 0;
      position: relative;

      &::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        top: 0;
        right: 0;
        z-index: 1;
        background: #EEE;
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }
    }

    .ant-select-selection:not(:disabled):hover {
      &::before {
        content: '';
        background: #3cabfa;
      }
    }

  }
}

.selector2 {
  width: 138px !important;

  :global {
    .ant-select-selection {
      border-color: #EEE;
      border-radius: 0 2px 2px 0;
      border-left: 0;
    }
  }
}

.selector2 + .selector3 {
  flex: 1;
}

.selector3 {
  width: 138px !important;
  margin-left: 8px;
}

.selector1 + .selector2,
.selector1 + .selector3 {
  margin-left: 0;

  :global {
    .ant-select-selection {
      border-left: 0;
      border-radius: 0 2px 2px 0;
      border-color: #D8D8D8;
    }
  }
}