import { defineComponent, reactive } from 'vue';
import { Checkbox, Select } from 'ant-design-vue';

import { MonthOptions, PushScheduleTypeEnum, SendWayOptions, TimeOptions, WeekOptions } from './config';
import styles from './time-selector.module.less';

const TimeSelector = defineComponent({
  name: 'TimeSelector',
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const fieldsValue = reactive({
      scheduleType: props.formData.scheduleType || PushScheduleTypeEnum.DAILY,
      hour: props.formData.hour,
      monthDays: props.formData.monthDays,
      weekDays: props.formData.weekDays,
      isWorkdayOnly: props.formData.isWorkdayOnly,
    });
    const handleChange = () => {
      emit('change', fieldsValue);
    };

    const handleWayChange = () => {
      switch (fieldsValue.scheduleType) {
        case PushScheduleTypeEnum.REAL:
          fieldsValue.monthDays = undefined;
          fieldsValue.weekDays = undefined;
          fieldsValue.hour = undefined;
          break;
        case PushScheduleTypeEnum.DAILY:
          fieldsValue.monthDays = undefined;
          fieldsValue.weekDays = undefined;
          fieldsValue.hour = TimeOptions[0].value;
          break;
        case PushScheduleTypeEnum.MONTHLY:
          fieldsValue.monthDays = MonthOptions[0].value;
          fieldsValue.weekDays = undefined;
          fieldsValue.hour = TimeOptions[0].value;
          break;
        case PushScheduleTypeEnum.WEEKLY:
          fieldsValue.monthDays = undefined;
          fieldsValue.weekDays = WeekOptions[0].value;
          fieldsValue.hour = TimeOptions[0].value;
          break;
        default:
      }
      handleChange();
    };

    return {
      fieldsValue,
      handleChange,
      handleWayChange,
    };
  },
  render() {
    return (
      <div class={styles.timeSelector}>
        <Select
          class={styles.selector1}
          placeholder="请选择"
          options={SendWayOptions}
          v-model={this.fieldsValue.scheduleType}
          onChange={this.handleWayChange}
        />
        {this.fieldsValue.scheduleType === PushScheduleTypeEnum.MONTHLY ? (
          <Select class={styles.selector2} placeholder="请选择" options={MonthOptions} v-model={this.fieldsValue.monthDays} />
        ) : null}
        {this.fieldsValue.scheduleType === PushScheduleTypeEnum.WEEKLY ? (
          <Select class={styles.selector2} placeholder="请选择" options={WeekOptions} v-model={this.fieldsValue.weekDays} />
        ) : null}
        <Select
          class={styles.selector3}
          placeholder="请选择"
          options={TimeOptions}
          v-model={this.fieldsValue.hour}
          onChange={this.handleChange}
          disabled={this.fieldsValue.scheduleType === PushScheduleTypeEnum.REAL}
        />
        {this.fieldsValue.scheduleType === PushScheduleTypeEnum.DAILY ? (
          <Checkbox
            class={styles.selector3}
            checked={!!this.fieldsValue.isWorkdayOnly}
            onChange={(e) => {
              this.fieldsValue.isWorkdayOnly = Number(e.target.checked);
              this.handleChange();
            }}
          >
            仅工作日推送
          </Checkbox>
        ) : null}
      </div>
    );
  },
});

export default TimeSelector;
