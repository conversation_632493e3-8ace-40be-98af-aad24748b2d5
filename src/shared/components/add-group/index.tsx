import { computed, defineComponent, onMounted, ref } from 'vue';
import { FormModel, Input, Popconfirm, Select } from 'ant-design-vue';
import { sortBy } from 'lodash';

import QModal from '@/components/global/q-modal';
import { createPromiseDialog } from '@/components/promise-dialogs';
import QSwitch from '@/components/global/q-switch';
import PushAlert from '@/shared/components/add-group/widgets/push-alert';
import { useTransformRules } from '@/shared/components/add-group/hooks/use-transform-rules';
import { setting, user as userService } from '@/shared/services';
import { useUserStore } from '@/shared/composables/use-user-store';

import styles from './add-group.module.less';

const GroupModal = defineComponent({
  props: {
    params: {
      type: Object,
      default: () => ({
        title: '分组管理',
        models: [],
      }),
    },
  },
  setup(props, { emit }) {
    const { profile } = useUserStore();

    const visible = ref(false);
    const popVisible = ref(false);
    const formRef = ref();
    const modelList = ref<{ label: string; value: number; [key: string]: unknown }[]>([]);
    // 获取模型列表，展示用
    const getModelList = async () => {
      const params = {
        product: 'SAAS_PRO',
        statuses: [1, 2],
        pageSize: 100,
        pageIndex: 1,
        modelType: 2,
      };
      const res = await setting.getModelLists(params);
      modelList.value = res.data.map((v) => ({
        ...v,
        label: v.modelName,
        value: v.modelId,
      }));
    };
    const rules = {
      name: [
        {
          required: true,
          message: '请输入组别名称',
        },
        {
          min: 3,
          max: 15,
          message: '请输入正确的组别名称，长度为3-15个字符',
        },
      ],
      monitorModelId: [
        {
          required: true,
          message: '请选择模型',
        },
      ],
      userIds: [{ required: true, message: '请选择查看权限范围' }],
    };

    const form = ref({
      name: '',
      monitorModelId: undefined,
      pushEnable: 0,
      userIds: [-1],
      transferUserId: undefined,
      ...props.params?.data,
    });

    const { pushAlertRef, list: ruleList } = useTransformRules(props.params?.data?.pushRules || []);
    const handleSubmit = async () => {
      try {
        await formRef.value?.validate();
        let monitorPushRules = form.value.pushRules;
        if (form.value.pushEnable) {
          monitorPushRules = await pushAlertRef.value?.getFormData();
        }
        visible.value = false;
        emit('resolve', { ...form.value, monitorPushRules });
      } catch (e) {
        console.error(e);
      }
      return null;
    };

    const placeholder = ref('请选择模型');

    const isOwner = computed(() => props.params.data?.ownerId === profile.value.userId);
    const isTransferUserEnabled = ref(!!form.value.transferUserId);
    const operatorList = ref<{ label: string; value: number }[]>([]);
    const operatorListWithoutSelf = ref<{ label: any; value: any; disabled: boolean }[]>([]);
    const getOperatorList = async () => {
      const data = await userService.getUserList();
      const res = sortBy(data, (v) => -v.active).map((v) => ({
        label: v.name,
        value: v.userId,
        disabled: v.active === 0,
      }));
      const activeUser = res.filter((v) => !v.disabled);
      operatorList.value = [{ label: '全部员工', value: -1 }, ...res];
      operatorListWithoutSelf.value = activeUser.filter((v) => v.value !== profile.value.userId);
    };

    onMounted(async () => {
      await getModelList();
      await getOperatorList();
      const currentModel = modelList.value.find((item) => item.value === form.value.monitorModelId);
      if (!currentModel && form.value.monitorModelId) {
        form.value.monitorModelId = undefined;
        placeholder.value = `${props.params?.data?.riskModelEntity?.modelName || '未知模型'}(已废弃)`;
      }
      if (form.value.monitorGroupUsers) {
        const res = form.value.monitorGroupUsers?.map((v) => v.userId) || [];
        form.value.userIds = res.length ? res : [-1];
      }
      visible.value = true;
    });

    const handleCancel = () => {
      visible.value = false;
    };

    const changeStatus = () => {
      form.value.monitorStatus = form.value.monitorStatus === 1 ? 2 : 1;
      popVisible.value = false;
    };

    return {
      visible,
      popVisible,
      form,
      formRef,
      modelList,
      rules,
      handleSubmit,
      handleCancel,
      pushAlertRef,
      ruleList,
      placeholder,
      changeStatus,
      operatorList,
      operatorListWithoutSelf,
      isTransferUserEnabled,
      isOwner,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            title: this.params?.title,
            visible: this.visible,
            destroyOnClose: true,
            wrapClassName: styles.modalContainer,
          },
          on: {
            cancel: this.handleCancel,
            ok: this.handleSubmit,
          },
        }}
        viewportDistance={250}
      >
        <FormModel
          ref="formRef"
          hideRequiredMark={true}
          rules={this.rules}
          props={{ model: this.form }}
          layout="horizontal"
          label-col={{ flex: '64px' }}
          colon={false}
          wrapper-col={{ flex: 'auto' }}
          labelAlign="left"
        >
          <FormModel.Item class="ant-row-flex" label="组别名称" prop="name">
            <Input
              placeholder="请输入组别名称"
              v-model={this.form.name}
              maxLength={15}
              allowClear={true}
              disabled={this.params.data?.groupType === 1}
            />
          </FormModel.Item>
          <FormModel.Item class="ant-row-flex" label="模型名称" prop="monitorModelId">
            <Select
              placeholder={this.placeholder}
              v-model={this.form.monitorModelId}
              options={this.modelList}
              onChange={() => {
                this.form.monitorStatus = 1;
              }}
            />
          </FormModel.Item>
          {this.isOwner || !this.params.isEdit ? (
            <FormModel.Item class="ant-row-flex" label="查看权限" prop="userIds">
              <Select
                mode="multiple"
                placeholder="请选择查看权限范围"
                showArrow={true}
                v-model={this.form.userIds}
                options={this.operatorList}
                onChange={(val) => {
                  if (!val.includes(-1)) return;
                  if (val?.[val.length - 1] === -1) {
                    this.form.userIds = [-1];
                  } else {
                    this.form.userIds = val.filter((v) => v !== -1);
                  }
                }}
              />
            </FormModel.Item>
          ) : null}
          {this.isOwner ? (
            <FormModel.Item class="ant-row-flex" label="权限移交" prop="transferUserId">
              <div>
                <QSwitch
                  checked={this.isTransferUserEnabled}
                  onChange={(checked) => {
                    this.isTransferUserEnabled = checked;
                    if (!checked) {
                      this.form.transferUserId = undefined;
                    }
                  }}
                />
                <div>
                  <Select
                    v-show={this.isTransferUserEnabled}
                    placeholder="请选择移交人(权限会转移至该用户)"
                    v-model={this.form.transferUserId}
                    options={this.operatorListWithoutSelf}
                  />
                </div>
              </div>
            </FormModel.Item>
          ) : null}
          <FormModel.Item class="ant-row-flex" label="监控状态" v-show={this.form.riskModelEntity}>
            <Popconfirm
              visible={this.popVisible}
              overlayStyle={{ width: '250px' }}
              onText="确认"
              cancelText="取消"
              onCancel={() => {
                this.popVisible = false;
              }}
              onConfirm={this.changeStatus}
            >
              <div slot="title">关闭该分组监控将不再产生和推送企业动态，可能导致业务风控盲区。请确认是否要执行该操作？</div>
              <QSwitch
                disabled={!this.form.monitorModelId}
                checked={this.form.monitorStatus === 1}
                onChange={() => {
                  if (this.form.monitorStatus === 2) {
                    this.changeStatus();
                    return;
                  }
                  this.popVisible = true;
                }}
              />
            </Popconfirm>
          </FormModel.Item>
          <FormModel.Item class="ant-row-flex" label="监控推送">
            <QSwitch
              checked={!!this.form.pushEnable}
              onChange={(checked) => {
                this.form.pushEnable = Number(checked);
              }}
            />
          </FormModel.Item>
        </FormModel>
        <PushAlert v-show={!!this.form.pushEnable} ref="pushAlertRef" list={this.ruleList} />
      </QModal>
    );
  },
});

export default GroupModal;

export const openGroupModal = createPromiseDialog(GroupModal);
