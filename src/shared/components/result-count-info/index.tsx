import { defineComponent, PropType } from 'vue';
import { Icon } from 'ant-design-vue';

import styles from './result-count-info.module.less';

type ColorTheme = 'success' | 'fail' | 'warning';

const ResultCountInfo = defineComponent({
  name: 'ResultCountInfo',
  props: {
    isLoading: {
      type: Boolean,
      default: false,
    },
    config: {
      type: Array as PropType<{ prefix?: string; suffix?: string; count: number; theme?: ColorTheme }[]>,
      required: true,
    },
  },
  render() {
    return (
      <div class={styles.resultInfo}>
        {this.config.map((item) => {
          return (
            <div class="flex items-center" style={{ gap: '4px' }}>
              {item.prefix}
              <Icon v-show={this.isLoading} type="sync" spin />
              <em class={styles[item.theme || 'default']} v-show={!this.isLoading}>
                {item.count}
              </em>
              {item.suffix}
            </div>
          );
        })}
      </div>
    );
  },
});

export default ResultCountInfo;
