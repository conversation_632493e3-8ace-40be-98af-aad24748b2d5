import { defineComponent } from 'vue';

import Icon from '../icon';
import styles from './download-link.module.less';

const DownloadLink = defineComponent({
  functional: true,
  render(h, { children, data }) {
    return (
      <a class={styles.container} {...data}>
        <Icon class={styles.icon} type="icon-xiazai" />
        <span>{children}</span>
      </a>
    );
  },
});

export default DownloadLink;
