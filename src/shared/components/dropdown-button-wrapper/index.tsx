import { PropType, defineComponent, ref } from 'vue';
import { Button, Dropdown, Menu, Popconfirm } from 'ant-design-vue';
import { pick } from 'lodash';

import QIcon from '@/components/global/q-icon';

const DEFAULTITEMS = [
  { key: 'batch', label: '删除选中' },
  { key: 'all', label: '删除全部' },
];
const DropdownButtonWrapper = defineComponent({
  props: {
    // 按钮文案
    btnText: {
      type: String,
      default: '',
    },
    totalCount: {
      type: Number,
      default: 0,
    },
    // 是否需要popover
    needPopConfirm: {
      type: Boolean,
      default: true,
    },
    // 下拉菜单的选项
    menuItems: {
      type: Array as PropType<any[]>,
      default: () => DEFAULTITEMS,
    },
    // popover的确认文案
    okText: {
      type: String,
      default: '确认',
    },
    // popover的取消文案
    cancelText: {
      type: String,
      default: '取消',
    },
    // popover的位置
    placement: {
      type: String,
      default: 'bottomLeft',
    },
    // popover的提示文案
    confirmText: {
      type: String,
      default: '此操作不可恢复，您确认删除该企业吗？',
    },
    // 勾选的数据数量
    selectIdlength: {
      type: Number,
      default: 0,
    },
  },
  setup() {
    const popVisible = ref(false);
    const dropVisible = ref(false);
    const key = ref('');
    const close = () => {
      popVisible.value = false;
      dropVisible.value = false;
    };
    return {
      popVisible,
      dropVisible,
      key,
      close,
    };
  },
  render() {
    const { popVisible, needPopConfirm, confirmText, totalCount } = this;
    return (
      <Popconfirm
        visible={popVisible && needPopConfirm}
        onConfirm={() => {
          this.$emit('confirm', this.key);
          this.close();
        }}
        onCancel={() => this.close()}
        {...{ props: pick(this, ['okText', 'cancelText', 'placement']) }}
        scopedSlots={{
          title: () => <div>{confirmText}</div>,
        }}
      >
        <Dropdown
          trigger={['hover']}
          disabled={!totalCount}
          {...{ props: this.$attrs }}
          onVisibleChange={(val) => {
            this.dropVisible = val;
          }}
        >
          {/* 如果传入了slot，不然用自己的 */}
          <template slot="overlay">
            {this.$slots.overlay ? (
              this.$slots.overlay
            ) : (
              <Menu slot="overlay">
                {this.menuItems.map((item) => (
                  <Menu.Item
                    key={item.key}
                    disabled={this.selectIdlength === 0 && ['batch', 'exportByIds'].includes(item.key)}
                    v-debounceclick={() => {
                      this.key = item.key;
                      // 只有需要显示popover的时候才会显示
                      if (needPopConfirm) {
                        this.popVisible = true;
                      } else {
                        this.$emit('confirm', item.key);
                      }
                    }}
                  >
                    {item.label}
                  </Menu.Item>
                ))}
              </Menu>
            )}
          </template>
          <Button>
            <span>{this.btnText}</span>
            <QIcon
              style={{
                fontSize: '16px',
                transform: this.dropVisible ? 'scaleY(0.75)' : 'scaleY(1)',
                marginLeft: '0',
              }}
              type={this.dropVisible ? 'icon-a-shixinshang1x' : 'icon-a-shixinxia1x1'}
            ></QIcon>
          </Button>
        </Dropdown>
      </Popconfirm>
    );
  },
});

export default DropdownButtonWrapper;
