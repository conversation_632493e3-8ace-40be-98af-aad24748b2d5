import { mount, shallowMount } from '@vue/test-utils';

import QIcon from '@/components/global/q-icon';

import DropdownButtonWrapper from '..';

describe('DropdownButtonWrapper', () => {
  afterEach(() => {
    vi.resetAllMocks();
  });
  it('renders nothing if totalCount is 0', () => {
    const wrapper = shallowMount(DropdownButtonWrapper, {
      propsData: {
        totalCount: 0,
      },
    });
    expect(wrapper.isEmpty()).toBe(false);
  });

  it('renders the button and dropdown when totalCount is greater than 0', () => {
    const wrapper = shallowMount(DropdownButtonWrapper, {
      propsData: {
        totalCount: 5,
        menuItems: [{ key: 'batch', label: 'Move selected' }],
      },
    });
    const dropdown = wrapper.findComponent({ name: 'ADropdown' });
    const button = wrapper.findComponent({ name: 'AButton' });
    expect(button.exists()).toBe(true);
    expect(dropdown.exists()).toBe(true);
  });

  it('closes the popconfirm when the cancel button is clicked', async () => {
    const wrapper = shallowMount(DropdownButtonWrapper, {
      propsData: {
        totalCount: 5,
        menuItems: [{ key: 'batch', label: 'Move selected' }],
      },
    });
    const popup = wrapper.findComponent({ name: 'APopconfirm' });
    popup.vm.$emit('cancel');
    expect(wrapper.vm.popVisible).toBeFalsy();
    popup.vm.$emit('confirm');
    expect(wrapper.emitted('confirm')).toBeTruthy();
    wrapper.setProps({ needPopConfirm: false });
    expect(popup.vm.$props.visible).toBeFalsy();
  });

  it('should change icon when dropdown is visible or not', async () => {
    const wrapper = shallowMount(DropdownButtonWrapper, {
      propsData: {
        totalCount: 5,
        menuItems: [{ key: 'batch', label: 'Move selected' }],
      },
    });
    const icon = wrapper.findComponent(QIcon);
    const dropdown = wrapper.findComponent({ name: 'ADropdown' });
    expect(icon.attributes('type')).toBe('icon-a-shixinxia1x1');
    await wrapper.vm.$nextTick();
    dropdown.vm.$emit('visibleChange', true);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.dropVisible).toBeTruthy();
    // expect(icon.attributes('type')).toBe('icon-a-shixinshang1x');
  });

  it('should render correct title when has slot', async () => {
    const wrapper = mount(DropdownButtonWrapper, {
      propsData: {
        totalCount: 5,
        menuItems: [{ key: 'batch', label: 'Move selected' }],
        confirmText: '<a href="#" id="test">确认链接</a>',
        needPopConfirm: false,
      },
    });
    await wrapper.setData({
      popVisible: true,
    });
    const popup = wrapper.findComponent({ name: 'APopconfirm' });

    expect(popup.vm.$props.visible).toBeFalsy();
  });
});
