@import '@/styles/token.less';

@gap: 12px;

.container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .content {
    display: flex;
    align-items: center;
    gap: @gap;
  }

  &.vertical {
    .content {
      flex-direction: column;
    }
  }

  &.horizontal {
    .content {
      flex-direction: row;
    }
  }

  .description {
    color: @qcc-color-black-300;
    font-size: @qcc-text-md;
    line-height: @qcc-leading-3;
  }
}
