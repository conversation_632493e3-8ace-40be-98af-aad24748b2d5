import { defineComponent } from 'vue';
import type { PropType } from 'vue';

import IconDefault from '../assets/images/icon_empty_default.svg';
import IconFinance from '../assets/images/icon_empty_finance.svg';
import IconNotification from '../assets/images/icon_empty_notification.svg';
import IconSearch from '../assets/images/icon_empty_search.svg';
import IconText from '../assets/images/icon_empty_text.svg';
import styles from './empty.module.less';

export type EmptyType = 'default' | 'finance' | 'search' | 'notification' | 'text';
export type EmptyDirection = 'vertical' | 'horizontal';

const ICONS_MAP = {
  default: IconDefault,
  finance: IconFinance,
  search: IconSearch,
  notification: IconNotification,
  text: IconText,
};

const Empty = defineComponent({
  functional: true,
  props: {
    /**
     * 类型
     */
    type: {
      type: String as PropType<EmptyType>,
      default: 'default',
    },
    direction: {
      type: String as PropType<EmptyDirection>,
      default: 'vertical',
    },
    description: {
      type: [String, Object],
      required: false,
    },
    height: {
      type: Number,
      required: false,
    },
    iconSize: {
      type: String,
      default: '100',
    },
  },
  render(h, { props, children }) {
    return (
      <div
        class={[styles.container, styles[props.direction]]}
        style={{
          height: props.height ? `${props.height}px` : '100%',
        }}
      >
        <div class={styles.content}>
          <img src={ICONS_MAP[props.type]} width={props.iconSize} height={props.iconSize} />

          {props.description ? <div class={styles.description}>{props.description}</div> : null}
        </div>

        {children}
      </div>
    );
  },
});

export default Empty;
