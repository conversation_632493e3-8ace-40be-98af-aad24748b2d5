// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`BatchUploadRennder > render has permission 1`] = `
<div class="container">
  <div class="container" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
    <div class="box">
      <div class="title">输入文本</div>
      <div data-testid="text-input" class="container lighter" style="height: 134px;">
        <div class="icon">
          <q-icon-stub type="icon-wenbenniantie"></q-icon-stub>
        </div>
        <div class="placeholder">
          <p class="text">
          <div>
            <div class="faker-blink"></div> 点击输入或粘贴企业名录
          </div>
          </p>
          <p class="hint" style="display: none;"></p>
        </div>
      </div>
    </div>
    <div class="description">
      <ul>
        <li>输入文本最多支持&nbsp;<em>50</em>&nbsp;家企业排查</li>
        <li>请按格式输入企业或产品名称</li>
        <li>支持自动模糊匹配对应企业</li>
      </ul>
    </div>
  </div>
  <div class="container" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
    <div class="box">
      <div class="title">上传文档</div>
      <aupload-stub type="drag" name="file" action="aaaaa" data="[object Object]" accept=".xls,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" beforeupload="[Function]" listtype="text" supportserverrender="true" style="height: 134px;" class="container lighter">
        <div class="icon">
          <localereceiver-stub componentname="Icon" style="display: none;"><i aria-label="undefined: loading" class="anticon anticon-loading">
              <antdicon-stub type="loading-o" focusable="false" class="anticon-spin"></antdicon-stub>
            </i></localereceiver-stub>
          <q-icon-stub type="icon-shangchuanwendang"></q-icon-stub>
        </div>
        <div class="placeholder">
          <p class="text">点击或拖拽文件到此上传</p>
          <p class="hint" style="display: none;"></p>
        </div>
      </aupload-stub>
    </div>
    <div class="description">
      <ul>
        <li><a href="https://qcc-static.qcc.com/rover/public/templates/import/批量排查企业导入模版.xlsx" download="批量排查模版.xlsx" class="container">
            <q-icon-stub type="icon-xiazai"></q-icon-stub><span>下载模板</span>
          </a><span>按照样例格式编辑表格，请勿增减列</span></li>
        <li>上传文件大小不超过2M，仅支持Excel格式</li>
        <li>上传文档最多支持&nbsp;<em>5000</em>&nbsp;家企业排查</li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`BatchUploadRennder > render hasno permission 1`] = `
<div class="container">
  <div class="container" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
    <div class="box">
      <div class="title">输入文本</div>
      <div data-testid="text-input" class="container lighter" style="height: 134px;">
        <div class="icon">
          <q-icon-stub type="icon-wenbenniantie"></q-icon-stub>
        </div>
        <div class="placeholder">
          <p class="text">
          <div>
            <div class="faker-blink"></div> 点击输入或粘贴企业名录
          </div>
          </p>
          <p class="hint" style="display: none;"></p>
        </div>
      </div>
    </div>
    <div class="description">
      <ul>
        <li>输入文本最多支持&nbsp;<em>50</em>&nbsp;家企业排查</li>
        <li>请按格式输入企业或产品名称</li>
        <li>支持自动模糊匹配对应企业</li>
      </ul>
    </div>
  </div>
  <div class="container" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
    <div class="box">
      <div class="title">上传文档</div>
      <aupload-stub type="drag" name="file" action="aaaaa" data="[object Object]" accept=".xls,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" beforeupload="[Function]" listtype="text" supportserverrender="true" style="height: 134px;" class="container lighter">
        <div class="icon">
          <localereceiver-stub componentname="Icon" style="display: none;"><i aria-label="undefined: loading" class="anticon anticon-loading">
              <antdicon-stub type="loading-o" focusable="false" class="anticon-spin"></antdicon-stub>
            </i></localereceiver-stub>
          <q-icon-stub type="icon-shangchuanwendang"></q-icon-stub>
        </div>
        <div class="placeholder">
          <p class="text">点击或拖拽文件到此上传</p>
          <p class="hint" style="display: none;"></p>
        </div>
      </aupload-stub>
    </div>
    <div class="description">
      <ul>
        <li><a href="https://qcc-static.qcc.com/rover/public/templates/import/批量排查企业导入模版.xlsx" download="批量排查模版.xlsx" class="container">
            <q-icon-stub type="icon-xiazai"></q-icon-stub><span>下载模板</span>
          </a><span>按照样例格式编辑表格，请勿增减列</span></li>
        <li>上传文件大小不超过2M，仅支持Excel格式</li>
        <li>上传文档最多支持&nbsp;<em>5000</em>&nbsp;家企业排查</li>
      </ul>
    </div>
  </div>
</div>
`;
