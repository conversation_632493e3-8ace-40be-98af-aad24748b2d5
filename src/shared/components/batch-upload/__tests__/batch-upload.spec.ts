import { mount, shallowMount } from '@vue/test-utils';

import BatchUpload from '..';

vi.mock('@/shared/composables/use-permission', () => {
  return {
    __esModule: true,
    hasPermission: vi
      .fn()
      .mockImplementationOnce(() => false)
      .mockImplementationOnce(() => true)
      .mockImplementationOnce(() => true),
  };
});

describe('BatchUploadRennder', () => {
  // 第三方导入没权限
  test('render hasno permission', () => {
    const wrapper = shallowMount(BatchUpload, {
      propsData: {
        action: 'aaaaa',
      },
    });
    const frameWrapper = wrapper.findComponent({ ref: 'batch_upload_frames' });
    expect(frameWrapper.element.childElementCount).toBe(2);
    expect(wrapper).toMatchSnapshot();
  });

  // 第三方导入有权限
  test('render has permission', () => {
    const wrapper = shallowMount(BatchUpload, {
      propsData: {
        action: 'aaaaa',
      },
    });
    const frameWrapper = wrapper.findComponent({ ref: 'batch_upload_frames' });
    expect(frameWrapper.element.childElementCount).toBe(2);
    expect(wrapper).toMatchSnapshot();
  });

  // test emit
  test('update', async () => {
    const wrapper = shallowMount(BatchUpload, {
      propsData: {
        action: 'aaaaa',
      },
    });
    await wrapper.vm.update({});
    expect(wrapper.emitted('updateData')).toBeTruthy();
  });

  // test onsuccess
  test('onsuccess', async () => {
    const wrapper = mount(BatchUpload, {
      propsData: {
        action: 'aaaaa',
      },
    });
    const spyFn = vi.spyOn(wrapper.vm, 'update');
    const fileUpload = wrapper.findComponent({ ref: 'fileUpload' });
    fileUpload.vm.$emit('success', 'mockFn');
    expect(spyFn).toBeCalled();
    expect(wrapper.emitted('updateData')).toBeTruthy();
  });

  // test download
  test('download', async () => {
    const wrapper = mount(BatchUpload, {
      propsData: {
        action: 'aaaaa',
      },
    });
    const spyFn = vi.spyOn(wrapper.vm, '$track');
    const downloadLink = wrapper.find('a[download]');
    await downloadLink.trigger('click');
    expect(spyFn).toBeCalled();
  });
});
