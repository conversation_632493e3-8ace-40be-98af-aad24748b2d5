import { defineComponent, PropType } from 'vue';

import BatchUploadFrame from '@/shared/components/batch-upload-frame';
import { IMPORT_COMPANY_TEMPLATE_URL } from '@/config';
import { createTrackEvent } from '@/config/tracking-events';

import FileUpload from '../file-upload';
import TextUpload from '../text-upload';
import styles from './batch-upload.module.less';
import DownloadLink from '../download-link';

const BatchUpload = defineComponent({
  name: 'BatchUpload',
  props: {
    /**
     * 上传地址
     */
    action: {
      type: [String, Function],
      required: true,
    },
    beforeFileUpload: {
      type: Function as PropType<(file: File) => Promise<boolean>>,
      required: false,
    },
  },
  setup(props, { emit }) {
    const update = (data) => {
      emit('updateData', data);
    };

    return {
      update,
    };
  },
  render() {
    return (
      <div ref="batch_upload_frames" class={styles.container}>
        <BatchUploadFrame title="输入文本">
          <TextUpload height="134px" theme="lighter" onSuccess={this.update}>
            <div slot="placeholder">
              <div class="faker-blink"></div>
              {` 点击输入或粘贴企业名录`}
            </div>
          </TextUpload>

          <ul slot="description">
            <li>
              输入文本最多支持&nbsp;<em>50</em>&nbsp;家企业排查
            </li>
            <li>请按格式输入企业或产品名称</li>
            <li>支持自动模糊匹配对应企业</li>
          </ul>
        </BatchUploadFrame>
        <BatchUploadFrame title="上传文档">
          <FileUpload
            ref="fileUpload"
            action={this.action}
            height="134px"
            placeholder="点击或拖拽文件到此上传"
            showUploadList={false}
            beforeFileUpload={this.beforeFileUpload}
            onSuccess={(e) => {
              this.update({ toDetail: true, ...e });
              this.$track(createTrackEvent(6208, '批量排查', '上传文档成功'));
            }}
            theme="lighter"
          />
          <ul slot="description">
            <li>
              <DownloadLink
                href={IMPORT_COMPANY_TEMPLATE_URL}
                download="批量排查模版.xlsx"
                onClick={() => {
                  this.$track(createTrackEvent(6208, '批量排查', '下载模板'));
                }}
              >
                下载模板
              </DownloadLink>
              <span>按照样例格式编辑表格，请勿增减列</span>
            </li>
            <li>上传文件大小不超过2M，仅支持Excel格式</li>
            <li>
              上传文档最多支持&nbsp;<em>5000</em>&nbsp;家企业排查
            </li>
          </ul>
        </BatchUploadFrame>
      </div>
    );
  },
});

export default BatchUpload;
