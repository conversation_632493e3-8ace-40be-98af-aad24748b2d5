import { <PERSON><PERSON>, Drawer } from 'ant-design-vue';
import AntPortalDirective from 'ant-design-vue/es/_util/portalDirective';
import Vue, { defineComponent } from 'vue';
import type { PropType } from 'vue';

import { createFunctionalEventEmitter } from '../_helpers/functional-component';
import Icon from '../icon';

// NOTE: 弹窗类组件需要 `ant-portal` 指令支持
// if (isVue2 && Vue2) {
//   Vue2.use(AntPortalDirective);
// }
Vue.use(AntPortalDirective);

type ModalConfirmType = 'primary' | 'default' | 'danger';

const BaseDrawer = defineComponent({
  functional: true,
  props: {
    title: {
      type: String,
      required: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [Number, String] as PropType<string | number>,
      default: 480,
    },
    /**
     * 确定按钮文案
     */
    okText: {
      type: String,
      default: '确定',
    },
    /**
     * 取消按钮文案
     */
    cancelText: {
      type: String,
      default: '取消',
    },
    /**
     * 确定按钮加载态
     */
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    /**
     * 确定按钮加载态
     */
    confirmType: {
      type: String as PropType<ModalConfirmType>,
      default: 'primary',
    },
    /**
     * 是否显示默认 "确定"，"取消" 按钮
     */
    hasFooter: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示蒙板
     */
    hasMask: {
      type: Boolean,
      default: true,
    },
    /**
     * 点击蒙层时是否关闭
     */
    maskClosable: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示关闭按钮
     */
    closable: {
      type: Boolean,
      default: true,
    },
    /**
     * 关闭弹窗是否销毁组件
     */
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    wrapClassName: {
      type: String,
      default: '',
    },
  },
  /**
   * @slot default
   */

  /**
   * 操作栏脚注插槽
   * @slot footnote
   */

  /**
   * 自定义操作栏
   * @slot footer
   */
  render(h, ctx) {
    const { props, listeners } = ctx;
    const slots = ctx.slots();
    const emitters = createFunctionalEventEmitter(listeners);
    const handleChange = emitters('change');
    const handleClose = () => {
      handleChange(false);
      emitters('close');
    };
    const handleFullyClose = (isVisible: boolean) => {
      if (!isVisible) {
        emitters('fullyClose')();
      }
    };
    const handleCancel = emitters('cancel');
    const handleOk = emitters('ok');
    const hasTitle = !!(slots?.title || props.title);

    return (
      <Drawer
        visible={props.visible}
        width={props.width}
        mask={props.hasMask}
        maskClosable={props.maskClosable}
        destroyOnClose={props.destroyOnClose}
        closable={false}
        onClose={handleClose}
        afterVisibleChange={handleFullyClose}
        wrapClassName={props.wrapClassName}
      >
        <div
          class={{
            'ant-drawer-body-inner': true,
            'ant-drawer-body-inner--whd': hasTitle, // with-header
            'ant-drawer-body-inner--wft': props.hasFooter, // with-footer
          }}
        >
          {slots.default}
        </div>

        {props.hasFooter ? (
          <div class="ant-drawer-footer">
            <div class="ant-modal-footer-inner">
              <div>{slots.footnote}</div>
              {slots.footer || (
                <div>
                  <Button onClick={handleCancel}>{props.cancelText}</Button>
                  <Button onClick={handleOk} type={props.confirmType} loading={props.confirmLoading}>
                    {props.okText}
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : null}

        {/* Title */}
        {hasTitle ? (
          <template slot="title">
            <div class="ant-drawer-header-inner">
              <div>{slots?.title || props.title}</div>
            </div>
          </template>
        ) : null}

        {/* Close */}
        <button v-show={props.closable} aria-label="Close" class="ant-drawer-close" onClick={handleClose}>
          <span class="ant-drawer-close-x">
            <Icon type="icon-tanchuangguanbi" />
          </span>
        </button>
      </Drawer>
    );
  },
});

// BaseDrawer.emits = ['change', 'close', 'cancel', 'ok', 'fullyClose'];
BaseDrawer.model = {
  prop: 'visible',
  event: 'change',
};

export default BaseDrawer;
