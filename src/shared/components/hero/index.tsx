import { defineComponent } from 'vue';

import styles from './hero.module.less';

const Hero = defineComponent({
  functional: true,
  props: {
    title: {
      type: String,
      required: false,
    },
    width: {
      type: String,
      default: '100%',
    },
  },
  render(h, { props, slots }) {
    const { title, default: children } = slots();
    return (
      <div class={styles.container}>
        <div class={styles.wrapper} style={{ width: props.width }}>
          <header class={styles.header}>{title || <h3 class={styles.title}>{props.title}</h3>}</header>
          <div class={styles.section}>{children}</div>
        </div>
      </div>
    );
  },
});

export default Hero;
