// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Hero组件 > 在没有传入标题时使用props中的title 1`] = `
<div class="container">
  <div class="wrapper" style="width: 100%;">
    <header class="header">
      <h3 class="title">Props标题</h3>
    </header>
    <div class="section">
      <p>测试内容</p>
    </div>
  </div>
</div>
`;

exports[`Hero组件 > 渲染标题和子组件 1`] = `
<div class="container">
  <div class="wrapper" style="width: 100%;">
    <header class="header">
      <h1>测试标题</h1>
    </header>
    <div class="section">
      <p>测试内容</p>
    </div>
  </div>
</div>
`;
