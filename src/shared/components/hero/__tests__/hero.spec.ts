import { shallowMount } from '@vue/test-utils';

import Hero from '..';

describe('Hero组件', () => {
  it('渲染标题和子组件', () => {
    const wrapper = shallowMount(Hero, {
      slots: {
        title: '<h1>测试标题</h1>',
        default: '<p>测试内容</p>',
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('在没有传入标题时使用props中的title', () => {
    const wrapper = shallowMount(Hero, {
      propsData: {
        title: 'Props标题',
      },
      slots: {
        default: '<p>测试内容</p>',
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
