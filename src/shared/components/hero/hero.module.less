@import '@/styles/token';

.container {
  background-image: url('./images/bg_hero_lt.png'), url('./images/bg_hero_lb.png'),
    url('./images/bg_hero_rt.png'), url('./images/bg_hero_rb.png'),
    linear-gradient(180deg, #08f 2%, #99d3ff 68%, #fff);
  background-repeat: no-repeat, no-repeat, no-repeat, no-repeat, repeat-x;
  background-position: 0 0, 15px 46.54px, calc(100% + 170px) 0, calc(100% + 45px) 109px;
  border-radius: @qcc-border-radius-middle;

  .wrapper {
    padding: 50px 16px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
  }

  .title {
    font-size: 38px;
    line-height: 1;
    font-weight: bold;
    color: @qcc-color-white;
    text-shadow: 1.35px 1.35px 0 #002fa3;
  }

  .header {
    margin-bottom: 36px;
    text-align: center;
  }

  .section {
    > *:not(:last-child) {
      margin-bottom: 20px;
    }
  }
}
