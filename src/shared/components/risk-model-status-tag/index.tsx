import { defineComponent, PropType } from 'vue';

import {
  DataModelDistributeEnum,
  DataStatusEnum,
  RiskModelStatusEnumMap,
  RiskModelStatusEnumThemeMap,
} from '@/shared/config/risk-model.config';

import styles from './risk-model-status-tag.module.less';

const RiskModelStatusTag = defineComponent({
  name: 'RiskModelStatusTag',
  props: {
    status: {
      type: Number as PropType<DataStatusEnum>,
      required: false,
    },
    distributeStatus: {
      type: Number as PropType<DataModelDistributeEnum>,
      required: false,
    },
  },
  render() {
    // 如果分发状态时启动和试用的时候，就取模型状态，否则取分发状态
    let status;
    if (!this.distributeStatus) {
      status = this.status;
    } else if ([DataModelDistributeEnum.Deprecated, DataModelDistributeEnum.Disable].includes(this.distributeStatus)) {
      status = this.distributeStatus === 3 ? 4 : 0;
    } else {
      status = this.status;
    }
    const label = RiskModelStatusEnumMap[status];
    const theme = RiskModelStatusEnumThemeMap[status];
    if (this.status === undefined || !label) {
      return null;
    }

    return <div class={[styles.container, styles[theme]]}>{label}</div>;
  },
});

export default RiskModelStatusTag;
