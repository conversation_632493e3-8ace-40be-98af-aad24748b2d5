export const createChartLine = (direction: 'left' | 'right' | 'none') => {
  switch (direction) {
    case 'left':
      return {
        sourceArrow: 'triangle',
        targetArrow: 'none',
      };
    case 'right':
      return {
        sourceArrow: 'none',
        targetArrow: 'triangle',
      };
    default:
      return {
        sourceArrow: 'none',
        targetArrow: 'none',
      };
  }
};

export const createChartData = (item) => {
  const nodes = [
    {
      data: { id: item.personId, label: item.personName, type: 'person' },
    },
    {
      data: { id: item.relatedCompanyId, label: item.relatedCompanyName, type: 'company' },
    },
    {
      data: { id: item.companyId, label: item.companyName, type: 'company' },
    },
  ].filter((node) => node.data.id);

  const edges = [
    {
      data: {
        id: `${item.personId}_1`,
        source: item.personId,
        target: item.relatedCompanyId,
        label: item.relatedJob || item.relatedStockPercent,
        ...createChartLine('right'),
      },
    },
    {
      data: {
        id: `${item.personId}_2`,
        source: item.personId,
        target: item.companyId,
        label: item.job || item.stockPercent || '投资',
        ...createChartLine('right'),
      },
    },
    {
      data: {
        // 如果是person则不显示
        id: item.personId ? null : `company_edge`,
        source: item.direction > 0 ? item.companyId : item.companyKeynoRelated,
        target: item.direction > 0 ? item.companyKeynoRelated : item.companyId,
        label: item.stockPercent || item.role,
        ...createChartLine('right'),
      },
    },
  ].filter((edge) => edge.data.source && edge.data.target && edge.data.id);

  return {
    nodes: item.direction > 0 ? nodes : nodes.reverse(),
    edges,
  };
};
