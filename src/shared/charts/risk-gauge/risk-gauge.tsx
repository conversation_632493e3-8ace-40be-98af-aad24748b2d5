import _ from 'lodash';
import { defineComponent, PropType, unref } from 'vue';

import Icon from '@/shared/components/icon';
import { RiskColor } from '@/config';
import { useI18n } from '@/shared/composables/use-i18n';

import Gauge from './gauge';
import styles from './risk-gauge.module.less';

interface Legend {
  level: string;
  color: string;
  label: string;
}

/**
 * 风险仪表盘
 */
const RiskGauge = defineComponent({
  functional: true,
  props: {
    level: {
      type: String,
      default: 'middle',
    },
    legends: {
      type: Array as PropType<Legend[]>,
      default: () => [
        {
          level: 'high',
          color: unref(RiskColor).hight,
          label: 'High Risk',
        },
        {
          level: 'middle',
          color: unref(RiskColor).middle,
          label: 'Medium Risk',
        },
        {
          level: 'low',
          color: unref(RiskColor).low,
          label: 'Low Risk',
        },
      ],
    },
    dimensions: {
      type: Array as PropType<any>,
      default: () => [],
    },
  },
  render(h, { props }) {
    const currentLevel = _.find(props.legends, { level: props.level }) as Legend;
    const { tc } = useI18n();
    return (
      <div class={styles.container}>
        <div class={styles.legend}>
          <ul>
            {props.legends.map(({ level, label, color }) => {
              return (
                <li key={level} class={styles[level]}>
                  <i
                    style={{
                      color,
                    }}
                  />
                  <span>{tc(label)}</span>
                </li>
              );
            })}
          </ul>
        </div>
        <div
          class={[styles.chart, styles[currentLevel.level]]}
          style={{
            color: currentLevel.color,
          }}
        >
          <div style={{ position: 'absolute' }}>
            <Gauge />
          </div>
          <div class={styles.label}>{tc(currentLevel.label)}</div>
          <svg class={styles.svg} viewBox="0 0 200 200">
            <circle class={styles.circle} cx="50%" cy="50%" r="50%" />
            <polygon points="100,19 95,32 105,32" class={styles.pointer} />
          </svg>
        </div>
        <div class={styles.info}>
          <ul>
            {props.dimensions.map(({ groupKey, level }) => {
              return (
                <li
                  key={groupKey}
                  class={{
                    [styles.risk]: level > 0,
                  }}
                >
                  <Icon class={styles.icon} type={level > 0 ? 'icon-zhuyi' : 'icon-a-1hongdianicon'} />
                  <span>{tc(groupKey)}</span>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    );
  },
});

export default RiskGauge;
