@import '@/styles/token.less';

.container {
  display: flex;
  justify-content: space-between;
  border: 1px solid #e4eef6;
  padding: 7px 15px;
  min-height: 157px;

  .legend,
  .info {
    line-height: 18px;
    color: @qcc-color-black-500;
    font-size: 12px;
  }

  .legend {
    li {
      &:not(:last-child) {
        margin-bottom: 5px;
      }

      i {
        width: 10px;
        height: 10px;
        display: inline-block;
        background-color: currentcolor;
        margin-right: 4px;
      }
    }
  }

  .info {
    li {
      &:not(:last-child) {
        margin-bottom: 6px;
      }

      &.risk {
        .icon {
          color: #141e8c;
        }
      }

      .icon {
        color: #cfe6f6;
        margin-right: 5px;
        font-size: 14px;
      }
    }
  }

  .chart {
    margin-top: 20px;
    width: 200px;
    height: 100px;
    background-size: contain;
    position: relative;
    overflow: hidden;

    &.low {
      .svg {
        transform: rotate(-60deg);
      }
    }

    &.middle {
      .svg {
        transform: rotate(0deg);
      }
    }

    &.high {
      .svg {
        transform: rotate(60deg);
      }
    }

    .label {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 10px;
      text-align: center;
      font-size: 22px;
      line-height: 30px;
      font-weight: @qcc-font-bold;
      color: currentcolor;
    }

    .svg {
      transform: rotate(0deg);

      .pointer {
        fill: currentcolor;
      }

      .circle {
        fill: transparent;
      }
    }
  }
}
