import locale from 'ant-design-vue/lib/locale/zh_CN';
import { ConfigProvider } from 'ant-design-vue';
import { computed, defineComponent } from 'vue';
import { useRoute } from 'vue-router/composables';

import LayoutPicker from '@/shared/layouts/layout-picker';

const RootApp = defineComponent({
  name: 'RootApp',

  setup() {
    const route = useRoute();
    const layout = computed(() => route.meta?.layout ?? 'workbench');
    return {
      layout,
    };
  },

  render() {
    const isRootRoute = !this.$route.name && this.$route.path === '/';
    return (
      <div id="app">
        <ConfigProvider locale={locale} autoInsertSpaceInButton={false}>
          {!isRootRoute ? <LayoutPicker name={this.layout} /> : null}
        </ConfigProvider>
      </div>
    );
  },
});

export default RootApp;
