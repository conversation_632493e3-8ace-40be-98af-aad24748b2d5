import _ from 'lodash';

import Tree from '@/utils/tree';
import { CascaderOption } from '@/interfaces/data.interface';

const data: CascaderOption[] = [
  {
    label: '招标公告',
    value: '3',
    children: [
      {
        label: '预告',
        value: '38',
      },
      {
        label: '招标',
        value: '31',
      },
      {
        label: '变更',
        value: '35',
      },
      {
        label: '澄清答疑',
        value: '311',
      },
    ],
  },
  {
    label: '中标结果',
    value: '4',
    children: [
      {
        label: '开标',
        value: '41',
      },
      {
        label: '中标候选',
        value: '420',
      },
      {
        label: '中标成交',
        value: '42',
      },
      {
        label: '合同验收',
        value: '48',
      },
      {
        label: '变更',
        value: '44',
      },
      {
        label: '废标流标终止',
        value: '45',
      },
    ],
  },
];

const ROOT_MAP: Record<'ifb' | 'wtb' | 'pps', Required<CascaderOption>> = {
  ifb: data.find(({ value }) => value === '3') as Required<CascaderOption>,
  wtb: data.find(({ value }) => value === '4') as Required<CascaderOption>,
  pps: data.find(({ value }) => value === '1') as Required<CascaderOption>,
};

export const tree = new Tree(data);

Object.freeze(data);
Object.freeze(ROOT_MAP);

export const isMatch = (top, value): boolean => {
  if (!top || !value) {
    return false;
  }

  const parent = data.find(({ value: v }) => v === top);

  if (!parent) {
    return false;
  }

  return parent.value === value || (parent.children || []).findIndex(({ value: cv }) => cv === value) > -1;
};

export const translate = (progressCodes?: string[]): string[] => {
  if (!Array.isArray(progressCodes) || !progressCodes.length) {
    return [];
  }

  return _.compact(
    progressCodes.map((code) => {
      const node = tree.getNodeByValue(code);

      if (!node) {
        return '';
      }

      return node.label;
    })
  );
};

export { data as default, ROOT_MAP };
