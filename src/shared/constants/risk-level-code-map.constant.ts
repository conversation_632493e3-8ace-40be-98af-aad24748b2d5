export enum RiskLevel {
  Low = 0,
  Middle = 1,
  High = 2,
}

export const RISK_LEVEL_CODE_MAP = {
  [RiskLevel.Low]: 'low',
  [RiskLevel.Middle]: 'middle', // TODO: rename to medium
  [RiskLevel.High]: 'high',
};

export const RISK_LEVEL_LABEL_MAP = {
  [RiskLevel.Low]: '无风险',
  [RiskLevel.Middle]: '关注',
  [RiskLevel.High]: '警示',
};

export const RISK_LEVEL_MAP = {
  [RISK_LEVEL_CODE_MAP[RiskLevel.Low]]: '无风险',
  [RISK_LEVEL_CODE_MAP[RiskLevel.Middle]]: '关注',
  [RISK_LEVEL_CODE_MAP[RiskLevel.High]]: '警示',
};

// 风险等级映射
export const RISK_LEVEL_CODE_THEME_MAP = {
  [RiskLevel.Low]: {
    type: 'success',
    label: '提示风险',
    style: 'color: #666; background: #C4F5E0; padding: 0 5px;',
    color: '#128BED',
  },
  [RiskLevel.Middle]: {
    type: 'warning',
    label: '关注风险',
    style: 'color: #666; background: #fec; padding: 0 5px;',
    color: '#FFAA00',
  },
  [RiskLevel.High]: {
    type: 'danger',
    label: '警示风险',
    style: 'color: #666; background: #fcc; padding: 0 5px;',
    color: '#f04040',
  },
};

export const RISK_LEVEL_LIST = [
  {
    label: '提示风险',
    value: RiskLevel.Low,
  },
  {
    label: '关注风险',
    value: RiskLevel.Middle,
  },
  {
    label: '警示风险',
    value: RiskLevel.High,
  },
];
