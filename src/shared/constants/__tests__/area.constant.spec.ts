import { transformAreaLabelAsValue } from '../area.constant';

describe('AreaConstant', () => {
  test('transformAreaLabelAsValue', () => {
    const source = [
      {
        label: 'AAA',
        children: [{ label: 'AAA-1' }],
      },
      {
        label: 'BBB',
      },
    ];
    const expected = [
      {
        label: 'AAA',
        value: 'AAA',
        children: [{ label: 'AAA-1', value: 'AAA-1' }],
      },
      {
        label: 'BBB',
        value: 'BBB',
      },
    ];
    expect(transformAreaLabelAsValue(source)).toEqual(expected);
  });
});
