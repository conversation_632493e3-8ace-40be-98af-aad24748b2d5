import Tree from '@/utils/tree';
import { CascaderOption } from '@/interfaces/data.interface';

/**
 * 将 value 的值替换为 label 的值
 * @param rawOptions
 */
const convertLabelToValueDepp = (rawOptions: CascaderOption[] | Readonly<CascaderOption[]>): CascaderOption[] => {
  const result = rawOptions.map((item) => {
    const { children, label } = item;
    const newItem = {
      label,
      value: label,
    };

    if (Array.isArray(children)) {
      return {
        ...newItem,
        children: convertLabelToValueDepp(children),
      };
    }
    return newItem;
  });
  return result;
};

/**
 * 数据来源: http://gitlab.greatld.com:18888/qcc/pc-web/-/blob/master/src/store/enshList.js
 */
export const INDUSTRY_CODE_OPTIONS: CascaderOption[] = [
  {
    label: '区块链',
    value: 'ensh.1',
    children: [
      {
        label: '数字资产',
        value: 'ensh.1.1',
      },
      {
        label: '数字货币',
        value: 'ensh.1.2',
        children: [
          {
            label: '数字资产管理',
            value: 'ensh.1.2.1',
          },
        ],
      },
      {
        label: '矿业',
        value: 'ensh.1.3',
        children: [
          {
            label: '矿机',
            value: 'ensh.1.3.1',
          },
          {
            label: '矿池',
            value: 'ensh.1.3.2',
          },
          {
            label: '云算力',
            value: 'ensh.1.3.3',
          },
          {
            label: '矿场',
            value: 'ensh.1.3.4',
          },
        ],
      },
      {
        label: '区块链行情资讯',
        value: 'ensh.1.4',
        children: [
          {
            label: '区块链媒体社区',
            value: 'ensh.1.4.1',
          },
          {
            label: '区块链行情',
            value: 'ensh.1.4.2',
          },
        ],
      },
      {
        label: '区块链工具',
        value: 'ensh.1.5',
        children: [
          {
            label: 'DAPP',
            value: 'ensh.1.5.1',
          },
          {
            label: '区块链钱包',
            value: 'ensh.1.5.2',
          },
          {
            label: '区块链浏览器',
            value: 'ensh.1.5.3',
          },
        ],
      },
      {
        label: '区块链应用',
        value: 'ensh.1.6',
        children: [
          {
            label: '防伪溯源',
            value: 'ensh.1.6.1',
          },
          {
            label: '区块链版权',
            value: 'ensh.1.6.2',
          },
          {
            label: '区块链物联网',
            value: 'ensh.1.6.3',
          },
          {
            label: '区块链金融',
            value: 'ensh.1.6.4',
          },
          {
            label: '区块链医疗',
            value: 'ensh.1.6.5',
          },
          {
            label: '区块链餐饮',
            value: 'ensh.1.6.6',
          },
          {
            label: '区块链社交',
            value: 'ensh.1.6.7',
          },
          {
            label: '区块链文娱',
            value: 'ensh.1.6.8',
          },
          {
            label: '区块链游戏',
            value: 'ensh.1.6.9',
          },
          {
            label: '区块链供应链管理',
            value: 'ensh.1.6.10',
          },
          {
            label: '区块链能源',
            value: 'ensh.1.6.11',
          },
        ],
      },
      {
        label: '区块链技术',
        value: 'ensh.1.7',
        children: [
          {
            label: '区块链身份认证',
            value: 'ensh.1.7.1',
          },
          {
            label: 'BaaS',
            value: 'ensh.1.7.2',
          },
          {
            label: '区块链底层平台',
            value: 'ensh.1.7.3',
          },
          {
            label: '智能合约',
            value: 'ensh.1.7.4',
          },
          {
            label: '区块链硬件',
            value: 'ensh.1.7.5',
          },
          {
            label: '区块链解决方案',
            value: 'ensh.1.7.6',
          },
        ],
      },
      {
        label: '数字货币交易服务',
        value: 'ensh.1.8',
        children: [
          {
            label: '场外交易',
            value: 'ensh.1.8.1',
          },
          {
            label: '数字货币交易所',
            value: 'ensh.1.8.2',
          },
          {
            label: 'ICO服务',
            value: 'ensh.1.8.3',
          },
        ],
      },
    ],
  },
  {
    label: '金融',
    value: 'ensh.2',
    children: [
      {
        label: '支付',
        value: 'ensh.2.1',
        children: [
          {
            label: '第三方支付',
            value: 'ensh.2.1.1',
          },
          {
            label: '跨境支付',
            value: 'ensh.2.1.2',
          },
          {
            label: '场景支付',
            value: 'ensh.2.1.3',
          },
          {
            label: 'NFC支付',
            value: 'ensh.2.1.4',
          },
          {
            label: '智能pos机',
            value: 'ensh.2.1.5',
          },
          {
            label: '支付技术支持',
            value: 'ensh.2.1.6',
          },
          {
            label: '虚拟货币支付',
            value: 'ensh.2.1.7',
          },
        ],
      },
      {
        label: '理财',
        value: 'ensh.2.2',
        children: [
          {
            label: '股票',
            value: 'ensh.2.2.1',
          },
          {
            label: 'P2P',
            value: 'ensh.2.2.2',
          },
          {
            label: '贵金属',
            value: 'ensh.2.2.3',
          },
          {
            label: '票据理财',
            value: 'ensh.2.2.4',
          },
          {
            label: '期货',
            value: 'ensh.2.2.5',
          },
          {
            label: '股权投资',
            value: 'ensh.2.2.6',
          },
          {
            label: '外汇交易',
            value: 'ensh.2.2.7',
          },
          {
            label: '期权',
            value: 'ensh.2.2.8',
          },
          {
            label: '金融衍生品',
            value: 'ensh.2.2.9',
          },
          {
            label: '企业理财',
            value: 'ensh.2.2.10',
          },
          {
            label: '实业投资',
            value: 'ensh.2.2.11',
          },
          {
            label: '基金理财',
            value: 'ensh.2.2.12',
          },
          {
            label: '债券理财',
            value: 'ensh.2.2.13',
          },
          {
            label: '海外理财',
            value: 'ensh.2.2.14',
          },
          {
            label: '理财自媒体',
            value: 'ensh.2.2.15',
          },
          {
            label: '理财师服务',
            value: 'ensh.2.2.16',
          },
          {
            label: '股票跟投社区',
            value: 'ensh.2.2.17',
          },
          {
            label: '司法拍卖',
            value: 'ensh.2.2.18',
          },
          {
            label: '财富管理',
            value: 'ensh.2.2.19',
          },
        ],
      },
      {
        label: '消费金融',
        value: 'ensh.2.3',
        children: [
          {
            label: '租房分期',
            value: 'ensh.2.3.1',
          },
          {
            label: '旅游分期',
            value: 'ensh.2.3.2',
          },
          {
            label: '美容分期',
            value: 'ensh.2.3.3',
          },
          {
            label: '教育分期',
            value: 'ensh.2.3.4',
          },
          {
            label: '汽车分期',
            value: 'ensh.2.3.5',
          },
          {
            label: '熟人借贷',
            value: 'ensh.2.3.6',
          },
          {
            label: '大学生分期',
            value: 'ensh.2.3.7',
          },
          {
            label: '蓝领分期',
            value: 'ensh.2.3.8',
          },
          {
            label: '医疗分期',
            value: 'ensh.2.3.9',
          },
          {
            label: '购物分期',
            value: 'ensh.2.3.10',
          },
          {
            label: '家装分期',
            value: 'ensh.2.3.11',
          },
          {
            label: '农业分期',
            value: 'ensh.2.3.12',
          },
          {
            label: '现金贷',
            value: 'ensh.2.3.13',
          },
          {
            label: '信用卡服务',
            value: 'ensh.2.3.14',
          },
          {
            label: '校园信贷',
            value: 'ensh.2.3.15',
          },
          {
            label: '婚庆金融',
            value: 'ensh.2.3.16',
          },
          {
            label: '移动信贷',
            value: 'ensh.2.3.17',
          },
        ],
      },
      {
        label: '金融大数据',
        value: 'ensh.2.4',
        children: [
          {
            label: '量化投资',
            value: 'ensh.2.4.1',
          },
          {
            label: '大数据征信',
            value: 'ensh.2.4.2',
          },
          {
            label: '大数据风控',
            value: 'ensh.2.4.3',
          },
          {
            label: '金融数据库',
            value: 'ensh.2.4.4',
          },
          {
            label: '精准保费厘定',
            value: 'ensh.2.4.5',
          },
        ],
      },
      {
        label: '金融科技',
        value: 'ensh.2.5',
        children: [
          {
            label: '智能投顾',
            value: 'ensh.2.5.1',
          },
          {
            label: '智能投研',
            value: 'ensh.2.5.2',
          },
          {
            label: '金融安全监控与预警',
            value: 'ensh.2.5.3',
          },
          {
            label: '金融预测',
            value: 'ensh.2.5.4',
          },
          {
            label: '个性化保险',
            value: 'ensh.2.5.5',
          },
          {
            label: '智能信贷',
            value: 'ensh.2.5.6',
          },
        ],
      },
      {
        label: '企业金融服务',
        value: 'ensh.2.6',
        children: [
          {
            label: '供应链金融',
            value: 'ensh.2.6.1',
          },
          {
            label: '催收',
            value: 'ensh.2.6.2',
          },
          {
            label: '融资租赁',
            value: 'ensh.2.6.3',
          },
          {
            label: '不良资产处置',
            value: 'ensh.2.6.4',
          },
          {
            label: '无抵押贷款',
            value: 'ensh.2.6.5',
          },
          {
            label: '企业征信',
            value: 'ensh.2.6.6',
          },
          {
            label: '现金流管理',
            value: 'ensh.2.6.7',
          },
          {
            label: '企业融资',
            value: 'ensh.2.6.8',
          },
          {
            label: '小微企业贷',
            value: 'ensh.2.6.9',
          },
          {
            label: '企业债权信贷',
            value: 'ensh.2.6.10',
          },
          {
            label: '企业担保服务',
            value: 'ensh.2.6.11',
          },
        ],
      },
      {
        label: '金融机构',
        value: 'ensh.2.7',
        children: [
          {
            label: '证券公司',
            value: 'ensh.2.7.1',
          },
          {
            label: '典当行',
            value: 'ensh.2.7.2',
          },
          {
            label: '保险公司',
            value: 'ensh.2.7.3',
          },
          {
            label: '互联网银行',
            value: 'ensh.2.7.4',
          },
          {
            label: '征信机构',
            value: 'ensh.2.7.5',
          },
          {
            label: '投融资机构',
            value: 'ensh.2.7.6',
          },
          {
            label: '基金公司',
            value: 'ensh.2.7.7',
          },
          {
            label: '信托机构',
            value: 'ensh.2.7.8',
          },
          {
            label: '银行',
            value: 'ensh.2.7.9',
          },
          {
            label: '风控机构',
            value: 'ensh.2.7.10',
          },
        ],
      },
      {
        label: '金融信息服务',
        value: 'ensh.2.8',
        children: [
          {
            label: '金融咨询',
            value: 'ensh.2.8.1',
          },
          {
            label: '金融IT外包',
            value: 'ensh.2.8.2',
          },
          {
            label: '投资咨询',
            value: 'ensh.2.8.3',
          },
          {
            label: '投资管理',
            value: 'ensh.2.8.4',
          },
        ],
      },
      {
        label: '金融终端设备',
        value: 'ensh.2.9',
      },
      {
        label: '保险',
        value: 'ensh.2.10',
        children: [
          {
            label: '互联网保险',
            value: 'ensh.2.10.1',
          },
          {
            label: '互助保险',
            value: 'ensh.2.10.2',
          },
          {
            label: '保险代理',
            value: 'ensh.2.10.3',
          },
          {
            label: '保险理赔',
            value: 'ensh.2.10.4',
          },
          {
            label: '保险产品',
            value: 'ensh.2.10.5',
          },
          {
            label: '保险经纪',
            value: 'ensh.2.10.6',
          },
          {
            label: '保险比价',
            value: 'ensh.2.10.7',
          },
          {
            label: '场景化保险',
            value: 'ensh.2.10.8',
          },
          {
            label: '保险导购',
            value: 'ensh.2.10.9',
          },
          {
            label: '保险特卖',
            value: 'ensh.2.10.10',
          },
          {
            label: '创新险种',
            value: 'ensh.2.10.11',
          },
          {
            label: '保险公估',
            value: 'ensh.2.10.12',
          },
          {
            label: '保险定制',
            value: 'ensh.2.10.13',
          },
          {
            label: 'UBI车险',
            value: 'ensh.2.10.14',
          },
          {
            label: '寿命保险',
            value: 'ensh.2.10.15',
          },
        ],
      },
      {
        label: '众筹',
        value: 'ensh.2.11',
        children: [
          {
            label: '股权众筹',
            value: 'ensh.2.11.1',
          },
          {
            label: '加盟',
            value: 'ensh.2.11.2',
          },
          {
            label: '房产众筹',
            value: 'ensh.2.11.3',
          },
          {
            label: '项目众筹',
            value: 'ensh.2.11.4',
          },
          {
            label: '商品众筹',
            value: 'ensh.2.11.5',
          },
          {
            label: '轻众筹',
            value: 'ensh.2.11.6',
          },
          {
            label: '众筹社区',
            value: 'ensh.2.11.7',
          },
        ],
      },
    ],
  },
  {
    label: '医疗',
    value: 'ensh.3',
    children: [
      {
        label: '兽医',
        value: 'ensh.3.1',
      },
      {
        label: '医生服务',
        value: 'ensh.3.2',
        children: [
          {
            label: '患者管理',
            value: 'ensh.3.2.1',
          },
          {
            label: '医生社交',
            value: 'ensh.3.2.2',
          },
          {
            label: '医疗教育',
            value: 'ensh.3.2.3',
          },
          {
            label: '医生集团',
            value: 'ensh.3.2.4',
          },
          {
            label: '患者教育',
            value: 'ensh.3.2.5',
          },
          {
            label: '医疗科研外包',
            value: 'ensh.3.2.6',
          },
          {
            label: '医疗媒体资讯',
            value: 'ensh.3.2.7',
          },
          {
            label: '医疗科研数据库',
            value: 'ensh.3.2.8',
          },
        ],
      },
      {
        label: '医疗设备',
        value: 'ensh.3.3',
        children: [
          {
            label: '医疗器械',
            value: 'ensh.3.3.1',
          },
          {
            label: '医疗耗材',
            value: 'ensh.3.3.2',
          },
          {
            label: '医疗设备研发生产',
            value: 'ensh.3.3.3',
          },
          {
            label: '医疗设备维修管理',
            value: 'ensh.3.3.4',
          },
          {
            label: '医疗设备融资租赁',
            value: 'ensh.3.3.5',
          },
        ],
      },
      {
        label: '养生保健',
        value: 'ensh.3.4',
        children: [
          {
            label: '体检',
            value: 'ensh.3.4.1',
          },
          {
            label: '睡眠管理',
            value: 'ensh.3.4.2',
          },
          {
            label: '产后护理',
            value: 'ensh.3.4.3',
          },
          {
            label: '女性健康',
            value: 'ensh.3.4.4',
          },
          {
            label: '产前检查',
            value: 'ensh.3.4.5',
          },
        ],
      },
      {
        label: '医疗大数据',
        value: 'ensh.3.5',
        children: [
          {
            label: '病历数据库',
            value: 'ensh.3.5.1',
          },
          {
            label: '医疗大数据云平台',
            value: 'ensh.3.5.2',
          },
          {
            label: '医疗科研大数据',
            value: 'ensh.3.5.3',
          },
          {
            label: '肿瘤大数据',
            value: 'ensh.3.5.4',
          },
          {
            label: '基因大数据',
            value: 'ensh.3.5.5',
          },
          {
            label: '医疗保险控费',
            value: 'ensh.3.5.6',
          },
          {
            label: '健康管理',
            value: 'ensh.3.5.7',
          },
          {
            label: '医疗信息保密',
            value: 'ensh.3.5.8',
          },
        ],
      },
      {
        label: '慢病',
        value: 'ensh.3.6',
        children: [
          {
            label: '糖尿病',
            value: 'ensh.3.6.1',
          },
          {
            label: '肾病',
            value: 'ensh.3.6.2',
          },
          {
            label: '肝病',
            value: 'ensh.3.6.3',
          },
          {
            label: '高血压',
            value: 'ensh.3.6.4',
          },
          {
            label: '神经疾病',
            value: 'ensh.3.6.5',
          },
          {
            label: '呼吸系统疾病',
            value: 'ensh.3.6.6',
          },
        ],
      },
      {
        label: '医疗机器人',
        value: 'ensh.3.7',
        children: [
          {
            label: '康复机器人',
            value: 'ensh.3.7.1',
          },
          {
            label: '手术机器人',
            value: 'ensh.3.7.2',
          },
          {
            label: '输液机器人',
            value: 'ensh.3.7.3',
          },
          {
            label: '胶囊机器人',
            value: 'ensh.3.7.4',
          },
          {
            label: '医院服务机器人',
            value: 'ensh.3.7.5',
          },
          {
            label: '牙科机器人',
            value: 'ensh.3.7.6',
          },
          {
            label: '护理机器人',
            value: 'ensh.3.7.7',
          },
          {
            label: '疾病诊断机器人',
            value: 'ensh.3.7.8',
          },
          {
            label: '外骨骼机器人',
            value: 'ensh.3.7.9',
          },
          {
            label: '远程医疗机器人',
            value: 'ensh.3.7.10',
          },
          {
            label: '输液监护机器人',
            value: 'ensh.3.7.11',
          },
          {
            label: '医疗模拟机器人',
            value: 'ensh.3.7.12',
          },
        ],
      },
      {
        label: '跨境医疗',
        value: 'ensh.3.8',
        children: [
          {
            label: '医疗旅游',
            value: 'ensh.3.8.1',
          },
          {
            label: '跨境会诊',
            value: 'ensh.3.8.2',
          },
          {
            label: '海外生殖',
            value: 'ensh.3.8.3',
          },
          {
            label: '海外重症',
            value: 'ensh.3.8.4',
          },
        ],
      },
      {
        label: '医疗3D打印',
        value: 'ensh.3.9',
        children: [
          {
            label: '医疗模型',
            value: 'ensh.3.9.1',
          },
          {
            label: '3D打印医疗器械',
            value: 'ensh.3.9.2',
          },
          {
            label: '3D打印器官',
            value: 'ensh.3.9.3',
          },
          {
            label: '牙科3D打印',
            value: 'ensh.3.9.4',
          },
          {
            label: '手术导板',
            value: 'ensh.3.9.5',
          },
        ],
      },
      {
        label: '患者服务',
        value: 'ensh.3.10',
        children: [
          {
            label: '挂号',
            value: 'ensh.3.10.1',
          },
          {
            label: '在线问诊',
            value: 'ensh.3.10.2',
          },
          {
            label: '家庭医生',
            value: 'ensh.3.10.3',
          },
          {
            label: '上门问诊',
            value: 'ensh.3.10.4',
          },
          {
            label: '分级诊疗',
            value: 'ensh.3.10.5',
          },
          {
            label: '自诊',
            value: 'ensh.3.10.6',
          },
          {
            label: '导诊',
            value: 'ensh.3.10.7',
          },
          {
            label: '诊后咨询',
            value: 'ensh.3.10.8',
          },
          {
            label: '护工护理',
            value: 'ensh.3.10.9',
          },
          {
            label: '疾病管理',
            value: 'ensh.3.10.10',
          },
          {
            label: '康复医疗',
            value: 'ensh.3.10.11',
          },
          {
            label: '病友社区',
            value: 'ensh.3.10.12',
          },
          {
            label: '临终关怀',
            value: 'ensh.3.10.13',
          },
        ],
      },
      {
        label: '消费医疗',
        value: 'ensh.3.11',
        children: [
          {
            label: '心理健康',
            value: 'ensh.3.11.1',
          },
          {
            label: '中医',
            value: 'ensh.3.11.2',
          },
          {
            label: '眼科',
            value: 'ensh.3.11.3',
          },
          {
            label: '牙科',
            value: 'ensh.3.11.4',
          },
          {
            label: '医美',
            value: 'ensh.3.11.5',
          },
          {
            label: '妇产',
            value: 'ensh.3.11.6',
          },
        ],
      },
      {
        label: '大病重症',
        value: 'ensh.3.12',
        children: [
          {
            label: '肿瘤筛查',
            value: 'ensh.3.12.1',
          },
          {
            label: '癌症治疗',
            value: 'ensh.3.12.2',
          },
          {
            label: '心脑血管疾病',
            value: 'ensh.3.12.3',
          },
        ],
      },
      {
        label: '其他疾病',
        value: 'ensh.3.13',
        children: [
          {
            label: '儿科',
            value: 'ensh.3.13.1',
          },
          {
            label: '骨科',
            value: 'ensh.3.13.2',
          },
          {
            label: '传染病',
            value: 'ensh.3.13.3',
          },
          {
            label: '遗传病',
            value: 'ensh.3.13.4',
          },
          {
            label: '耳科',
            value: 'ensh.3.13.5',
          },
          {
            label: '男科',
            value: 'ensh.3.13.6',
          },
          {
            label: '皮肤病',
            value: 'ensh.3.13.7',
          },
          {
            label: '免疫性疾病',
            value: 'ensh.3.13.8',
          },
          {
            label: '过敏性疾病',
            value: 'ensh.3.13.9',
          },
        ],
      },
      {
        label: '医疗技术',
        value: 'ensh.3.14',
        children: [
          {
            label: '基因检测',
            value: 'ensh.3.14.1',
          },
          {
            label: '免疫治疗',
            value: 'ensh.3.14.2',
          },
          {
            label: '精准医疗',
            value: 'ensh.3.14.3',
          },
          {
            label: '医疗影像',
            value: 'ensh.3.14.4',
          },
          {
            label: 'poct',
            value: 'ensh.3.14.5',
          },
          {
            label: '体外诊断',
            value: 'ensh.3.14.6',
          },
          {
            label: '分子诊断',
            value: 'ensh.3.14.7',
          },
          {
            label: '辅助生殖',
            value: 'ensh.3.14.8',
          },
          {
            label: '基因编辑',
            value: 'ensh.3.14.9',
          },
        ],
      },
      {
        label: '医疗信息化',
        value: 'ensh.3.15',
        children: [
          {
            label: '移动医疗',
            value: 'ensh.3.15.1',
          },
          {
            label: '远程医疗',
            value: 'ensh.3.15.2',
          },
          {
            label: 'HIS系统',
            value: 'ensh.3.15.3',
          },
          {
            label: 'EMRS系统',
            value: 'ensh.3.15.4',
          },
          {
            label: 'PACS系统',
            value: 'ensh.3.15.5',
          },
          {
            label: 'RIS系统',
            value: 'ensh.3.15.6',
          },
          {
            label: '医疗智能终端',
            value: 'ensh.3.15.7',
          },
          {
            label: '云医疗',
            value: 'ensh.3.15.8',
          },
          {
            label: '药品流通信息化',
            value: 'ensh.3.15.9',
          },
          {
            label: '医保信息化',
            value: 'ensh.3.15.10',
          },
          {
            label: '医疗信息化硬件',
            value: 'ensh.3.15.11',
          },
          {
            label: '药店管理saas',
            value: 'ensh.3.15.12',
          },
          {
            label: '诊所管理saas',
            value: 'ensh.3.15.13',
          },
          {
            label: 'CDSS',
            value: 'ensh.3.15.14',
          },
          {
            label: '医疗搜索引擎',
            value: 'ensh.3.15.15',
          },
          {
            label: '医药查询',
            value: 'ensh.3.15.16',
          },
          {
            label: '循证医学',
            value: 'ensh.3.15.17',
          },
          {
            label: '医院wifi',
            value: 'ensh.3.15.18',
          },
        ],
      },
      {
        label: '生物医药',
        value: 'ensh.3.16',
        children: [
          {
            label: '药物研发',
            value: 'ensh.3.16.1',
          },
          {
            label: '原料药',
            value: 'ensh.3.16.2',
          },
          {
            label: '医药中间体',
            value: 'ensh.3.16.3',
          },
          {
            label: '靶向药物',
            value: 'ensh.3.16.4',
          },
          {
            label: '中药研发',
            value: 'ensh.3.16.5',
          },
          {
            label: '疫苗及血制品',
            value: 'ensh.3.16.6',
          },
          {
            label: '医药安全',
            value: 'ensh.3.16.7',
          },
          {
            label: '医药批发',
            value: 'ensh.3.16.8',
          },
          {
            label: '医药零售',
            value: 'ensh.3.16.9',
          },
          {
            label: '药用辅料',
            value: 'ensh.3.16.10',
          },
          {
            label: '药物研发外包',
            value: 'ensh.3.16.11',
          },
          {
            label: '生物技术',
            value: 'ensh.3.16.12',
          },
        ],
      },
      {
        label: '养老',
        value: 'ensh.3.17',
        children: [
          {
            label: '养老服务',
            value: 'ensh.3.17.1',
          },
          {
            label: '智慧养老',
            value: 'ensh.3.17.2',
          },
          {
            label: '养老机构',
            value: 'ensh.3.17.3',
          },
          {
            label: '老年人用品',
            value: 'ensh.3.17.4',
          },
        ],
      },
      {
        label: '医疗机构',
        value: 'ensh.3.18',
        children: [
          {
            label: '口腔诊所',
            value: 'ensh.3.18.1',
          },
          {
            label: '儿科诊所',
            value: 'ensh.3.18.2',
          },
          {
            label: '全科诊所',
            value: 'ensh.3.18.3',
          },
          {
            label: '中医诊所',
            value: 'ensh.3.18.4',
          },
          {
            label: '社区诊所',
            value: 'ensh.3.18.5',
          },
          {
            label: '医院',
            value: 'ensh.3.18.6',
          },
          {
            label: '诊所',
            value: 'ensh.3.18.7',
          },
        ],
      },
    ],
  },
  {
    label: '教育',
    value: 'ensh.4',
    children: [
      {
        label: '语言学习',
        value: 'ensh.4.1',
        children: [
          {
            label: '英语学习',
            value: 'ensh.4.1.1',
          },
          {
            label: '外教',
            value: 'ensh.4.1.2',
          },
          {
            label: '小语种',
            value: 'ensh.4.1.3',
          },
          {
            label: '词典单词',
            value: 'ensh.4.1.4',
          },
          {
            label: '汉语学习',
            value: 'ensh.4.1.5',
          },
          {
            label: '语言学习社区',
            value: 'ensh.4.1.6',
          },
        ],
      },
      {
        label: '职业教育',
        value: 'ensh.4.2',
        children: [
          {
            label: '企业培训',
            value: 'ensh.4.2.1',
          },
          {
            label: '金融培训',
            value: 'ensh.4.2.2',
          },
          {
            label: '教师培训',
            value: 'ensh.4.2.3',
          },
          {
            label: '医学教育',
            value: 'ensh.4.2.4',
          },
          {
            label: '商学院',
            value: 'ensh.4.2.5',
          },
          {
            label: 'IT技能培训',
            value: 'ensh.4.2.6',
          },
          {
            label: '法律教育',
            value: 'ensh.4.2.7',
          },
          {
            label: '财会教育',
            value: 'ensh.4.2.8',
          },
          {
            label: '烹饪培训',
            value: 'ensh.4.2.9',
          },
          {
            label: '美业培训',
            value: 'ensh.4.2.10',
          },
          {
            label: '职称考证培训',
            value: 'ensh.4.2.11',
          },
        ],
      },
      {
        label: '在线教育',
        value: 'ensh.4.3',
        children: [
          {
            label: 'MOOC',
            value: 'ensh.4.3.1',
          },
          {
            label: '互联网大学',
            value: 'ensh.4.3.2',
          },
          {
            label: '在线英语教育',
            value: 'ensh.4.3.3',
          },
          {
            label: '在线一对一教育',
            value: 'ensh.4.3.4',
          },
          {
            label: '在线一对多教育',
            value: 'ensh.4.3.5',
          },
          {
            label: '直播教育',
            value: 'ensh.4.3.6',
          },
        ],
      },
      {
        label: '教育信息化',
        value: 'ensh.4.4',
        children: [
          {
            label: '远程教育',
            value: 'ensh.4.4.1',
          },
          {
            label: '智慧校园',
            value: 'ensh.4.4.2',
          },
          {
            label: '教育资源共享',
            value: 'ensh.4.4.3',
          },
          {
            label: '云教学',
            value: 'ensh.4.4.4',
          },
          {
            label: '教育测评',
            value: 'ensh.4.4.5',
          },
          {
            label: '数字教科书',
            value: 'ensh.4.4.6',
          },
          {
            label: '一站式图书馆',
            value: 'ensh.4.4.7',
          },
          {
            label: '校园一卡通',
            value: 'ensh.4.4.8',
          },
        ],
      },
      {
        label: '早教',
        value: 'ensh.4.5',
        children: [
          {
            label: '幼儿园',
            value: 'ensh.4.5.1',
          },
          {
            label: '亲子教育',
            value: 'ensh.4.5.2',
          },
          {
            label: '胎教',
            value: 'ensh.4.5.3',
          },
          {
            label: '儿童托管',
            value: 'ensh.4.5.4',
          },
          {
            label: '早教产品内容',
            value: 'ensh.4.5.5',
          },
          {
            label: '儿童智力开发',
            value: 'ensh.4.5.6',
          },
          {
            label: '母婴育儿',
            value: 'ensh.4.5.7',
          },
          {
            label: 'VRAR早教产品',
            value: 'ensh.4.5.8',
          },
        ],
      },
      {
        label: '素质教育',
        value: 'ensh.4.6',
        children: [
          {
            label: '兴趣教育',
            value: 'ensh.4.6.1',
          },
          {
            label: '艺术教育',
            value: 'ensh.4.6.2',
          },
          {
            label: '编程教育',
            value: 'ensh.4.6.3',
          },
          {
            label: '机器人教育',
            value: 'ensh.4.6.4',
          },
          {
            label: '创客教育',
            value: 'ensh.4.6.5',
          },
          {
            label: 'STEAM教育',
            value: 'ensh.4.6.6',
          },
          {
            label: '武术教育',
            value: 'ensh.4.6.7',
          },
          {
            label: '情商教育',
            value: 'ensh.4.6.8',
          },
          {
            label: '国学教育',
            value: 'ensh.4.6.9',
          },
          {
            label: '传统文化教育',
            value: 'ensh.4.6.10',
          },
          {
            label: '青少年培训',
            value: 'ensh.4.6.11',
          },
          {
            label: '礼仪教育',
            value: 'ensh.4.6.12',
          },
          {
            label: '职业体验教育',
            value: 'ensh.4.6.13',
          },
          {
            label: '创造力教育',
            value: 'ensh.4.6.14',
          },
        ],
      },
      {
        label: '留学教育',
        value: 'ensh.4.7',
        children: [
          {
            label: '留学服务',
            value: 'ensh.4.7.1',
          },
          {
            label: '游学',
            value: 'ensh.4.7.2',
          },
          {
            label: '留学考试培训',
            value: 'ensh.4.7.3',
          },
        ],
      },
      {
        label: 'K12教育',
        value: 'ensh.4.8',
        children: [
          {
            label: 'K12在线辅导',
            value: 'ensh.4.8.1',
          },
          {
            label: 'K12线下机构',
            value: 'ensh.4.8.2',
          },
          {
            label: 'K12题库',
            value: 'ensh.4.8.3',
          },
          {
            label: '高考服务',
            value: 'ensh.4.8.4',
          },
          {
            label: 'K12产品内容',
            value: 'ensh.4.8.5',
          },
          {
            label: '上门辅导',
            value: 'ensh.4.8.6',
          },
        ],
      },
      {
        label: '教育大数据',
        value: 'ensh.4.9',
        children: [
          {
            label: '智能题库',
            value: 'ensh.4.9.1',
          },
          {
            label: '学情分析',
            value: 'ensh.4.9.2',
          },
          {
            label: '教育机构搜索',
            value: 'ensh.4.9.3',
          },
          {
            label: '学习数据采集',
            value: 'ensh.4.9.4',
          },
          {
            label: '教育数据采集',
            value: 'ensh.4.9.5',
          },
        ],
      },
      {
        label: '知识付费',
        value: 'ensh.4.10',
        children: [
          {
            label: '付费问答',
            value: 'ensh.4.10.1',
          },
          {
            label: '技能出租',
            value: 'ensh.4.10.2',
          },
        ],
      },
      {
        label: 'AI教育',
        value: 'ensh.4.11',
        children: [
          {
            label: '分级阅读',
            value: 'ensh.4.11.1',
          },
          {
            label: '智能评测',
            value: 'ensh.4.11.2',
          },
          {
            label: '智能阅卷',
            value: 'ensh.4.11.3',
          },
          {
            label: '在线作业批改',
            value: 'ensh.4.11.4',
          },
          {
            label: '教育决策支持系统',
            value: 'ensh.4.11.5',
          },
          {
            label: '自动化答疑',
            value: 'ensh.4.11.6',
          },
        ],
      },
      {
        label: '大学教育',
        value: 'ensh.4.12',
        children: [
          {
            label: '就业指导',
            value: 'ensh.4.12.1',
          },
          {
            label: '司法考试',
            value: 'ensh.4.12.2',
          },
          {
            label: '求职教育',
            value: 'ensh.4.12.3',
          },
          {
            label: '考证考级',
            value: 'ensh.4.12.4',
          },
          {
            label: '考研公考',
            value: 'ensh.4.12.5',
          },
        ],
      },
      {
        label: '线下机构',
        value: 'ensh.4.13',
        children: [
          {
            label: '留学中介',
            value: 'ensh.4.13.1',
          },
          {
            label: '托管机构',
            value: 'ensh.4.13.2',
          },
          {
            label: '儿童游乐空间',
            value: 'ensh.4.13.3',
          },
          {
            label: '辅导机构',
            value: 'ensh.4.13.4',
          },
        ],
      },
    ],
  },
  {
    label: '交通运输',
    value: 'ensh.5',
    children: [
      {
        label: '汽车金融',
        value: 'ensh.5.1',
        children: [
          {
            label: '汽车保险',
            value: 'ensh.5.1.1',
          },
          {
            label: '汽车贷款',
            value: 'ensh.5.1.2',
          },
          {
            label: '汽车抵押',
            value: 'ensh.5.1.3',
          },
          {
            label: '二手车汽车金融',
            value: 'ensh.5.1.4',
          },
          {
            label: '车险分期',
            value: 'ensh.5.1.5',
          },
          {
            label: '农村汽车金融服务',
            value: 'ensh.5.1.6',
          },
          {
            label: '新能源汽车',
            value: 'ensh.5.1.7',
          },
          {
            label: '新能源汽车制造',
            value: 'ensh.5.1.8',
          },
          {
            label: '新能源汽车电池组',
            value: 'ensh.5.1.9',
          },
          {
            label: '新能源汽车充电桩',
            value: 'ensh.5.1.10',
          },
          {
            label: '汽车无线充电',
            value: 'ensh.5.1.11',
          },
          {
            label: '汽车融资租赁',
            value: 'ensh.5.1.12',
          },
        ],
      },
      {
        label: '车载智能硬件',
        value: 'ensh.5.2',
        children: [
          {
            label: 'HUD',
            value: 'ensh.5.2.1',
          },
          {
            label: '行车记录仪',
            value: 'ensh.5.2.2',
          },
          {
            label: 'OBD设备',
            value: 'ensh.5.2.3',
          },
          {
            label: '车载音箱',
            value: 'ensh.5.2.4',
          },
          {
            label: '车载导航',
            value: 'ensh.5.2.5',
          },
          {
            label: '车联网终端',
            value: 'ensh.5.2.6',
          },
          {
            label: '车载路由器',
            value: 'ensh.5.2.7',
          },
          {
            label: '智能后视镜',
            value: 'ensh.5.2.8',
          },
          {
            label: '车载数码产品',
            value: 'ensh.5.2.9',
          },
          {
            label: '双目视觉摄像头',
            value: 'ensh.5.2.10',
          },
          {
            label: '汽车传感器',
            value: 'ensh.5.2.11',
          },
        ],
      },
      {
        label: '无人驾驶',
        value: 'ensh.5.3',
        children: [
          {
            label: 'ADAS',
            value: 'ensh.5.3.1',
          },
          {
            label: '车载中控系统',
            value: 'ensh.5.3.2',
          },
          {
            label: '高精地图识别',
            value: 'ensh.5.3.3',
          },
          {
            label: '无人驾驶系统',
            value: 'ensh.5.3.4',
          },
        ],
      },
      {
        label: '汽车配件',
        value: 'ensh.5.4',
        children: [
          {
            label: '汽车零部件',
            value: 'ensh.5.4.1',
          },
          {
            label: '汽配B2B',
            value: 'ensh.5.4.2',
          },
          {
            label: '车辆座椅',
            value: 'ensh.5.4.3',
          },
          {
            label: '车辆空调',
            value: 'ensh.5.4.4',
          },
          {
            label: '车身附件',
            value: 'ensh.5.4.5',
          },
          {
            label: '汽车维修设备',
            value: 'ensh.5.4.6',
          },
          {
            label: '汽配电商',
            value: 'ensh.5.4.7',
          },
        ],
      },
      {
        label: '新车制造',
        value: 'ensh.5.5',
        children: [
          {
            label: '传统汽车制造',
            value: 'ensh.5.5.1',
          },
          {
            label: '智能汽车制造',
            value: 'ensh.5.5.2',
          },
        ],
      },
      {
        label: '航空航运',
        value: 'ensh.5.6',
        children: [
          {
            label: '机场',
            value: 'ensh.5.6.1',
          },
          {
            label: '航空公司',
            value: 'ensh.5.6.2',
          },
          {
            label: '港口',
            value: 'ensh.5.6.3',
          },
          {
            label: '船舶工业',
            value: 'ensh.5.6.4',
          },
          {
            label: '航运公司',
            value: 'ensh.5.6.5',
          },
          {
            label: '商用飞机研发',
            value: 'ensh.5.6.6',
          },
        ],
      },
      {
        label: '轨交铁路',
        value: 'ensh.5.7',
        children: [
          {
            label: '铁路建设',
            value: 'ensh.5.7.1',
          },
          {
            label: '铁路设备',
            value: 'ensh.5.7.2',
          },
          {
            label: '轨道交通信息系统',
            value: 'ensh.5.7.3',
          },
          {
            label: '地铁运营',
            value: 'ensh.5.7.4',
          },
        ],
      },
      {
        label: '汽车交通相关服务',
        value: 'ensh.5.8',
        children: [
          {
            label: '汽车媒体',
            value: 'ensh.5.8.1',
          },
          {
            label: '驾考培训',
            value: 'ensh.5.8.2',
          },
          {
            label: '公交到站查询',
            value: 'ensh.5.8.3',
          },
          {
            label: '交通信息化',
            value: 'ensh.5.8.4',
          },
          {
            label: '虚拟驾驶',
            value: 'ensh.5.8.5',
          },
        ],
      },
      {
        label: '汽车后服务',
        value: 'ensh.5.9',
        children: [
          {
            label: '洗车',
            value: 'ensh.5.9.1',
          },
          {
            label: '汽车改装',
            value: 'ensh.5.9.2',
          },
          {
            label: '汽车美容',
            value: 'ensh.5.9.3',
          },
          {
            label: '道路救援',
            value: 'ensh.5.9.4',
          },
          {
            label: '智能停车',
            value: 'ensh.5.9.5',
          },
          {
            label: '汽车充电',
            value: 'ensh.5.9.6',
          },
          {
            label: '代客泊车',
            value: 'ensh.5.9.7',
          },
          {
            label: '共享停车',
            value: 'ensh.5.9.8',
          },
          {
            label: '汽车加油',
            value: 'ensh.5.9.9',
          },
          {
            label: '汽车年检',
            value: 'ensh.5.9.10',
          },
          {
            label: '汽车违章查询',
            value: 'ensh.5.9.11',
          },
          {
            label: '汽车售后服务',
            value: 'ensh.5.9.12',
          },
          {
            label: '汽车O2O服务',
            value: 'ensh.5.9.13',
          },
          {
            label: '汽车检测',
            value: 'ensh.5.9.14',
          },
          {
            label: '汽车维修保养',
            value: 'ensh.5.9.15',
          },
        ],
      },
      {
        label: '汽车交易',
        value: 'ensh.5.10',
        children: [
          {
            label: '汽车电商',
            value: 'ensh.5.10.1',
          },
          {
            label: '二手车交易',
            value: 'ensh.5.10.2',
          },
          {
            label: '平行进口车',
            value: 'ensh.5.10.3',
          },
          {
            label: '新车销售',
            value: 'ensh.5.10.4',
          },
          {
            label: '汽车交易信息平台',
            value: 'ensh.5.10.5',
          },
          {
            label: '汽车B2B',
            value: 'ensh.5.10.6',
          },
          {
            label: '豪车销售',
            value: 'ensh.5.10.7',
          },
          {
            label: '汽车比价平台',
            value: 'ensh.5.10.8',
          },
          {
            label: '汽车定价',
            value: 'ensh.5.10.9',
          },
          {
            label: '二手车估价',
            value: 'ensh.5.10.10',
          },
          {
            label: '农村汽车交易',
            value: 'ensh.5.10.11',
          },
          {
            label: '汽车特卖',
            value: 'ensh.5.10.12',
          },
        ],
      },
      {
        label: '出行服务',
        value: 'ensh.5.11',
        children: [
          {
            label: '拼车',
            value: 'ensh.5.11.1',
          },
          {
            label: '代驾',
            value: 'ensh.5.11.2',
          },
          {
            label: '自行车',
            value: 'ensh.5.11.3',
          },
          {
            label: '电动车',
            value: 'ensh.5.11.4',
          },
          {
            label: '巴士',
            value: 'ensh.5.11.5',
          },
          {
            label: '共享汽车',
            value: 'ensh.5.11.6',
          },
          {
            label: '传统租车',
            value: 'ensh.5.11.7',
          },
          {
            label: '网约车',
            value: 'ensh.5.11.8',
          },
          {
            label: '境外用车',
            value: 'ensh.5.11.9',
          },
          {
            label: '共享电动车',
            value: 'ensh.5.11.10',
          },
          {
            label: '共享单车',
            value: 'ensh.5.11.11',
          },
        ],
      },
    ],
  },
  {
    label: '物联网',
    value: 'ensh.6',
    children: [
      {
        label: '车联网',
        value: 'ensh.6.1',
        children: [
          {
            label: '智能交通',
            value: 'ensh.6.1.1',
          },
          {
            label: '车载娱乐系统',
            value: 'ensh.6.1.2',
          },
          {
            label: '车联网云服务',
            value: 'ensh.6.1.3',
          },
          {
            label: '车联网大数据',
            value: 'ensh.6.1.4',
          },
          {
            label: '车载WIFI',
            value: 'ensh.6.1.5',
          },
          {
            label: 'CAN总线',
            value: 'ensh.6.1.6',
          },
          {
            label: '车联网防火墙',
            value: 'ensh.6.1.7',
          },
        ],
      },
      {
        label: '智能安防',
        value: 'ensh.6.2',
        children: [
          {
            label: '智能安防硬件',
            value: 'ensh.6.2.1',
          },
          {
            label: '智能巡检系统',
            value: 'ensh.6.2.2',
          },
          {
            label: '智能报警系统',
            value: 'ensh.6.2.3',
          },
          {
            label: '智能锁',
            value: 'ensh.6.2.4',
          },
          {
            label: '远程视频监控',
            value: 'ensh.6.2.5',
          },
          {
            label: '数字安防',
            value: 'ensh.6.2.6',
          },
          {
            label: '家用安防系统',
            value: 'ensh.6.2.7',
          },
        ],
      },
      {
        label: '物联网管理支持',
        value: 'ensh.6.3',
        children: [
          {
            label: '物联大数据云平台',
            value: 'ensh.6.3.1',
          },
          {
            label: '物联网云计算',
            value: 'ensh.6.3.2',
          },
          {
            label: '物联网云平台',
            value: 'ensh.6.3.3',
          },
          {
            label: '物联网智能终端',
            value: 'ensh.6.3.4',
          },
          {
            label: '物联网服务',
            value: 'ensh.6.3.5',
          },
        ],
      },
      {
        label: '物联网基础技术',
        value: 'ensh.6.4',
        children: [
          {
            label: 'RFID',
            value: 'ensh.6.4.1',
          },
          {
            label: 'm2m',
            value: 'ensh.6.4.2',
          },
          {
            label: '光谱识别技术',
            value: 'ensh.6.4.3',
          },
          {
            label: '传感器技术',
            value: 'ensh.6.4.4',
          },
          {
            label: '嵌入式无线模块',
            value: 'ensh.6.4.5',
          },
          {
            label: '物联网中间件',
            value: 'ensh.6.4.6',
          },
          {
            label: '物联网芯片',
            value: 'ensh.6.4.7',
          },
        ],
      },
      {
        label: '物联网应用',
        value: 'ensh.6.5',
        children: [
          {
            label: '智慧城市',
            value: 'ensh.6.5.1',
          },
          {
            label: '智能电网',
            value: 'ensh.6.5.2',
          },
          {
            label: '智慧交通',
            value: 'ensh.6.5.3',
          },
          {
            label: '工业物联网',
            value: 'ensh.6.5.4',
          },
          {
            label: '农业物联网',
            value: 'ensh.6.5.5',
          },
          {
            label: '智慧供水',
            value: 'ensh.6.5.6',
          },
          {
            label: '智慧办公',
            value: 'ensh.6.5.7',
          },
          {
            label: '智慧酒店',
            value: 'ensh.6.5.8',
          },
          {
            label: '智慧物业',
            value: 'ensh.6.5.9',
          },
          {
            label: '智能消防',
            value: 'ensh.6.5.10',
          },
        ],
      },
    ],
  },
  {
    label: '智能硬件',
    value: 'ensh.7',
    children: [
      {
        label: '可穿戴设备',
        value: 'ensh.7.1',
        children: [
          {
            label: '智能手环',
            value: 'ensh.7.1.1',
          },
          {
            label: '智能手表',
            value: 'ensh.7.1.2',
          },
          {
            label: '智能口罩',
            value: 'ensh.7.1.3',
          },
          {
            label: 'VR头盔',
            value: 'ensh.7.1.4',
          },
          {
            label: 'VR眼镜',
            value: 'ensh.7.1.5',
          },
          {
            label: '生物传感设备',
            value: 'ensh.7.1.6',
          },
          {
            label: '足部可穿戴设备',
            value: 'ensh.7.1.7',
          },
          {
            label: '定位追踪器',
            value: 'ensh.7.1.8',
          },
          {
            label: '随身空气净化器',
            value: 'ensh.7.1.9',
          },
          {
            label: '体感枪',
            value: 'ensh.7.1.10',
          },
          {
            label: '手势控制臂环',
            value: 'ensh.7.1.11',
          },
        ],
      },
      {
        label: '智能家居',
        value: 'ensh.7.2',
        children: [
          {
            label: '智能马桶',
            value: 'ensh.7.2.1',
          },
          {
            label: '小家电',
            value: 'ensh.7.2.2',
          },
          {
            label: '智能厨房',
            value: 'ensh.7.2.3',
          },
          {
            label: '智能照明',
            value: 'ensh.7.2.4',
          },
          {
            label: '智能插座',
            value: 'ensh.7.2.5',
          },
          {
            label: '智能门锁',
            value: 'ensh.7.2.6',
          },
          {
            label: '智能楼宇',
            value: 'ensh.7.2.7',
          },
          {
            label: '电子感应垃圾桶',
            value: 'ensh.7.2.8',
          },
          {
            label: '智能门铃',
            value: 'ensh.7.2.9',
          },
          {
            label: '智能路由器',
            value: 'ensh.7.2.10',
          },
          {
            label: '智能音箱',
            value: 'ensh.7.2.11',
          },
          {
            label: '智能家庭影院',
            value: 'ensh.7.2.12',
          },
          {
            label: '智能烤箱',
            value: 'ensh.7.2.13',
          },
          {
            label: '家用水质监测设备',
            value: 'ensh.7.2.14',
          },
          {
            label: '智能投影',
            value: 'ensh.7.2.15',
          },
          {
            label: '智能淋浴',
            value: 'ensh.7.2.16',
          },
          {
            label: '智能门窗',
            value: 'ensh.7.2.17',
          },
          {
            label: '自助咖啡机',
            value: 'ensh.7.2.18',
          },
          {
            label: '智能睡眠产品',
            value: 'ensh.7.2.19',
          },
          {
            label: '自动叠衣衣柜',
            value: 'ensh.7.2.20',
          },
        ],
      },
      {
        label: '车载智能硬件',
        value: 'ensh.7.3',
      },
      {
        label: '智能玩具',
        value: 'ensh.7.4',
        children: [
          {
            label: '编程机器人',
            value: 'ensh.7.4.1',
          },
          {
            label: '陪伴机器人',
            value: 'ensh.7.4.2',
          },
          {
            label: '智能积木',
            value: 'ensh.7.4.3',
          },
        ],
      },
      {
        label: '消费类硬件',
        value: 'ensh.7.5',
        children: [
          {
            label: '智能手机',
            value: 'ensh.7.5.1',
          },
          {
            label: '智能电视',
            value: 'ensh.7.5.2',
          },
          {
            label: '运动相机',
            value: 'ensh.7.5.3',
          },
          {
            label: '手机配件',
            value: 'ensh.7.5.4',
          },
          {
            label: '空气净化器',
            value: 'ensh.7.5.5',
          },
          {
            label: '智能牙刷',
            value: 'ensh.7.5.6',
          },
          {
            label: '智能体温计',
            value: 'ensh.7.5.7',
          },
          {
            label: '扫地机器人',
            value: 'ensh.7.5.8',
          },
          {
            label: '高端吸尘器',
            value: 'ensh.7.5.9',
          },
          {
            label: '智能绿植',
            value: 'ensh.7.5.10',
          },
          {
            label: '智能称',
            value: 'ensh.7.5.11',
          },
          {
            label: '智能奶瓶',
            value: 'ensh.7.5.12',
          },
          {
            label: '智能耳机',
            value: 'ensh.7.5.13',
          },
          {
            label: '智能情趣用品',
            value: 'ensh.7.5.14',
          },
          {
            label: '电视盒子',
            value: 'ensh.7.5.15',
          },
          {
            label: '智能乐器',
            value: 'ensh.7.5.16',
          },
          {
            label: '智能鱼缸',
            value: 'ensh.7.5.17',
          },
        ],
      },
      {
        label: '医疗智能硬件',
        value: 'ensh.7.6',
        children: [
          {
            label: '血糖仪',
            value: 'ensh.7.6.1',
          },
          {
            label: '智能轮椅',
            value: 'ensh.7.6.2',
          },
          {
            label: '人工耳蜗',
            value: 'ensh.7.6.3',
          },
          {
            label: '健康监测硬件',
            value: 'ensh.7.6.4',
          },
          {
            label: '胎儿监测贴',
            value: 'ensh.7.6.5',
          },
          {
            label: '智能假肢',
            value: 'ensh.7.6.6',
          },
          {
            label: '智能助孕仪',
            value: 'ensh.7.6.7',
          },
          {
            label: '智能测肤设备',
            value: 'ensh.7.6.8',
          },
          {
            label: '智能戒烟设备',
            value: 'ensh.7.6.9',
          },
        ],
      },
      {
        label: '农业智能硬件',
        value: 'ensh.7.7',
        children: [
          {
            label: '农业传感器',
            value: 'ensh.7.7.1',
          },
          {
            label: '智慧畜牧',
            value: 'ensh.7.7.2',
          },
          {
            label: '养殖水质监测设备',
            value: 'ensh.7.7.3',
          },
          {
            label: '农田智能监测器',
            value: 'ensh.7.7.4',
          },
          {
            label: '动物电子标识',
            value: 'ensh.7.7.5',
          },
          {
            label: '小型气象站',
            value: 'ensh.7.7.6',
          },
          {
            label: '智能捕虫计',
            value: 'ensh.7.7.7',
          },
          {
            label: '水肥一体机',
            value: 'ensh.7.7.8',
          },
          {
            label: '温室一体机',
            value: 'ensh.7.7.9',
          },
          {
            label: '牧场智能监测器',
            value: 'ensh.7.7.10',
          },
        ],
      },
      {
        label: '体育智能硬件',
        value: 'ensh.7.8',
        children: [
          {
            label: '运动头盔',
            value: 'ensh.7.8.1',
          },
          {
            label: '智能运动器材',
            value: 'ensh.7.8.2',
          },
          {
            label: '足球智能穿戴设备',
            value: 'ensh.7.8.3',
          },
          {
            label: '智能滑雪记录仪',
            value: 'ensh.7.8.4',
          },
          {
            label: '智能羽毛球拍',
            value: 'ensh.7.8.5',
          },
          {
            label: '智能网球拍',
            value: 'ensh.7.8.6',
          },
          {
            label: '智能篮球',
            value: 'ensh.7.8.7',
          },
          {
            label: '智能足球',
            value: 'ensh.7.8.8',
          },
          {
            label: '智慧球场',
            value: 'ensh.7.8.9',
          },
          {
            label: 'VR跑步机',
            value: 'ensh.7.8.10',
          },
          {
            label: '智能鞋垫',
            value: 'ensh.7.8.11',
          },
          {
            label: '室内滑雪模拟机',
            value: 'ensh.7.8.12',
          },
        ],
      },
      {
        label: '智能代步',
        value: 'ensh.7.9',
        children: [
          {
            label: '平衡车',
            value: 'ensh.7.9.1',
          },
          {
            label: '智能自行车',
            value: 'ensh.7.9.2',
          },
          {
            label: '智能电动车',
            value: 'ensh.7.9.3',
          },
          {
            label: '电动滑板',
            value: 'ensh.7.9.4',
          },
        ],
      },
    ],
  },
  {
    label: '体育',
    value: 'ensh.8',
    children: [
      {
        label: '体育运动',
        value: 'ensh.8.1',
        children: [
          {
            label: '瑜伽',
            value: 'ensh.8.1.1',
          },
          {
            label: '舞蹈',
            value: 'ensh.8.1.2',
          },
          {
            label: '极限运动',
            value: 'ensh.8.1.3',
          },
          {
            label: '户外运动',
            value: 'ensh.8.1.4',
          },
          {
            label: '跑步',
            value: 'ensh.8.1.5',
          },
          {
            label: '高尔夫',
            value: 'ensh.8.1.6',
          },
          {
            label: '游泳',
            value: 'ensh.8.1.7',
          },
          {
            label: '滑雪',
            value: 'ensh.8.1.8',
          },
          {
            label: '马拉松',
            value: 'ensh.8.1.9',
          },
          {
            label: '射箭',
            value: 'ensh.8.1.10',
          },
          {
            label: '篮球运动',
            value: 'ensh.8.1.11',
          },
          {
            label: '足球运动',
            value: 'ensh.8.1.12',
          },
          {
            label: '格斗搏击',
            value: 'ensh.8.1.13',
          },
          {
            label: '拳击',
            value: 'ensh.8.1.14',
          },
          {
            label: '水上运动',
            value: 'ensh.8.1.15',
          },
          {
            label: '网球运动',
            value: 'ensh.8.1.16',
          },
          {
            label: '羽毛球运动',
            value: 'ensh.8.1.17',
          },
          {
            label: '乒乓球运动',
            value: 'ensh.8.1.18',
          },
        ],
      },
      {
        label: '体育赛事',
        value: 'ensh.8.2',
        children: [
          {
            label: '赛事运营',
            value: 'ensh.8.2.1',
          },
          {
            label: '赛事票务',
            value: 'ensh.8.2.2',
          },
          {
            label: '赛事IP',
            value: 'ensh.8.2.3',
          },
          {
            label: '赛场运营',
            value: 'ensh.8.2.4',
          },
          {
            label: '赛事宣传',
            value: 'ensh.8.2.5',
          },
          {
            label: '体育经纪人',
            value: 'ensh.8.2.6',
          },
        ],
      },
      {
        label: '健身',
        value: 'ensh.8.3',
        children: [
          {
            label: '减肥',
            value: 'ensh.8.3.1',
          },
          {
            label: '健身房',
            value: 'ensh.8.3.2',
          },
          {
            label: '健身器材',
            value: 'ensh.8.3.3',
          },
          {
            label: '健身教练',
            value: 'ensh.8.3.4',
          },
          {
            label: '健身课程',
            value: 'ensh.8.3.5',
          },
          {
            label: '健身管理',
            value: 'ensh.8.3.6',
          },
          {
            label: '共享健身房',
            value: 'ensh.8.3.7',
          },
          {
            label: 'Mini健身仓',
            value: 'ensh.8.3.8',
          },
          {
            label: '健身app',
            value: 'ensh.8.3.9',
          },
          {
            label: '24h健身房',
            value: 'ensh.8.3.10',
          },
          {
            label: '无人健身房',
            value: 'ensh.8.3.11',
          },
          {
            label: '健身教练资格培训',
            value: 'ensh.8.3.12',
          },
          {
            label: '健身服饰',
            value: 'ensh.8.3.13',
          },
          {
            label: '健身营养品',
            value: 'ensh.8.3.14',
          },
          {
            label: '健身方案定制',
            value: 'ensh.8.3.15',
          },
        ],
      },
      {
        label: '电子竞技',
        value: 'ensh.8.4',
        children: [
          {
            label: '电竞直播',
            value: 'ensh.8.4.1',
          },
          {
            label: '电竞战队',
            value: 'ensh.8.4.2',
          },
          {
            label: '电竞赛事',
            value: 'ensh.8.4.3',
          },
          {
            label: '电竞场馆',
            value: 'ensh.8.4.4',
          },
          {
            label: '电竞培训',
            value: 'ensh.8.4.5',
          },
          {
            label: '电竞数据分析',
            value: 'ensh.8.4.6',
          },
          {
            label: '电竞俱乐部',
            value: 'ensh.8.4.7',
          },
          {
            label: '电竞内容制作',
            value: 'ensh.8.4.8',
          },
        ],
      },
      {
        label: '体育媒体',
        value: 'ensh.8.5',
        children: [
          {
            label: '体育资讯',
            value: 'ensh.8.5.1',
          },
          {
            label: '体育媒体社区',
            value: 'ensh.8.5.2',
          },
          {
            label: '体育自媒体',
            value: 'ensh.8.5.3',
          },
        ],
      },
      {
        label: '体育场馆',
        value: 'ensh.8.6',
        children: [
          {
            label: '场馆预定',
            value: 'ensh.8.6.1',
          },
          {
            label: '场馆运营',
            value: 'ensh.8.6.2',
          },
        ],
      },
      {
        label: '体育培训',
        value: 'ensh.8.7',
        children: [
          {
            label: '健身教练培训',
            value: 'ensh.8.7.1',
          },
          {
            label: '青少年体育培训',
            value: 'ensh.8.7.2',
          },
        ],
      },
      {
        label: '体育大数据',
        value: 'ensh.8.8',
      },
      {
        label: '体育智能硬件',
        value: 'ensh.8.9',
      },
      {
        label: '体育消费品',
        value: 'ensh.8.10',
        children: [
          {
            label: '瑜伽用品',
            value: 'ensh.8.10.1',
          },
          {
            label: '篮球用品',
            value: 'ensh.8.10.2',
          },
          {
            label: '跑步用品',
            value: 'ensh.8.10.3',
          },
          {
            label: '游泳用品',
            value: 'ensh.8.10.4',
          },
          {
            label: '滑雪用品',
            value: 'ensh.8.10.5',
          },
          {
            label: '足球用品',
            value: 'ensh.8.10.6',
          },
          {
            label: '舞蹈用品',
            value: 'ensh.8.10.7',
          },
          {
            label: '体育营养品',
            value: 'ensh.8.10.8',
          },
          {
            label: '户外运动装备',
            value: 'ensh.8.10.9',
          },
        ],
      },
      {
        label: '体育社交',
        value: 'ensh.8.11',
        children: [
          {
            label: '跑步社区',
            value: 'ensh.8.11.1',
          },
          {
            label: '户外运动社交',
            value: 'ensh.8.11.2',
          },
          {
            label: '极限运动社交',
            value: 'ensh.8.11.3',
          },
          {
            label: '足球论坛',
            value: 'ensh.8.11.4',
          },
          {
            label: '篮球论坛',
            value: 'ensh.8.11.5',
          },
        ],
      },
    ],
  },
  {
    label: '旅游',
    value: 'ensh.9',
    children: [
      {
        label: '旅游信息化',
        value: 'ensh.9.1',
        children: [
          {
            label: '旅游大数据',
            value: 'ensh.9.1.1',
          },
        ],
      },
      {
        label: '旅游媒体',
        value: 'ensh.9.2',
        children: [
          {
            label: '旅行综艺',
            value: 'ensh.9.2.1',
          },
          {
            label: '旅游地方门户',
            value: 'ensh.9.2.2',
          },
        ],
      },
      {
        label: '旅游金融',
        value: 'ensh.9.3',
        children: [
          {
            label: '旅游保险',
            value: 'ensh.9.3.1',
          },
        ],
      },
      {
        label: '预订平台',
        value: 'ensh.9.4',
        children: [
          {
            label: '酒店预订',
            value: 'ensh.9.4.1',
          },
          {
            label: '机票预订',
            value: 'ensh.9.4.2',
          },
          {
            label: '船票预订',
            value: 'ensh.9.4.3',
          },
          {
            label: '车票预订',
            value: 'ensh.9.4.4',
          },
          {
            label: '门票预订',
            value: 'ensh.9.4.5',
          },
          {
            label: '民宿预订',
            value: 'ensh.9.4.6',
          },
        ],
      },
      {
        label: '酒店民宿',
        value: 'ensh.9.5',
        children: [
          {
            label: '酒店品牌',
            value: 'ensh.9.5.1',
          },
          {
            label: '酒店管理系统',
            value: 'ensh.9.5.2',
          },
          {
            label: '民宿品牌',
            value: 'ensh.9.5.3',
          },
        ],
      },
      {
        label: '向导服务',
        value: 'ensh.9.6',
        children: [
          {
            label: '导游',
            value: 'ensh.9.6.1',
          },
          {
            label: '智能语音导游',
            value: 'ensh.9.6.2',
          },
        ],
      },
      {
        label: '景区服务',
        value: 'ensh.9.7',
        children: [
          {
            label: '智慧景区',
            value: 'ensh.9.7.1',
          },
          {
            label: '景区管理系统',
            value: 'ensh.9.7.2',
          },
          {
            label: '景区流量管理',
            value: 'ensh.9.7.3',
          },
        ],
      },
      {
        label: '旅游出行',
        value: 'ensh.9.8',
        children: [
          {
            label: '签证',
            value: 'ensh.9.8.1',
          },
          {
            label: '随身WiFi',
            value: 'ensh.9.8.2',
          },
          {
            label: '旅游包车',
            value: 'ensh.9.8.3',
          },
        ],
      },
      {
        label: '旅游产品',
        value: 'ensh.9.9',
        children: [
          {
            label: '亲子游',
            value: 'ensh.9.9.1',
          },
          {
            label: '出境游',
            value: 'ensh.9.9.2',
          },
          {
            label: '自由行',
            value: 'ensh.9.9.3',
          },
          {
            label: '户外游',
            value: 'ensh.9.9.4',
          },
          {
            label: '周边游',
            value: 'ensh.9.9.5',
          },
          {
            label: '旅行社',
            value: 'ensh.9.9.6',
          },
          {
            label: '商旅',
            value: 'ensh.9.9.7',
          },
          {
            label: '旅游B2B',
            value: 'ensh.9.9.8',
          },
          {
            label: '定制游',
            value: 'ensh.9.9.9',
          },
          {
            label: '跟团游',
            value: 'ensh.9.9.10',
          },
          {
            label: '乡村旅游',
            value: 'ensh.9.9.11',
          },
          {
            label: '团队游',
            value: 'ensh.9.9.12',
          },
          {
            label: '半自由行',
            value: 'ensh.9.9.13',
          },
          {
            label: '体验式旅游',
            value: 'ensh.9.9.14',
          },
          {
            label: '目的地游',
            value: 'ensh.9.9.15',
          },
          {
            label: 'OTA',
            value: 'ensh.9.9.16',
          },
        ],
      },
      {
        label: '旅游电商',
        value: 'ensh.9.10',
        children: [
          {
            label: '旅游购物',
            value: 'ensh.9.10.1',
          },
          {
            label: '当地特产',
            value: 'ensh.9.10.2',
          },
          {
            label: '旅游导购',
            value: 'ensh.9.10.3',
          },
          {
            label: '装备租赁',
            value: 'ensh.9.10.4',
          },
        ],
      },
      {
        label: '旅游社交',
        value: 'ensh.9.11',
        children: [
          {
            label: '结伴游',
            value: 'ensh.9.11.1',
          },
          {
            label: '游记分享社区',
            value: 'ensh.9.11.2',
          },
          {
            label: '旅行直播',
            value: 'ensh.9.11.3',
          },
          {
            label: '背包客旅游',
            value: 'ensh.9.11.4',
          },
        ],
      },
    ],
  },
  {
    label: '社交',
    value: 'ensh.10',
    children: [
      {
        label: '语音社交',
        value: 'ensh.10.1',
        children: [
          {
            label: '口语学习社交',
            value: 'ensh.10.1.1',
          },
          {
            label: '声音交友',
            value: 'ensh.10.1.2',
          },
          {
            label: '陌生人语音聊天',
            value: 'ensh.10.1.3',
          },
          {
            label: '游戏语音社交',
            value: 'ensh.10.1.4',
          },
          {
            label: '语音电话',
            value: 'ensh.10.1.5',
          },
        ],
      },
      {
        label: '图片社交',
        value: 'ensh.10.2',
        children: [
          {
            label: '图片分享',
            value: 'ensh.10.2.1',
          },
          {
            label: '表情包',
            value: 'ensh.10.2.2',
          },
          {
            label: '动图社交',
            value: 'ensh.10.2.3',
          },
          {
            label: '即时图片社交',
            value: 'ensh.10.2.4',
          },
          {
            label: '拍照社交',
            value: 'ensh.10.2.5',
          },
          {
            label: '图片聊天',
            value: 'ensh.10.2.6',
          },
          {
            label: '有声图片',
            value: 'ensh.10.2.7',
          },
        ],
      },
      {
        label: '社区',
        value: 'ensh.10.3',
        children: [
          {
            label: '女性社区',
            value: 'ensh.10.3.1',
          },
          {
            label: '母婴社区',
            value: 'ensh.10.3.2',
          },
          {
            label: '医生社区',
            value: 'ensh.10.3.3',
          },
          {
            label: '电影社区',
            value: 'ensh.10.3.4',
          },
          {
            label: '汽车社区',
            value: 'ensh.10.3.5',
          },
          {
            label: '知识社区',
            value: 'ensh.10.3.6',
          },
          {
            label: '运动社区',
            value: 'ensh.10.3.7',
          },
          {
            label: '分享社区',
            value: 'ensh.10.3.8',
          },
          {
            label: '亲子社区',
            value: 'ensh.10.3.9',
          },
          {
            label: '情感倾诉社区',
            value: 'ensh.10.3.10',
          },
          {
            label: '理财社区',
            value: 'ensh.10.3.11',
          },
          {
            label: '辩论社区',
            value: 'ensh.10.3.12',
          },
          {
            label: '综合内容社区',
            value: 'ensh.10.3.13',
          },
          {
            label: '活动推荐社区',
            value: 'ensh.10.3.14',
          },
          {
            label: '文玩社区',
            value: 'ensh.10.3.15',
          },
          {
            label: '禅意生活社群',
            value: 'ensh.10.3.16',
          },
          {
            label: '患者交流社区',
            value: 'ensh.10.3.17',
          },
          {
            label: 'VR内容社区',
            value: 'ensh.10.3.18',
          },
          {
            label: '写作社区',
            value: 'ensh.10.3.19',
          },
        ],
      },
      {
        label: '社交4.0',
        value: 'ensh.10.4',
      },
      {
        label: '垂直人群社交',
        value: 'ensh.10.5',
        children: [
          {
            label: '新生代社交',
            value: 'ensh.10.5.1',
          },
          {
            label: '同性社交',
            value: 'ensh.10.5.2',
          },
          {
            label: '熟人社交',
            value: 'ensh.10.5.3',
          },
          {
            label: '校园社交',
            value: 'ensh.10.5.4',
          },
          {
            label: '女性社交',
            value: 'ensh.10.5.5',
          },
          {
            label: '蓝领社交',
            value: 'ensh.10.5.6',
          },
          {
            label: '男性社交',
            value: 'ensh.10.5.7',
          },
          {
            label: '中老年社交',
            value: 'ensh.10.5.8',
          },
          {
            label: '青少年社交',
            value: 'ensh.10.5.9',
          },
          {
            label: '留学生社交',
            value: 'ensh.10.5.10',
          },
        ],
      },
      {
        label: '兴趣社交',
        value: 'ensh.10.6',
        children: [
          {
            label: '宠物社交',
            value: 'ensh.10.6.1',
          },
          {
            label: '美食社交',
            value: 'ensh.10.6.2',
          },
          {
            label: '旅行社交',
            value: 'ensh.10.6.3',
          },
          {
            label: '运动社交',
            value: 'ensh.10.6.4',
          },
          {
            label: '音乐社交',
            value: 'ensh.10.6.5',
          },
          {
            label: '二次元社交',
            value: 'ensh.10.6.6',
          },
          {
            label: '技能社交',
            value: 'ensh.10.6.7',
          },
          {
            label: '骑行社交',
            value: 'ensh.10.6.8',
          },
          {
            label: '垂钓社交',
            value: 'ensh.10.6.9',
          },
          {
            label: '飞行爱好者社交',
            value: 'ensh.10.6.10',
          },
          {
            label: '军事迷社交',
            value: 'ensh.10.6.11',
          },
          {
            label: '占星社交',
            value: 'ensh.10.6.12',
          },
          {
            label: '艺术社交',
            value: 'ensh.10.6.13',
          },
          {
            label: '围棋社交',
            value: 'ensh.10.6.14',
          },
          {
            label: '动漫社交',
            value: 'ensh.10.6.15',
          },
          {
            label: '阅读社交',
            value: 'ensh.10.6.16',
          },
          {
            label: '情趣社交',
            value: 'ensh.10.6.17',
          },
          {
            label: '电影社交',
            value: 'ensh.10.6.18',
          },
          {
            label: '手艺人社交',
            value: 'ensh.10.6.19',
          },
          {
            label: '广场舞社交',
            value: 'ensh.10.6.20',
          },
        ],
      },
      {
        label: '陌生人社交',
        value: 'ensh.10.7',
        children: [
          {
            label: '同城交友',
            value: 'ensh.10.7.1',
          },
          {
            label: 'LBS社交',
            value: 'ensh.10.7.2',
          },
          {
            label: '婚恋社交',
            value: 'ensh.10.7.3',
          },
          {
            label: '匿名社交',
            value: 'ensh.10.7.4',
          },
          {
            label: '直播社交',
            value: 'ensh.10.7.5',
          },
          {
            label: '弹幕社交',
            value: 'ensh.10.7.6',
          },
          {
            label: '随机匹配交友',
            value: 'ensh.10.7.7',
          },
          {
            label: '国际社交',
            value: 'ensh.10.7.8',
          },
          {
            label: '陪聊社交',
            value: 'ensh.10.7.9',
          },
        ],
      },
      {
        label: '视频社交',
        value: 'ensh.10.8',
        children: [
          {
            label: '实时群组视频',
            value: 'ensh.10.8.1',
          },
          {
            label: '随机匹配视频交友',
            value: 'ensh.10.8.2',
          },
          {
            label: '美颜视频社交',
            value: 'ensh.10.8.3',
          },
          {
            label: '移动视频社交',
            value: 'ensh.10.8.4',
          },
          {
            label: '原创视频社交',
            value: 'ensh.10.8.5',
          },
          {
            label: '短视频社交',
            value: 'ensh.10.8.6',
          },
          {
            label: '一对一视频社交',
            value: 'ensh.10.8.7',
          },
        ],
      },
      {
        label: '社交媒体',
        value: 'ensh.10.9',
      },
    ],
  },
  {
    label: '农业',
    value: 'ensh.11',
    children: [
      {
        label: '农机',
        value: 'ensh.11.1',
        children: [
          {
            label: '农机研发',
            value: 'ensh.11.1.1',
          },
          {
            label: '农机维修',
            value: 'ensh.11.1.2',
          },
          {
            label: '农机交易',
            value: 'ensh.11.1.3',
          },
          {
            label: '农机融资租赁',
            value: 'ensh.11.1.4',
          },
        ],
      },
      {
        label: '农村金融',
        value: 'ensh.11.2',
        children: [
          {
            label: '农业供应链金融',
            value: 'ensh.11.2.1',
          },
          {
            label: '农业信贷',
            value: 'ensh.11.2.2',
          },
          {
            label: '农产品众筹',
            value: 'ensh.11.2.3',
          },
          {
            label: '农业保险',
            value: 'ensh.11.2.4',
          },
        ],
      },
      {
        label: '农业智能硬件',
        value: 'ensh.11.3',
      },
      {
        label: '生产种植技术',
        value: 'ensh.11.4',
        children: [
          {
            label: '微生物技术',
            value: 'ensh.11.4.1',
          },
          {
            label: '克隆及转基因',
            value: 'ensh.11.4.2',
          },
          {
            label: '分子态植物提纯',
            value: 'ensh.11.4.3',
          },
          {
            label: '杂交技术',
            value: 'ensh.11.4.4',
          },
          {
            label: '动物育种',
            value: 'ensh.11.4.5',
          },
          {
            label: '植物育种',
            value: 'ensh.11.4.6',
          },
          {
            label: '畜禽防治',
            value: 'ensh.11.4.7',
          },
          {
            label: '养殖技术',
            value: 'ensh.11.4.8',
          },
          {
            label: '作物提取',
            value: 'ensh.11.4.9',
          },
        ],
      },
      {
        label: '农村物流',
        value: 'ensh.11.5',
        children: [
          {
            label: '农机配送',
            value: 'ensh.11.5.1',
          },
          {
            label: '农村大件物流',
            value: 'ensh.11.5.2',
          },
        ],
      },
      {
        label: '农业信息化',
        value: 'ensh.11.6',
        children: [
          {
            label: '牧场SaaS',
            value: 'ensh.11.6.1',
          },
          {
            label: '养猪SaaS',
            value: 'ensh.11.6.2',
          },
          {
            label: '农业资讯平台',
            value: 'ensh.11.6.3',
          },
          {
            label: '农场管理系统',
            value: 'ensh.11.6.4',
          },
          {
            label: '农技分享',
            value: 'ensh.11.6.5',
          },
        ],
      },
      {
        label: '农业大数据',
        value: 'ensh.11.7',
      },
      {
        label: '农业服务',
        value: 'ensh.11.8',
        children: [
          {
            label: '农技服务',
            value: 'ensh.11.8.1',
          },
          {
            label: '动物保健',
            value: 'ensh.11.8.2',
          },
        ],
      },
      {
        label: '农产品流通',
        value: 'ensh.11.9',
        children: [
          {
            label: '农业电商',
            value: 'ensh.11.9.1',
          },
          {
            label: '农产品电商',
            value: 'ensh.11.9.2',
          },
          {
            label: '农产品品牌',
            value: 'ensh.11.9.3',
          },
          {
            label: '农产品加工',
            value: 'ensh.11.9.4',
          },
          {
            label: '品牌农业',
            value: 'ensh.11.9.5',
          },
          {
            label: '生鲜连锁',
            value: 'ensh.11.9.6',
          },
          {
            label: '农产品配送',
            value: 'ensh.11.9.7',
          },
        ],
      },
      {
        label: '农村土地',
        value: 'ensh.11.10',
        children: [
          {
            label: '农村土地流转',
            value: 'ensh.11.10.1',
          },
          {
            label: '农村土地资讯',
            value: 'ensh.11.10.2',
          },
        ],
      },
      {
        label: '农资',
        value: 'ensh.11.11',
        children: [
          {
            label: '饲料',
            value: 'ensh.11.11.1',
          },
          {
            label: '农药',
            value: 'ensh.11.11.2',
          },
          {
            label: '化肥',
            value: 'ensh.11.11.3',
          },
          {
            label: '种子',
            value: 'ensh.11.11.4',
          },
        ],
      },
      {
        label: '广义农业',
        value: 'ensh.11.12',
        children: [
          {
            label: '林业',
            value: 'ensh.11.12.1',
          },
          {
            label: '渔业',
            value: 'ensh.11.12.2',
          },
          {
            label: '畜牧业',
            value: 'ensh.11.12.3',
          },
          {
            label: '种植业',
            value: 'ensh.11.12.4',
          },
        ],
      },
      {
        label: '现代农业',
        value: 'ensh.11.13',
        children: [
          {
            label: '有机农业',
            value: 'ensh.11.13.1',
          },
        ],
      },
    ],
  },
  {
    label: '电子商务',
    value: 'ensh.12',
    children: [
      {
        label: 'B2B',
        value: 'ensh.12.1',
        children: [
          {
            label: '医药B2B',
            value: 'ensh.12.1.1',
          },
          {
            label: '食材B2B',
            value: 'ensh.12.1.2',
          },
          {
            label: '快消品B2B',
            value: 'ensh.12.1.3',
          },
          {
            label: '工业B2B',
            value: 'ensh.12.1.4',
          },
          {
            label: '纺织面料B2B',
            value: 'ensh.12.1.5',
          },
          {
            label: '生鲜B2B',
            value: 'ensh.12.1.6',
          },
          {
            label: '能源B2B',
            value: 'ensh.12.1.7',
          },
          {
            label: '电子元件B2B',
            value: 'ensh.12.1.8',
          },
          {
            label: '木材B2B',
            value: 'ensh.12.1.9',
          },
          {
            label: '化工原料B2B',
            value: 'ensh.12.1.10',
          },
          {
            label: '金属B2B',
            value: 'ensh.12.1.11',
          },
          {
            label: '办公用品B2B',
            value: 'ensh.12.1.12',
          },
          {
            label: '建材B2B',
            value: 'ensh.12.1.13',
          },
          {
            label: '珠宝B2B',
            value: 'ensh.12.1.14',
          },
          {
            label: '玻璃B2B',
            value: 'ensh.12.1.15',
          },
        ],
      },
      {
        label: 'S2b',
        value: 'ensh.12.2',
        children: [
          {
            label: 'S2b供应链',
            value: 'ensh.12.2.1',
          },
          {
            label: '家装S2b',
            value: 'ensh.12.2.2',
          },
          {
            label: '服装S2b',
            value: 'ensh.12.2.3',
          },
        ],
      },
      {
        label: '综合电商',
        value: 'ensh.12.3',
      },
      {
        label: '垂直电商',
        value: 'ensh.12.4',
        children: [
          {
            label: '奢侈品电商',
            value: 'ensh.12.4.1',
          },
          {
            label: '眼镜电商',
            value: 'ensh.12.4.2',
          },
          {
            label: '母婴电商',
            value: 'ensh.12.4.3',
          },
          {
            label: '生鲜电商',
            value: 'ensh.12.4.4',
          },
          {
            label: '艺术品电商',
            value: 'ensh.12.4.5',
          },
          {
            label: '医药电商',
            value: 'ensh.12.4.6',
          },
          {
            label: '农村电商',
            value: 'ensh.12.4.7',
          },
          {
            label: '服装电商',
            value: 'ensh.12.4.8',
          },
          {
            label: '家居电商',
            value: 'ensh.12.4.9',
          },
          {
            label: '酒类电商',
            value: 'ensh.12.4.10',
          },
          {
            label: '礼品电商',
            value: 'ensh.12.4.11',
          },
          {
            label: '美妆电商',
            value: 'ensh.12.4.12',
          },
          {
            label: '鲜花电商',
            value: 'ensh.12.4.13',
          },
        ],
      },
      {
        label: '新兴电商',
        value: 'ensh.12.5',
        children: [
          {
            label: '跨境电商',
            value: 'ensh.12.5.1',
          },
          {
            label: '社群电商',
            value: 'ensh.12.5.2',
          },
          {
            label: '微商',
            value: 'ensh.12.5.3',
          },
          {
            label: '二手电商',
            value: 'ensh.12.5.4',
          },
          {
            label: '内容电商',
            value: 'ensh.12.5.5',
          },
          {
            label: '定制电商',
            value: 'ensh.12.5.6',
          },
          {
            label: '导购电商',
            value: 'ensh.12.5.7',
          },
          {
            label: '在线便利店',
            value: 'ensh.12.5.8',
          },
          {
            label: '订阅式电商',
            value: 'ensh.12.5.9',
          },
          {
            label: '租赁电商',
            value: 'ensh.12.5.10',
          },
          {
            label: 'F2C电商',
            value: 'ensh.12.5.11',
          },
        ],
      },
      {
        label: '电商服务',
        value: 'ensh.12.6',
        children: [
          {
            label: '电商解决方案',
            value: 'ensh.12.6.1',
          },
          {
            label: '比价服务',
            value: 'ensh.12.6.2',
          },
        ],
      },
    ],
  },
  {
    label: '生活服务',
    value: 'ensh.13',
    children: [
      {
        label: '数码快印',
        value: 'ensh.13.1',
      },
      {
        label: '母婴用品',
        value: 'ensh.13.2',
      },
      {
        label: '共享经济',
        value: 'ensh.13.3',
        children: [
          {
            label: '共享充电宝',
            value: 'ensh.13.3.1',
          },
          {
            label: '共享雨伞',
            value: 'ensh.13.3.2',
          },
          {
            label: '共享珠宝',
            value: 'ensh.13.3.3',
          },
          {
            label: '共享衣橱',
            value: 'ensh.13.3.4',
          },
          {
            label: '共享家居',
            value: 'ensh.13.3.5',
          },
          {
            label: '共享篮球',
            value: 'ensh.13.3.6',
          },
          {
            label: '共享玩具',
            value: 'ensh.13.3.7',
          },
          {
            label: '共享奢侈品',
            value: 'ensh.13.3.8',
          },
          {
            label: '共享睡眠仓',
            value: 'ensh.13.3.9',
          },
          {
            label: '共享图书',
            value: 'ensh.13.3.10',
          },
          {
            label: '共享时间',
            value: 'ensh.13.3.11',
          },
        ],
      },
      {
        label: '婚嫁',
        value: 'ensh.13.4',
        children: [
          {
            label: '婚车',
            value: 'ensh.13.4.1',
          },
          {
            label: '婚礼策划',
            value: 'ensh.13.4.2',
          },
          {
            label: '婚礼定制',
            value: 'ensh.13.4.3',
          },
          {
            label: '婚恋交友',
            value: 'ensh.13.4.4',
          },
          {
            label: '蜜月旅行',
            value: 'ensh.13.4.5',
          },
          {
            label: '婚宴酒店服务',
            value: 'ensh.13.4.6',
          },
          {
            label: '相亲服务',
            value: 'ensh.13.4.7',
          },
          {
            label: '一站式婚礼',
            value: 'ensh.13.4.8',
          },
          {
            label: '婚礼珠宝服务',
            value: 'ensh.13.4.9',
          },
          {
            label: '婚纱服务',
            value: 'ensh.13.4.10',
          },
        ],
      },
      {
        label: '美业',
        value: 'ensh.13.5',
        children: [
          {
            label: '美妆',
            value: 'ensh.13.5.1',
          },
          {
            label: '美容院',
            value: 'ensh.13.5.2',
          },
          {
            label: '美发店',
            value: 'ensh.13.5.3',
          },
          {
            label: '整形医院',
            value: 'ensh.13.5.4',
          },
          {
            label: '美甲店',
            value: 'ensh.13.5.5',
          },
        ],
      },
      {
        label: '宠物服务',
        value: 'ensh.13.6',
        children: [
          {
            label: '宠物医疗',
            value: 'ensh.13.6.1',
          },
          {
            label: '宠物食品',
            value: 'ensh.13.6.2',
          },
          {
            label: '宠物美容',
            value: 'ensh.13.6.3',
          },
          {
            label: '宠物寄养',
            value: 'ensh.13.6.4',
          },
          {
            label: '宠物交易',
            value: 'ensh.13.6.5',
          },
          {
            label: '宠物玩具',
            value: 'ensh.13.6.6',
          },
          {
            label: '宠物旅游',
            value: 'ensh.13.6.7',
          },
          {
            label: '宠物保健',
            value: 'ensh.13.6.8',
          },
          {
            label: '宠物配种',
            value: 'ensh.13.6.9',
          },
          {
            label: '宠物直播',
            value: 'ensh.13.6.10',
          },
        ],
      },
      {
        label: '回收维修',
        value: 'ensh.13.7',
        children: [
          {
            label: '家电维修',
            value: 'ensh.13.7.1',
          },
          {
            label: '废品回收',
            value: 'ensh.13.7.2',
          },
          {
            label: '衣服捐赠回收',
            value: 'ensh.13.7.3',
          },
          {
            label: '上门回收',
            value: 'ensh.13.7.4',
          },
          {
            label: '数码回收',
            value: 'ensh.13.7.5',
          },
          {
            label: '日用品修理',
            value: 'ensh.13.7.6',
          },
          {
            label: '奢侈品回收',
            value: 'ensh.13.7.7',
          },
          {
            label: '二手数码回收',
            value: 'ensh.13.7.8',
          },
          {
            label: '金属回收',
            value: 'ensh.13.7.9',
          },
          {
            label: '废旧蓄电池回收',
            value: 'ensh.13.7.10',
          },
          {
            label: '数码设备维修',
            value: 'ensh.13.7.11',
          },
        ],
      },
      {
        label: '其他生活服务',
        value: 'ensh.13.8',
        children: [
          {
            label: '校园服务',
            value: 'ensh.13.8.1',
          },
          {
            label: 'WIFI服务',
            value: 'ensh.13.8.2',
          },
          {
            label: '彩票',
            value: 'ensh.13.8.3',
          },
          {
            label: '物业管理',
            value: 'ensh.13.8.4',
          },
          {
            label: '食品安全',
            value: 'ensh.13.8.5',
          },
          {
            label: '跑腿服务',
            value: 'ensh.13.8.6',
          },
          {
            label: '电子烟',
            value: 'ensh.13.8.7',
          },
          {
            label: '母婴服务',
            value: 'ensh.13.8.8',
          },
          {
            label: '殡葬服务',
            value: 'ensh.13.8.9',
          },
          {
            label: '真人管家',
            value: 'ensh.13.8.10',
          },
          {
            label: '在线祈福',
            value: 'ensh.13.8.11',
          },
          {
            label: '文印服务',
            value: 'ensh.13.8.12',
          },
          {
            label: '搬迁服务',
            value: 'ensh.13.8.13',
          },
        ],
      },
      {
        label: '本地生活',
        value: 'ensh.13.9',
        children: [
          {
            label: 'KTV',
            value: 'ensh.13.9.1',
          },
          {
            label: '生活资讯',
            value: 'ensh.13.9.2',
          },
          {
            label: '网吧',
            value: 'ensh.13.9.3',
          },
          {
            label: '公共服务',
            value: 'ensh.13.9.4',
          },
          {
            label: '游乐园',
            value: 'ensh.13.9.5',
          },
          {
            label: '私人影院',
            value: 'ensh.13.9.6',
          },
          {
            label: '棋牌室',
            value: 'ensh.13.9.7',
          },
          {
            label: '桌游吧',
            value: 'ensh.13.9.8',
          },
          {
            label: '一站式派对',
            value: 'ensh.13.9.9',
          },
          {
            label: '地区媒体',
            value: 'ensh.13.9.10',
          },
          {
            label: '浴场',
            value: 'ensh.13.9.11',
          },
          {
            label: '沐足',
            value: 'ensh.13.9.12',
          },
          {
            label: '按摩',
            value: 'ensh.13.9.13',
          },
          {
            label: '麻将馆',
            value: 'ensh.13.9.14',
          },
        ],
      },
      {
        label: '夜生活',
        value: 'ensh.13.10',
        children: [
          {
            label: '酒吧',
            value: 'ensh.13.10.1',
          },
          {
            label: '夜店',
            value: 'ensh.13.10.2',
          },
        ],
      },
      {
        label: '摄影',
        value: 'ensh.13.11',
        children: [
          {
            label: '婚纱摄影',
            value: 'ensh.13.11.1',
          },
          {
            label: '摄影O2O',
            value: 'ensh.13.11.2',
          },
          {
            label: '旅拍',
            value: 'ensh.13.11.3',
          },
          {
            label: '儿童摄影',
            value: 'ensh.13.11.4',
          },
          {
            label: '个人写真',
            value: 'ensh.13.11.5',
          },
          {
            label: '孕婴摄影',
            value: 'ensh.13.11.6',
          },
          {
            label: '家庭摄影',
            value: 'ensh.13.11.7',
          },
          {
            label: '摄影工作室',
            value: 'ensh.13.11.8',
          },
          {
            label: '证件照拍摄',
            value: 'ensh.13.11.9',
          },
          {
            label: '摄影消费团购',
            value: 'ensh.13.11.10',
          },
          {
            label: '修图软件',
            value: 'ensh.13.11.11',
          },
          {
            label: '证件照自助机',
            value: 'ensh.13.11.12',
          },
        ],
      },
      {
        label: '居家服务',
        value: 'ensh.13.12',
        children: [
          {
            label: '月嫂',
            value: 'ensh.13.12.1',
          },
          {
            label: '搬家',
            value: 'ensh.13.12.2',
          },
          {
            label: '洗衣',
            value: 'ensh.13.12.3',
          },
          {
            label: '推拿',
            value: 'ensh.13.12.4',
          },
          {
            label: '送水',
            value: 'ensh.13.12.5',
          },
          {
            label: '家政维修',
            value: 'ensh.13.12.6',
          },
          {
            label: '二手物交易',
            value: 'ensh.13.12.7',
          },
          {
            label: '家政保洁',
            value: 'ensh.13.12.8',
          },
        ],
      },
      {
        label: '餐饮',
        value: 'ensh.13.13',
        children: [
          {
            label: '餐饮信息化',
            value: 'ensh.13.13.1',
          },
          {
            label: '餐饮O2O',
            value: 'ensh.13.13.2',
          },
          {
            label: '外卖品牌',
            value: 'ensh.13.13.3',
          },
          {
            label: '甜品店',
            value: 'ensh.13.13.4',
          },
          {
            label: '品牌餐饮',
            value: 'ensh.13.13.5',
          },
          {
            label: '餐饮连锁',
            value: 'ensh.13.13.6',
          },
          {
            label: '特产小吃',
            value: 'ensh.13.13.7',
          },
          {
            label: '餐饮团购',
            value: 'ensh.13.13.8',
          },
          {
            label: '外卖平台',
            value: 'ensh.13.13.9',
          },
          {
            label: '奶茶店',
            value: 'ensh.13.13.10',
          },
          {
            label: '咖啡店',
            value: 'ensh.13.13.11',
          },
          {
            label: '外卖',
            value: 'ensh.13.13.12',
          },
        ],
      },
    ],
  },
  {
    label: '先进制造',
    value: 'ensh.14',
    children: [
      {
        label: '无人机',
        value: 'ensh.14.1',
      },
      {
        label: '3D打印',
        value: 'ensh.14.2',
        children: [
          {
            label: '工业3D打印',
            value: 'ensh.14.2.1',
          },
          {
            label: '服装鞋包3D打印',
            value: 'ensh.14.2.2',
          },
          {
            label: '汽车配件3D打印',
            value: 'ensh.14.2.3',
          },
          {
            label: '食物3D打印',
            value: 'ensh.14.2.4',
          },
          {
            label: '房屋建筑3D打印',
            value: 'ensh.14.2.5',
          },
        ],
      },
      {
        label: '无线充电',
        value: 'ensh.14.3',
        children: [
          {
            label: '共享充电',
            value: 'ensh.14.3.1',
          },
          {
            label: '手机无线充电',
            value: 'ensh.14.3.2',
          },
          {
            label: 'NFC电源',
            value: 'ensh.14.3.3',
          },
          {
            label: '无线充电芯片',
            value: 'ensh.14.3.4',
          },
          {
            label: '无线充电设备',
            value: 'ensh.14.3.5',
          },
        ],
      },
      {
        label: '航空航天',
        value: 'ensh.14.4',
        children: [
          {
            label: '卫星',
            value: 'ensh.14.4.1',
          },
          {
            label: '火箭',
            value: 'ensh.14.4.2',
          },
          {
            label: '航天技术',
            value: 'ensh.14.4.3',
          },
          {
            label: '飞行模拟机',
            value: 'ensh.14.4.4',
          },
          {
            label: '航空器零部件',
            value: 'ensh.14.4.5',
          },
        ],
      },
      {
        label: '集成电路',
        value: 'ensh.14.5',
        children: [
          {
            label: '半导体器件',
            value: 'ensh.14.5.1',
          },
          {
            label: 'IC设计',
            value: 'ensh.14.5.2',
          },
          {
            label: '电子信息材料',
            value: 'ensh.14.5.3',
          },
          {
            label: '半导体设备',
            value: 'ensh.14.5.4',
          },
        ],
      },
      {
        label: '传感设备',
        value: 'ensh.14.6',
        children: [
          {
            label: '激光雷达',
            value: 'ensh.14.6.1',
          },
          {
            label: '传感器芯片',
            value: 'ensh.14.6.2',
          },
          {
            label: '传感摄像头',
            value: 'ensh.14.6.3',
          },
          {
            label: '毫米波雷达',
            value: 'ensh.14.6.4',
          },
          {
            label: '雷达传感器',
            value: 'ensh.14.6.5',
          },
          {
            label: '激光传感器',
            value: 'ensh.14.6.6',
          },
          {
            label: '光纤传感器',
            value: 'ensh.14.6.7',
          },
          {
            label: '声波传感器',
            value: 'ensh.14.6.8',
          },
          {
            label: '光电传感器',
            value: 'ensh.14.6.9',
          },
          {
            label: '半导体传感器',
            value: 'ensh.14.6.10',
          },
          {
            label: '3D传感器',
            value: 'ensh.14.6.11',
          },
          {
            label: '自动化传感器',
            value: 'ensh.14.6.12',
          },
          {
            label: '生物传感器',
            value: 'ensh.14.6.13',
          },
          {
            label: '磁传感器',
            value: 'ensh.14.6.14',
          },
          {
            label: '压力传感器',
            value: 'ensh.14.6.15',
          },
          {
            label: 'MEMS惯性传感器',
            value: 'ensh.14.6.16',
          },
          {
            label: '光谱传感器',
            value: 'ensh.14.6.17',
          },
          {
            label: '直读传感器',
            value: 'ensh.14.6.18',
          },
        ],
      },
      {
        label: '新材料',
        value: 'ensh.14.7',
        children: [
          {
            label: '纳米材料',
            value: 'ensh.14.7.1',
          },
          {
            label: '磁性材料',
            value: 'ensh.14.7.2',
          },
          {
            label: '光学材料',
            value: 'ensh.14.7.3',
          },
          {
            label: '节能环保材料',
            value: 'ensh.14.7.4',
          },
          {
            label: '先进复合材料',
            value: 'ensh.14.7.5',
          },
          {
            label: '先进金属材料',
            value: 'ensh.14.7.6',
          },
          {
            label: '先进陶瓷材料',
            value: 'ensh.14.7.7',
          },
          {
            label: '稀土材料',
            value: 'ensh.14.7.8',
          },
          {
            label: '碳材料',
            value: 'ensh.14.7.9',
          },
          {
            label: '膜材料',
            value: 'ensh.14.7.10',
          },
          {
            label: '超导材料',
            value: 'ensh.14.7.11',
          },
          {
            label: '生物医用材料',
            value: 'ensh.14.7.12',
          },
          {
            label: '新型建筑材料',
            value: 'ensh.14.7.13',
          },
        ],
      },
      {
        label: '工业4.0',
        value: 'ensh.14.8',
        children: [
          {
            label: '工业自动化',
            value: 'ensh.14.8.1',
          },
          {
            label: '工业信息化',
            value: 'ensh.14.8.2',
          },
        ],
      },
      {
        label: '智能制造装备',
        value: 'ensh.14.9',
        children: [
          {
            label: '数控机床',
            value: 'ensh.14.9.1',
          },
          {
            label: '大型智能工程机械',
            value: 'ensh.14.9.2',
          },
          {
            label: '高效农业机械',
            value: 'ensh.14.9.3',
          },
          {
            label: '自动化纺织机械',
            value: 'ensh.14.9.4',
          },
          {
            label: '精密和智能仪器仪表',
            value: 'ensh.14.9.5',
          },
          {
            label: '精密机械',
            value: 'ensh.14.9.6',
          },
          {
            label: '自动化设备',
            value: 'ensh.14.9.7',
          },
          {
            label: '智能印刷机械',
            value: 'ensh.14.9.8',
          },
        ],
      },
      {
        label: '工业机器人',
        value: 'ensh.14.10',
      },
    ],
  },
  {
    label: '机器人',
    value: 'ensh.15',
    children: [
      {
        label: '娱乐机器人',
        value: 'ensh.15.1',
        children: [
          {
            label: '聊天机器人',
            value: 'ensh.15.1.1',
          },
          {
            label: '跳舞机器人',
            value: 'ensh.15.1.2',
          },
          {
            label: '音乐机器人',
            value: 'ensh.15.1.3',
          },
          {
            label: '足球机器人',
            value: 'ensh.15.1.4',
          },
          {
            label: '玩具机器人',
            value: 'ensh.15.1.5',
          },
          {
            label: '格斗机器人',
            value: 'ensh.15.1.6',
          },
        ],
      },
      {
        label: '商用机器人',
        value: 'ensh.15.2',
        children: [
          {
            label: '清洁机器人',
            value: 'ensh.15.2.1',
          },
          {
            label: '送餐机器人',
            value: 'ensh.15.2.2',
          },
          {
            label: '仿人机器人',
            value: 'ensh.15.2.3',
          },
          {
            label: '点餐机器人',
            value: 'ensh.15.2.4',
          },
          {
            label: '迎宾机器人',
            value: 'ensh.15.2.5',
          },
          {
            label: '巡逻机器人',
            value: 'ensh.15.2.6',
          },
          {
            label: '物业机器人',
            value: 'ensh.15.2.7',
          },
          {
            label: '导航机器人',
            value: 'ensh.15.2.8',
          },
          {
            label: '导购机器人',
            value: 'ensh.15.2.9',
          },
        ],
      },
      {
        label: '医疗机器人',
        value: 'ensh.15.3',
      },
      {
        label: '机器人零部件',
        value: 'ensh.15.4',
      },
      {
        label: '机器人技术',
        value: 'ensh.15.5',
        children: [
          {
            label: '智能控制系统',
            value: 'ensh.15.5.1',
          },
          {
            label: '伺服系统',
            value: 'ensh.15.5.2',
          },
          {
            label: 'SLAM技术',
            value: 'ensh.15.5.3',
          },
          {
            label: '运动规划算法',
            value: 'ensh.15.5.4',
          },
          {
            label: '仿生技术',
            value: 'ensh.15.5.5',
          },
        ],
      },
      {
        label: '机器人定制',
        value: 'ensh.15.6',
      },
      {
        label: '物流机器人',
        value: 'ensh.15.7',
        children: [
          {
            label: '送货机器人',
            value: 'ensh.15.7.1',
          },
          {
            label: '装货机器人',
            value: 'ensh.15.7.2',
          },
          {
            label: '搬运机器人',
            value: 'ensh.15.7.3',
          },
          {
            label: '分拣机器人',
            value: 'ensh.15.7.4',
          },
        ],
      },
      {
        label: '工业机器人',
        value: 'ensh.15.8',
        children: [
          {
            label: '移动机器人',
            value: 'ensh.15.8.1',
          },
          {
            label: '安防机器人',
            value: 'ensh.15.8.2',
          },
          {
            label: '协作机器人',
            value: 'ensh.15.8.3',
          },
          {
            label: '锡焊机器人',
            value: 'ensh.15.8.4',
          },
          {
            label: '桌面机器人',
            value: 'ensh.15.8.5',
          },
          {
            label: '机械臂',
            value: 'ensh.15.8.6',
          },
          {
            label: '工业检测机器人',
            value: 'ensh.15.8.7',
          },
          {
            label: '软体机器人',
            value: 'ensh.15.8.8',
          },
          {
            label: '工业搬运机器人',
            value: 'ensh.15.8.9',
          },
          {
            label: '涂装机器人',
            value: 'ensh.15.8.10',
          },
          {
            label: '水果去核去皮机器人',
            value: 'ensh.15.8.11',
          },
          {
            label: '测量机器人',
            value: 'ensh.15.8.12',
          },
        ],
      },
      {
        label: '水下机器人',
        value: 'ensh.15.9',
        children: [
          {
            label: '水下测绘机器人',
            value: 'ensh.15.9.1',
          },
          {
            label: '载人潜水机器人',
            value: 'ensh.15.9.2',
          },
          {
            label: '水下打捞机器人',
            value: 'ensh.15.9.3',
          },
          {
            label: '深海机器人',
            value: 'ensh.15.9.4',
          },
          {
            label: '自主水下机器人',
            value: 'ensh.15.9.5',
          },
          {
            label: '有缆遥控水下机器人',
            value: 'ensh.15.9.6',
          },
          {
            label: '水下探测机器人',
            value: 'ensh.15.9.7',
          },
          {
            label: '水下摄影机器人',
            value: 'ensh.15.9.8',
          },
        ],
      },
      {
        label: '家用机器人',
        value: 'ensh.15.10',
        children: [
          {
            label: '机器人管家',
            value: 'ensh.15.10.1',
          },
          {
            label: '睡眠机器人',
            value: 'ensh.15.10.2',
          },
        ],
      },
      {
        label: '教育机器人',
        value: 'ensh.15.11',
        children: [
          {
            label: '早教机器人',
            value: 'ensh.15.11.1',
          },
          {
            label: '拟脑机器人',
            value: 'ensh.15.11.2',
          },
          {
            label: '钢琴陪练机器人',
            value: 'ensh.15.11.3',
          },
          {
            label: '助教机器人',
            value: 'ensh.15.11.4',
          },
          {
            label: '编程教育机器人',
            value: 'ensh.15.11.5',
          },
          {
            label: '作业批改机器人',
            value: 'ensh.15.11.6',
          },
          {
            label: '远程听课机器人',
            value: 'ensh.15.11.7',
          },
        ],
      },
      {
        label: '机器人竞赛',
        value: 'ensh.15.12',
        children: [
          {
            label: '机器人格斗赛事',
            value: 'ensh.15.12.1',
          },
          {
            label: '机器人制造赛事',
            value: 'ensh.15.12.2',
          },
        ],
      },
      {
        label: '救援机器人',
        value: 'ensh.15.13',
      },
      {
        label: '无人机',
        value: 'ensh.15.14',
        children: [
          {
            label: '工业无人机',
            value: 'ensh.15.14.1',
          },
          {
            label: '军用无人机',
            value: 'ensh.15.14.2',
          },
          {
            label: '植保无人机',
            value: 'ensh.15.14.3',
          },
          {
            label: '消费无人机',
            value: 'ensh.15.14.4',
          },
          {
            label: '无人机管理平台',
            value: 'ensh.15.14.5',
          },
          {
            label: '物流无人机',
            value: 'ensh.15.14.6',
          },
          {
            label: '航拍无人机',
            value: 'ensh.15.14.7',
          },
          {
            label: '反无人机系统',
            value: 'ensh.15.14.8',
          },
          {
            label: '海洋无人机',
            value: 'ensh.15.14.9',
          },
        ],
      },
    ],
  },
  {
    label: '人工智能',
    value: 'ensh.16',
    children: [
      {
        label: '自然语言处理',
        value: 'ensh.16.1',
        children: [
          {
            label: '文本分析',
            value: 'ensh.16.1.1',
          },
          {
            label: '知识图谱',
            value: 'ensh.16.1.2',
          },
          {
            label: '语义分析',
            value: 'ensh.16.1.3',
          },
          {
            label: '信息检索',
            value: 'ensh.16.1.4',
          },
          {
            label: '机器翻译',
            value: 'ensh.16.1.5',
          },
        ],
      },
      {
        label: '人工智能芯片',
        value: 'ensh.16.2',
      },
      {
        label: '计算机视觉',
        value: 'ensh.16.3',
        children: [
          {
            label: '物体识别',
            value: 'ensh.16.3.1',
          },
          {
            label: '人脸识别',
            value: 'ensh.16.3.2',
          },
          {
            label: '双目视觉',
            value: 'ensh.16.3.3',
          },
          {
            label: '图像识别',
            value: 'ensh.16.3.4',
          },
          {
            label: '字符识别',
            value: 'ensh.16.3.5',
          },
          {
            label: '情绪识别',
            value: 'ensh.16.3.6',
          },
          {
            label: '手势识别',
            value: 'ensh.16.3.7',
          },
          {
            label: '指纹识别',
            value: 'ensh.16.3.8',
          },
          {
            label: '掌纹识别',
            value: 'ensh.16.3.9',
          },
          {
            label: '物体与场景识别',
            value: 'ensh.16.3.10',
          },
          {
            label: '虹膜识别',
            value: 'ensh.16.3.11',
          },
          {
            label: '行为识别',
            value: 'ensh.16.3.12',
          },
          {
            label: '视频识别',
            value: 'ensh.16.3.13',
          },
          {
            label: '零件检测',
            value: 'ensh.16.3.14',
          },
          {
            label: '静脉识别',
            value: 'ensh.16.3.15',
          },
        ],
      },
      {
        label: 'AI工具',
        value: 'ensh.16.4',
        children: [
          {
            label: '虚拟试衣',
            value: 'ensh.16.4.1',
          },
          {
            label: '智能视频制作',
            value: 'ensh.16.4.2',
          },
          {
            label: '新闻推荐',
            value: 'ensh.16.4.3',
          },
          {
            label: '智能化编辑',
            value: 'ensh.16.4.4',
          },
          {
            label: '虚拟语音助手',
            value: 'ensh.16.4.5',
          },
          {
            label: 'AI音乐创作',
            value: 'ensh.16.4.6',
          },
          {
            label: 'AI媒体资讯',
            value: 'ensh.16.4.7',
          },
        ],
      },
      {
        label: '人工智能行业应用',
        value: 'ensh.16.5',
        children: [
          {
            label: '智能驾驶',
            value: 'ensh.16.5.1',
          },
          {
            label: '智能教育',
            value: 'ensh.16.5.2',
          },
          {
            label: '智能金融',
            value: 'ensh.16.5.3',
          },
          {
            label: '智能零售',
            value: 'ensh.16.5.4',
          },
          {
            label: '智能法务',
            value: 'ensh.16.5.5',
          },
          {
            label: '智能音乐',
            value: 'ensh.16.5.6',
          },
          {
            label: '舆情监测',
            value: 'ensh.16.5.7',
          },
          {
            label: '智能医疗',
            value: 'ensh.16.5.8',
          },
        ],
      },
      {
        label: '人工智能基础技术',
        value: 'ensh.16.6',
        children: [
          {
            label: '人机交互',
            value: 'ensh.16.6.1',
          },
          {
            label: '推荐引擎',
            value: 'ensh.16.6.2',
          },
          {
            label: '深度学习',
            value: 'ensh.16.6.3',
          },
          {
            label: '脑机接口',
            value: 'ensh.16.6.4',
          },
          {
            label: '自适应学习',
            value: 'ensh.16.6.5',
          },
          {
            label: '语音识别',
            value: 'ensh.16.6.6',
          },
          {
            label: '机器学习',
            value: 'ensh.16.6.7',
          },
        ],
      },
    ],
  },
  {
    label: '大数据',
    value: 'ensh.17',
    children: [
      {
        label: '医疗大数据',
        value: 'ensh.17.1',
      },
      {
        label: '金融大数据',
        value: 'ensh.17.2',
      },
      {
        label: '教育大数据',
        value: 'ensh.17.3',
      },
      {
        label: '农业大数据',
        value: 'ensh.17.4',
        children: [
          {
            label: '智慧农业',
            value: 'ensh.17.4.1',
          },
          {
            label: '气象预测',
            value: 'ensh.17.4.2',
          },
          {
            label: '卫星遥感大数据',
            value: 'ensh.17.4.3',
          },
          {
            label: '农业数据采集',
            value: 'ensh.17.4.4',
          },
          {
            label: '精准农业',
            value: 'ensh.17.4.5',
          },
          {
            label: '灌溉管理分析',
            value: 'ensh.17.4.6',
          },
          {
            label: '农业数字化',
            value: 'ensh.17.4.7',
          },
          {
            label: '农产品流通追踪',
            value: 'ensh.17.4.8',
          },
        ],
      },
      {
        label: '工业大数据',
        value: 'ensh.17.5',
        children: [
          {
            label: '工业安全监控',
            value: 'ensh.17.5.1',
          },
          {
            label: '工业故障诊断',
            value: 'ensh.17.5.2',
          },
          {
            label: '工业供应链优化',
            value: 'ensh.17.5.3',
          },
          {
            label: '工业大数据交易',
            value: 'ensh.17.5.4',
          },
          {
            label: '工业采购比价',
            value: 'ensh.17.5.5',
          },
        ],
      },
      {
        label: '电商大数据',
        value: 'ensh.17.6',
        children: [
          {
            label: '广告精准投放',
            value: 'ensh.17.6.1',
          },
          {
            label: '竞争对手监测',
            value: 'ensh.17.6.2',
          },
          {
            label: '品牌危机监测',
            value: 'ensh.17.6.3',
          },
          {
            label: '电商供应链优化',
            value: 'ensh.17.6.4',
          },
        ],
      },
      {
        label: '体育大数据',
        value: 'ensh.17.7',
        children: [
          {
            label: '足球大数据',
            value: 'ensh.17.7.1',
          },
          {
            label: '赛事分析',
            value: 'ensh.17.7.2',
          },
          {
            label: '体彩大数据',
            value: 'ensh.17.7.3',
          },
          {
            label: '战术辅助决策',
            value: 'ensh.17.7.4',
          },
        ],
      },
      {
        label: '大数据垂直应用',
        value: 'ensh.17.8',
        children: [
          {
            label: '地理大数据',
            value: 'ensh.17.8.1',
          },
          {
            label: '娱乐大数据',
            value: 'ensh.17.8.2',
          },
          {
            label: '气象大数据',
            value: 'ensh.17.8.3',
          },
          {
            label: '电竞大数据',
            value: 'ensh.17.8.4',
          },
          {
            label: '汽车大数据',
            value: 'ensh.17.8.5',
          },
          {
            label: '房产大数据',
            value: 'ensh.17.8.6',
          },
          {
            label: '媒体大数据',
            value: 'ensh.17.8.7',
          },
          {
            label: '电力大数据',
            value: 'ensh.17.8.8',
          },
          {
            label: '政务大数据',
            value: 'ensh.17.8.9',
          },
          {
            label: '税务大数据',
            value: 'ensh.17.8.10',
          },
        ],
      },
      {
        label: '大数据基础技术',
        value: 'ensh.17.9',
        children: [
          {
            label: '数据采集',
            value: 'ensh.17.9.1',
          },
          {
            label: '数据处理',
            value: 'ensh.17.9.2',
          },
          {
            label: 'hadoop',
            value: 'ensh.17.9.3',
          },
          {
            label: '数据恢复',
            value: 'ensh.17.9.4',
          },
          {
            label: '分布式文件系统',
            value: 'ensh.17.9.5',
          },
          {
            label: '数据库技术',
            value: 'ensh.17.9.6',
          },
          {
            label: '数据统计',
            value: 'ensh.17.9.7',
          },
          {
            label: '数据集成',
            value: 'ensh.17.9.8',
          },
          {
            label: '数据基础架构',
            value: 'ensh.17.9.9',
          },
          {
            label: '数据标注',
            value: 'ensh.17.9.10',
          },
          {
            label: '数据交易',
            value: 'ensh.17.9.11',
          },
          {
            label: '大数据芯片',
            value: 'ensh.17.9.12',
          },
        ],
      },
      {
        label: '大数据通用应用',
        value: 'ensh.17.10',
        children: [
          {
            label: '大数据营销',
            value: 'ensh.17.10.1',
          },
          {
            label: 'BI',
            value: 'ensh.17.10.2',
          },
          {
            label: '用户行为分析',
            value: 'ensh.17.10.3',
          },
          {
            label: '大数据反欺诈',
            value: 'ensh.17.10.4',
          },
          {
            label: '日志分析',
            value: 'ensh.17.10.5',
          },
          {
            label: '销售数据分析',
            value: 'ensh.17.10.6',
          },
          {
            label: '机器大数据分析',
            value: 'ensh.17.10.7',
          },
        ],
      },
    ],
  },
  {
    label: '物流仓储',
    value: 'ensh.18',
    children: [
      {
        label: '跨境物流',
        value: 'ensh.18.1',
        children: [
          {
            label: '跨境电商物流',
            value: 'ensh.18.1.1',
          },
          {
            label: '国际快递配送',
            value: 'ensh.18.1.2',
          },
          {
            label: '综合航运物流',
            value: 'ensh.18.1.3',
          },
          {
            label: '国际空运',
            value: 'ensh.18.1.4',
          },
          {
            label: '进出口物流',
            value: 'ensh.18.1.5',
          },
          {
            label: '国际转运',
            value: 'ensh.18.1.6',
          },
        ],
      },
      {
        label: '冷链',
        value: 'ensh.18.2',
        children: [
          {
            label: '冷链配送',
            value: 'ensh.18.2.1',
          },
          {
            label: '药物冷链',
            value: 'ensh.18.2.2',
          },
          {
            label: '生鲜冷链',
            value: 'ensh.18.2.3',
          },
          {
            label: '第三方冷链',
            value: 'ensh.18.2.4',
          },
          {
            label: '冷链仓库',
            value: 'ensh.18.2.5',
          },
        ],
      },
      {
        label: '仓储服务',
        value: 'ensh.18.3',
        children: [
          {
            label: '仓储机器人',
            value: 'ensh.18.3.1',
          },
          {
            label: '智能仓储系统',
            value: 'ensh.18.3.2',
          },
          {
            label: '自动化立体仓库',
            value: 'ensh.18.3.3',
          },
          {
            label: '仓储供应链',
            value: 'ensh.18.3.4',
          },
          {
            label: '个人仓储',
            value: 'ensh.18.3.5',
          },
          {
            label: '危险品仓储',
            value: 'ensh.18.3.6',
          },
          {
            label: '无人叉车',
            value: 'ensh.18.3.7',
          },
          {
            label: '智能配货',
            value: 'ensh.18.3.8',
          },
          {
            label: '物流仓库',
            value: 'ensh.18.3.9',
          },
        ],
      },
      {
        label: '物流金融',
        value: 'ensh.18.4',
        children: [
          {
            label: '物流保险',
            value: 'ensh.18.4.1',
          },
          {
            label: '物流供应链金融',
            value: 'ensh.18.4.2',
          },
        ],
      },
      {
        label: '大件物流',
        value: 'ensh.18.5',
        children: [
          {
            label: '货运物流',
            value: 'ensh.18.5.1',
          },
          {
            label: '家具物流',
            value: 'ensh.18.5.2',
          },
          {
            label: '家电物流',
            value: 'ensh.18.5.3',
          },
          {
            label: '机械设备运输',
            value: 'ensh.18.5.4',
          },
          {
            label: '整车运输',
            value: 'ensh.18.5.5',
          },
        ],
      },
      {
        label: '危险品物流',
        value: 'ensh.18.6',
      },
      {
        label: '物流地产',
        value: 'ensh.18.7',
      },
      {
        label: '快递收发',
        value: 'ensh.18.8',
        children: [
          {
            label: '智能快递柜',
            value: 'ensh.18.8.1',
          },
          {
            label: '代收货',
            value: 'ensh.18.8.2',
          },
          {
            label: '24H自助取货',
            value: 'ensh.18.8.3',
          },
          {
            label: '社区快件中转',
            value: 'ensh.18.8.4',
          },
          {
            label: '校园快递代领',
            value: 'ensh.18.8.5',
          },
        ],
      },
      {
        label: '最后一公里',
        value: 'ensh.18.9',
        children: [
          {
            label: '农村最后一公里',
            value: 'ensh.18.9.1',
          },
          {
            label: '校园最后一公里',
            value: 'ensh.18.9.2',
          },
        ],
      },
      {
        label: '同城物流',
        value: 'ensh.18.10',
        children: [
          {
            label: '生鲜配送',
            value: 'ensh.18.10.1',
          },
          {
            label: '同城货运',
            value: 'ensh.18.10.2',
          },
          {
            label: '即时物流',
            value: 'ensh.18.10.3',
          },
          {
            label: '众包快递',
            value: 'ensh.18.10.4',
          },
          {
            label: '同城专人直送',
            value: 'ensh.18.10.5',
          },
        ],
      },
      {
        label: '物流运输',
        value: 'ensh.18.11',
        children: [
          {
            label: '公路物流',
            value: 'ensh.18.11.1',
          },
          {
            label: '铁路物流',
            value: 'ensh.18.11.2',
          },
          {
            label: '航空物流',
            value: 'ensh.18.11.3',
          },
          {
            label: '水运物流',
            value: 'ensh.18.11.4',
          },
        ],
      },
      {
        label: '快递服务',
        value: 'ensh.18.12',
      },
      {
        label: '物流信息化',
        value: 'ensh.18.13',
        children: [
          {
            label: '仓储SaaS',
            value: 'ensh.18.13.1',
          },
          {
            label: '智能物流设备',
            value: 'ensh.18.13.2',
          },
          {
            label: '自动分拣设备',
            value: 'ensh.18.13.3',
          },
          {
            label: '智能快递分发平台',
            value: 'ensh.18.13.4',
          },
          {
            label: '物流比价平台',
            value: 'ensh.18.13.5',
          },
          {
            label: '物流路线优化',
            value: 'ensh.18.13.6',
          },
          {
            label: '车货匹配平台',
            value: 'ensh.18.13.7',
          },
          {
            label: '运输管理系统',
            value: 'ensh.18.13.8',
          },
          {
            label: '货运信息平台',
            value: 'ensh.18.13.9',
          },
          {
            label: '物流跟踪',
            value: 'ensh.18.13.10',
          },
          {
            label: '无人配送车',
            value: 'ensh.18.13.11',
          },
        ],
      },
    ],
  },
  {
    label: '房地产',
    value: 'ensh.19',
    children: [
      {
        label: '房产交易',
        value: 'ensh.19.1',
        children: [
          {
            label: '海外置业',
            value: 'ensh.19.1.1',
          },
          {
            label: '房产中介',
            value: 'ensh.19.1.2',
          },
          {
            label: '新房交易',
            value: 'ensh.19.1.3',
          },
          {
            label: '二手房交易',
            value: 'ensh.19.1.4',
          },
        ],
      },
      {
        label: '房产开发',
        value: 'ensh.19.2',
        children: [
          {
            label: '商业地产',
            value: 'ensh.19.2.1',
          },
          {
            label: '品牌公寓',
            value: 'ensh.19.2.2',
          },
          {
            label: 'BIM',
            value: 'ensh.19.2.3',
          },
          {
            label: '绿色建筑',
            value: 'ensh.19.2.4',
          },
          {
            label: '园区开发',
            value: 'ensh.19.2.5',
          },
          {
            label: '酒店地产',
            value: 'ensh.19.2.6',
          },
          {
            label: '住宅地产',
            value: 'ensh.19.2.7',
          },
          {
            label: '养老地产',
            value: 'ensh.19.2.8',
          },
          {
            label: '土地流转',
            value: 'ensh.19.2.9',
          },
        ],
      },
      {
        label: '房产租赁',
        value: 'ensh.19.3',
        children: [
          {
            label: '长租',
            value: 'ensh.19.3.1',
          },
          {
            label: '短租',
            value: 'ensh.19.3.2',
          },
          {
            label: '海外租房',
            value: 'ensh.19.3.3',
          },
          {
            label: '办公租赁',
            value: 'ensh.19.3.4',
          },
          {
            label: '留学生租房',
            value: 'ensh.19.3.5',
          },
          {
            label: '租房信息平台',
            value: 'ensh.19.3.6',
          },
        ],
      },
      {
        label: '建材家具',
        value: 'ensh.19.4',
        children: [
          {
            label: '家具',
            value: 'ensh.19.4.1',
          },
          {
            label: '装修建材',
            value: 'ensh.19.4.2',
          },
        ],
      },
      {
        label: '家装',
        value: 'ensh.19.5',
        children: [
          {
            label: '互联网家装',
            value: 'ensh.19.5.1',
          },
          {
            label: '软装',
            value: 'ensh.19.5.2',
          },
          {
            label: '家装设计',
            value: 'ensh.19.5.3',
          },
          {
            label: '硬装',
            value: 'ensh.19.5.4',
          },
          {
            label: '家装监理',
            value: 'ensh.19.5.5',
          },
          {
            label: 'VR家装',
            value: 'ensh.19.5.6',
          },
          {
            label: '家装后服务',
            value: 'ensh.19.5.7',
          },
          {
            label: '家居家纺',
            value: 'ensh.19.5.8',
          },
        ],
      },
      {
        label: '房产金融',
        value: 'ensh.19.6',
        children: [
          {
            label: '装修分期',
            value: 'ensh.19.6.1',
          },
          {
            label: '住房金融',
            value: 'ensh.19.6.2',
          },
          {
            label: '房产抵押',
            value: 'ensh.19.6.3',
          },
        ],
      },
    ],
  },
  {
    label: '文娱传媒',
    value: 'ensh.20',
    children: [
      {
        label: '音乐',
        value: 'ensh.20.1',
        children: [
          {
            label: '音乐节',
            value: 'ensh.20.1.1',
          },
          {
            label: '音乐剧',
            value: 'ensh.20.1.2',
          },
          {
            label: '音乐创作',
            value: 'ensh.20.1.3',
          },
          {
            label: '民谣',
            value: 'ensh.20.1.4',
          },
          {
            label: '电音',
            value: 'ensh.20.1.5',
          },
          {
            label: '嘻哈',
            value: 'ensh.20.1.6',
          },
          {
            label: '配音配乐',
            value: 'ensh.20.1.7',
          },
          {
            label: '迷你KTV',
            value: 'ensh.20.1.8',
          },
          {
            label: '唱片公司',
            value: 'ensh.20.1.9',
          },
          {
            label: '音乐票务',
            value: 'ensh.20.1.10',
          },
          {
            label: '演唱会运营商',
            value: 'ensh.20.1.11',
          },
          {
            label: '音乐播放器',
            value: 'ensh.20.1.12',
          },
          {
            label: '在线K歌',
            value: 'ensh.20.1.13',
          },
          {
            label: '音乐电台',
            value: 'ensh.20.1.14',
          },
          {
            label: '音乐版权',
            value: 'ensh.20.1.15',
          },
          {
            label: 'livehouse',
            value: 'ensh.20.1.16',
          },
        ],
      },
      {
        label: '直播',
        value: 'ensh.20.2',
        children: [
          {
            label: '赛事直播',
            value: 'ensh.20.2.1',
          },
          {
            label: '旅游直播',
            value: 'ensh.20.2.2',
          },
          {
            label: '秀场类直播',
            value: 'ensh.20.2.3',
          },
          {
            label: '移动直播平台',
            value: 'ensh.20.2.4',
          },
          {
            label: '直播电商',
            value: 'ensh.20.2.5',
          },
          {
            label: '直播技术支持',
            value: 'ensh.20.2.6',
          },
          {
            label: '直播平台',
            value: 'ensh.20.2.7',
          },
        ],
      },
      {
        label: '媒体',
        value: 'ensh.20.3',
        children: [
          {
            label: '垂直媒体',
            value: 'ensh.20.3.1',
          },
          {
            label: '门户网站',
            value: 'ensh.20.3.2',
          },
          {
            label: '网络电台',
            value: 'ensh.20.3.3',
          },
          {
            label: '自媒体',
            value: 'ensh.20.3.4',
          },
          {
            label: '电视台',
            value: 'ensh.20.3.5',
          },
          {
            label: '传媒公司',
            value: 'ensh.20.3.6',
          },
          {
            label: '媒体社区',
            value: 'ensh.20.3.7',
          },
          {
            label: '出版发行',
            value: 'ensh.20.3.8',
          },
          {
            label: '媒体资讯',
            value: 'ensh.20.3.9',
          },
          {
            label: '个性化阅读',
            value: 'ensh.20.3.10',
          },
          {
            label: '新闻网站',
            value: 'ensh.20.3.11',
          },
        ],
      },
      {
        label: '数字出版',
        value: 'ensh.20.4',
        children: [
          {
            label: '网络小说',
            value: 'ensh.20.4.1',
          },
          {
            label: '电子阅读',
            value: 'ensh.20.4.2',
          },
          {
            label: '写作阅读平台',
            value: 'ensh.20.4.3',
          },
          {
            label: '图书出版',
            value: 'ensh.20.4.4',
          },
          {
            label: '对话式小说',
            value: 'ensh.20.4.5',
          },
          {
            label: '网络文学平台',
            value: 'ensh.20.4.6',
          },
          {
            label: '有声阅读',
            value: 'ensh.20.4.7',
          },
          {
            label: '儿童绘本',
            value: 'ensh.20.4.8',
          },
        ],
      },
      {
        label: '影视',
        value: 'ensh.20.5',
        children: [
          {
            label: '影视制作',
            value: 'ensh.20.5.1',
          },
          {
            label: '影视众筹',
            value: 'ensh.20.5.2',
          },
          {
            label: '影音设备',
            value: 'ensh.20.5.3',
          },
          {
            label: '电影院线',
            value: 'ensh.20.5.4',
          },
          {
            label: '影视发行',
            value: 'ensh.20.5.5',
          },
          {
            label: '影视大数据',
            value: 'ensh.20.5.6',
          },
          {
            label: '电影票务',
            value: 'ensh.20.5.7',
          },
          {
            label: '摄影器材',
            value: 'ensh.20.5.8',
          },
        ],
      },
      {
        label: '二次元',
        value: 'ensh.20.6',
        children: [
          {
            label: '动画',
            value: 'ensh.20.6.1',
          },
          {
            label: '动漫',
            value: 'ensh.20.6.2',
          },
          {
            label: '漫画',
            value: 'ensh.20.6.3',
          },
          {
            label: 'cosplay',
            value: 'ensh.20.6.4',
          },
          {
            label: '二次元电商',
            value: 'ensh.20.6.5',
          },
          {
            label: '二次元社区',
            value: 'ensh.20.6.6',
          },
          {
            label: '二次元衍生品',
            value: 'ensh.20.6.7',
          },
          {
            label: '二次元游戏',
            value: 'ensh.20.6.8',
          },
          {
            label: '漫展签售',
            value: 'ensh.20.6.9',
          },
          {
            label: '二次元声音制作',
            value: 'ensh.20.6.10',
          },
          {
            label: '二次元视频网站',
            value: 'ensh.20.6.11',
          },
        ],
      },
      {
        label: 'IP版权',
        value: 'ensh.20.7',
        children: [
          {
            label: 'IP孵化',
            value: 'ensh.20.7.1',
          },
          {
            label: 'IP运营',
            value: 'ensh.20.7.2',
          },
          {
            label: '版权交易',
            value: 'ensh.20.7.3',
          },
          {
            label: '动漫IP',
            value: 'ensh.20.7.4',
          },
          {
            label: '影视IP',
            value: 'ensh.20.7.5',
          },
          {
            label: 'IP创作',
            value: 'ensh.20.7.6',
          },
          {
            label: 'IP衍生品',
            value: 'ensh.20.7.7',
          },
          {
            label: '文学IP',
            value: 'ensh.20.7.8',
          },
        ],
      },
      {
        label: '综艺',
        value: 'ensh.20.8',
        children: [
          {
            label: '电视节目',
            value: 'ensh.20.8.1',
          },
          {
            label: '脱口秀',
            value: 'ensh.20.8.2',
          },
          {
            label: '真人秀',
            value: 'ensh.20.8.3',
          },
          {
            label: '网络综艺',
            value: 'ensh.20.8.4',
          },
        ],
      },
      {
        label: '艺人经纪',
        value: 'ensh.20.9',
        children: [
          {
            label: '粉丝经济',
            value: 'ensh.20.9.1',
          },
          {
            label: '网红',
            value: 'ensh.20.9.2',
          },
          {
            label: '粉丝社区',
            value: 'ensh.20.9.3',
          },
          {
            label: '经纪公司',
            value: 'ensh.20.9.4',
          },
          {
            label: '网红经济',
            value: 'ensh.20.9.5',
          },
          {
            label: '网红孵化',
            value: 'ensh.20.9.6',
          },
          {
            label: 'mcn',
            value: 'ensh.20.9.7',
          },
          {
            label: '偶像团体',
            value: 'ensh.20.9.8',
          },
          {
            label: '虚拟偶像',
            value: 'ensh.20.9.9',
          },
          {
            label: '文化演出',
            value: 'ensh.20.9.10',
          },
          {
            label: '戏剧',
            value: 'ensh.20.9.11',
          },
          {
            label: '话剧',
            value: 'ensh.20.9.12',
          },
          {
            label: '相声小品',
            value: 'ensh.20.9.13',
          },
          {
            label: '歌舞剧',
            value: 'ensh.20.9.14',
          },
        ],
      },
      {
        label: '内容制作',
        value: 'ensh.20.10',
        children: [
          {
            label: '配音',
            value: 'ensh.20.10.1',
          },
          {
            label: '配乐',
            value: 'ensh.20.10.2',
          },
          {
            label: '内容变现',
            value: 'ensh.20.10.3',
          },
          {
            label: '内容创作',
            value: 'ensh.20.10.4',
          },
          {
            label: '内容宣发',
            value: 'ensh.20.10.5',
          },
          {
            label: '特效技术',
            value: 'ensh.20.10.6',
          },
          {
            label: '二次创作',
            value: 'ensh.20.10.7',
          },
        ],
      },
      {
        label: '游戏',
        value: 'ensh.20.11',
        children: [
          {
            label: '游戏直播',
            value: 'ensh.20.11.1',
          },
          {
            label: '游戏发行',
            value: 'ensh.20.11.2',
          },
          {
            label: '游戏开发',
            value: 'ensh.20.11.3',
          },
          {
            label: '游戏运营',
            value: 'ensh.20.11.4',
          },
          {
            label: '游戏社交',
            value: 'ensh.20.11.5',
          },
          {
            label: '游戏硬件',
            value: 'ensh.20.11.6',
          },
          {
            label: 'VR游戏',
            value: 'ensh.20.11.7',
          },
          {
            label: '游戏分发渠道',
            value: 'ensh.20.11.8',
          },
          {
            label: '游戏衍生品',
            value: 'ensh.20.11.9',
          },
          {
            label: '游戏支持服务',
            value: 'ensh.20.11.10',
          },
          {
            label: '手游',
            value: 'ensh.20.11.11',
          },
          {
            label: '游戏垂直媒体',
            value: 'ensh.20.11.12',
          },
          {
            label: '网络游戏',
            value: 'ensh.20.11.13',
          },
        ],
      },
      {
        label: '视频',
        value: 'ensh.20.12',
        children: [
          {
            label: '视频播放器',
            value: 'ensh.20.12.1',
          },
          {
            label: '短视频',
            value: 'ensh.20.12.2',
          },
          {
            label: '短视频聚合平台',
            value: 'ensh.20.12.3',
          },
          {
            label: '视频制作工具',
            value: 'ensh.20.12.4',
          },
          {
            label: '综合视频网站',
            value: 'ensh.20.12.5',
          },
          {
            label: '垂直视频网站',
            value: 'ensh.20.12.6',
          },
        ],
      },
    ],
  },
  {
    label: '消费',
    value: 'ensh.21',
    children: [
      {
        label: '百货零售',
        value: 'ensh.21.2',
        children: [
          {
            label: '百货商场',
            value: 'ensh.21.2.1',
          },
          {
            label: '无人便利店',
            value: 'ensh.21.2.2',
          },
          {
            label: '连锁便利店',
            value: 'ensh.21.2.3',
          },
          {
            label: '连锁超市',
            value: 'ensh.21.2.4',
          },
        ],
      },
      {
        label: '食品饮料',
        value: 'ensh.21.3',
        children: [
          {
            label: '饮料',
            value: 'ensh.21.3.1',
          },
          {
            label: '酒类',
            value: 'ensh.21.3.2',
          },
          {
            label: '调味品',
            value: 'ensh.21.3.3',
          },
          {
            label: '休闲食品',
            value: 'ensh.21.3.4',
          },
          {
            label: '奶制品',
            value: 'ensh.21.3.5',
          },
          {
            label: '食品添加剂',
            value: 'ensh.21.3.6',
          },
        ],
      },
      {
        label: '垂直消费',
        value: 'ensh.21.4',
        children: [
          {
            label: '女性经济',
            value: 'ensh.21.4.1',
          },
          {
            label: '保健品',
            value: 'ensh.21.4.2',
          },
          {
            label: '知识经济',
            value: 'ensh.21.4.3',
          },
          {
            label: '珠宝饰品',
            value: 'ensh.21.4.4',
          },
          {
            label: '原创品牌',
            value: 'ensh.21.4.5',
          },
          {
            label: '服装鞋包',
            value: 'ensh.21.4.6',
          },
          {
            label: '美妆品牌',
            value: 'ensh.21.4.7',
          },
          {
            label: '奢侈品品牌',
            value: 'ensh.21.4.8',
          },
        ],
      },
      {
        label: '儿童消费',
        value: 'ensh.21.5',
        children: [
          {
            label: '儿童玩具',
            value: 'ensh.21.5.1',
          },
          {
            label: '童装',
            value: 'ensh.21.5.2',
          },
          {
            label: '儿童奶粉',
            value: 'ensh.21.5.3',
          },
        ],
      },
      {
        label: '线下消费',
        value: 'ensh.21.6',
        children: [
          {
            label: '按摩椅',
            value: 'ensh.21.6.1',
          },
          {
            label: '办公室货架',
            value: 'ensh.21.6.2',
          },
          {
            label: '快闪店',
            value: 'ensh.21.6.3',
          },
          {
            label: '无人健身仓',
            value: 'ensh.21.6.4',
          },
          {
            label: '抓娃娃机',
            value: 'ensh.21.6.5',
          },
          {
            label: '自助贩卖机',
            value: 'ensh.21.6.6',
          },
        ],
      },
      {
        label: '定制消费',
        value: 'ensh.21.7',
        children: [
          {
            label: '服装定制',
            value: 'ensh.21.7.1',
          },
          {
            label: '珠宝定制',
            value: 'ensh.21.7.2',
          },
        ],
      },
    ],
  },
  {
    label: '传统行业',
    value: 'ensh.22',
    children: [
      {
        label: '建筑业',
        value: 'ensh.22.1',
        children: [
          {
            label: '规划设计',
            value: 'ensh.22.1.1',
          },
          {
            label: '建筑施工',
            value: 'ensh.22.1.2',
          },
          {
            label: '建筑设备',
            value: 'ensh.22.1.3',
          },
          {
            label: '园林绿化',
            value: 'ensh.22.1.4',
          },
          {
            label: '土木工程',
            value: 'ensh.22.1.5',
          },
          {
            label: '招标采购',
            value: 'ensh.22.1.6',
          },
          {
            label: '建筑装饰',
            value: 'ensh.22.1.7',
          },
          {
            label: '建筑信息化',
            value: 'ensh.22.1.8',
          },
          {
            label: '爆破服务',
            value: 'ensh.22.1.9',
          },
          {
            label: '装配式建筑',
            value: 'ensh.22.1.10',
          },
          {
            label: '建筑工程',
            value: 'ensh.22.1.11',
          },
          {
            label: '建筑材料',
            value: 'ensh.22.1.12',
          },
          {
            label: '勘察测绘',
            value: 'ensh.22.1.13',
          },
          {
            label: '景观设计',
            value: 'ensh.22.1.14',
          },
          {
            label: '装饰工程',
            value: 'ensh.22.1.15',
          },
          {
            label: '建筑设计',
            value: 'ensh.22.1.16',
          },
          {
            label: '建筑工程管理',
            value: 'ensh.22.1.17',
          },
          {
            label: '建筑工程咨询',
            value: 'ensh.22.1.18',
          },
        ],
      },
      {
        label: '材料类',
        value: 'ensh.22.2',
        children: [
          {
            label: '化工新材料',
            value: 'ensh.22.2.1',
          },
        ],
      },
      {
        label: '电子制造',
        value: 'ensh.22.3',
        children: [
          {
            label: '家电',
            value: 'ensh.22.3.1',
          },
          {
            label: '电子元器件',
            value: 'ensh.22.3.2',
          },
          {
            label: '消费电子',
            value: 'ensh.22.3.3',
          },
          {
            label: 'LED产业',
            value: 'ensh.22.3.4',
          },
          {
            label: '电器',
            value: 'ensh.22.3.5',
          },
          {
            label: '数码产品',
            value: 'ensh.22.3.6',
          },
        ],
      },
      {
        label: '石油矿采',
        value: 'ensh.22.4',
        children: [
          {
            label: '冶金工业',
            value: 'ensh.22.4.1',
          },
          {
            label: '钢铁产业',
            value: 'ensh.22.4.2',
          },
          {
            label: '勘探技术',
            value: 'ensh.22.4.3',
          },
          {
            label: '能源开采',
            value: 'ensh.22.4.4',
          },
          {
            label: '煤炭开采',
            value: 'ensh.22.4.5',
          },
          {
            label: '矿石开采',
            value: 'ensh.22.4.6',
          },
          {
            label: '石油开采',
            value: 'ensh.22.4.7',
          },
          {
            label: '开采设备',
            value: 'ensh.22.4.8',
          },
        ],
      },
      {
        label: '纺织工业',
        value: 'ensh.22.5',
        children: [
          {
            label: '纺织印染',
            value: 'ensh.22.5.1',
          },
          {
            label: '纺织设备',
            value: 'ensh.22.5.2',
          },
          {
            label: '皮革加工',
            value: 'ensh.22.5.3',
          },
          {
            label: '服装制造',
            value: 'ensh.22.5.4',
          },
          {
            label: '纺织原料',
            value: 'ensh.22.5.5',
          },
          {
            label: '鞋袜配饰',
            value: 'ensh.22.5.6',
          },
          {
            label: '服装辅料',
            value: 'ensh.22.5.7',
          },
        ],
      },
      {
        label: '轻工制造',
        value: 'ensh.22.6',
        children: [
          {
            label: '印刷',
            value: 'ensh.22.6.1',
          },
          {
            label: '包装',
            value: 'ensh.22.6.2',
          },
          {
            label: '造纸',
            value: 'ensh.22.6.3',
          },
        ],
      },
      {
        label: '通信通讯',
        value: 'ensh.22.7',
        children: [
          {
            label: '通讯技术',
            value: 'ensh.22.7.1',
          },
          {
            label: '通讯设备',
            value: 'ensh.22.7.2',
          },
          {
            label: '光通信',
            value: 'ensh.22.7.3',
          },
          {
            label: '通讯卫星',
            value: 'ensh.22.7.4',
          },
          {
            label: '光纤传输',
            value: 'ensh.22.7.5',
          },
          {
            label: '无线集群通信',
            value: 'ensh.22.7.6',
          },
          {
            label: '通信基站',
            value: 'ensh.22.7.7',
          },
          {
            label: '量子通讯',
            value: 'ensh.22.7.8',
          },
          {
            label: '通信芯片',
            value: 'ensh.22.7.9',
          },
          {
            label: '电信运营商',
            value: 'ensh.22.7.10',
          },
        ],
      },
      {
        label: '零部件制造',
        value: 'ensh.22.8',
      },
      {
        label: '环保行业',
        value: 'ensh.22.9',
        children: [
          {
            label: '污水处理',
            value: 'ensh.22.9.1',
          },
          {
            label: '环保设备',
            value: 'ensh.22.9.2',
          },
          {
            label: '环保生活用品',
            value: 'ensh.22.9.3',
          },
          {
            label: '环保能源',
            value: 'ensh.22.9.4',
          },
          {
            label: '环保材料',
            value: 'ensh.22.9.5',
          },
          {
            label: '固废处理',
            value: 'ensh.22.9.6',
          },
          {
            label: '大气治理',
            value: 'ensh.22.9.7',
          },
          {
            label: '环境修复',
            value: 'ensh.22.9.8',
          },
          {
            label: '生态保护',
            value: 'ensh.22.9.9',
          },
          {
            label: '环保工程',
            value: 'ensh.22.9.10',
          },
          {
            label: '环保技术',
            value: 'ensh.22.9.11',
          },
          {
            label: '噪声处理',
            value: 'ensh.22.9.12',
          },
        ],
      },
      {
        label: '军工产业',
        value: 'ensh.22.10',
      },
      {
        label: '电气行业',
        value: 'ensh.22.11',
        children: [
          {
            label: '电气工程',
            value: 'ensh.22.11.1',
          },
          {
            label: '电气设备',
            value: 'ensh.22.11.2',
          },
        ],
      },
      {
        label: '酒业',
        value: 'ensh.22.12',
        children: [
          {
            label: '酒庄',
            value: 'ensh.22.12.1',
          },
        ],
      },
      {
        label: '化学工业',
        value: 'ensh.22.13',
        children: [
          {
            label: '精细化工',
            value: 'ensh.22.13.1',
          },
          {
            label: '化工原料',
            value: 'ensh.22.13.2',
          },
          {
            label: '化工制品',
            value: 'ensh.22.13.3',
          },
          {
            label: '化工制剂',
            value: 'ensh.22.13.4',
          },
          {
            label: '农药化肥',
            value: 'ensh.22.13.5',
          },
          {
            label: '塑料制品',
            value: 'ensh.22.13.6',
          },
          {
            label: '塑胶制品',
            value: 'ensh.22.13.7',
          },
        ],
      },
      {
        label: '能源行业',
        value: 'ensh.22.14',
        children: [
          {
            label: '新能源',
            value: 'ensh.22.14.1',
          },
          {
            label: '电力供应',
            value: 'ensh.22.14.2',
          },
          {
            label: '能源技术',
            value: 'ensh.22.14.3',
          },
          {
            label: '太阳能光伏',
            value: 'ensh.22.14.4',
          },
          {
            label: '能源工程',
            value: 'ensh.22.14.5',
          },
          {
            label: '热力供应',
            value: 'ensh.22.14.6',
          },
          {
            label: '传统能源',
            value: 'ensh.22.14.7',
          },
          {
            label: '节能技术',
            value: 'ensh.22.14.8',
          },
          {
            label: '电池技术',
            value: 'ensh.22.14.9',
          },
          {
            label: '电源科技',
            value: 'ensh.22.14.10',
          },
          {
            label: '清洁能源',
            value: 'ensh.22.14.11',
          },
          {
            label: '分布式能源',
            value: 'ensh.22.14.12',
          },
        ],
      },
      {
        label: '重工业',
        value: 'ensh.22.15',
      },
      {
        label: '消防行业',
        value: 'ensh.22.16',
        children: [
          {
            label: '防火技术',
            value: 'ensh.22.16.1',
          },
          {
            label: '灭火器',
            value: 'ensh.22.16.2',
          },
          {
            label: '火灾探测',
            value: 'ensh.22.16.3',
          },
          {
            label: '消防报警',
            value: 'ensh.22.16.4',
          },
        ],
      },
      {
        label: '机械制造',
        value: 'ensh.22.17',
        children: [
          {
            label: '机械设备',
            value: 'ensh.22.17.1',
          },
          {
            label: '电力设备',
            value: 'ensh.22.17.2',
          },
          {
            label: '汽车制造业',
            value: 'ensh.22.17.3',
          },
          {
            label: '通风设备',
            value: 'ensh.22.17.4',
          },
          {
            label: '消防设备',
            value: 'ensh.22.17.5',
          },
          {
            label: '实验室仪器',
            value: 'ensh.22.17.6',
          },
          {
            label: '电机',
            value: 'ensh.22.17.7',
          },
          {
            label: '触控设备',
            value: 'ensh.22.17.8',
          },
          {
            label: '钻掘设备',
            value: 'ensh.22.17.9',
          },
          {
            label: '超声设备',
            value: 'ensh.22.17.10',
          },
          {
            label: '液压',
            value: 'ensh.22.17.11',
          },
        ],
      },
      {
        label: '五金交电',
        value: 'ensh.22.18',
      },
    ],
  },
  {
    label: '软件工具',
    value: 'ensh.23',
    children: [
      {
        label: '电子名片',
        value: 'ensh.23.1',
      },
      {
        label: '即时通讯',
        value: 'ensh.23.2',
      },
      {
        label: '图片工具',
        value: 'ensh.23.3',
      },
      {
        label: '地图',
        value: 'ensh.23.4',
      },
      {
        label: '应用分发',
        value: 'ensh.23.5',
      },
      {
        label: '开发工具',
        value: 'ensh.23.6',
      },
      {
        label: '搜索引擎',
        value: 'ensh.23.7',
      },
      {
        label: '效率工具',
        value: 'ensh.23.8',
      },
      {
        label: '日常应用',
        value: 'ensh.23.9',
      },
      {
        label: '美颜',
        value: 'ensh.23.10',
      },
      {
        label: '记账工具',
        value: 'ensh.23.11',
      },
      {
        label: '电子邮箱',
        value: 'ensh.23.12',
        children: [
          {
            label: '语音邮箱',
            value: 'ensh.23.12.1',
          },
        ],
      },
    ],
  },
  {
    label: '企业服务',
    value: 'ensh.24',
    children: [
      {
        label: '招聘',
        value: 'ensh.24.1',
        children: [
          {
            label: '职场社交',
            value: 'ensh.24.1.1',
          },
          {
            label: '兼职招聘',
            value: 'ensh.24.1.2',
          },
          {
            label: '猎头',
            value: 'ensh.24.1.3',
          },
          {
            label: '校园招聘',
            value: 'ensh.24.1.4',
          },
          {
            label: '蓝领招聘',
            value: 'ensh.24.1.5',
          },
        ],
      },
      {
        label: '企业安全',
        value: 'ensh.24.2',
        children: [
          {
            label: '网络安全',
            value: 'ensh.24.2.1',
          },
          {
            label: '防火墙',
            value: 'ensh.24.2.2',
          },
          {
            label: '视频监控',
            value: 'ensh.24.2.3',
          },
          {
            label: '物联网安全',
            value: 'ensh.24.2.4',
          },
          {
            label: '电子签约',
            value: 'ensh.24.2.5',
          },
          {
            label: '防DDoS服务',
            value: 'ensh.24.2.6',
          },
          {
            label: '信息安全',
            value: 'ensh.24.2.7',
          },
          {
            label: '数据安全',
            value: 'ensh.24.2.8',
          },
          {
            label: '漏洞众测',
            value: 'ensh.24.2.9',
          },
          {
            label: '漏洞分析',
            value: 'ensh.24.2.10',
          },
        ],
      },
      {
        label: '企业管理软件',
        value: 'ensh.24.4',
        children: [
          {
            label: 'CRM',
            value: 'ensh.24.4.1',
          },
          {
            label: '协同办公',
            value: 'ensh.24.4.2',
          },
          {
            label: 'ERP',
            value: 'ensh.24.4.3',
          },
          {
            label: '进销存',
            value: 'ensh.24.4.4',
          },
          {
            label: '办公自动化',
            value: 'ensh.24.4.5',
          },
          {
            label: 'HRM',
            value: 'ensh.24.4.6',
          },
        ],
      },
      {
        label: '创业服务',
        value: 'ensh.24.5',
        children: [
          {
            label: '孵化器',
            value: 'ensh.24.5.1',
          },
          {
            label: '加速器',
            value: 'ensh.24.5.2',
          },
          {
            label: '众创空间',
            value: 'ensh.24.5.3',
          },
          {
            label: '融资平台',
            value: 'ensh.24.5.4',
          },
          {
            label: '创业培训',
            value: 'ensh.24.5.5',
          },
          {
            label: '创业咨询',
            value: 'ensh.24.5.6',
          },
          {
            label: '创业推广',
            value: 'ensh.24.5.7',
          },
          {
            label: '创业融资服务',
            value: 'ensh.24.5.8',
          },
        ],
      },
      {
        label: '开发者服务',
        value: 'ensh.24.6',
        children: [
          {
            label: 'API',
            value: 'ensh.24.6.1',
          },
          {
            label: 'SDK',
            value: 'ensh.24.6.2',
          },
          {
            label: 'PaaS',
            value: 'ensh.24.6.3',
          },
          {
            label: 'DaaS',
            value: 'ensh.24.6.4',
          },
          {
            label: '开发平台',
            value: 'ensh.24.6.5',
          },
          {
            label: '测试服务',
            value: 'ensh.24.6.6',
          },
          {
            label: 'CaaS',
            value: 'ensh.24.6.7',
          },
        ],
      },
      {
        label: '数据服务',
        value: 'ensh.24.7',
        children: [
          {
            label: '数据分析',
            value: 'ensh.24.7.1',
          },
          {
            label: '数据挖掘',
            value: 'ensh.24.7.2',
          },
          {
            label: '数据可视化',
            value: 'ensh.24.7.3',
          },
          {
            label: '数据备份',
            value: 'ensh.24.7.4',
          },
          {
            label: '数据存储',
            value: 'ensh.24.7.5',
          },
        ],
      },
      {
        label: '企业IT服务',
        value: 'ensh.24.8',
        children: [
          {
            label: 'CDN',
            value: 'ensh.24.8.1',
          },
          {
            label: 'IDC',
            value: 'ensh.24.8.2',
          },
          {
            label: 'IT基础设施',
            value: 'ensh.24.8.3',
          },
          {
            label: 'IT运维',
            value: 'ensh.24.8.4',
          },
          {
            label: '外包开发',
            value: 'ensh.24.8.5',
          },
        ],
      },
      {
        label: '云服务',
        value: 'ensh.24.9',
        children: [
          {
            label: 'Docker',
            value: 'ensh.24.9.1',
          },
          {
            label: '云基础架构',
            value: 'ensh.24.9.2',
          },
          {
            label: '云存储',
            value: 'ensh.24.9.3',
          },
          {
            label: '云安全',
            value: 'ensh.24.9.4',
          },
          {
            label: '云爬虫',
            value: 'ensh.24.9.5',
          },
          {
            label: '云端机器人',
            value: 'ensh.24.9.6',
          },
          {
            label: '云视频',
            value: 'ensh.24.9.7',
          },
          {
            label: '云计算',
            value: 'ensh.24.9.8',
          },
          {
            label: '云通讯',
            value: 'ensh.24.9.9',
          },
          {
            label: '公有云',
            value: 'ensh.24.9.10',
          },
          {
            label: '混合云',
            value: 'ensh.24.9.11',
          },
          {
            label: '私有云',
            value: 'ensh.24.9.12',
          },
        ],
      },
      {
        label: '广告营销',
        value: 'ensh.24.10',
        children: [
          {
            label: '广告技术',
            value: 'ensh.24.10.1',
          },
          {
            label: '户外广告',
            value: 'ensh.24.10.2',
          },
          {
            label: '整合营销',
            value: 'ensh.24.10.3',
          },
          {
            label: '新媒体营销',
            value: 'ensh.24.10.4',
          },
          {
            label: '精准营销',
            value: 'ensh.24.10.5',
          },
          {
            label: '公关服务',
            value: 'ensh.24.10.6',
          },
          {
            label: '社交营销',
            value: 'ensh.24.10.7',
          },
          {
            label: '会展服务',
            value: 'ensh.24.10.8',
          },
          {
            label: 'ASO优化',
            value: 'ensh.24.10.9',
          },
          {
            label: '电梯楼宇广告',
            value: 'ensh.24.10.10',
          },
        ],
      },
      {
        label: '企业通用服务',
        value: 'ensh.24.11',
        children: [
          {
            label: '行业解决方案',
            value: 'ensh.24.11.1',
          },
          {
            label: '财税服务',
            value: 'ensh.24.11.2',
          },
          {
            label: '后勤服务',
            value: 'ensh.24.11.3',
          },
          {
            label: '差旅管理',
            value: 'ensh.24.11.4',
          },
          {
            label: '外包服务',
            value: 'ensh.24.11.5',
          },
          {
            label: '商业分析',
            value: 'ensh.24.11.6',
          },
          {
            label: '企业wifi',
            value: 'ensh.24.11.7',
          },
          {
            label: '宣传片拍摄',
            value: 'ensh.24.11.8',
          },
          {
            label: '地推服务',
            value: 'ensh.24.11.9',
          },
          {
            label: '会务服务',
            value: 'ensh.24.11.10',
          },
          {
            label: '团建服务',
            value: 'ensh.24.11.11',
          },
          {
            label: '采购服务',
            value: 'ensh.24.11.12',
          },
          {
            label: '知识产权服务',
            value: 'ensh.24.11.13',
          },
          {
            label: '供应链服务',
            value: 'ensh.24.11.14',
          },
          {
            label: '设计服务',
            value: 'ensh.24.11.15',
          },
          {
            label: '法律服务',
            value: 'ensh.24.11.16',
          },
          {
            label: '人力资源',
            value: 'ensh.24.11.17',
          },
        ],
      },
      {
        label: '咨询服务',
        value: 'ensh.24.12',
        children: [
          {
            label: '财务咨询',
            value: 'ensh.24.12.1',
          },
          {
            label: '科技咨询',
            value: 'ensh.24.12.2',
          },
          {
            label: '人力资源咨询',
            value: 'ensh.24.12.3',
          },
          {
            label: '法律咨询',
            value: 'ensh.24.12.4',
          },
        ],
      },
      {
        label: '智能客服',
        value: 'ensh.24.13',
        children: [
          {
            label: '客服机器人',
            value: 'ensh.24.13.1',
          },
          {
            label: '智能金融客服',
            value: 'ensh.24.13.2',
          },
          {
            label: '智能客服技术',
            value: 'ensh.24.13.3',
          },
          {
            label: '智能购物助理',
            value: 'ensh.24.13.4',
          },
          {
            label: '对话式商务',
            value: 'ensh.24.13.5',
          },
        ],
      },
    ],
  },
  {
    label: 'VRAR',
    value: 'ensh.25',
    children: [
      {
        label: 'VRAR内容',
        value: 'ensh.25.1',
        children: [
          {
            label: 'AR游戏',
            value: 'ensh.25.1.1',
          },
          {
            label: 'VR电影',
            value: 'ensh.25.1.2',
          },
          {
            label: 'VR视频',
            value: 'ensh.25.1.3',
          },
        ],
      },
      {
        label: 'VRAR媒体',
        value: 'ensh.25.2',
        children: [
          {
            label: 'VR社区',
            value: 'ensh.25.2.1',
          },
          {
            label: 'VR内容UGC平台',
            value: 'ensh.25.2.2',
          },
          {
            label: 'VR内容分发平台',
            value: 'ensh.25.2.3',
          },
        ],
      },
      {
        label: 'VRAR应用',
        value: 'ensh.25.3',
        children: [
          {
            label: 'VR旅游',
            value: 'ensh.25.3.1',
          },
          {
            label: 'AR社交',
            value: 'ensh.25.3.2',
          },
          {
            label: 'VR社交',
            value: 'ensh.25.3.3',
          },
          {
            label: 'VR看房',
            value: 'ensh.25.3.4',
          },
          {
            label: 'VR娱乐',
            value: 'ensh.25.3.5',
          },
          {
            label: 'VR教育',
            value: 'ensh.25.3.6',
          },
          {
            label: 'VR学车',
            value: 'ensh.25.3.7',
          },
          {
            label: 'VR医疗',
            value: 'ensh.25.3.8',
          },
          {
            label: 'VR直播',
            value: 'ensh.25.3.9',
          },
          {
            label: 'VR线下体验',
            value: 'ensh.25.3.10',
          },
          {
            label: 'VR情趣',
            value: 'ensh.25.3.11',
          },
        ],
      },
      {
        label: 'VRAR底层技术',
        value: 'ensh.25.4',
        children: [
          {
            label: '动作捕捉',
            value: 'ensh.25.4.1',
          },
          {
            label: '手势捕捉',
            value: 'ensh.25.4.2',
          },
          {
            label: 'VR防眩晕技术',
            value: 'ensh.25.4.3',
          },
        ],
      },
      {
        label: 'VRAR支撑软件',
        value: 'ensh.25.5',
        children: [
          {
            label: 'VR系统平台',
            value: 'ensh.25.5.1',
          },
          {
            label: 'VR开发sdk',
            value: 'ensh.25.5.2',
          },
          {
            label: 'VR转码',
            value: 'ensh.25.5.3',
          },
          {
            label: 'VR软件',
            value: 'ensh.25.5.4',
          },
          {
            label: 'AR软件',
            value: 'ensh.25.5.5',
          },
        ],
      },
      {
        label: 'VRAR设备',
        value: 'ensh.25.6',
        children: [
          {
            label: 'VR体感枪',
            value: 'ensh.25.6.1',
          },
          {
            label: '运动捕捉设备',
            value: 'ensh.25.6.2',
          },
          {
            label: 'VR输入设备',
            value: 'ensh.25.6.3',
          },
          {
            label: 'VR摄像头',
            value: 'ensh.25.6.4',
          },
          {
            label: 'VR手柄',
            value: 'ensh.25.6.5',
          },
          {
            label: 'VR动作传感器',
            value: 'ensh.25.6.6',
          },
          {
            label: 'VR座椅',
            value: 'ensh.25.6.7',
          },
          {
            label: 'AR眼镜',
            value: 'ensh.25.6.8',
          },
        ],
      },
      {
        label: '混合现实',
        value: 'ensh.25.7',
      },
    ],
  },
];

export const INDUSTRY_OPTIONS: CascaderOption[] = convertLabelToValueDepp(INDUSTRY_CODE_OPTIONS);

Object.freeze(INDUSTRY_CODE_OPTIONS);
Object.freeze(INDUSTRY_OPTIONS);

export const tree = new Tree(INDUSTRY_OPTIONS);
