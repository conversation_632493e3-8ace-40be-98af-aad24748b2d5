/**
 * 诉讼结果映射
 */
export const LAWSUIT_RESULT_CODE_MAP = {
  '1': '支持',
  '2': '撤诉',
  '3': '诉讼中止',
  '4': '发回重审',
  '5': '达成调解',
  '6': '驳回',
  '7': '同意人身保护令',
  '8': '同意管辖权异议',
  '9': '适用普通程序',
  '10': '适用简易程序',
  '11': '撤回申请',
  '12': '不支持',
  '13': '部分支持',
  '14': '撤回上诉',
  '15': '驳回上诉',
  '16': '撤销原判',
  '17': '追加被执行人',
  '18': '财产保全',
  '19': '解除财产保全',
  '20': '对方撤诉',
  '21': '不支持',
  '22': '对方被驳回',
  '23': '对方撤回申请',
  '24': '不承担责任',
  '25': '执行完毕',
  '26': '执行中止',
  '27': '执行法院变更',
  '28': '终结本次执行',
  '29': '申请人被驳回',
  '30': '原告撤诉',
  '31': '驳回管辖权异议',
};

export const DocTypeList = [
  { value: 'ver', label: '判决书' },
  { value: 'adj', label: '裁定书' },
  { value: 'med', label: '调解书' },
  { value: 'dec', label: '决定书' },
  { value: 'not', label: '通知书' },
  { value: 'rep', label: '批复' },
  { value: 'ans', label: '答复' },
  { value: 'let', label: '函' },
  { value: 'mak', label: '令' },
  { value: 'other', label: '其他' },
];

export const CourtLevelList = [
  {
    value: '1',
    label: '最高法院',
  },
  {
    value: '2',
    label: '高级法院',
  },
  {
    value: '3',
    label: '中级法院',
  },
  {
    value: '4',
    label: '基层法院',
  },
  {
    value: '5',
    label: '其他',
  },
];

export const CaseTypesList = [
  { label: '民事案件', value: 'ms' },
  { label: '刑事案件', value: 'xs' },
  { label: '行政案件', value: 'xz' },
  { label: '管辖案件', value: 'gx' },
  { label: '保全案件', value: 'bq' },
  { label: '执行案件', value: 'zx' },
  { label: '其他', value: 'qt' },
  { label: '其他', value: 'other' },
];

export const CaseStatusList = [
  { label: '已结案', value: '2' },
  { label: '待结案', value: '-1' },
];

// 专利法律状态标签颜色处理
export const hanlePatentStatusColor = (status) => {
  let tagClass = ''

  const blueList = ['ZT001', 'ZT001001', 'ZT001002', 'ZT001003', 'ZT005', 'ZT005001', 'ZT005002']
  const greenList = ['ZT002', 'ZT002001', 'ZT002002', 'ZT002003', 'ZT002004', 'ZT002005', 'ZT002006']
  const redList = ['ZT003',
    'ZT003001',
    'ZT003002',
    'ZT003003',
    'ZT003004',
    'ZT003005',
    'ZT003006',
    'ZT003007',
    'ZT003008',
    'ZT003009',
    'ZT003010',
    'ZT003011',
    'ZT006',
    'ZT006001',
    'ZT006002']
  if (greenList.includes(status)) {
    tagClass = 'text-success'
  } else if (redList.includes(status)) {
    tagClass = 'text-danger'
  } else if (blueList.includes(status)) {
    tagClass = 'text-primary'
  }
  return tagClass
}