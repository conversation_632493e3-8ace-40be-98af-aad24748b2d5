/**
 * 公司或组织类型
 */
export enum CompanyTypeEnum {
  // 公司
  Company = 0,
  // 社会组织
  Org = 1,
  // 主要人员
  Employee = 2,
  // 香港公司
  HKCompany = 3,
  // 政府机构和学校
  Government = 4,
  // 台湾公司
  TWCompany = 5,
  // 私募基金产品
  PefundProduct = 6,
  // 医院
  Hospital = 7,
  // 海外公司
  Oversea = 8,
  // 海外公司
  Oversea2 = 9,
  // 基金会
  Fund = 10,
  // 事业单位
  Institution = 11,
  // 律师事务所
  LawOffice = 12,
  // 投资机构
  Invest = 13,
  // 美股
  UsStock = 14,
  // 无法判断
  CompanyWithoutKeyNo = -1,
  // 没有Id的人名
  PersonWithoutCerNo = -2,
  // 其他
  Other = -3,
}
