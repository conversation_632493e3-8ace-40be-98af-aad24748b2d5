export const Menu = {};

export default {
  Menu,
  Home: 'Home',
  'Risk Checks': 'Risk Center',
  'Batch Checks': 'Batch Screening',
  'Third Party': 'Third Party',
  'Third Party List': 'Third Party List',
  'Cooperative Monitor': 'Cooperative Monitor',
  Blacklist: 'Blacklist',
  'Internal Blacklist': 'Internal Blacklist',
  'External Blacklist': 'External Blacklist',
  'Employee List': 'Employee List',
  Dashboard: 'Dashboard',
  'Total Third Party': 'Total Third-Party',
  'High Risk Third Party': 'No.of Third-party with High Risks',
  'Risk Third Party': 'Third-party with Risk Signs',
  'Medium Risk Third Party': 'No.of Third-party with Medium Risks',
  'Risk Third Party Percents': '%Risk Signs',
  'Low Risk Third Party': 'No.of Third-party with Low Risks',
  risk_legal: '法律风险',
  risk_administrative_supervision: '行政监管风险',
  risk_operateStability: '经营稳定性风险',
  risk_negative_opinion: 'Negative Information',
  risk_negative_news: 'Negative News',
  risk_inner_blacklist: '内部黑名单',
  risk_outer_blacklist: '外部黑名单',
  risk_interest_conflict: 'Potential COI',
  risk_partner_investigation: 'Multiple-Path Relationship',
  risk_base_info: '基础资质',
  risk_operate_stability: '经营稳定性风险',
  risk_blacklist: 'Blacklisted Third Party',
  risk_operate_compliance: '合规风险',
  risk_punished_employees: 'Sanctioned (Ex-) Employees',
  risk_newly_established: 'Young Third-Party',
  'High Risk': 'High Risk',
  'Medium Risk': 'Medium Risk',
  'Low Risk': 'Low Risk',
  'Tasks List': 'Tasks List',
  'Import Tasks': 'Import Tasks',
  'Verification Tasks': 'Verification Tasks',
  'Export tasks': 'Export tasks',
  'Report tasks': 'Report tasks',
  'Identity verification tasks': 'Identity verification tasks',
  'Settings Center': 'Setting Center',
  Account: 'Account',
  'Account Settings': 'Account Settings',
  'Account Organization': 'Account Organization',
  'Access Check Settings': 'DD Settings',
  'Investigation Settings': 'Investigation Settings',
  'Tender Settings': 'Tender Settings',
  'Interest Settings': 'Interest Relation Screening Settings',
  'Monitor Settings': 'Monitor Settings',
  'Message Center': 'Messages',
  'All Message': 'All Messages',
  'Task Message': 'Task Messages',
  'Download Message': 'Download Messages',
  'Active Message': 'Risk Messages',
  Overview: 'Overview',
  Total: 'Total',
  Refresh: 'Updated',
  'Refresh Success': 'Refresh Success',
  'Dimensional Analysis': 'Dimensional Analysis',
  dimensionalAnalysisSymbol: {
    riskCount: 'The number of companies matched to this type of risk;',
    riskPercentage:
      'The percentage of businesses that match the current risk type out of all businesses that match to the risk, including high & medium risk; Calculation formula: Number of enterprises matching the current risk type / Number of all enterprises matching to risk (including high risk & medium risk) * 100%.',
    increaseCount:
      'The growth rate of the number of such risky enterprises in this screening compared with the previous screening ; Calculation formula: the number of enterprises hit by this risk / the number of all risky enterprises this time * % - the number of enterprises hit by this risk last time / the number of all risk enterprises last time * %, that is, the growth rate = the proportion of this time - the proportion of the last time; The first screening of this item shows 0.00%.',
  },
  'Departmental Statistics': 'Departmental Statistics',
  'Third-party Risk Screening': 'Third-party Risk Screening',
  'Please enter a company name/USCI': 'Please enter a company name/USCI',
  Search: 'Search',
  法定代表人: 'Legal  Representative',
  'Basic information': 'Basic information',
  'Compliance risk': 'Compliance risk',
  'Company Name': 'Company Name',
  USCI: 'USCI',
  'Registered Capital': 'Registered Capital',
  Report: 'Report',
  'Download Report': 'Download Report',
  'Report Generating': 'Report Generating',
  'Follow-up': 'Follow-up',
  'Screening time': 'Screening time',
  export: 'Export',
  'Heat Map Desc': 'Heat maps showing the quantity distribution of a business in different areas in different color depths',
  'Tender Screening': 'Tender Screening',
  'Tender Screening Records': 'Tender Screening Records',
  'Risk Statistics': 'Risk Statistics',
  'Groups Setting': 'Groups Setting',
  'Tags Setting': 'Tags Setting',
  'Bidding Warning': 'Bidding Warning',
  'Subscription Setting': 'Subscription Setting',
  'Association Settings': 'Association Settings',
  'Cooperative Monitor Setting': 'Cooperative Monitor Setting',
  Other: 'Other',
  Unknown: 'Unknown',
  'Risk Annual Review': 'Risk Annual Review',
  'Refresh At': 'Refresh At',

  'Compliance Screening': 'Compliance Screening',
  'Interest Relation Screening': 'Interest Relation Screening',
  'Interest Relation Screening Records': 'Interest Relation Screening Records',

  'Due Diligence': 'Due Diligence',
  'Access Checks': 'Screening',
  'Check Records': 'Screening Records',
  'Due Diligence Model': 'Due Diligence Model',

  'Risk Monitoring': 'Risk Monitoring',
  'Monitoring Dashboard': 'Monitoring Dashboard',
  'Check Trends': 'Risk Dynamics',
  'Company List': 'Screening List',
  'Model Setting': 'Monitoring Model Setting',
  'Identity Verification': 'Identity Verification',
  'Verification History': 'Verification History',
};
