import { defineComponent } from 'vue';

import DefaultLayout from '../default';
import WorkbenchLayout from '../workbench';
import EmbedLayout from '../embed';
import ExternalLayout from '../external';

const LayoutPicker = defineComponent({
  functional: true,
  props: {
    name: {
      type: String,
      default: 'workbench',
    },
  },

  render(h, { props }) {
    // ref 用于从 root 找到 ConfigProvider 下的第一个子组件
    switch (props.name) {
      case 'workbench':
        return <WorkbenchLayout ref="layout" enableCompanySearch />;
      case 'embed':
        return <EmbedLayout ref="layout" />;
      case 'external':
        return <ExternalLayout ref="layout" />;
      case 'default':
      default:
        return <DefaultLayout ref="layout" />;
    }
  },
});

export default LayoutPicker;
