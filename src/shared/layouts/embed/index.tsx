import mixins from 'vue-typed-mixins';

import { PromiseDialogsWrapper } from '@/components/promise-dialogs';

import AuthRequiredLayoutMixin from '../_mixins/auth-required-layout.mixin';
import styles from '../workbench/workbench.layout.module.less';

const EmbedLayout = mixins(AuthRequiredLayoutMixin).extend({
  name: 'EmbedLayout',
  beforeMount() {
    // 不执行轮询
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.header}>
          {this.headerRenderer({
            isExternal: true,
          })}
        </div>
        <main class={styles.main}>
          <div class={styles.body}>
            <router-view></router-view>
          </div>
        </main>

        {/* 弹窗 */}
        <PromiseDialogsWrapper></PromiseDialogsWrapper>
      </div>
    );
  },
});

export default EmbedLayout;
