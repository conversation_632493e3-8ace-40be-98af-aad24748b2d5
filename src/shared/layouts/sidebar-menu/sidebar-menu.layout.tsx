import { defineComponent, ref } from 'vue';

import { useWebTitle } from '@/shared/composables/use-web-title';
import { useI18n } from '@/shared/composables/use-i18n';
import Sidebar from '@/shared/layouts/widgets/app-sidebar';
import { getCurrentLocale } from '@/utils/locale';

import styles from './sidebar-menu.layout.module.less';

const SidebarMenuLayout = defineComponent({
  name: 'SidebarMenuLayout',
  props: {
    /**
     * 侧边导航菜单配置
     */
    menu: {
      type: Array,
      required: true,
    },
    /**
     * 页面标题
     */
    pageTitle: {
      type: String,
      required: false,
    },
  },
  setup(props) {
    const { locale } = useI18n();
    locale.value = getCurrentLocale();

    const { setWebTitle, resetWebTitle } = useWebTitle(props.pageTitle);

    const collapse = ref(false);

    return {
      setWebTitle,
      resetWebTitle,
      collapse,
    };
  },
  watch: {
    '$route.path': {
      handler() {
        if (this.pageTitle) {
          this.setWebTitle(this.pageTitle);
        } else {
          this.resetWebTitle();
        }
      },
    },
  },
  render() {
    return (
      <div class={{ [styles.container]: true, [styles.collapse]: this.collapse }}>
        <aside class={styles.aside}>
          {/* 收起/展开 */}
          <Sidebar
            menuItems={this.menu}
            onCollapse={(val) => {
              this.collapse = val;
            }}
          ></Sidebar>
        </aside>
        <main class={styles.main}>
          {/* NOTE: 使用 key 来保证路径变化时需新渲染组件（例如：/notice/* ） */}
          <router-view key={this.$route.path}></router-view>
          {/* {this.$route.meta?.keepAlive ? ( */}
          {/*   <keep-alive> */}
          {/*     <router-view /> */}
          {/*   </keep-alive> */}
          {/* ) : ( */}
          {/*   <router-view /> */}
          {/* )} */}
        </main>
      </div>
    );
  },
});

export default SidebarMenuLayout;
