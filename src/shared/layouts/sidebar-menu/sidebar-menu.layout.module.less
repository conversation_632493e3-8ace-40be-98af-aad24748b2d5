@import "@/styles/token.less";

@sidebar-width: 180px;
@sidebar-collapse-width: 60px;
@header-height: 52px;
@sidebar-gap: 10px;

.container {
  .aside {
    background: #fff;
    width: @sidebar-width;
    position: fixed;
    top: @header-height;
    bottom: 0;
    z-index: 10;
    overflow-y: auto;
    box-shadow: inset -1px 0 0 @qcc-color-gray-500;
  }

  .main {
    margin-left: @sidebar-width;
    padding: @sidebar-gap;
    min-height: calc(100vh - @header-height - 20px);
  }

  &.collapse {
    .aside {
      width: @sidebar-collapse-width;
    }

    .main {
      margin-left: @sidebar-collapse-width;
    }
  }
}
