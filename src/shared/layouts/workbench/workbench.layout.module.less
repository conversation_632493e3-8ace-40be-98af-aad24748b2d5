@import "@/styles/token.less";

.container {
  width: @full-width;
  height: @full-height;
  // padding-left: @navigation-width;
  // padding-top: @header-height;
  max-height: 100vh;
  overflow: hidden;

  /**
   * 任务栏透明
   */
  .header.translucent {
    background: transparent;
    box-shadow: none;
  }

  .header {
    z-index: 30;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    height: @header-height;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }

  .aside {
    // height: 100%;
    position: fixed;
    top: @header-height;
    bottom: 0;
    left: 0;
    z-index: 11;
    width: @navigation-width;
  }

  .main {
    margin-top: @header-height;
    height: calc(100vh - @header-height);
    overflow: auto;

    &::-webkit-scrollbar {
      width: 10px;
      height: 10px
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(50, 50, 50, .25);
      border: 2px solid rgba(0, 0, 0, 0);
      border-radius: 10px;

      &:hover {
        background-color: rgba(50, 50, 50, .5)
      }
    }
  }

  .search {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 25;
    background: rgba(0, 0, 0, 0.6);
    visibility: hidden;
    opacity: 0;
    will-change: opacity;
    transition: opacity 0.13s ease-in-out;

    .companySearch {
      :global {
        .ant-btn {
          width: 130px;
        }
      }
    }

    &.expand {
      visibility: visible;
      opacity: 1;
    }

    .overlay {
      padding-top: 51px;
      width: 100%;
      height: 100%;
    }

    .box {
      padding: 50px 0;
      text-align: center;
      background: #fff;
      transform: translateY(-100%);
      transition: all 0.18s ease;

      &.expand {
        transform: translateY(0);
      }
    }
  }
}

.user-message {
  font-size: 14px;

  .icon {
    margin-right: 6px;
    font-size: 18px;
  }
}

.user-message-header {
  height: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: @qcc-font-bold;
    min-width: 150px;
  }

  .extra {
    color: @qcc-color-black-300;
  }
}

.user-message-body {
  margin-top: 8px;
  line-height: 22px;
  color: @qcc-color-black-500;

  :global {
    b {
      font-weight: @qcc-font-bold;
    }

    em {
      &.success {
        color: @qcc-color-green-500;
      }

      &.failed {
        color: @qcc-color-red-500;
      }
    }
  }

  .action {
    margin-top: 8px;
  }
}

.notification {
  padding: 16px;
  z-index: 1051;

  :global {
    .ant-notification-notice-icon {
      margin-top: 1px;
      font-size: 18px;
      line-height: 18px;
    }

    .ant-notification-notice-message {
      margin-left: 26px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: calc(100% - 40px);
    }

    .ant-notification-notice-description {
      margin-left: 26px;
    }
  }
}

.user-message-read-all {
  cursor: pointer;
  padding: 3px;
  background-color: transparent;
  font-size: 16px;
  border-radius: 2px;
  color: @qcc-color-black-300;

  &:hover {
    background-color: @qcc-color-blue-200;
    color: @qcc-color-blue-500;
  }
}

.user-info {
  color: @qcc-color-black-500;
  font-size: 12px;
  line-height: 16px;

  .expire {
    color: @qcc-color-red-500;
  }

  li:not(:last-child) {
    margin-bottom: 5px;
  }

  .name {
    color: @qcc-color-black-600;
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
  }

  .date {
    color: #999;
    font-size: 12px;
    line-height: 18px;
  }
}

.btn {
  padding: 0 5px;
  border-radius: 2px;
  font-size: 14px !important;
  line-height: 22px !important;

  &:hover {
    background-color: #f2f8fe;
  }
}

.tinySearch {
  min-width: 69px;
  background: #fafafa;
  border: 1px solid #d8d8d8;
  padding: 4px 10px;
  line-height: 22px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 240px;
  white-space: nowrap;
  overflow: hidden;
  transition: width 0.13s ease;

  &:hover {
    border-color: @qcc-color-blue-500;
  }

  &.expand {
    width: 69px;
  }

  i {
    font-size: 12px;
    color: #999;
  }

  span {
    margin-left: 5px;
    color: #bbb;
  }
}

.dropDown {
  :global {
    .ant-dropdown-menu {
      padding: 5px 0;

      .ant-dropdown-menu-item {
        width: 160px;
        padding: 5px 15px;

        a:hover {
          color: #128bed !important;
        }
      }
    }
  }
}
