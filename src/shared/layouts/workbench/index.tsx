import mixins from 'vue-typed-mixins';

import { PromiseDialogsWrapper } from '@/components/promise-dialogs';
import CompanySearch from '@/shared/components/company-search';
import RiskWatermark from '@/components/full-watermark';
// import { createTrackEvent } from '@/config/tracking-events';

import styles from './workbench.layout.module.less';
import AuthRequiredLayoutMixin from '../_mixins/auth-required-layout.mixin';

const WorkbenchLayout = mixins(AuthRequiredLayoutMixin).extend({
  name: 'WorkbenchLayout',
  props: {
    /**
     * 是否显示搜索框
     */
    enableCompanySearch: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    defaultKeywords(): string {
      return (this.$route?.query?.keyword as string) || '';
    },
  },
  methods: {
    stopPropagation(ev) {
      ev.stopPropagation();
    },
    // 窗口变成活动状态时，执行下面的代码
    handleVisibilityChange() {
      const isActive = document.visibilityState === 'visible';
      if (isActive) {
        (this as any).getUsage();
      }
    },

    /**
     * 企业搜索：关键字
     */
    handleSearch(keywords) {
      // this.$track(
      //   createTrackEvent(6893, {
      //     pageName: '三方风险顶部菜单',
      //     searchType: '企业信息查询',
      //     searchKeywords: keywords,
      //   })
      // );

      this.closeSearchBox();
      this.$router.push({
        name: 'company-search',
        query: {
          keyword: keywords,
          from: 'searchbar',
        },
      });
    },

    /**
     * 企业搜索：从下拉列表选中时直接跳转到企业详情页
     */
    handleSelect(option) {
      this.resetSearch();
      window.open(`/embed/companyDetail?keyNo=${option.id}&title=${option.value}`);
    },

    resetSearch() {
      (this.$refs?.CompanySearch as any)?.handleClear();
      this.closeSearchBox();
    },
  },
  watch: {
    isSearchMode(value: boolean) {
      if (value) {
        (this.$refs?.CompanySearch as any)?.focus();
      }
    },
  },
  beforeMount() {
    // const { getSettingInfo } = useSettingStore();
    // const { getSettingInfo: getMonitorSettingInfo } = useMonitorSettingStore();
    // const { getSetting: getBiddingSettingInfo } = getSettingStore();

    // TODO: 暂时禁用请求
    // (this as any).getBundle();
    (this as any).getUsage();
    // (this as any).getDimensionData();
    // getSettingInfo();
    // getMonitorSettingInfo();
    // getBiddingSettingInfo();
    (this as any).getOrganizations();
    (this as any).getMessages();
    (this as any).pollingMessages();
  },
  mounted() {
    // 窗口变成活动状态时，执行下面的代码
    window.addEventListener('visibilitychange', this.handleVisibilityChange);
  },
  beforeDestroy() {
    clearInterval(this.pollingMessageId);
    window.removeEventListener('visibilitychange', this.handleVisibilityChange);
  },
  render() {
    return (
      <RiskWatermark
        content={(this as any)?.profile?.uidHash ?? ''}
        width={window.innerWidth}
        height={window.innerHeight}
        fontColor="rgba(100, 100, 100, 0.01)"
        fillOffset={{
          x: 100,
          y: 100,
        }}
      >
        <div class={styles.container}>
          <div class={{ [styles.header]: true, [styles.translucent]: (this as any).$route.meta?.translucentHeader }}>
            {this.headerRenderer()}
          </div>
          <div
            class={{
              [styles.search]: true,
              [styles.expand]: this.isSearchMode,
            }}
          >
            <div class={styles.overlay} onClick={this.closeSearchBox}>
              <div
                class={{
                  [styles.box]: true,
                  [styles.expand]: this.isSearchMode,
                }}
                onClick={(ev) => {
                  ev.stopPropagation();
                }}
              >
                <CompanySearch
                  storagePrefix="COMPANY_SEARCH_HISTORY"
                  class={styles.companySearch}
                  defaultKeywords={this.defaultKeywords}
                  ref="CompanySearch"
                  title="企业信息查询"
                  placeholder="请输入企业名、人名、产品名、地址、电话、经营范围等，多关键词用空格隔开"
                  enterText="查一下"
                  height="52px"
                  hasBulkSearch={false}
                  onSearch={this.handleSearch}
                  onSelect={this.handleSelect}
                />
              </div>
            </div>
          </div>
          <main id="workbench-layout-main" class={styles.main}>
            <div class={styles.body}>
              <router-view></router-view>
            </div>
          </main>
          <PromiseDialogsWrapper></PromiseDialogsWrapper>
        </div>
      </RiskWatermark>
    );
  },
});

export default WorkbenchLayout;
