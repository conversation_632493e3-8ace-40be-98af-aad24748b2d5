@import '@/styles/token.less';

.container {
  min-height: calc(100vh - 52px - 20px);
  display: flex;
  flex-direction: column;

  &.gap {
    > * + * {
      margin-top: 10px;
    }
  }

  .hero,
  .children {
    // min-width: 1158px;
    min-width: 900px;
    border-radius: @qcc-border-radius-middle;
    position: relative;
  }

  .children {
    display: flex;
    flex-direction: column;
    background-color: @qcc-color-white;
    flex: 1;
    // 控制布局
    &.top {
      display: flex;
      align-items: flex-start;
    }

    &.bottom {
      display: flex;
      align-items: flex-end;
    }

    &.center {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.spin {
  :global {
    div .ant-spin {
      background-color: #fff;
      max-height: none;
    }
  }
}
