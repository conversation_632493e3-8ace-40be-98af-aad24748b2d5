import { defineComponent, PropType } from 'vue';
import { Spin } from 'ant-design-vue';

import styles from './heroic.layout.module.less';

const HeroicLayout = defineComponent({
  functional: true,
  props: {
    align: {
      type: String as PropType<'center' | 'top' | 'bottom'>,
      required: false,
    },
    innerStyle: {
      type: Object,
      default: () => ({}),
    },
    bodyStyle: {
      type: Object,
      default: () => ({}),
    },
    gap: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  render(h, { props, slots }) {
    const { default: children, hero } = slots();
    return (
      <Spin class={styles.spin} spinning={props.loading}>
        <div
          class={{
            [styles.container]: true,
            [styles.gap]: props.gap,
          }}
          style={props.innerStyle}
        >
          {hero ? <header class={styles.hero}>{hero}</header> : null}
          {children ? (
            <section class={props.align ? [styles.children, styles[props.align]] : [styles.children]} style={props.bodyStyle}>
              {children}
            </section>
          ) : null}
        </div>
      </Spin>
    );
  },
});

export default HeroicLayout;
