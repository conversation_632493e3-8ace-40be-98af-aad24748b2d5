import { defineComponent, watch } from 'vue';
import { RouterView } from 'vue-router';
import { useRoute } from 'vue-router/composables';

import { useWebTitle } from '@/shared/composables/use-web-title';
import { useI18n } from '@/shared/composables/use-i18n';
import { getCurrentLocale } from '@/utils/locale';

import styles from './insights.layout.module.less';

const InsightsLayout = defineComponent({
  name: 'InsightsLayout',
  props: {
    /**
     * 页面标题
     */
    pageTitle: {
      type: String,
      required: false,
    },
  },
  setup(props) {
    const { locale } = useI18n();
    locale.value = getCurrentLocale();

    const { setWebTitle, resetWebTitle } = useWebTitle(props.pageTitle);

    const route = useRoute();

    watch(
      () => route.path,
      () => {
        if (props.pageTitle) {
          setWebTitle(props.pageTitle);
        } else {
          resetWebTitle();
        }
      }
    );

    return {
      setWebTitle,
      resetWebTitle,
    };
  },
  render() {
    return (
      <div class={styles.wrapper}>
        <div class={styles.container}>
          <RouterView key={this.$route.path} />
        </div>
      </div>
    );
  },
});

export default InsightsLayout;
