import { defineComponent, onBeforeMount, onMounted } from 'vue';

import { PromiseDialogsWrapper } from '@/components/promise-dialogs';
// import { useSocket } from '@/hooks/use-socket';
// import { useUserStore } from '@/shared/composables/use-user-store';

const ExternalLayout = defineComponent({
  name: 'ExternalLayout',
  setup() {
    onMounted(() => {
      // const { profile } = useUserStore();
      // const { joinRoom } = useSocket('/rover/socket');
      //
      // // 加入房间
      // joinRoom(profile.value, 'TenderDiligence');
      // joinRoom(profile.value, 'BatchProcessMonitor');
    });
  },
  render() {
    return (
      <div>
        <router-view />
        <PromiseDialogsWrapper />
      </div>
    );
  },
});

export default ExternalLayout;
