import { defineComponent } from 'vue';
import { mapGetters, mapActions } from 'vuex';

const MonitorLayout = defineComponent({
  name: 'MonitorLayout',
  computed: {
    ...mapGetters('monitor', ['groupList', 'recycleCount']),
  },
  data() {
    return {};
  },
  mounted() {
    if (this.$route.name !== 'monitor-home') {
      this.getGroupList();
    }
  },
  methods: {
    ...mapActions('monitor', ['getGroupList']),
    monitorNav(item = {}) {
      const { name, params } = this.$route;
      const routeObj = {
        name: item.targetRoute,
        params: {},
      };
      if (item.groupId) {
        routeObj.params.groupid = item.groupId;
        // routeObj.params.tab = params.tab || 'list';
      }
      if (!item.monitor) {
        routeObj.params.tab = 'list';
      } else {
        routeObj.params.tab = 'dynamic';
      }
      if (routeObj.name !== name || JSON.stringify(routeObj.params) !== JSON.stringify(params)) {
        this.$router.push(routeObj);
      }
    },
  },
});

export default MonitorLayout;
