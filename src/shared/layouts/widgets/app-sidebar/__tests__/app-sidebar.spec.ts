import { shallowMount } from '@vue/test-utils';

import AppSidebar from '..';

describe('AppSidebar', () => {
  test('render: empty', () => {
    const wrapper = shallowMount<InstanceType<typeof AppSidebar>>(AppSidebar);
    expect(wrapper).toMatchSnapshot();
  });

  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof AppSidebar>>(AppSidebar, {
      propsData: {
        menuItems: [
          {
            key: 'a',
            label: 'A',
            icon: 'ICON',
            children: [
              {
                key: '1-1',
                label: '1-1',
              },
              {
                key: '1-2',
                label: '1-2',
              },
            ],
          },
        ],
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
