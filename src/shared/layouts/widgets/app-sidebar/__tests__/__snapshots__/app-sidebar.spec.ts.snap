// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AppSidebar > render 1`] = `
<div class="container">
  <div class="menu">
    <div>
      <div class="menuItem">
        <atooltip-stub trigger="hover" placement="rightTop" transitionname="zoom-big" overlaystyle="[object Object]" overlayclassname="menuPopover" prefixcls="ant-popover" mouseenterdelay="0.1" mouseleavedelay="0.1" autoadjustoverflow="true" align="[object Object]"><template>
            <div>
              <div class="ant-popover-inner-content">
                <div class="menuItemLabel active"><span class="icon"><q-icon-stub type="ICON"></q-icon-stub></span><span class="content">A</span><span class="mark" style="display: none;"><q-icon-stub type="icon-a-xianduanxia"></q-icon-stub></span></div>
                <div class="menuItemSection collapse">
                  <div class="group">
                    <div class="menuGroup">
                      <div class="item">1-1</div>
                      <div class="item">1-2</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <div class="menuItemLabel"><span class="icon"><q-icon-stub type="ICON"></q-icon-stub></span><span class="content">A</span><span class="mark"><q-icon-stub type="icon-a-xianduanxia"></q-icon-stub></span></div>
        </atooltip-stub>
        <div class="menuItemSection collapse">
          <div class="group">
            <div class="menuGroup">
              <div class="item">1-1</div>
              <div class="item">1-2</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`AppSidebar > render: empty 1`] = `
<div class="container">
  <div class="menu">
    <div></div>
  </div>
</div>
`;
