import { PropType, defineComponent } from 'vue';
import { Dropdown, Menu } from 'ant-design-vue';

import { RouterLink } from 'vue-router';
import { hasPermission } from '@/shared/composables/use-permission';
import { useUserStore } from '@/shared/composables/use-user-store';

import styles from './app-dropdown-menu.module.less';

const AppDropDownMenu = defineComponent({
  name: 'AppDropDownMenu',
  props: {
    menuList: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    linkStyle: {
      type: Object as PropType<any>,
      default: () => ({
        fontSize: '16px',
        lineHeight: '32px',
      }),
    },
    textStyle: {
      type: Object as PropType<any>,
      default: () => ({
        lineHeight: '52px',
        cursor: 'pointer',
      }),
    },
  },
  setup() {
    const { orgModules } = useUserStore();
    return {
      orgModules,
    };
  },
  render() {
    return (
      <div class={[styles.container, 'flex items-center']}>
        {this.menuList.map((menu: any) => {
          const { subMenuList, moduleId, menuLabel } = menu;
          if (!this.orgModules.includes(moduleId)) {
            return null;
          }
          // 处理子维度的权限
          const subs = subMenuList.filter((sub) => hasPermission(sub.permission));
          if (!subs.length) {
            return null;
          }
          return (
            <Dropdown>
              <template slot="overlay">
                <Menu>
                  {subMenuList.map((subMenu: any) => {
                    const { link, menuLabel, permission } = subMenu;
                    return (
                      <Menu.Item>
                        <RouterLink to={link} style={{ ...this.linkStyle }}>
                          {menuLabel}
                        </RouterLink>
                      </Menu.Item>
                    );
                  })}
                </Menu>
              </template>
              <div style={{ ...this.textStyle }}>
                <span>{menuLabel}</span>
                {this.$slots.menuIcon}
                {/* <QIcon type="icon-a-shixinxia1x1" /> */}
              </div>
            </Dropdown>
          );
        })}
      </div>
    );
  },
});

export default AppDropDownMenu;
