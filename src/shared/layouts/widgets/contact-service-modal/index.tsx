import { computed, defineComponent, onMounted, ref } from 'vue';
import { isEmpty } from 'lodash';
import { Spin } from 'ant-design-vue';

import { withModalWrapper } from '@/shared/components/base-modal';
import { useStore } from '@/store';
import { auth } from '@/shared/services';

import styles from './contact-service-modal.module.less';

const DEFAULT_MANAGER = {
  name: '小查',
  phone: '************',
  email: '<EMAIL>',
  wchat: null,
};

const ContactService = defineComponent({
  name: 'ContactService',
  props: {
    visible: {
      type: Boolean,
      required: false,
    },
  },
  setup() {
    const store = useStore();
    const profile = computed(() => store.getters['user/profile']);
    const isLoading = ref(false);
    const manager = ref(DEFAULT_MANAGER);

    const getManager = async () => {
      try {
        const { orgName } = profile.value;
        if (!orgName) {
          throw new Error('暂无数据');
        }
        isLoading.value = true;
        const res = await auth.getCustomerManger(orgName);
        if (!isEmpty(res)) {
          manager.value = res;
        }
        isLoading.value = false;
      } catch (error) {
        isLoading.value = false;
      }
    };

    onMounted(() => {
      getManager();
    });

    return {
      manager,
      isLoading,
    };
  },
  render() {
    const { isLoading, manager } = this;
    return (
      <div class={styles.containe9r}>
        {isLoading ? (
          <Spin>
            <div style={{ height: '108px' }}></div>
          </Spin>
        ) : (
          <div class={styles.container}>
            <h3 class={styles.name}>{manager.name}</h3>
            <ul class={styles.contact}>
              {manager.phone ? (
                <li>
                  <span class={styles.contactDes}>电话：</span>
                  {manager.phone}
                </li>
              ) : null}
              {manager.email ? (
                <li>
                  <span class={styles.contactDes}>邮箱：</span>
                  {manager.email}
                </li>
              ) : null}
              {manager.wchat ? (
                <li class={styles.contactDes}>
                  <span>微信：</span> {manager.wchat}
                </li>
              ) : null}
            </ul>
          </div>
        )}
        <div class={styles.description}>第三方风险排查系统，风险早发现，信任零距离</div>
      </div>
    );
  },
});

const ContactServiceModal = withModalWrapper(ContactService, {
  width: 400,
  title: '联系客户经理',
  hasFooter: false,
  dialogStyle: {
    top: '50px',
  },
});

export default ContactServiceModal;
