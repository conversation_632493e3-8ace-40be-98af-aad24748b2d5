import { createLocalVue, mount } from '@vue/test-utils';

import { auth } from '@/shared/services';
import { useStore } from '@/store';

import ContactServiceModal from '..';

vi.mock('@/shared/services', () => ({
  auth: {
    getCustomerManger: vi.fn(),
  },
}));

vi.mock('@/store', () => ({
  useStore: vi.fn(),
}));

describe('ContactServiceModal', () => {
  const localVue = createLocalVue();
  let wrapper;
  beforeEach(() => {
    wrapper = mount(ContactServiceModal, {
      localVue,
    });
  });
  afterEach(() => {
    wrapper.destroy();
    vi.clearAllMocks();
  });

  it('应该在弹窗打开时调用getManager', async () => {
    vi.mocked<any>(auth.getCustomerManger).mockResolvedValueOnce({});

    vi.mocked<any>(useStore).mockImplementationOnce(() => ({
      getters: {
        'user/profile': {
          orgName: '测试公司',
        },
      },
    }));

    await wrapper.setProps({ visible: true });
    await wrapper.vm.$nextTick();

    expect(auth.getCustomerManger).toHaveBeenCalledWith('测试公司');
  });

  it('应该在orgName为空时抛出错误', async () => {
    vi.mocked<any>(useStore).mockImplementationOnce(() => ({
      getters: {
        'user/profile': {
          orgName: null,
        },
      },
    }));

    try {
      await wrapper.setProps({ visible: true });
      await wrapper.vm.$nextTick();
    } catch (error) {
      expect(auth.getCustomerManger).not.toHaveBeenCalled();
      expect(error.message).toBe('暂无数据');
    }
  });

  it('应该在isLoading为false时显示manager信息', async () => {
    vi.mocked<any>(auth.getCustomerManger).mockResolvedValueOnce({
      name: 'test',
      phone: '17000000000',
      email: '<EMAIL>',
    });

    vi.mocked<any>(useStore).mockImplementationOnce(() => ({
      getters: {
        'user/profile': {
          orgName: '测试公司',
        },
      },
    }));

    await wrapper.setProps({ visible: true });

    await wrapper.vm.$nextTick();

    expect(wrapper.text()).toContain('test');
    expect(wrapper.text()).toContain('17000000000');
    expect(wrapper.text()).toContain('<EMAIL>');
  });

  it('数据缺失时应该正确渲染', async () => {
    vi.mocked<any>(auth.getCustomerManger).mockResolvedValueOnce({
      name: 'test',
      phone: '17000000000',
    });

    vi.mocked<any>(useStore).mockImplementationOnce(() => ({
      getters: {
        'user/profile': {
          orgName: '测试公司',
        },
      },
    }));

    await wrapper.setProps({ visible: true });

    await wrapper.vm.$nextTick();

    expect(wrapper.text()).not.toContain('邮箱');
  });
  it('数据缺失时应该正确渲染', async () => {
    vi.mocked<any>(auth.getCustomerManger).mockResolvedValueOnce({
      name: 'test',
    });

    vi.mocked<any>(useStore).mockImplementationOnce(() => ({
      getters: {
        'user/profile': {
          orgName: '测试公司',
        },
      },
    }));

    await wrapper.setProps({ visible: true });

    await wrapper.vm.$nextTick();

    expect(wrapper.text()).not.toContain('电话');
  });
});
