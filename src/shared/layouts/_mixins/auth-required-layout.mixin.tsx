import Vue, { defineComponent, unref } from 'vue';
import { Badge, Dropdown, Tooltip, notification } from 'ant-design-vue';
import { mapActions, mapGetters, mapState } from 'vuex';

import QIcon from '@/shared/components/icon';
import { loginURI, logoutURI } from '@/shared/composables/use-auth';
import * as AppHeader from '@/shared/components/app-header-pro';
import { autoFormat } from '@/utils/format/date';
import { appRouter as router } from '@/router/app.router';
import { dateFormat } from '@/utils/format';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useI18n } from '@/shared/composables/use-i18n';
import { useTranslateZeiss } from '@/shared/composables/use-translate';
import env from '@/shared/config/env';
import store from '@/store';
import { createTrackEvent } from '@/config/tracking-events';
import { useSocket } from '@/hooks/use-socket';
import { getMsgIcon } from '@/apps/notification/config';

import styles from '../workbench/workbench.layout.module.less';
import InsightsLogo from '../workbench/widgets/insights-logo';
import QccLogo from '../workbench/widgets/qcc-logo';

export const openNewPage = (data) => {
  if (!data) return;
  const urlObj = router.resolve(data);
  window.open(urlObj.href, '_blank');
};

const LOCALES = [
  { label: '简体中文', value: 'en' },
  { label: 'English', value: 'zh-CN' },
];

const MessageHeader = defineComponent({
  functional: true,
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
  },
  render(h, { props }) {
    const { value, showIcon = true } = props;
    const { t } = useTranslateZeiss();
    return (
      <header class={styles.userMessageHeader}>
        {showIcon && <q-icon type={getMsgIcon(value.msgType, value.content)} class={styles.icon} />}

        <span class={styles.title}>{t(value.title)}</span>
        <span class={styles.extra}>{autoFormat(value.updateDate)}</span>
      </header>
    );
  },
});

const handleReadMessage = async (message) => {
  notification.destroy();
  try {
    await store.dispatch('message/readMessage', message.id);
  } catch (err) {
    console.error(err);
  } finally {
    openNewPage({
      path: message.url || '/notice/all-remind',
    });
  }
};

const MessageContent = defineComponent({
  functional: true,
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  render(h, { props }) {
    const { value } = props;
    return (
      <div class={styles.userMessageBody}>
        <div class={styles.content} domPropsInnerHTML={value.content} />
        <div class={styles.action}>
          <a onClick={() => handleReadMessage(value)}>
            <span>点击查看</span>
            <QIcon type="icon-a-xianduanyou" />
          </a>
        </div>
      </div>
    );
  },
});

export const MessageItem = defineComponent({
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { value } = this;

    return (
      <div class={styles.userMessage}>
        <MessageHeader value={value} />
        <MessageContent value={value} />
      </div>
    );
  },
});

const AuthRequiredLayoutMixin = Vue.extend({
  props: {
    /**
     * 是否显示搜索框
     */
    enableCompanySearch: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      pollingMessageId: undefined as number | undefined,
      isSearchMode: false,
    };
  },
  setup() {
    const { isZeiss } = useUserStore();
    return {
      isZeiss,
    };
  },
  computed: {
    ...mapGetters('user', ['profile']),
    ...mapState('message', ['messages', 'messageCount']),
    // 是蔡司，并且在本地开发环境
    lang(): string {
      return LOCALES.find(({ value }) => value === (this.$i18n as any).locale)?.label || '简体中文';
    },
    searchPlaceholder(): string {
      const withKeywords = (this.$route.query?.keyword as string) && this.$route.meta?.widthSearchKeywords;
      if (withKeywords) {
        return this.$route.query?.keyword as string;
      }
      return this.isSearchMode ? '搜索' : '请输入企业名、人名、产品名等';
    },
    menuData(): Array<any> {
      return [
        {
          label: '用户手册',
          icon: 'icon-yonghushouce',
          urlName: '/portal/document/guide-manual/service-intro',
          show: false,
        },
        {
          label: '更新日志',
          icon: 'icon-gengxinrizhi',
          urlName: '/portal/document/release-log',
          show: false,
        },
        {
          label: 'Tasks List',
          tooltip: null,
          icon: 'icon-renwuliebiao2',
          urlName: '/app/tasklist',
          show: true,
        },
      ].filter((item) => item.show);
    },
    isIdentityVerificationApp(): boolean {
      return /^\/identity-verification\//.test((this as any).$route.path);
    },
  },
  methods: {
    ...mapActions('user', ['getBundle', 'getUsage', 'getOrganizations', 'getDimensionData']),
    ...mapActions('message', ['getMessages', 'readMessage', 'readAllMessages', 'addMessage']),
    handleMenuItemChange(data) {
      const { key, menuItem } = data;
      this.$track(createTrackEvent(6895, '个人中心', menuItem?.label));

      // 切换组织统一跳初始页
      if (key === 'switch') {
        this.$router.push({
          name: 'switch-org',
          query: {
            redirect: '/app',
          },
        });
      }
    },
    async handleReadMessage(message) {
      try {
        await (this as any).readMessage(message.id);
      } catch (err) {
        console.error(err);
      } finally {
        if (message.url) {
          router
            .push({
              path: message.url || '/notice/all-remind',
            })
            .catch(() => undefined);
        }
      }
    },

    /**
     * 读取全部消息
     */
    handleReadAllMessages(ev) {
      (this as any).readAllMessages({
        msgType: 0,
      });
      ev.stopPropagation();
    },

    /**
     * 新消息提醒
     */
    handleMessageNotify(systemMessage: Record<string, any>) {
      notification.info({
        class: styles.notification,
        key: systemMessage?.id ? String(systemMessage.id) : undefined,
        message: (h) =>
          h(MessageHeader, {
            props: {
              value: systemMessage,
              showIcon: false,
            },
          }),
        description: (h) =>
          h(MessageContent, {
            props: {
              value: systemMessage,
            },
          }),
        icon: (h) =>
          h('q-icon', {
            attrs: { type: getMsgIcon(systemMessage.msgType, systemMessage.content) },
            class: styles.icon,
          }),
      });
    },

    pollingMessages() {
      const { socket, joinRoom } = useSocket('/insights/socket');

      // 加入房间
      joinRoom((this as any).profile, 'BatchProcessMonitor');
      // joinRoom((this as any).profile, 'TenderDiligence');
      // joinRoom((this as any).profile, 'SpecificDiligence');

      socket.on('SystemMessage', async (incomingMessage: Record<string, any>) => {
        // 手动将socket获取到的消息拼接到队列的第一个，并进行同步
        // const data = [incomingMessage[0], ...(this as any).messages.slice(0, 9)];
        // const total = (this as any).messageCount + 1;
        await (this as any).addMessage(incomingMessage);
        this.handleMessageNotify(incomingMessage);
      });
    },
    logoRenderer() {
      const logoColor = (this as any).$route.meta?.translucentHeader ? '#fff' : 'inherit';
      return (
        <span
          style={{
            color: logoColor,
          }}
        >
          {this.isIdentityVerificationApp ? <QccLogo /> : <InsightsLogo />}
        </span>
      );
    },
    openSearchBox(event) {
      this.isSearchMode = true;
      event.preventDefault();
    },
    closeSearchBox() {
      this.isSearchMode = false;
    },
    // Renderer
    headerRenderer(options?: Record<string, unknown>) {
      const { t } = useI18n();
      const { profile } = useUserStore();
      const USER_MENU_ITEMS = [
        {
          key: 'switch',
          icon: 'swap',
          label: '切换组织',
        },
        // {
        //   key: 'settings',
        //   icon: 'setting',
        //   label: '设置中心',
        //   link: '/setting',
        //   external: false,
        //   permission: [2081, 2083, 2085, 2087, 2089, 2132, 2033, 2048, 2063, 2103, 2035, 2049],
        // },
        {
          key: 'account',
          iconType: 'qicon',
          icon: 'icon-renyuanjintiao',
          label: '个人中心',
          link: '/account/settings',
          external: false,
        },
        {
          key: 'enterprise-center',
          icon: 'icon-enterprise',
          iconType: 'qicon',
          label: '企业中心',
          link: `${env.ENTERPRISE_HOME}/qcc/e/changeOrg/${unref(profile)?.currentOrg}?redirect=/e`,
          external: true,
          permission: (permissions: number[]) =>
            // 1开头的是企业中心的权限，如果有，说明有权限
            permissions.some((permission) => /^1\d{3}$/.test(permission.toString())),
        },
      ] as any[];

      return (
        <AppHeader.default translucent={false}>
          <div slot="logo" class="flex items-center">
            <AppHeader.Logo href={options?.isExternal ? undefined : '/app'}>{this.logoRenderer()}</AppHeader.Logo>
          </div>

          {!options?.isExternal ? (
            <AppHeader.QuickMenu slot="quick">
              {this.enableCompanySearch && !this.isIdentityVerificationApp ? (
                <div
                  class={{
                    [styles.tinySearch]: true,
                    [styles.expand]: this.isSearchMode,
                  }}
                  onClick={this.openSearchBox}
                >
                  <QIcon type="icon-chaxun" />
                  <span>{this.searchPlaceholder}</span>
                </div>
              ) : null}

              <ul>
                {this.menuData.map((menuData) => {
                  if (menuData.tooltip) {
                    return (
                      <li key={menuData.icon}>
                        <Tooltip title={t(menuData.tooltip)} placement="bottom">
                          <div
                            onClick={() => {
                              window.open(menuData.urlName, '_blank');
                            }}
                          >
                            <QIcon type={menuData.icon} />
                            <span>{t(menuData.label)}</span>
                          </div>
                        </Tooltip>
                      </li>
                    );
                  }
                  return (
                    <li key={menuData.icon}>
                      <div
                        onClick={() => {
                          window.open(menuData.urlName, '_blank');
                        }}
                      >
                        <q-icon type={menuData.icon} style="margin-right: 5px;"></q-icon>
                        <span>{t(menuData.label)}</span>
                      </div>
                    </li>
                  );
                })}

                <Dropdown>
                  <AppHeader.Messages
                    slot="overlay"
                    scopedSlots={{
                      message: (message) => {
                        return <MessageItem value={message} />;
                      },
                    }}
                    count={(this as any).messageCount}
                    messages={(this as any).messages}
                  >
                    <div slot="extra">
                      <Tooltip title="全部已读" placement="bottom" v-show={(this as any).messages?.length > 0}>
                        <span class={styles.userMessageReadAll} onClick={this.handleReadAllMessages}>
                          <QIcon type="icon-querenruwei1" />
                        </span>
                      </Tooltip>
                    </div>
                    <div slot="footer">
                      <div
                        onClick={() => {
                          openNewPage({
                            path: '/notice/all-remind',
                          });
                        }}
                        style={{ cursor: 'pointer' }}
                      >
                        <span>查看全部</span>
                        <QIcon type="icon-wenzilianjiantou" />
                      </div>
                    </div>
                  </AppHeader.Messages>
                  <li>
                    <Badge
                      count={(this as any).messageCount}
                      onClick={() => {
                        this.$router.push({ path: '/notice/all-remind' }).catch(() => undefined);
                      }}
                    >
                      <div>
                        <QIcon type="icon-xiaoxizhongxin" />
                      </div>
                    </Badge>
                  </li>
                </Dropdown>
              </ul>
            </AppHeader.QuickMenu>
          ) : null}

          {!options?.isExternal ? (
            <AppHeader.UserMenu
              user={(this as any).profile}
              slot="user"
              items={USER_MENU_ITEMS}
              onChange={this.handleMenuItemChange}
              loginPath={loginURI()}
              logoutPath={logoutURI()}
            >
              <ul class={styles.userInfo}>
                <li class={styles.name}>{(this as any).profile?.name}</li>
                <li class={styles.date}>
                  到期日期：<span>{dateFormat((this as any).profile?.bundle?.endDate)}</span>
                </li>
              </ul>
            </AppHeader.UserMenu>
          ) : null}
        </AppHeader.default>
      );
    },
  },
});

export default AuthRequiredLayoutMixin;
