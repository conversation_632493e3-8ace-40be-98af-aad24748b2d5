import Vue from 'vue';

const DimensionMixin = Vue.extend({
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data(): {
    mDimenData: unknown;
  } {
    return {
      mDimenData: null,
    };
  },
  watch: {},
  methods: {
    mDimenGetData(params: any, apiKey?: string) {
      return this.$service.dimension[apiKey || this.info.api](params);
    },
    // 获取列表并转换结构
    async mDimenGetList(params: any, apiKey?: string, serviceName = 'dimension') {
      const dimensionKey = apiKey || this.info.api;
      let idsPaging: any = {};

      // 风险扫描等共用维度，需要先获取ids，然后用ids请求接口
      if (this.info.isRiskScan && this.info.isValid === 1) {
        if (this.info.query) {
          await this.$service.dimension
            .getRiskListIds({
              ...params,
              query: this.info.query,
            })
            .then((data) => {
              if (data.Paging) {
                idsPaging = {
                  TotalRecords: data.Paging.TotalRecords,
                  PageSize: data.Paging.PageSize,
                  PageIndex: data.Paging.PageIndex,
                };
              }
              params.ids = data?.List;
              params.pageIndex = 1;
            });
        }
        params = {
          ...params,
          ...this.info.extraParams,
        };
      }

      params.isRiskScan = this.info.isRiskScan;

      const mDimenPromise = this.$service[serviceName][dimensionKey]
        ? this.$service[serviceName][dimensionKey](params)
        : this.$service.dimension.demensionAutoList(dimensionKey, params);
      return mDimenPromise.then((data: any) => {
        if (data) {
          if (data.data?.Result) {
            return data.data;
          }
          let paging: any = {};
          if (data.pageInfo) {
            paging = {
              TotalRecords: data.pageInfo.total,
              PageSize: data.pageInfo.pageSize,
              PageIndex: data.pageInfo.pageIndex,
            };
          } else if (data.Paging) {
            // 兼容新接口返回数据
            paging = {
              ...data.Paging,
              TotalRecords: data.Paging.TotalRecords,
              PageSize: data.Paging.PageSize,
              PageIndex: data.Paging.PageIndex,
            };
          } else if (data.list?.pageInfo) {
            paging = {
              TotalRecords: data.list.pageInfo.TotalRecords,
              PageSize: data.list.pageInfo.PageSize,
              PageIndex: data.list.pageInfo.PageIndex,
            };
          } else if (data.list?.Paging) {
            paging = {
              TotalRecords: data.list.Paging.TotalRecords,
              PageSize: data.list.Paging.PageSize,
              PageIndex: data.list.Paging.PageIndex,
            };
          }
          return {
            Result: data.data || data.Result || data.List || data.list?.data || data,
            Paging: this.info.query ? idsPaging : paging,
            GroupItems: data.groupItems || data.GroupItems,
            GroupNewItems: data.GroupNewItems || {},
          };
        }
        return null;
      });
    },
  },
});

export default DimensionMixin;
