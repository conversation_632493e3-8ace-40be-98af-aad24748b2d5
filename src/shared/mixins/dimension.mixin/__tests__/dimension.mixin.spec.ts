import { shallowMount } from '@vue/test-utils';
import { defineComponent } from 'vue';

import DimensionMixin from '..';

describe('DimensionMixin', () => {
  let TestComponent;

  beforeEach(() => {
    TestComponent = defineComponent({
      name: 'TestComponent',
      mixins: [DimensionMixin],
      template: '<div />',
    });
  });
  test('render', () => {
    const wrapper = shallowMount(TestComponent, {
      propsData: {},
    });
    expect(wrapper).toMatchSnapshot();
  });
});
