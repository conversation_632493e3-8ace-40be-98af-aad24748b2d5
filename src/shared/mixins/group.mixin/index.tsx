import { defineComponent } from 'vue';

import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

const listMixin = createMixin();

const GroupMixin = defineComponent({
  props: {
    keyNo: {
      type: String,
      required: true,
    },
    info: {
      type: Object,
      required: true,
    },
  },
  mixins: [dimensionMixin, listMixin],
  methods: {
    fetchDataSource({ pagination }) {
      return (this as any).mDimenGetList(
        {
          groupId: this.keyNo,
          ...pagination,
        },
        this.info.api,
        'group'
      );
    },
  },
});
export default GroupMixin;
