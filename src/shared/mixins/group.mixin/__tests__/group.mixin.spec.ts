import { shallowMount } from '@vue/test-utils';
import { defineComponent } from 'vue';

import GroupMixin from '..';

describe('GroupMixin', () => {
  let TestComponent;

  beforeEach(() => {
    TestComponent = defineComponent({
      name: 'TestComponent',
      mixins: [GroupMixin],
      template: '<div />',
    });
  });
  test('render', () => {
    const wrapper = shallowMount(TestComponent, {
      propsData: {
        keyNo: 'KEY_NO',
        info: {
          api: 'API',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
