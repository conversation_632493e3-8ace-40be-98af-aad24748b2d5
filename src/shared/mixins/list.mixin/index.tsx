import Vue, { CreateElement, VNode, PropType } from 'vue';
import _ from 'lodash';

import { ListResponse, GroupItemMap } from '@/interfaces';
import scrollToTop, { supportsNativeSmoothScroll } from '@/utils/scroll-to-top';
import { normalizeGroupItems, genGroupItemsOptions, GroupOption } from '@/utils/response';

type Pagination = { pageSize: number; current: number; total: number };

type FetchOptions = {
  key: string;
  pagination: {
    pageSize: number;
    pageIndex: number;
  };
};

export type DataModule<Meta = Record<string, any>> = {
  key: string;
  rowKey: string; // 默认 id
  pagination: Pagination;
  pageSizeOptions: string[];
  maxTotal: number; // 可翻页的最大数
  fePagination: boolean; // 是否前端分页
  autoScroll: boolean; // 翻页后是否滚动到对应表格顶部
  excludeDefaultOptionKeys: string[]; // 不需要添加“不限”到 optionsMap 选项中的字段名
  fetchDataSource?(e: { meta: Meta; vm: Vue } & FetchOptions): Promise<ListResponse<unknown>>;
  render?(h: CreateElement, e: { meta: Meta; vm: Vue; tableData: Record<string, any> }): VNode;
  meta?(e: { vm: Vue }): Meta;
  // 内部使用，一般情况下无需传参
  dataSource: unknown[];
  initGroupItems: GroupItemMap | undefined;
  groupItems: GroupItemMap | undefined;
  optionsMap: Record<string, GroupOption[]>;
  loading: boolean;
  init: boolean;
  initFinished: boolean;
};

type MixinOptions = {
  lazy?: boolean; // 是否启用懒加载
  autoInit?: boolean;
  scrollContainer?(vm: Vue): HTMLElement;
};

const isInViewPort = (ref: Vue) => {
  const el = Array.isArray(ref) ? ref[0].$el : (ref?.$el as HTMLElement);
  if (!el || !el.offsetParent) {
    // el.offsetParent 用于检测当前元素是否可见，可能为 display: none，此时无需初始化
    return false;
  }
  const edge = 40;
  const { innerHeight } = window;
  const { top, bottom } = el.getBoundingClientRect();

  if (top > innerHeight + edge || bottom < -edge) {
    return false;
  }

  return true;
};

const findScrollParent = (el: HTMLElement): HTMLElement | null => {
  let parent: HTMLElement | null = el.parentElement;

  while (parent) {
    if (parent.scrollHeight > parent.clientHeight) {
      return parent;
    }
    parent = parent.parentElement;
  }

  return null;
};

const scrollToElement = (el: HTMLElement, fixedTop: number) => {
  const scroll = findScrollParent(el);
  if (!scroll || [scroll.style.overflow, scroll.style.overflowY].includes('hidden')) {
    return;
  }

  if (scroll === document.body) {
    const { top } = el.getBoundingClientRect();

    scrollToTop((document.documentElement.scrollTop || window.scrollY || 0) + top - fixedTop);
  } else if (scroll.scroll && supportsNativeSmoothScroll) {
    scroll.scroll({
      top: 0,
      behavior: 'smooth',
    });
  } else {
    // 这里先滚动到顶部，因为模态框可能有固定在顶部标题，这部分高度还要计算
    scroll.scrollTop = 0;
  }
};

export const createMixin = (
  modules?: Array<string | (Partial<DataModule> & { key: string })>,
  defaultModuleData?: Partial<DataModule>,
  options?: MixinOptions
) => {
  const defaultState = (defaultModule?: string) => {
    const keys: string[] = [];
    const stateModules = (modules || ['default']).reduce(
      (m, k) => {
        const data: DataModule = {
          rowKey: '__list_key__',
          pagination: { current: 1, total: 0, pageSize: 10 },
          dataSource: [],
          maxTotal: 5000,
          fePagination: false,
          loading: true,
          init: false,
          initFinished: false,
          pageSizeOptions: ['10', '30', '50'],
          autoScroll: true,
          initGroupItems: undefined,
          groupItems: undefined,
          optionsMap: {},
          excludeDefaultOptionKeys: [],
          ..._.cloneDeep(defaultModuleData),
          ...(_.isString(k) ? { key: k } : _.cloneDeep(k)),
        };

        if (!data.pageSizeOptions.includes(data.pagination.pageSize.toString())) {
          data.pagination.pageSize = _.toNumber(data.pageSizeOptions[0]);
        }

        keys.push(data.key);

        return {
          ...m,
          [data.key]: data,
        };
      },
      {} as Record<string, DataModule>
    );
    return {
      mListModules: stateModules,
      mListActiveKey: (defaultModule && (keys.includes(defaultModule) ? defaultModule : keys[0])) || keys[0],
    };
  };
  const lazy = options?.lazy ?? false;
  const autoInit = options?.autoInit ?? true;

  return Vue.extend({
    props: {
      // 和 dimension 混用时用于需要生成 rowKey
      info: {
        type: Object as PropType<{ key: string }>,
        required: false,
      },
      defaultModule: {
        type: String,
        required: false,
      },
    },
    data(): {
      mListModules: Record<string, DataModule>;
      mListActiveKey: string;
    } {
      return defaultState(this.defaultModule);
    },
    watch: {
      mListActiveKey(key) {
        this.$nextTick(() => {
          this.mListInit(key);
        });
      },
    },
    methods: {
      // placeholder
      mListRenderItem(): VNode {
        return <div />;
      },
      // 在上层定义获取数据的方法
      fetchDataSource(o: FetchOptions): Promise<ListResponse<unknown>> {
        return Promise.resolve({
          GroupItems: {},
          Paging: {
            PageIndex: 1,
            PageSize: 10,
            TotalRecords: 0,
          },
          Result: [],
        });
      },
      // placeholder end
      mListInit(key?: string) {
        if (!key) {
          key = this.mListActiveKey;
        }
        const m = this.mListModules[key];

        if (m?.init) {
          return Promise.resolve();
        }
        m.init = true;

        return this.mListFetchList(key, true);
      },
      mListInitAll() {
        return Object.keys(this.mListModules).map((key) => this.mListInit(key));
      },
      mListGetState(key?: string): DataModule {
        if (!key) {
          key = this.mListActiveKey;
        }

        return this.mListModules[key];
      },
      mListGetTotal(key?: string): DataModule {
        if (!key) {
          key = this.mListActiveKey;
        }

        return _.get((this as any).mListModules[key], 'pagination.total');
      },
      /**
       * @private
       */
      mListHandlePaginationChange(key: string, index: number) {
        this.mListRefreshPagination(key, {
          current: index,
        });

        // const m = this.mListModules[key];

        // m.pagination.current = index;

        // if (!m.fePagination) {
        //   this.mListFetchList(key, false);
        // }
      },
      /**
       * @private
       */
      mListHandlePageSizeChange(key: string, index: number, pageSize: number) {
        this.mListRefreshPagination(key, {
          current: 1,
          pageSize,
        });
        // const m = this.mListModules[key];

        // Object.assign(m.pagination, {
        //   current: 1,
        //   pageSize,
        // });

        // if (!m.fePagination) {
        //   this.mListFetchList(key, true);
        // }
      },
      /**
       * @private
       */
      mListFetchList(key: string, isInit = false, pagination: Partial<Pagination> = {}) {
        const m = this.mListModules[key];

        return this.mListFetchDataSource(
          {
            key,
            pagination: {
              pageIndex: pagination?.current ?? m.pagination.current,
              pageSize: pagination?.pageSize ?? m.pagination.pageSize,
            },
          },
          isInit
        );
      },
      /**
       * @private
       */
      mListFetchDataSource(o: FetchOptions, isInit: boolean): Promise<ListResponse<unknown>> {
        const m = this.mListModules[o.key];
        let request;

        if (m.fetchDataSource) {
          request = m.fetchDataSource({
            ...o,
            vm: this,
            meta: m.meta ? m.meta({ vm: this }) : {},
          });
        } else {
          request = this.fetchDataSource(o);
        }

        if (!request || !request.then) {
          request = Promise.resolve(request);
        }
        m.loading = true;
        return request
          .then((data) => {
            m.loading = false;
            m.initFinished = true;

            if (m.fePagination) {
              if (Array.isArray(data)) {
                Object.assign(m.pagination, o.pagination, {
                  total: data.length,
                });
                m.dataSource = data;
              }

              return data;
            }

            const { Result, Paging, GroupItems } = data || {};
            Object.assign(m.pagination, o.pagination, {
              total: _.get(Paging, 'TotalRecords.value', _.get(Paging, 'TotalRecords')) ?? 0,
              current: _.get(Paging, 'PageIndex', 1),
            });

            const groupItems = normalizeGroupItems(GroupItems);

            if (isInit) {
              // 保留初始化时的 groupItems
              m.initGroupItems = groupItems;
            }

            m.dataSource = Array.isArray(Result)
              ? Result.map((r) => ({
                  ...r,
                  __list_key__: ['id', 'ID', 'Id', 'key', 'Key', 'KEY', 'KeyNo'].map((k) => _.get(r, k, '')).join(','),
                }))
              : [];
            m.groupItems = groupItems;
            m.optionsMap = genGroupItemsOptions(groupItems, m.excludeDefaultOptionKeys);
            // 初始化可能是批量的，所以此时不做滚动
            if (m.autoScroll && !isInit) {
              this.$nextTick(() => {
                const refs = this.$refs[o.key] as Vue | Vue[];
                const ref = Array.isArray(refs) ? refs[0] : refs;

                if (ref && ref.$el) {
                  let top = 42 + 38; // header + tab
                  const node = ref.$el as HTMLElement;

                  // 详情页先这么判断
                  if (this.$route.path.split('/').length > 2) {
                    top += 40;
                  }
                  // 维度下的，往上层找 q-firm-section
                  if (this.info) {
                    let parent: HTMLElement | null = node;

                    for (let i = 0; i < 3; i++) {
                      if (!parent) {
                        break;
                      }

                      if (parent.classList.contains('q-section')) {
                        scrollToElement(parent, top);
                        return;
                      }
                      parent = parent.parentElement;
                    }
                  }

                  scrollToElement(node, top);
                }
              });
            }

            return data;
          })
          .catch((e) => {
            if (import.meta.env.NODE_ENV === 'development') {
              throw e;
            }
            m.dataSource = [];
            m.groupItems = {};
            m.optionsMap = {};
            m.loading = false;
          });
      },
      /**
       * @private
       */
      mListGetPagination(key: string) {
        const { pagination, maxTotal, pageSizeOptions } = this.mListModules[key];
        const total = pagination.total || 0;

        return {
          ...pagination,
          total: total > maxTotal ? maxTotal : total,
          pageSizeOptions,
          showQuickJumper: true,
          showSizeChanger: true,
          // size: 'small',
          onChange: (index: number) => this.mListHandlePaginationChange(key, index),
          onShowSizeChange: (index: number, pageSize: number) => this.mListHandlePageSizeChange(key, index, pageSize),
        };
      },
      mListGetTableData(key?: string) {
        if (!key) {
          key = this.mListActiveKey;
        }
        const { dataSource, loading, rowKey, fePagination, pagination } = this.mListModules[key];
        const paginationConfig = this.mListGetPagination(key);
        const { current, pageSize } = pagination;

        return {
          ref: key,
          props: {
            rowKey: this.info
              ? (record: any, index: number) => {
                  return `${this.info.key}_${current * pageSize + index}_${record[rowKey]}`;
                }
              : rowKey,
            dataSource,
            // 前端根据分页截断数据
            truncated: !!fePagination,
            loading,
            pagination: paginationConfig,
          },
        };
      },
      mListGetListData(key?: string) {
        if (!key) {
          key = this.mListActiveKey;
        }
        const { rowKey, loading, dataSource } = this.mListModules[key];

        return {
          ref: key,
          props: {
            renderItem: this.mListRenderItem,
          },
          attrs: {
            rowKey,
            loading,
            dataSource,
            pagination: this.mListGetPagination(key),
          },
        };
      },
      mListReset() {
        _.forEach(this.mListModules, (v, k) => {
          this.mListResetModule(k);
        });
      },
      mListResetModule(
        key?: string,
        preservedFields = ['initGroupItems', 'optionsMap', 'groupItems', 'pagination.pageSize', 'dataSource']
      ) {
        if (!key) {
          key = this.mListActiveKey;
        }
        const prev = this.mListModules[key];
        const next = defaultState().mListModules[key];

        preservedFields.forEach((path) => {
          _.set(next, path, _.get(prev, path));
        });

        this.mListModules[key] = next;
      },
      mListRefreshPagination(key: string, pagination: Partial<Pagination>) {
        const m = this.mListModules[key];

        // NOTE: 透传 `pagination`, 在请求加载完再更新页码
        if (!m.fePagination) {
          this.mListFetchList(key, false, pagination);
        } else {
          Object.assign(m.pagination, pagination);
        }
      },
      mListRefreshModule(key?: string) {
        if (!key) {
          key = this.mListActiveKey;
        }
        const preservedFields = ['initGroupItems', 'optionsMap', 'groupItems', 'pagination.pageSize', 'pagination.total', 'dataSource'];
        const prev = this.mListModules[key];
        const next = defaultState().mListModules[key];

        preservedFields.forEach((path) => {
          _.set(next, path, _.get(prev, path));
        });

        this.mListModules[key] = next;

        this.mListInit(key);
      },
      /**
       * @private
       */
      mListDetectInit() {
        let allInit = true;

        _.forEach(this.mListModules, ({ key, init }) => {
          if (init) {
            return;
          }

          if (isInViewPort(this.$refs[key] as Vue)) {
            this.mListInit(key);
          } else {
            allInit = false;
          }
        });
        if (allInit) {
          this.mListGetScrollContainer().removeEventListener('scroll', this.mListDetectInit);
        }
      },
      /**
       * @private
       */
      mListDebounceDetectInit() {
        // ignore
      },
      mListRender(key: string) {
        const m = this.mListGetState(key);

        if (m.render) {
          return m.render(this.$createElement, {
            vm: this,
            tableData: this.mListGetTableData(key),
            meta: m.meta ? m.meta({ vm: this }) : {},
          });
        }

        return null;
      },
      mListGetScrollContainer() {
        return window;
        // if (options?.scrollContainer) {
        //   return options.scrollContainer(this);
        // }

        // let parent: HTMLElement | null = this.$el.parentElement;

        // while (parent) {
        //   if (parent.classList.contains('q-scroll')) {
        //     return parent;
        //   }

        //   parent = parent.parentElement;
        // }

        // if (!parent) {
        //   return window;
        // }

        // return parent;
      },
      mListIsEmpty(key?: string) {
        if (!key) {
          key = this.mListActiveKey;
        }

        const m = this.mListGetState(key);

        if (m && m.init && !m.loading && m.pagination.total <= 0) {
          return true;
        }

        return false;
      },
    },
    mounted() {
      if (lazy) {
        this.mListDetectInit();
        this.mListDebounceDetectInit = _.throttle(this.mListDetectInit, 100);
        this.mListGetScrollContainer().addEventListener('scroll', this.mListDebounceDetectInit);
      } else if (autoInit) {
        const initPromise = new Promise<void>((resolve) => {
          let isResolved = false;
          const doResolve = () => {
            if (!isResolved) {
              isResolved = true;
              resolve();
            }
          };

          if (this.defaultModule) {
            this.mListInit(this.defaultModule).finally(doResolve);
          } else {
            Promise.all(this.mListInitAll() as Promise<any>[]).finally(doResolve);
          }

          setTimeout(doResolve, 5000);
        });

        this.$emit('initStart', initPromise);
      }
    },
    beforeDestroy() {
      if (lazy) {
        this.mListGetScrollContainer().removeEventListener('scroll', this.mListDebounceDetectInit);
      }
    },
  });
};
