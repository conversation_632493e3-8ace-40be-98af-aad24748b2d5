import { shallowMount } from '@vue/test-utils';
import { defineComponent } from 'vue';

import { createMixin } from '..';

describe('ListMixin', () => {
  let listMixin;
  let TestComponent;

  beforeEach(() => {
    listMixin = createMixin();
    TestComponent = defineComponent({
      name: 'TestComponent',
      mixins: [listMixin],
      template: '<div />',
    });
  });

  test('data', async () => {
    const wrapper = shallowMount(TestComponent, {
      propsData: {},
    });
    expect(wrapper).toMatchSnapshot();
  });
});
