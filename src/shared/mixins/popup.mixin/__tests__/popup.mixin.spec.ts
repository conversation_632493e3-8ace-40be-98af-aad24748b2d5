import { shallowMount } from '@vue/test-utils';
import { defineComponent } from 'vue';

import PopupMixin from '..';

describe('PopupMixin', () => {
  let TestComponent;

  beforeEach(() => {
    TestComponent = defineComponent({
      name: 'TestComponent',
      mixins: [PopupMixin],
      template: '<div />',
    });
  });
  test('render', () => {
    const wrapper = shallowMount(TestComponent, {
      propsData: {},
    });
    expect(wrapper).toMatchInlineSnapshot(`<div></div>`);
  });
});
