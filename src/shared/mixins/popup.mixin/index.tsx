import Vue, { PropType } from 'vue';

type CbFunc = () => void | Promise<void>;
const promiseCallback = (cb: CbFunc): Promise<void> => {
  return Promise.resolve(cb());
};

const PopupMixin = Vue.extend({
  inheritAttrs: false,
  model: {
    prop: 'visible',
    event: 'visibleChange',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    onOk: {
      type: Function as PropType<CbFunc>,
    },
    onCancel: {
      type: Function as PropType<CbFunc>,
    },
  },
  methods: {
    mPopupClose() {
      this.$emit('visibleChange', false);
    },
    mPopupGetModalData() {
      return {
        props: {
          visible: this.visible,
          ...this.$attrs,
        },
        on: {
          ok: () => {
            if (this.onOk) {
              promiseCallback(this.onOk)
                .then(this.mPopupClose)
                .catch(() => {
                  // ignore
                });
            } else {
              this.mPopupClose();
            }

            this.$emit('ok');
          },
          cancel: () => {
            if (this.onCancel) {
              promiseCallback(this.onCancel)
                .then(this.mPopupClose)
                .catch(() => {
                  // ignore
                });
            } else {
              this.mPopupClose();
            }

            this.$emit('cancel');
          },
        },
      };
    },
  },
});

export default PopupMixin;
