import Vue from 'vue';
import * as _ from 'lodash';
import moment from 'moment';
import Big from 'big.js';

import { Company, Person, Achievement, Tender } from '@/interfaces/construction.entity';

const PLACEHOLDER = '-';

type withExtraInfo = {
  label?: string;
  __QCC__?: boolean;
  onClick?(e): void;
};

// 转换 100.000 类似的 string 为 number 格式
const numberStringRegex = /^\d+\.\d+$/;

const createEvents = (info: withExtraInfo, native = true) => {
  if (info.onClick) {
    if (native) {
      return {
        nativeOn: {
          click: info.onClick,
        },
      };
    }

    return {
      on: { click: info.onClick },
    };
  }

  return undefined;
};

export const addExtra = (value, extra?: withExtraInfo) => {
  if (!extra) {
    return value;
  }

  if (Array.isArray(value)) {
    return value.map((v) => ({ ...v, ...extra }));
  }

  if (_.isPlainObject(value)) {
    return { ...value, ...extra };
  }

  return value;
};

const ConstructionMixin = Vue.extend({
  methods: {
    mRAddExtra: addExtra,
    mRExtractHighlights<T>(source: T, fields: string[]): T {
      return _.reduce(
        fields,
        (m, f) => {
          return {
            [f]: _.get(source, ['highlight', f, '0'], _.get(source, f)),
            ...m,
          };
        },
        {} as T
      );
    },
    mRFB(value?: string | number, template?: string, tableRenderIndex?: number) {
      // tableRenderIndex 用于标识是否用于表格 customRender，是的话则不会触发 template
      let fbValue = value === 0 || !!value ? value : PLACEHOLDER;

      if (_.isString(value) && numberStringRegex.test(value)) {
        const s = _.toNumber(value);
        if (!_.isNaN(s)) {
          fbValue = s.toString();
        }
      }

      if (fbValue !== PLACEHOLDER && template && _.isString(template) && !_.isNumber(tableRenderIndex)) {
        const compile = _.template(template);

        return compile({ value: fbValue });
      }

      return fbValue;
    },
    // 返回 table column render 格式
    mRFBCompany(field: string, extra?: withExtraInfo) {
      return (value: Company | Company[], record: Record<string, string>) => this.mRRenderCompany(addExtra(value, extra), record[field]);
    },
    // 返回 table column render 格式
    mRFBQccCompany(field: string, extra?: withExtraInfo) {
      return this.mRFBCompany(field, extra);
    },
    // 返回 table column render 格式
    mRFBPerson(field: string, extra?: withExtraInfo) {
      return (value: Person | Person[], record: Record<string, string>) => this.mRRenderPerson(addExtra(value, extra), record[field]);
    },
    mRRenderCompany(company: (Company & withExtraInfo) | Company[], fallback?: string) {
      const placeholder = _.isString(fallback) && !!fallback ? fallback : PLACEHOLDER;

      if (Array.isArray(company)) {
        const list = _.compact(company.map((c) => this.mRRenderCompany(c, '')));

        if (!list.length) {
          return placeholder;
        }
        return list.reduce((r, n, i) => {
          if (i !== 0) {
            r.push(', ');
          }
          r.push(n);
          return r;
        }, []);
      }

      if (!company) {
        return placeholder;
      }

      if (!company.KeyNo) {
        return <span domPropsInnerHTML={company.Name} />;
      }

      return (
        <q-link
          // key={company.KeyNo}
          to={`/firm/${company.KeyNo}`}
          {...createEvents(company)}
          domPropsInnerHTML={company.label || company.Name}
        ></q-link>
      );
    },
    mRRenderPerson(person: (Person & withExtraInfo) | Person[], fallback?: string) {
      const placeholder = _.isString(fallback) && fallback ? fallback : PLACEHOLDER;

      if (Array.isArray(person)) {
        const list = _.compact(person.map((p) => this.mRRenderPerson(p, '')));

        if (!list.length) {
          return placeholder;
        }
        return list.reduce((r, n, i) => {
          if (i !== 0) {
            r.push(', ');
          }
          r.push(n);
          return r;
        }, []);
      }
      if (!person || !person.Name) {
        return placeholder;
      }

      if (!person.PerId) {
        return <span domPropsInnerHTML={person.Name} />;
      }

      return (
        <q-link
          key={person.PerId}
          to={person.__QCC__ ? `/pl/${person.PerId}` : `/construction/person/${person.PerId}`}
          {...createEvents(person)}
          domPropsInnerHTML={person.label ? person.label : person.Name}
        ></q-link>
      );
    },
    mRRenderAchievement(
      achievement: (Achievement & withExtraInfo & { highlight?: { projectname: string } }) | Achievement[],
      fallback?: string
    ) {
      const placeholder = _.isString(fallback) ? fallback : PLACEHOLDER;

      if (Array.isArray(achievement)) {
        const list = _.compact(achievement.map((p) => this.mRRenderAchievement(p, '')));

        if (!list.length) {
          return placeholder;
        }
        return list.reduce((r, n, i) => {
          if (i !== 0) {
            r.push(', ');
          }
          r.push(n);
          return r;
        }, []);
      }
      if (!achievement) {
        return placeholder;
      }

      if (!achievement.projectid) {
        return achievement.projectname;
      }

      return (
        <q-link
          key={achievement.projectid}
          to={`/construction/achievement/${achievement.projectid}`}
          {...createEvents(achievement)}
          domPropsInnerHTML={achievement.label || _.get(achievement.highlight, 'projectname') || achievement.projectname}
        ></q-link>
      );
    },
    mRRenderTender(tender: Tender & withExtraInfo) {
      if (!tender.id) {
        return tender.name || PLACEHOLDER;
      }
      return (
        <q-link
          key={tender.id}
          to={`/construction/tender/${tender.id}`}
          {...createEvents(tender)}
          domPropsInnerHTML={tender.label || tender.name}
        />
      );
    },
    // copy mRRenderCompany
    mRRenderOwner(owner: (Company & withExtraInfo) | Company[], fallback?: string) {
      const placeholder = _.isString(fallback) && !!fallback ? fallback : PLACEHOLDER;
      if (Array.isArray(owner)) {
        const list = _.compact(owner.map((c) => this.mRRenderOwner(c, '')));

        if (!list.length) {
          return placeholder;
        }
        return list.reduce((r, n, i) => {
          if (i !== 0) {
            r.push(', ');
          }
          r.push(n);
          return r;
        }, []);
      }

      if (!owner) {
        return placeholder;
      }

      if (!owner.KeyNo) {
        return <span domPropsInnerHTML={owner.Name} />;
      }
      return (
        <q-link
          // key={owner.KeyNo}
          to={`/firm/${owner.KeyNo}`}
          {...createEvents(owner)}
          domPropsInnerHTML={owner.label || owner.Name}
        ></q-link>
      );
    },
    mRRenderUnixDate(date: number) {
      return date ? moment.unix(date).format('YYYY-MM-DD') : PLACEHOLDER;
    },
    mRRenderStringDate(date: string) {
      return date ? moment(date, 'YYYYMMDD').format('YYYY-MM-DD') : PLACEHOLDER;
    },
    mRRenderTimeScopeDate(date: string) {
      return date ? moment(date).format('YYYY-MM-DD') : PLACEHOLDER;
    },
    mRRenderBoolean(value?: number | boolean) {
      if (_.isNumber(value) || _.isBoolean(value)) {
        return value ? '是' : '否';
      }

      return PLACEHOLDER;
    },
    // 项目经理
    mRRenderProjectLeader({ projectleader, perid, onClick }: { projectleader?: string; perid?: string; onClick?(e): void }) {
      return projectleader
        ? this.mRRenderPerson({
            Name: projectleader,
            PerId: perid || '',
            onClick,
          })
        : PLACEHOLDER;
    },

    /**
     * 勘察、设计人员角色为项目负责人
     * 监理单位人员角色为总监理工程师
     * 其他任意企业角色人员角色为项目经理
     */
    mRRenderPersonRole(role: string): string {
      switch (role) {
        case '设计企业':
        case '勘察企业':
          return '项目负责人';
        case '监理企业':
          return '总监理工程师';
        default:
          return '项目经理';
      }
    },
    // 电力资质许可证名称
    mRRenderElecLabel(qualificationcode: string): string {
      if (qualificationcode.startsWith('DL001')) return '承装（修、试）电力设施许可证信息';
      return '电力业务许可证';
    },
    mRRenderAmount(amount: string | number) {
      if (_.isNil(amount)) {
        return PLACEHOLDER;
      }

      const n = _.toNumber(amount);

      if (_.isNaN(n) || n <= 0) {
        return PLACEHOLDER;
      }

      return new Big(n).div(10000).toString();
    },
  },
});

export default ConstructionMixin;
