import { shallowMount, createLocalVue } from '@vue/test-utils';

import ConstructionMixin, { addExtra } from '@/shared/mixins/construction.mixin/index';
import { flushPromises } from '@/test-utils/flush-promises';

describe('ConstructionMixin', () => {
  let wrapper;

  beforeEach(async () => {
    const localVue = createLocalVue();
    // localVue.mixin(ConstructionMixin);
    wrapper = shallowMount(
      {
        template: '<div />',
      },
      {
        mixins: [ConstructionMixin],
        localVue,
      }
    );
    await flushPromises();
  });

  describe('addExtra', () => {
    it('should return the original value if no extra is provided', () => {
      expect(addExtra('value')).toBe('value');
    });

    it('should add extra info to an object', () => {
      expect(addExtra({ key: 'value' }, { label: 'extra' })).toEqual({ key: 'value', label: 'extra' });
    });

    it('should add extra info to an array of objects', () => {
      expect(addExtra([{ key: 'value1' }, { key: 'value2' }], { label: 'extra' })).toEqual([
        { key: 'value1', label: 'extra' },
        { key: 'value2', label: 'extra' },
      ]);
    });
  });

  describe('mRExtractHighlights', () => {
    it('should extract highlights from an object', () => {
      const source = { highlight: { field1: ['value1'] }, field1: 'fallback' };
      expect(wrapper.vm.mRExtractHighlights(source, ['field1'])).toEqual({ field1: 'value1' });
    });

    it('should use fallback value when no highlight is present', () => {
      const source = { field1: 'fallback' };
      expect(wrapper.vm.mRExtractHighlights(source, ['field1'])).toEqual({ field1: 'fallback' });
    });

    it('should handle multiple fields', () => {
      const source = {
        highlight: { field1: ['value1'], field2: ['value2'] },
        field1: 'fallback1',
        field2: 'fallback2',
      };
      expect(wrapper.vm.mRExtractHighlights(source, ['field1', 'field2'])).toEqual({
        field1: 'value1',
        field2: 'value2',
      });
    });
  });

  describe('mRFB', () => {
    it('should return the value if it is truthy', () => {
      expect(wrapper.vm.mRFB('value')).toBe('value');
    });

    it('should return the placeholder if the value is falsy', () => {
      expect(wrapper.vm.mRFB('')).toBe('-');
    });

    it('should convert number string to number', () => {
      expect(wrapper.vm.mRFB('100.000')).toBe('100');
    });

    it('should use template if provided and value is not placeholder', () => {
      expect(wrapper.vm.mRFB('value', '<%= value %>')).toBe('value');
    });

    it('should not use template if tableRenderIndex is provided', () => {
      expect(wrapper.vm.mRFB('value', '<%= value %>', 1)).toBe('value');
    });
  });

  describe('mRRenderCompany', () => {
    it('should return placeholder if company is falsy', () => {
      expect(wrapper.vm.mRRenderCompany(null)).toBe('-');
    });

    it.todo('should return span if KeyNo is missing', () => {
      const company = { Name: 'Company Name' };
      const renderReturn = wrapper.vm.mRRenderCompany(company);
      expect(renderReturn.tag).toBe('span');
    });

    it.todo('should return q-link with correct props', () => {
      const company = { KeyNo: '123', Name: 'Company Name', label: 'Custom Label' };
      const link = wrapper.vm.mRRenderCompany(company);
      expect(link.tag).toBe('q-link');
      expect(link.data.attrs.to).toBe('/firm/123');
      expect(link.data.domProps.innerHTML).toBe('Custom Label');
    });
  });

  describe('mRRenderPerson', () => {
    it('should return placeholder if person is falsy', () => {
      expect(wrapper.vm.mRRenderPerson(null)).toBe('-');
    });

    it.todo('should return span if PerId is missing', () => {
      const person = { Name: 'Person Name' };
      expect(wrapper.vm.mRRenderPerson(person).tag).toBe('span');
    });

    it.todo('should return q-link with correct props', () => {
      const person = { PerId: '123', Name: 'Person Name', label: 'Custom Label' };
      const link = wrapper.vm.mRRenderPerson(person);
      expect(link.tag).toBe('q-link');
      expect(link.data.attrs.to).toBe('/construction/person/123');
      expect(link.data.domProps.innerHTML).toBe('Custom Label');
    });
  });

  describe('mRRenderAchievement', () => {
    it('should return placeholder if achievement is falsy', () => {
      expect(wrapper.vm.mRRenderAchievement(null)).toBe('-');
    });

    it('should return projectname if projectid is missing', () => {
      const achievement = { projectname: 'Project Name' };
      expect(wrapper.vm.mRRenderAchievement(achievement)).toBe('Project Name');
    });

    it.todo('should return q-link with correct props', () => {
      const achievement = { projectid: '123', projectname: 'Project Name', label: 'Custom Label' };
      const link = wrapper.vm.mRRenderAchievement(achievement);
      expect(link.tag).toBe('q-link');
      expect(link.data.attrs.to).toBe('/construction/achievement/123');
      expect(link.data.domProps.innerHTML).toBe('Custom Label');
    });
  });

  describe('mRRenderTender', () => {
    it('should return placeholder if id is missing and name is falsy', () => {
      expect(wrapper.vm.mRRenderTender({ name: '' })).toBe('-');
    });

    it('should return name if id is missing', () => {
      expect(wrapper.vm.mRRenderTender({ name: 'Tender Name' })).toBe('Tender Name');
    });

    it.todo('should return q-link with correct props', () => {
      const tender = { id: '123', name: 'Tender Name', label: 'Custom Label' };
      const link = wrapper.vm.mRRenderTender(tender);
      expect(link.tag).toBe('q-link');
      expect(link.data.attrs.to).toBe('/construction/tender/123');
      expect(link.data.domProps.innerHTML).toBe('Custom Label');
    });
  });

  describe('mRRenderOwner', () => {
    it('should return placeholder if owner is falsy', () => {
      expect(wrapper.vm.mRRenderOwner(null)).toBe('-');
    });

    it.todo('should return span if KeyNo is missing', () => {
      const owner = { Name: 'Owner Name' };
      expect(wrapper.vm.mRRenderOwner(owner).tag).toBe('span');
    });

    it.todo('should return q-link with correct props', () => {
      const owner = { KeyNo: '123', Name: 'Owner Name', label: 'Custom Label' };
      const link = wrapper.vm.mRRenderOwner(owner);
      expect(link.tag).toBe('q-link');
      expect(link.data.attrs.to).toBe('/firm/123');
      expect(link.data.domProps.innerHTML).toBe('Custom Label');
    });
  });

  describe('mRRenderUnixDate', () => {
    it('should return formatted date if value is truthy', () => {
      expect(wrapper.vm.mRRenderUnixDate(1609459200)).toBe('2021-01-01');
    });

    it('should return placeholder if value is falsy', () => {
      expect(wrapper.vm.mRRenderUnixDate(0)).toBe('-'); // Unix 0 is 1970-01-01, but in test we consider falsy as 0 for simplicity
      expect(wrapper.vm.mRRenderUnixDate(null)).toBe('-');
    });
  });

  describe('mRRenderStringDate', () => {
    it('should return formatted date if value is truthy', () => {
      expect(wrapper.vm.mRRenderStringDate('20210101')).toBe('2021-01-01');
    });

    it('should return placeholder if value is falsy', () => {
      expect(wrapper.vm.mRRenderStringDate('')).toBe('-');
      expect(wrapper.vm.mRRenderStringDate(null)).toBe('-');
    });
  });

  describe('mRRenderTimeScopeDate', () => {
    it('should return formatted date if value is truthy', () => {
      expect(wrapper.vm.mRRenderTimeScopeDate('2021-01-01')).toBe('2021-01-01');
    });

    it('should return placeholder if value is falsy', () => {
      expect(wrapper.vm.mRRenderTimeScopeDate('')).toBe('-');
      expect(wrapper.vm.mRRenderTimeScopeDate(null)).toBe('-');
    });
  });

  describe('mRRenderBoolean', () => {
    it('should return "是" if value is truthy', () => {
      expect(wrapper.vm.mRRenderBoolean(true)).toBe('是');
      expect(wrapper.vm.mRRenderBoolean(1)).toBe('是');
    });

    it('should return "否" if value is falsy', () => {
      expect(wrapper.vm.mRRenderBoolean(false)).toBe('否');
      expect(wrapper.vm.mRRenderBoolean(0)).toBe('否');
    });

    it('should return placeholder if value is nil', () => {
      expect(wrapper.vm.mRRenderBoolean(null)).toBe('-');
      expect(wrapper.vm.mRRenderBoolean(undefined)).toBe('-');
    });
  });

  describe('mRRenderProjectLeader', () => {
    it('should return placeholder if projectleader is missing', () => {
      expect(wrapper.vm.mRRenderProjectLeader({})).toBe('-');
    });

    it.todo('should return q-link with correct props', () => {
      const projectLeader = { projectleader: 'Leader Name', perid: '123' };
      const link = wrapper.vm.mRRenderProjectLeader(projectLeader);
      expect(link.data.attrs.to).toBe('/construction/person/123');
      expect(link.data.domProps.innerHTML).toBe('Leader Name');
    });
  });

  describe('mRRenderPersonRole', () => {
    it('should return "项目负责人" for "设计企业" and "勘察企业"', () => {
      expect(wrapper.vm.mRRenderPersonRole('设计企业')).toBe('项目负责人');
      expect(wrapper.vm.mRRenderPersonRole('勘察企业')).toBe('项目负责人');
    });

    it('should return "总监理工程师" for "监理企业"', () => {
      expect(wrapper.vm.mRRenderPersonRole('监理企业')).toBe('总监理工程师');
    });

    it('should return "项目经理" for any other role', () => {
      expect(wrapper.vm.mRRenderPersonRole('Other Role')).toBe('项目经理');
    });
  });

  describe('mRRenderElecLabel', () => {
    it('should return "承装（修、试）电力设施许可证信息" if qualificationcode starts with "DL001"', () => {
      expect(wrapper.vm.mRRenderElecLabel('DL001001')).toBe('承装（修、试）电力设施许可证信息');
    });

    it('should return "电力业务许可证" if qualificationcode does not start with "DL001"', () => {
      expect(wrapper.vm.mRRenderElecLabel('DL002001')).toBe('电力业务许可证');
    });
  });

  describe('mRRenderAmount', () => {
    it('should return formatted amount if value is truthy', () => {
      expect(wrapper.vm.mRRenderAmount('123456')).toBe('12.3456');
      expect(wrapper.vm.mRRenderAmount(123456)).toBe('12.3456');
    });

    it('should return placeholder if value is falsy', () => {
      expect(wrapper.vm.mRRenderAmount('')).toBe('-');
      expect(wrapper.vm.mRRenderAmount(0)).toBe('-');
    });

    it('should return placeholder if value is nil', () => {
      expect(wrapper.vm.mRRenderAmount(null)).toBe('-');
      expect(wrapper.vm.mRRenderAmount(undefined)).toBe('-');
    });

    it('should return placeholder if value is not a number', () => {
      expect(wrapper.vm.mRRenderAmount('abc')).toBe('-');
    });
  });
});
