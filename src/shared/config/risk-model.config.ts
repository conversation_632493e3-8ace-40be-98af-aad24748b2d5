// 模型的分发状态
export enum DataModelDistributeEnum {
  Disable = 0, // 禁用
  Enable = 1, // 启用
  Trial = 2, // 试用
  Deprecated = 3, // 用户自己废弃
}
// 模型自身的状态
export enum DataStatusEnum {
  /**  无效 */
  Disable = 0,
  /**  启用 */
  Enable = 1,
  /**  开发中 像是一个模型，已经创建，正在开发中(只能在平台内部使用),与发布的模型是两个概念，发布模型后会变成Enable状态, */
  Developing = 2,
  /**  待废弃 */
  Deprecating = 3,
  /**  已废弃 */
  Deprecated = 4,
}

export const RiskModelStatusEnumMap = {
  [DataStatusEnum.Disable]: '已禁用',
  [DataStatusEnum.Enable]: '已启用',
  [DataStatusEnum.Developing]: '开发中',
  [DataStatusEnum.Deprecating]: '待废弃',
  [DataStatusEnum.Deprecated]: '已废弃',
};

export const RiskModelStatusEnumThemeMap = {
  [DataStatusEnum.Disable]: 'disable',
  [DataStatusEnum.Enable]: 'enable',
  [DataStatusEnum.Developing]: 'developing',
  [DataStatusEnum.Deprecating]: 'deprecating',
  [DataStatusEnum.Deprecated]: 'deprecated',
};

export const DistributeStatusEnumMap = {
  [DataModelDistributeEnum.Disable]: '禁用',
  [DataModelDistributeEnum.Enable]: '启用',
  [DataModelDistributeEnum.Trial]: '试用',
  [DataModelDistributeEnum.Deprecated]: '废弃',
};
