import { message } from 'ant-design-vue';

import env from '@/shared/config/env';
import { appRouter as router } from '@/router/app.router';

import { getNoPermissionLabelByCode, hasPermission } from './use-permission';

/**
 * FIXME: 预发环境登录跳转兼容(通过 hostname 判断)
 * @param {string} officialLink
 */
export const getQccHome = (officialLink) => {
  const releaseHostName = 'release.kezhaozhao.com';
  const releaseLink = 'https://webtest.qcc.com/';
  const isReleaseEnv = window.location.hostname === releaseHostName;
  return isReleaseEnv ? releaseLink : officialLink;
};

export const loginURI = (path = null) => {
  // const target = isNil(path) ? window.location.pathname : path;
  // const transit = `${window.location.origin}/rover/transit`;
  // const targetPath = target === '/' ? transit : `${transit}?target=${target}`;
  // return `${getQccHome(env.QCC_HOME)}weblogin?back=${encodeURIComponent(targetPath)}`;
  return `${env.INSIGHTS_HOME}/portal/login`;
};

export const login = (path) => {
  window.location.href = loginURI(path);
};

// 跳转产品主页
export const toProductHome = () => {
  window.location.href = '/app/supplier/investigation';
};

// 跳转落地页
export const redirectToHome = (whiteList: string[] = []) => {
  // if (process.env.NODE_ENV === 'development') {
  //   console.warn('跳转登陆');
  //   return;
  // }
  const target = window.location.pathname;
  if (target !== '/' && !whiteList.includes(target)) {
    window.location.href = '/';
  }
};

export const switchOrgURI = () => {
  return `/app/switch`;
};

export const logoutURI = () => {
  return '/qcc/user/buser/logout';
};

export const handle403 = async (response) => {
  const { code, error: msg } = response;
  // 200011-套餐过期 / 200008-当前组织无Rover套餐
  if ([200008, 200011, 200003].includes(code)) {
    if (window.location.pathname === '/') {
      return;
    }
    window.location.href = '/switch';
  }

  if (window.location.pathname === '/') {
    message.warn(msg);
  } else {
    redirectToHome();
  }
};

export const redirect = (statusCode, dataCode, res) => {
  switch (statusCode) {
    case 403:
      handle403(res);
      break;
    case 401:
    default: // NOTE: 避免无需登录的页面被跳转回首页
      redirectToHome();
      break;
  }
};

export const routerTryPush = (route) => {
  const resolve = router.resolve(route);
  if (!resolve) {
    return false;
  }
  const { permission = [] } = resolve?.route.meta || {};
  const code = Array.isArray(permission) ? permission[0] : permission;
  if (!hasPermission(resolve?.route.meta?.permission)) {
    message.warning({
      content: getNoPermissionLabelByCode(code),
      key: code,
    });
    return false;
  }
  return router.push(route);
};
