import { defineComponent } from 'vue';
import { shallowMount } from '@vue/test-utils';

import { useWebTitle } from '..';

function createTestingComponent(setupFn, props = {}) {
  const component = defineComponent({
    setup: setupFn,
    render: () => null,
  });
  return shallowMount(component, props);
}

describe('useWebTitle', () => {
  let originalTitle: string;

  beforeEach(() => {
    originalTitle = document.title;
  });

  afterEach(() => {
    document.title = originalTitle;
  });

  test.each([
    { title: 'Test Title', expected: 'Test Title-风险洞察' },
    { title: undefined, expected: '' },
  ])('should set the web title with the given text %s', ({ title, expected }) => {
    createTestingComponent(() => {
      const { setWebTitle } = useWebTitle(title);
      return {
        setWebTitle,
      };
    });
    expect(document.title).toBe(expected);
  });
});
