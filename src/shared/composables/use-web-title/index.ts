import { onMounted, onUnmounted, ref } from 'vue';

import { getHTMLText } from '@/utils';

export const useWebTitle = (text?: string) => {
  const hasChange = ref(false);

  const setWebTitle = (title = text) => {
    if (title && !title.includes('undefined')) {
      document.title = `${getHTMLText(title)}-风险洞察`;
      hasChange.value = true;
    }
  };

  const resetWebTitle = () => {
    document.title = '企查查-风险洞察';
  };

  onMounted(() => {
    setWebTitle();
  });

  onUnmounted(() => {
    if (hasChange.value) {
      resetWebTitle();
    }
  });

  return {
    setWebTitle,
    resetWebTitle,
  };
};
