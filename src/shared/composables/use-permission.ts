import { intersection, isArray } from 'lodash';

import { useUserStore } from '@/shared/composables/use-user-store';

const PERMISSIONS = [
  {
    groupId: 25,
    order: 0,
    category: '工作台',
    permissions: [
      {
        name: '工作台-查看',
        permission: 2137,
      },
    ],
  },
  {
    groupId: 5,
    order: 1,
    category: '准入排查',
    permissions: [
      {
        name: '准入排查-查看',
        permission: 2001,
      },
      {
        name: '准入排查-生成报告',
        permission: 2002,
      },
      {
        name: '准入排查-审核跟进',
        permission: 2003,
      },
      {
        name: '准入排查-人员核实',
        permission: 2004,
      },
    ],
  },
  {
    groupId: 6,
    order: 2,
    category: '批量排查',
    permissions: [
      {
        name: '批量排查-查看',
        permission: 2011,
      },
      {
        name: '批量排查-从第三方列表中选择',
        permission: 2012,
      },
      {
        name: '批量排查-导出',
        permission: 2016,
      },
    ],
  },
  {
    groupId: 7,
    order: 3,
    category: '排查记录',
    permissions: [
      {
        name: '排查记录-查看',
        permission: 2021,
      },
      {
        name: '排查记录-导出列表',
        permission: 2022,
      },
    ],
  },
  {
    groupId: 21,
    order: 4,
    category: '招标排查',
    permissions: [
      {
        name: '招标排查-查看',
        permission: 2110,
      },
      {
        name: '招标排查-批量添加',
        permission: 2111,
      },
      {
        name: '招标排查-从第三方列表选择',
        permission: 2112,
      },
      {
        name: '招标排查-招标批量排查',
        permission: 2114,
      },
      {
        name: '招标排查-导出报告',
        permission: 2115,
      },
    ],
  },
  {
    groupId: 23,
    order: 5,
    category: '招标排查-历史记录',
    permissions: [
      {
        name: '招标排查-历史记录-查看',
        permission: 2113,
      },
    ],
  },
  {
    groupId: 19,
    order: 6,
    category: '合作监控-监控动态',
    permissions: [
      {
        name: '监控动态-风险动态查看',
        permission: 2091,
      },
      {
        name: '合作监控-风险动态导出',
        permission: 2093,
      },
      {
        name: '监控动态-舆情动态查看',
        permission: 2092,
      },
      {
        name: '合作监控-舆情动态导出',
        permission: 2094,
      },
    ],
  },
  {
    groupId: 20,
    order: 7,
    category: '合作监控-监控列表',
    permissions: [
      {
        name: '排查列表-查看',
        permission: 2101,
      },
      {
        name: '排查列表-添加企业',
        permission: 2102,
      },
      {
        name: '排查列表-移动分组',
        permission: 2104,
      },
      {
        name: '排查列表-删除列表',
        permission: 2105,
      },
      {
        name: '排查列表-添加关联方',
        permission: 2106,
      },
      {
        name: '排查列表-恢复失效企业',
        permission: 2107,
      },
    ],
  },
  {
    groupId: 8,
    order: 8,
    category: '第三方列表',
    permissions: [
      {
        name: '第三方列表-查看',
        permission: 2031,
      },
      {
        name: '第三方列表-列表-编辑',
        permission: 2038,
      },
      {
        name: '第三方列表-新增合作伙伴',
        permission: 2032,
      },
      {
        name: '第三方列表-移动分组',
        permission: 2034,
      },
      {
        name: '第三方列表-批量修改标签',
        permission: 2036,
      },
      {
        name: '第三方列表-删除列表',
        permission: 2037,
      },
      {
        name: '第三方列表-移入内部黑名单',
        permission: 2039,
      },
    ],
  },
  {
    groupId: 27,
    order: 9,
    category: '风险年检',
    permissions: [
      {
        name: '风险年检-查看',
        permission: 2014,
      },
      {
        name: '风险年检-重新风险年检',
        permission: 2013,
      },
      {
        name: '风险年检-导出',
        permission: 2015,
      },
    ],
  },
  {
    groupId: 9,
    order: 10,
    category: '内部黑名单',
    permissions: [
      {
        name: '内部黑名单-查看',
        permission: 2041,
      },
      {
        name: '内部黑名单-列表-编辑',
        permission: 2045,
      },
      {
        name: '内部黑名单-新增黑名单',
        permission: 2042,
      },
      {
        name: '内部黑名单-移动分组',
        permission: 2046,
      },
      {
        name: '内部黑名单-批量修改标签',
        permission: 2047,
      },
      {
        name: '内部黑名单-删除列表',
        permission: 2043,
      },
      {
        name: '内部黑名单-导出列表',
        permission: 2044,
      },
    ],
  },
  {
    groupId: 10,
    order: 11,
    category: '外部黑名单',
    permissions: [
      {
        name: '外部黑名单-查看',
        permission: 2051,
      },
    ],
  },
  {
    groupId: 11,
    order: 12,
    category: '人员管理',
    permissions: [
      {
        name: '人员管理-查看',
        permission: 2061,
      },
      {
        name: '人员管理-列表-编辑',
        permission: 2067,
      },
      {
        name: '人员管理-新增人员',
        permission: 2062,
      },
      {
        name: '人员管理-移动分组',
        permission: 2064,
      },
      {
        name: '人员管理-删除人员',
        permission: 2065,
      },
      {
        name: '人员管理-导出列表',
        permission: 2066,
      },
    ],
  },
  {
    groupId: 12,
    order: 13,
    category: '分析看板',
    permissions: [
      {
        name: '分析看板-查看',
        permission: 2071,
      },
      {
        name: '分析看板-更新',
        permission: 2072,
      },
    ],
  },
  {
    groupId: 24,
    order: 14,
    category: '投标预警',
    permissions: [
      {
        name: '投标预警-查看',
        permission: 2135,
      },
      {
        name: '投标预警-导出列表',
        permission: 2136,
      },
    ],
  },
  {
    groupId: 13,
    order: 15,
    category: '设置中心',
    permissions: [
      {
        name: '准入排查设置-查看',
        permission: 2081,
      },
      {
        name: '准入排查设置-编辑',
        permission: 2082,
      },
      {
        name: '风险年检设置-查看',
        permission: 2083,
      },
      {
        name: '风险年检设置-编辑',
        permission: 2084,
      },
      {
        name: '招标排查设置-查看',
        permission: 2085,
      },
      {
        name: '招标排查设置-编辑',
        permission: 2086,
      },
      {
        name: '合作监控-风险动态设置-查看',
        permission: 2087,
      },
      {
        name: '合作监控-风险动态设置-编辑',
        permission: 2088,
      },
      {
        name: '合作监控-舆情动态设置-查看',
        permission: 2089,
      },
      {
        name: '合作监控-舆情动态设置-编辑',
        permission: 2090,
      },
      {
        name: '投标预警设置-查看',
        permission: 2132,
      },
      {
        name: '投标预警设置-新增',
        permission: 2131,
      },
      {
        name: '投标预警设置-编辑',
        permission: 2133,
      },
      {
        name: '投标预警设置-删除',
        permission: 2134,
      },
      {
        name: '第三方列表-分组管理',
        permission: 2033,
      },
      {
        name: '内部黑名单-分组管理',
        permission: 2048,
      },
      {
        name: '人员管理-分组管理',
        permission: 2063,
      },
      {
        name: '合作监控-分组管理',
        permission: 2103,
      },
      {
        name: '第三方列表-标签管理',
        permission: 2035,
      },
      {
        name: '内部黑名单-标签管理',
        permission: 2049,
      },
    ],
  },
  {
    groupId: 22,
    order: 16,
    category: '套餐权益',
    permissions: [
      {
        name: '套餐权益-查看',
        permission: 2121,
      },
    ],
  },
];

const PERMISSION_CODE_MAP = PERMISSIONS.flatMap((item) => item.permissions).reduce((map, item) => {
  map[item.permission] = item.name;
  return map;
}, {});

export const getCurrentUserPermission = (): Array<any> => {
  const { permissions } = useUserStore();
  return permissions.value || [];
};

export const getNoPermissionLabelByCode = (code: string) => {
  const permissionStr = PERMISSION_CODE_MAP[code]?.split('-') || [];
  return `${permissionStr[0] || ''}${permissionStr[0] ? '-' : ''}暂无${permissionStr[1] || ''}权限`;
};

// 是否有任意权限
export const hasPermission = (permissions: number[] | number) => {
  if (!permissions) return false;

  const currentUserPermission = getCurrentUserPermission();
  const permissionList = Array.isArray(permissions) ? permissions : [permissions];
  const hasIntersection = Boolean(intersection(permissionList, currentUserPermission).length);
  return hasIntersection;
};

// 是否有全部权限
export const hasEveryPermission = (permissions: number[] | number) => {
  if (!permissions) return false;
  if (!isArray(permissions)) {
    permissions = [permissions];
  }
  const currentUserPermission = getCurrentUserPermission();
  const allPassed = permissions.every((code) => currentUserPermission.includes(code));
  return allPassed;
};
