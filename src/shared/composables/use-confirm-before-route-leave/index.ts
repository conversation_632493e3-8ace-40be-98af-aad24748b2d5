import { Ref, ref } from 'vue';
import { onBeforeRouteLeave, useRouter } from 'vue-router/composables';

/**
 * 离开当前路由前，确认是否保存
 */
export function useConfirmBeforeRouteLeave(changed: Ref<boolean>) {
  const isLeaving = ref(false); // 标记是否触发了路由离开状态
  const confirmed = ref(false); // 确定要跳转
  const redirectURL = ref<string>(); // 跳转地址

  /** 路由操作 */
  const router = useRouter();
  const continueRedirect = () => {
    confirmed.value = true;
    // console.log('push', redirectURL.value);
    router.push({
      path: redirectURL.value,
    });
  };

  /** 监听路由跳转 */
  onBeforeRouteLeave((to, from, next) => {
    isLeaving.value = true; // 标记路由状态
    redirectURL.value = to.fullPath; // 保存跳转链接
    if (!changed.value || confirmed.value) {
      next();
    }
  });

  return [isLeaving, continueRedirect] as const;
}
