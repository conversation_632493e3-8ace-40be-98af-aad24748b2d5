import { cloneDeep } from 'lodash';

import { useUserStore } from '@/shared/composables/use-user-store';

const ZEISS_TRANSLATE_MAP = {
  客商: '第三方',
  谨慎合作: '中风险',
  黑名单排查: '第三方黑名单',
};

// 蔡司文本处理
export const useTranslateZeiss = () => {
  const { isZeiss } = useUserStore();

  const t = (text: string, fn = (val) => String(val).replaceAll('客商', '第三方')) => {
    if (isZeiss.value) {
      return fn(ZEISS_TRANSLATE_MAP[text] || text);
    }
    return text;
  };
  return { t };
};

// 处理筛选项的
export const getTranslateZeissFilterGroups = (arr: Record<string, any>[]) => {
  const { t } = useTranslateZeiss();
  arr = cloneDeep(arr);
  arr.forEach((item) => {
    if (item.children) {
      item.children = getTranslateZeissFilterGroups(item.children);
    }
    if (item.options) {
      item.options = item.options.map((option) => ({ ...option, label: t(option.label) }));
    }
    item.label = t(item.label);
  });
  return arr;
};

// 处理table columns的
export const getTranslateZeissTableColumn = (arr: Record<string, any>[]) => {
  const { t } = useTranslateZeiss();
  arr = cloneDeep(arr);
  arr.forEach((item) => {
    item.title = t(item.title);
  });
  return arr;
};
