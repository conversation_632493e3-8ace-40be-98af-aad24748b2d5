import { useUserStore } from '@/shared/composables/use-user-store';

import { getTranslateZeissFilterGroups, getTranslateZeissTableColumn, useTranslateZeiss } from '../use-zeiss-translate';

vi.mock('@/shared/composables/use-user-store');

describe('useTranslateZeiss', () => {
  test('should translate text if isZeiss is true', () => {
    // Arrange
    vi.mocked<any>(useUserStore).mockReturnValue({ isZeiss: { value: true } });
    // Act
    const { t } = useTranslateZeiss();
    const translatedText = t('Hello');
    // Assert
    expect(translatedText).toBe('Hello');
  });

  test('should not translate text if isZeiss is false', () => {
    // Arrange
    vi.mocked<any>(useUserStore).mockReturnValue({ isZeiss: { value: false } });
    // Act
    const { t } = useTranslateZeiss();
    const translatedText = t('Hello');
    // Assert
    expect(translatedText).toBe('Hello');
  });

  describe('getTranslateZeissFilterGroups', () => {
    test('should translate the labels of filter groups and options', () => {
      // Arrange
      vi.mocked<any>(useUserStore).mockReturnValue({ isZeiss: { value: false } });

      const mockArr = [
        {
          label: 'Group 1',
          children: [
            {
              label: 'Option 1',
            },
            {
              label: 'Option 2',
            },
          ],
        },
        {
          label: 'Group 2',
          options: [
            {
              label: 'Option 3',
            },
            {
              label: 'Option 4',
            },
          ],
        },
      ];
      // Act
      const result = getTranslateZeissFilterGroups(mockArr);
      // Assert
      expect(result).toEqual([
        {
          children: [
            {
              label: 'Option 1',
            },
            {
              label: 'Option 2',
            },
          ],
          label: 'Group 1',
        },
        {
          label: 'Group 2',
          options: [
            {
              label: 'Option 3',
            },
            {
              label: 'Option 4',
            },
          ],
        },
      ]);
    });
  });

  describe('getTranslateZeissTableColumn', () => {
    test('should translate the title of each item in the array', () => {
      // Arrange
      vi.mocked<any>(useUserStore).mockReturnValue({ isZeiss: { value: false } });
      const mockArr = [
        { title: 'English Title 1', key: 'col1' },
        { title: 'English Title 2', key: 'col2' },
      ];
      // Act
      const result = getTranslateZeissTableColumn(mockArr);
      // Assert
      expect(result).toEqual([
        {
          key: 'col1',
          title: 'English Title 1',
        },
        {
          key: 'col2',
          title: 'English Title 2',
        },
      ]);
    });
  });
});
