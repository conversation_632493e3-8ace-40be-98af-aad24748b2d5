import { get } from 'lodash';
import { computed } from 'vue';

import { useStore } from '@/store';

export const useUserStore = () => {
  const store = useStore();
  const isZeiss = computed(() => store.getters['user/isZeissOrg']);
  const profile = computed(() => store.getters['user/profile'] || {});
  const usage = computed(() => store.getters['user/usage']);
  const permissions = computed(() => profile.value?.permissions || []);
  const isOwner = computed(() => store.getters['user/isOwner']); // 是否是机构所有者
  const currentOrgId = computed(() => profile.value?.currentOrg || '');
  // 获取当前套餐开通的模块
  const orgModules = computed(() => get(profile.value, 'bundle.bundles[0].bundle.paramsSetting.modules', []));

  return {
    isZeiss,
    profile,
    permissions,
    usage,
    isOwner,
    currentOrgId,
    orgModules,
  };
};
