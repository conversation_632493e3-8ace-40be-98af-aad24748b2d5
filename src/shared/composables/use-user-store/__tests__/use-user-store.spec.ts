import { useUserStore } from '..';

// Mock the store getters
const mockStore = {
  getters: {
    'user/profile': {
      permissions: [1, 2, 3],
      bundle: {
        bundles: [
          {
            bundle: {
              paramsSetting: {
                modules: ['multiSettings'],
              },
            },
          },
        ],
      },
    },
    'user/isZeissOrg': true,
    'user/usage': {
      bundleUsage: {},
      departmentBundleUsage: {},
    },
    'user/isOwner': true,
  },
};
vi.mock('@/store', async () => {
  const originalModule = await vi.importActual('@/store');
  return {
    __esModule: true,
    ...originalModule,
    useStore: () => mockStore,
  };
});

describe('useUserStore', () => {
  test('should return the correct properties', async () => {
    const userStore = useUserStore();

    // Assert that the returned properties are correct
    expect(userStore.isZeiss.value).toBe(true);
    expect(userStore.profile.value).toEqual(mockStore.getters['user/profile']);
    expect(userStore.permissions.value).toEqual(mockStore.getters['user/profile'].permissions);
    expect(userStore.usage.value).toEqual(mockStore.getters['user/usage']);
    expect(userStore.isOwner.value).toBe(true);
  });
});
