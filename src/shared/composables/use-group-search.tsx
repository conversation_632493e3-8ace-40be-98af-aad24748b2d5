import { useRequest } from '@/shared/composables/use-request';

import { monitor as monitorService } from '@/shared/services';
import { computed, onMounted } from 'vue';

//  自动请求
export const getGroupList = (mountExcute: boolean = true) => {
  const monitorGroups = useRequest(monitorService.getAllGroups);

  const groupList = computed(() => monitorGroups.data?.value?.data ?? []);
  onMounted(async () => {
    if (mountExcute) {
      await monitorGroups.execute<any>({ notUsePage: true });
    }
  });
  return {
    groupList,
    monitorGroups,
  };
};
