import { message } from 'ant-design-vue';
import { reactive } from 'vue';

export enum GenerateReportStatus {
  Idle,
  Pending,
  Success,
  Failed,
}

/**
 * 生成报告
 */
export function useGenerateReportFile(request, delay = 2000) {
  const defaults = {
    /** 报告下载链接 */
    downloadLink: undefined,
    /** 轮询报告生成状态 */
    status: GenerateReportStatus.Idle,
  };

  const state = reactive({
    ...defaults,
  });

  /**
   * 轮询
   * @param args 动态参数
   */
  const polling = (...args) => {
    state.status = GenerateReportStatus.Pending;
    const timer = setInterval(async () => {
      try {
        const { detailFile, status } = await request(...args);
        if (status === 3) {
          throw new Error('报告生成失败');
        }
        if (status === 2 && !!detailFile) {
          state.downloadLink = detailFile; // 下载链接赋值
          state.status = GenerateReportStatus.Success;
          clearInterval(timer);
        }
      } catch (error) {
        message.warn('报告生成失败，请稍后再试');
        state.status = GenerateReportStatus.Failed;
        clearInterval(timer);

        console.error(error);
      }
    }, delay);
  };

  /**
   * 重置
   */
  const reset = () => {
    Object.assign(state, defaults);
  };

  return [state, polling, reset] as const;
}
