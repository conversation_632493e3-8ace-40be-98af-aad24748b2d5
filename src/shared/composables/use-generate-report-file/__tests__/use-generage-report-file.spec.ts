import { flushPromises } from '@/test-utils/flush-promises';

import { useGenerateReportFile, GenerateReportStatus } from '..';

const mockRequest = vi.fn();

describe('useGenerateReportFile', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });
  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  test('成功生成报告并返回下载链接', async () => {
    const detailFile = 'http://example.com/report.pdf';
    mockRequest.mockResolvedValueOnce({ detailFile, status: 2 });

    const [state, polling] = useGenerateReportFile(mockRequest);
    polling();

    await vi.advanceTimersByTimeAsync(2000);
    expect(state.status).toBe(GenerateReportStatus.Success);
    expect(state.downloadLink).toBe(detailFile);
  });

  test('请求失败', async () => {
    mockRequest.mockRejectedValueOnce(new Error('请求失败'));
    const consoleSpy = vi.spyOn(console, 'error');

    const [state, polling] = useGenerateReportFile(mockRequest);
    polling();

    await vi.advanceTimersByTimeAsync(2000);

    expect(state.status).toBe(GenerateReportStatus.Failed);
    expect(mockRequest).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith(new Error('请求失败'));
  });

  test('报告生成失败', async () => {
    mockRequest.mockResolvedValueOnce({ detailFile: null, status: 3 });
    const consoleSpy = vi.spyOn(console, 'error');

    const [state, polling] = useGenerateReportFile(mockRequest);
    polling();

    await vi.advanceTimersByTimeAsync(2000);

    expect(state.status).toBe(GenerateReportStatus.Failed);
    expect(consoleSpy).toHaveBeenCalledWith(new Error('报告生成失败'));
  });
});
