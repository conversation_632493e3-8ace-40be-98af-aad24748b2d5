import { flushPromises } from '@/test-utils/flush-promises';

import { useRequest } from '..';

describe('useRequest', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = vi.fn().mockResolvedValue(() => Promise.resolve({ status: true }));
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('should throw an error if request is not provided', () => {
    expect(() => {
      useRequest(undefined);
    }).toThrow('Request 不存在');
  });

  test('should set initial status to idle', () => {
    const { status } = useRequest(mockRequest);
    expect(status.value).toBe('idle');
  });

  test('should set status to pending when execute is called', async () => {
    const { status, execute } = useRequest(mockRequest);
    execute();
    expect(status.value).toBe('pending');

    await flushPromises();

    expect(status.value).toBe('success');
  });

  test('should set status to success if request resolves', async () => {
    const mockData = { foo: 'bar' };
    mockRequest.mockResolvedValue(mockData);
    const { status, execute, data } = useRequest(mockRequest);
    await execute();
    expect(status.value).toBe('success');
    expect(data.value).toBe(mockData);
  });

  test('should set status to error if request rejects', async () => {
    const mockError = new Error('Something went wrong');
    mockRequest.mockRejectedValue(mockError);
    const { status, execute, error } = useRequest(mockRequest);
    await execute();
    expect(status.value).toBe('error');
    expect(error.value).toBe(mockError);
  });

  test('should reset status to idle, data to undefined, and error to undefined when reset is called', () => {
    const { status, data, error, reset } = useRequest(mockRequest);
    data.value = 'some data';
    error.value = new Error('Some error');
    status.value = 'success';
    reset();
    expect(status.value).toBe('idle');
    expect(data.value).toBe(undefined);
    expect(error.value).toBe(undefined);
  });

  test('should clear data before request if clearBeforeRequest option is true', async () => {
    const { data, execute } = useRequest(mockRequest, { clearBeforeRequest: true });
    data.value = 'some data';
    execute();
    expect(data.value).toBeUndefined();
  });

  test('should not clear data before request if clearBeforeRequest option is false', async () => {
    const { data, execute } = useRequest(mockRequest, { clearBeforeRequest: false });
    data.value = 'some data';
    execute();
    expect(data.value).not.toBeUndefined();
  });
});
