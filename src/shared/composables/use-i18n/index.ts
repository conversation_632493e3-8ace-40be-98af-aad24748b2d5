import { computed, getCurrentInstance } from 'vue';

import { en } from '@/shared/locales';

export type LangType = keyof typeof en | string;

export const useI18n = () => {
  const instance = getCurrentInstance();
  const vm = instance?.proxy as any;
  const t = (val: LangType) => {
    if (instance?.proxy?.$i18n) {
      return instance.proxy.$i18n.t(val);
    }
    return val;
  };
  const tc = (val: LangType, ...args) => {
    if (instance?.proxy?.$i18n) {
      return instance.proxy.$i18n.tc(val, ...args);
    }
    return val;
  };
  const locale = computed({
    get: () => {
      return vm?.$i18n?.locale || 'zh-CN';
    },
    set: (val) => {
      vm.$i18n.locale = val;
    },
  });

  return {
    t,
    tc,
    locale,
  };
};
