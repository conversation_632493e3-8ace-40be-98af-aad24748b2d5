import { computed, ref, Ref } from 'vue';
import { UseInfiniteScrollOptions, useInfiniteScroll as useInfiniteScrollOrigin } from '@vueuse/core';

interface UseInfiniteScrollReturn {
  isLoadingMore: Ref<boolean>;
  hasMore: Ref<boolean>;
  resetScroll: () => void;
  initScroll: (element: HTMLElement) => void;
}

export function useInfiniteScroll(loadMore: () => Promise<unknown>, options: UseInfiniteScrollOptions = {}): UseInfiniteScrollReturn {
  const isLoadingMore = ref(false);
  let scrollState: ReturnType<typeof useInfiniteScrollOrigin> | null = null;

  const canLoadMore = (el: HTMLElement): boolean => {
    if (isLoadingMore.value) {
      return false;
    }
    return options.canLoadMore ? options.canLoadMore(el) : true;
  };

  const hasMore = computed(() => true);

  const resetScroll = () => {
    if (scrollState) {
      scrollState.reset();
    }
  };

  const handleLoadMore = async () => {
    if (isLoadingMore.value) return;

    isLoadingMore.value = true;
    try {
      await loadMore();
    } finally {
      isLoadingMore.value = false;
    }
  };

  const initScroll = (element: HTMLElement) => {
    scrollState = useInfiniteScrollOrigin(element, handleLoadMore, {
      ...options,
      canLoadMore,
    });
  };

  return {
    isLoadingMore,
    hasMore,
    resetScroll,
    initScroll,
  };
}
