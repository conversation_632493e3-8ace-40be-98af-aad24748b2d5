import { Ref, onMounted, watch } from 'vue';

import { useRequest } from './use-request';

export type AggsField = {
  aggsField: number[]; // 聚合的对象， 1 今日动态， 2 监控企业， 3 动态分布 , 4 搜索项聚合;
  favoriteGroupId?: string;
  includeRelatedParty?: number; // 是否包含关联方, 0 不包含 1 包含， 默认0
  aggsTopN?: number; // 聚合结果返回条数，默认10
  createDate?: object; // 日期
  sortField?: string; // 排序字段
  isSortAsc?: boolean; // 是否升序
  metricType?: number[]; // 动态类型: 1.监管类动态, 2.业务类动态
  metricsIds?: number[]; // moxing
  riskLevels?: number[]; // moxing
  pageIndex?: number; // 页码
  pageSize?: number; // 每页条数
  keyword?: string; // 搜索关键字
  companyId?: string[]; // 企业id
};
export const useMonitorAggsSearch = (payload: Ref<AggsField>, searchFn) => {
  const { execute, data: dataSource, isLoading } = useRequest(searchFn);

  onMounted(() => {
    execute(payload.value);
  });

  watch(
    () => payload.value,
    (value) => {
      execute(value);
    },
    {
      deep: true,
    }
  );

  return {
    execute,
    dataSource,
    isLoading,
  };
};
