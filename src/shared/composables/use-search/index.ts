import { computed, ref } from 'vue';

export const useSearch = (request: (payload?) => Promise<any>) => {
  const state = ref('idle');
  const isLoading = computed(() => {
    return state.value === 'loading';
  });

  const search = async (payload?) => {
    state.value = 'loading';
    try {
      await request(payload);
      state.value = 'done';
    } catch (err) {
      // console.error(err);
      state.value = 'done';
    }
  };

  return {
    isLoading,
    state,
    search,
  } as const;
};
