import { flushPromises } from '@/test-utils/flush-promises';

import { useSearch } from '..';

describe('useSearch', () => {
  test('should set state to loading and then to done when request resolves', async () => {
    const mockRequest = vi.fn(() => Promise.resolve());
    const { state, isLoading, search } = useSearch(mockRequest);

    expect(state.value).toBe('idle');
    expect(isLoading.value).toBe(false);

    search();

    expect(state.value).toBe('loading');
    expect(isLoading.value).toBe(true);

    await flushPromises();

    expect(state.value).toBe('done');
    expect(isLoading.value).toBe(false);
    expect(mockRequest).toHaveBeenCalled();
  });

  test('should set state to done and log error when request rejects', async () => {
    const mockRequest = vi.fn(() => Promise.reject(new Error('Test error')));
    const { state, isLoading, search } = useSearch(mockRequest);

    expect(state.value).toBe('idle');
    expect(isLoading.value).toBe(false);

    search();

    expect(state.value).toBe('loading');
    expect(isLoading.value).toBe(true);

    await flushPromises(); // wait for async operations

    expect(state.value).toBe('done');
    expect(isLoading.value).toBe(false);
  });
});
