/**
 * @module FIXME: 移除全局状态污染，统一通过 vuex 管理
 */
import { computed, ref, unref } from 'vue';
import { get, omit, throttle } from 'lodash';

import { useFetchState } from '@/hooks/use-fetch-state';
import { setting } from '@/shared/services';
import { flattenAndAddProperties } from '@/hooks/helper/flatten-dimension-settings';

type Setting = {
  key: string;
  name: string;
  status: number;
  sort: number;
  level: number;
  parentKey: string;
  rootKey: string;
  [key: string]: any;
};

const result = ref<Record<string, any>>({});

const isEdit = ref(false);
const visible = ref(false);
export const useMonitorSettingStore = () => {
  const settings = computed(() => unref(result)?.content || []);

  // 打平后的setting
  const flatSettings = computed<Setting[]>(() => {
    const items = unref(settings).map((value: any) => {
      const subDimensionList = get(value, 'items', []);
      return {
        ...omit(value, 'items'),
        subDimensionList,
      };
    });
    return flattenAndAddProperties(items);
  });

  // 获取其他打开的Setting
  // 主要用来判断当前是不是最后一个打开的维度，如果是的话，不允许关闭
  const getAnotherOpenSetting = (key: string) => {
    const current = unref(flatSettings).find((item) => item.key === key);
    if (!current) return [];
    const hasAncestorWithStatus = (item) => {
      if (!get(item, 'status', 0)) {
        return false;
      }
      if (item.key === key || item.parentKey === key) {
        return false;
      }
      if (!item.parentKey) {
        return item.status;
      }
      const parent = unref(flatSettings).find((v) => v.key === item.parentKey);
      if (parent && !hasAncestorWithStatus(parent)) {
        return false;
      }
      return item.status;
    };
    // 通过传入的key进行判断，如果parentKey和key没有交集，说明是其他打开的setting
    const anotherOpenSetting = unref(flatSettings)
      .filter((item) => item.key !== key && item.key !== current.parentKey)
      .filter(hasAncestorWithStatus);
    return anotherOpenSetting;
  };

  const { execute, isLoading } = useFetchState(async () => {
    const res = await setting.getMonitorSetting();
    (res?.content || []).forEach((singleSetting) => {
      singleSetting.status = 1;
    });
    result.value = res;
  });

  const getSettingInfo = throttle(execute, 100);

  const editSettingInfo = (content) => {
    return setting.saveMonitorSetting({ ...result.value, content });
  };

  const upgradeSettingInfo = (content) => {
    return setting.saveMonitorSetting({ ...result.value, content });
  };

  return {
    isEdit,
    getSettingInfo,
    settings,
    result,
    flatSettings,
    getAnotherOpenSetting,
    editSettingInfo,
    upgradeSettingInfo,
    isLoading,
    visible,
  };
};
