import { HttpClient } from '@/utils';

export const createService = (httpClient: HttpClient) => ({
  // 受益股东-股权机构分布
  getBeneficiaryData(params) {
    return httpClient.get('v1/api/proxy/graph/BeneficiaryGraph/EnterpriseBeneficiaryGraph', { params });
  },
  // 风险图谱
  getRiskGraph(params) {
    return httpClient.post('v1/api/proxy/graph/RiskGraph/GetGraph', params);
  },
  // 实际控制人图谱
  getSuspectedActualControllerV2(params) {
    return httpClient.get('v1/api/proxy/graph/SKRGraph/GetSuspectedActualController', { params });
  },
  /* -------------------- 关系图谱------------------------ */
  relationGetGraph(params) {
    return httpClient.get('v1/api/proxy/graph/Graph/GetCompanyGraph', { params });
  },
  /* -------------------- 关系图谱 ------------------------ */
  /* -------------------- 企业图谱 ------------------------ */
  getEnterpriseGraph(params) {
    return httpClient.get('v1/api/proxy/graph/EntityGraph/EnterpriseGraph', { params });
  },
  getPersonGraph(params) {
    return httpClient.get('v1/api/proxy/graph/EntityGraph/PersonGraph', { params });
  },
  /* -------------------- 企业图谱 ------------------------ */
  /* -------------------- 股权穿透图------------------------ */
  getOwnershipStructure(params) {
    return httpClient.get('v1/api/proxy/graph/Relation/GetOwnershipStructure', { params });
  },
  getEquityInvestment(params) {
    return httpClient.get('v1/api/proxy/graph/VIP/GetEquityInvestment', { params });
  },
  getUltimateBeneficiaryNoPath(params) {
    return httpClient.get('v1/api/proxy/graph/VIP/GetUltimateBeneficiaryNoPath', { params });
  },
  getHoldingCompany(params) {
    return httpClient.post('/v1/api/dimension/basic-info/get-control-list', params);
  },
  getSuspectedActualControllerNoPathV2(params) {
    return httpClient.get('v1/api/proxy/graph/SKRGraph/GetSuspectedActualControllerNoPath', { params });
  },
  getBossEquityInvestment(params) {
    return httpClient.get('v1/api/proxy/graph/Person/GetBossEquityInvestment', {
      params: { personId: params.keyNo },
    });
  },
  /* -------------------- 股权穿透图 ------------------------ */
  /* -------------------- 股权结构图 ------------------------ */
  getPartnerList(params) {
    return httpClient.post('/v1/api/dimension/basic-info/get-partner-list', params);
  },
  getHisHolders(params) {
    return httpClient.get('v1/api/proxy/graph/Relation/GetHistoryEquityShare', { params });
  },
  /* -------------------- 股权结构图 ------------------------ */
  /* -------------------- 关联方认定图 ------------------------ */
  glfGetNodes(params) {
    return httpClient.post('v1/api/proxy/graph/EREGraph/GetGraph', params);
  },
  glfSearchNode(params) {
    return httpClient.post('v1/api/proxy/graph/EREGraph/SearchNode', params);
  },
  glfSearchAll(params) {
    return httpClient.post('v1/api/proxy/graph/EREGraph/SearchAll', params);
  },
  glfGetDetailsByIds(params) {
    return httpClient.post('v1/api/proxy/graph/ECILocal/GetDetailsByIds', params);
  },
  /* -------------------- 关联方认定图 ------------------------ */
  /* -------------------- 集团人员图谱 ------------------------ */
  getStaffGraph(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/GetStaffGraph', params);
  },
  searchStaffGraph(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/SearchStaffGraph', params);
  },
  getSearchResultGraph(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/GetSearchResultGraph', params);
  },
  /* -------------------- 集团人员图谱 ------------------------ */
  /* -------------------- 集团成员图谱 ------------------------ */
  getMemberGraph(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/GetMemberGraph', params);
  },
  filterMember(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/filterMember', params);
  },
  findPaths(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/FindPaths', params);
  },
  /* -------------------- 集团成员图谱 ------------------------ */
  /* -------------------- common ------------------------ */
  getPersonOverview(params) {
    return httpClient.get('v1/api/proxy/graph/EntityGraph/PersonOverview', { params });
  },

  getEnterpriseOverview(params) {
    return httpClient.get('v1/api/proxy/graph/EntityGraph/EnterpriseOverview', { params });
  },
  // 图谱项目接口
  getMemberOverview(params) {
    return httpClient.post('v1/api/proxy/graph/GroupGraph/GetMemberOverview', params);
  },

  /* -------------------- common ------------------------ */
});
