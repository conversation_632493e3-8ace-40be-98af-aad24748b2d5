import { HttpClient } from '@/utils';

const BASE_URL = '/verification';
const VERIFY = `${BASE_URL}/executeVerification`;
const EXECUTE_VERIFY = `${BASE_URL}/person/excel/execute`;
const SEARCH_VERIFICATION_RECORD = `${BASE_URL}/searchVerificationRecord`;
const GET_VERIFICATION_RECORD = `${BASE_URL}/getVerificationRecord`;
const BATCH_EXPORT_VERIFICATION_RECORD = `/batch${BASE_URL}/record`;
const BATCH_EXPORT_VERIFICATION_RECORD_REGULAR = `/batch${BASE_URL}/result/regular`;
const BATCH_EXPORT_VERIFICATION_RECORD_DEEP = `/batch${BASE_URL}/result/deep`;
// 验证记录接口
export interface IVerificationRecord {
  // 记录ID
  id: string;
  // 人员信息
  personInfo: {
    // 姓名
    name: string;
    // 身份证号
    idNumber: string;
  };
  // 企业信息
  companyInfo: {
    // 企业名称
    name: string;
    // 企业编号
    keyNo?: string;
  };
  // 验证结果
  verifyResult: 'match' | 'unmatch';
  // 验证时间
  verifyTime: string;
  // 操作人
  operator: {
    // 操作人ID
    id: string;
    // 操作人姓名
    name: string;
  };
  // 其他可能的字段
  [key: string]: any;
}

// 验证记录查询参数接口
export interface ISearchVerificationRecordParams {
  // 页码
  pageIndex?: number;
  // 每页数量
  pageSize?: number;
  // 搜索关键词
  keyword?: string;
  // 验证时间范围开始
  verifyTimeStart?: string;
  // 验证时间范围结束
  verifyTimeEnd?: string;
  // 操作人ID
  operatorId?: string;
  // 验证结果，可能的值：match（匹配）, unmatch（不匹配）
  verifyResult?: 'match' | 'unmatch';
  // 其他可能的筛选条件
  [key: string]: any;
}

// 验证记录查询结果接口
export interface ISearchVerificationRecordResult {
  // 总记录数
  total: number;
  // 当前页码
  pageIndex: number;
  // 每页数量
  pageSize: number;
  // 记录列表
  data: IVerificationRecord[];
}

export interface IGetVerificationRecordParams {
  // 记录ID
  recordId: number;
  // 批次ID
  batchId: number;
  // 每页数量
  pageSize: number;
  // 当前页码
  pageIndex: number;
}

export interface IGetVerificationRecordResult {
  [key: string]: any;
}

export interface IExportVerificationRecordParams {
  // 记录ID
  recordIds?: number[];
  // 批次ID
  batchId?: number;
}

export interface IExportVerificationRecordResult {
  [key: string]: any;
}

export const createService = (httpClient: HttpClient) => ({
  // 根据公司搜索对应的供应商客户
  getVerifyHistory(data) {
    return httpClient.post(`/verification/searchVerificationRecord`, data);
  },
  // 执行核验
  executeVerify(data) {
    return httpClient.post(`${EXECUTE_VERIFY}`, data);
  },
  // 执行核验
  identityVerify(data) {
    return httpClient.post(`${VERIFY}`, data);
  },
  // 查询核验记录
  searchVerificationRecord(params: ISearchVerificationRecordParams): Promise<ISearchVerificationRecordResult> {
    return httpClient.post(SEARCH_VERIFICATION_RECORD, params);
  },
  // 获取核验记录
  getVerificationRecord(params: IGetVerificationRecordParams): Promise<IGetVerificationRecordResult> {
    return httpClient.post(GET_VERIFICATION_RECORD, params);
  },
  // 导出核验记录
  exportVerificationRecord(params: IExportVerificationRecordParams): Promise<IExportVerificationRecordResult> {
    return httpClient.post(BATCH_EXPORT_VERIFICATION_RECORD, params);
  },
  // 导出核验记录
  exportVerificationRecordRegular(params: IExportVerificationRecordParams): Promise<IExportVerificationRecordResult> {
    return httpClient.post(BATCH_EXPORT_VERIFICATION_RECORD_REGULAR, params);
  },
  // 批量导出验证记录
  exportVerificationRecordDeep(params: IExportVerificationRecordParams): Promise<IExportVerificationRecordResult> {
    return httpClient.post(BATCH_EXPORT_VERIFICATION_RECORD_DEEP, params);
  },
});
