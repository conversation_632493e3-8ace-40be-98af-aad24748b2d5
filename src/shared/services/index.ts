import { HttpClient } from '@/utils';
import {
  authByTokenInterceptor,
  authInterceptor,
  errorInterceptor,
  retryInterceptor,
  stripInterceptor,
  tracerInterceptor,
} from '@/utils/http-client/interceptors';
import { redirect } from '@/shared/composables/use-auth';

import { createService as createCompanyService } from './company.service';
import { createService as createPersonService } from './person.service';
import { createService as createUserService } from './user.service';
import { createService as createDimensionService } from './dimension.service';
import { createService as createSnsService } from './sns.service';
import { createService as createSearchService } from './search.service';
import { createService as createGraphService } from './graph.service';
import { createService as createConsService } from './construction.service';
import { createService as createDimensionDetailService } from './dimension-detail.service';
import { createService as createMonitorService } from './monitor.service';
import { createService as createAuthService } from './auth.service';
import { createService as createDiligenceService } from './diligence.service';
import { createService as createSettingService } from './setting.service';
import { createService as createBatchService } from './risk-batch-import.service';
import { createService as createMessageService } from './message.service';
import { createService as createIdentityService } from './identity-verification.service';

/**
 * 创建 HttpClient 实例
 */
const createHttpClient = (baseURL = '/') => {
  const httpClient = new HttpClient({
    baseURL,
    headers: {
      'content-type': 'application/json',
      'x-requested-with': 'XMLHttpRequest',
      'x-kzz-request-from': 'qcc-insights-web',
    },
  });

  httpClient.inject(authByTokenInterceptor, {
    headerField: 'kzzsessid',
    storageField: 'KZZSESSID',
  });
  httpClient.inject(authInterceptor, {
    redirect,
  });
  httpClient.inject(errorInterceptor);
  httpClient.inject(retryInterceptor);
  httpClient.inject(stripInterceptor);
  httpClient.inject(tracerInterceptor, {
    traceId: 'x-kzz-request-id',
  });
  return httpClient;
};

const httpClientInsights = createHttpClient('/insights');

export const user = createUserService(httpClientInsights);
export const setting = createSettingService(httpClientInsights);
export const diligence = createDiligenceService(httpClientInsights);
export const company = createCompanyService(httpClientInsights);
export const dimensionDetail = createDimensionDetailService(httpClientInsights);
export const dimension = createDimensionService(httpClientInsights);
export const auth = createAuthService(httpClientInsights);
export const search = createSearchService(httpClientInsights);
export const person = createPersonService(httpClientInsights);
export const sns = createSnsService(httpClientInsights);
export const graph = createGraphService(httpClientInsights);
export const batchImport = createBatchService(httpClientInsights);
export const message = createMessageService(httpClientInsights);

export const monitor = createMonitorService(httpClientInsights); // 风险监控API
export const identityVerification = createIdentityService(httpClientInsights); // 风险监控API

export const cons = createConsService(httpClientInsights);
