import { IProToken } from '@/interfaces';
import { HttpClient } from '@/utils/http-client';

const AUTH_GET_PRO_TOKEN = `/account/getProToken`; // 获取用户专业版 Token (访问详情页)
const AUTH_COUSTOMER_MANAGER = '/account/customerInfo'; // 获取当前企业的客户经理

export const createService = (httpClient: HttpClient) => ({
  /**
   * 专业版登录-获取token
   */
  getProToken(): Promise<Readonly<IProToken>> {
    return httpClient.post(AUTH_GET_PRO_TOKEN);
  },

  // 获取当前企业的客户经理
  getCustomerManger(companyName) {
    return httpClient.get(`${AUTH_COUSTOMER_MANAGER}?companyName=${JSON.stringify(companyName)}`);
  },
});
