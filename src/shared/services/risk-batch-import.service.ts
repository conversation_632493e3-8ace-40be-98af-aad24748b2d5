import qs from 'querystring';

import { useMultiSettingStore } from '@/hooks/use-multi-setting-store';
import type { HttpClient } from '@/utils/http-client';

export const BATCH_IMPORT_BASE = '/batch/import';
export const BATCH_IMPORT_KEYNO = `${BATCH_IMPORT_BASE}/diligence/data`;
export const BATCH_IMPORT_CUSTOMER = `${BATCH_IMPORT_BASE}/diligence/customer`;
export const BATCH_IMPORT_DETAIL = `${BATCH_IMPORT_BASE}/detail`;
export const BATCH_IMPORT_STATISTICS = `${BATCH_IMPORT_BASE}/detail`;
export const BATCH_SCAN = `/batch/customer/diligence_analyze`;
export const BATCH_COMPANY_DETAIL = `${BATCH_IMPORT_DETAIL}/company`;
export const BATCH_UPLOAD_DATA = `${BATCH_IMPORT_BASE}/diligence/excel/item/search`;
export const BATCH_DELETE = `${BATCH_IMPORT_BASE}/diligence/excel/item`;
export const BATCH_EXCUTE = `${BATCH_IMPORT_BASE}/diligence/excel/execute`;

export const createService = (httpClient: HttpClient) => ({
  search(data): Promise<any> {
    return httpClient.post(`/batch/search`, data);
  },
  batchImport(type, params) {
    const { selectedModel } = useMultiSettingStore();
    params.settingId = selectedModel?.value?.id;
    switch (type) {
      case 'keyNo':
        return httpClient.post(BATCH_IMPORT_KEYNO, params);
      case 'customer':
        return httpClient.post(BATCH_IMPORT_CUSTOMER, params);
      default:
        return Promise.reject(new Error('必须提供批量类型类型'));
    }
  },

  importByKeyNo(data): Promise<any> {
    const { selectedModel } = useMultiSettingStore();
    data.settingId = selectedModel?.value?.id;
    return httpClient.post(BATCH_IMPORT_KEYNO, data);
  },
  importByCustomer(data): Promise<any> {
    const { selectedModel } = useMultiSettingStore();
    data.settingId = selectedModel?.value?.id;
    return httpClient.post(BATCH_IMPORT_CUSTOMER, data);
  },
  detail(data): Promise<any> {
    return httpClient.post(`${BATCH_IMPORT_DETAIL}`, data);
  },
  export(data = {}): Promise<any> {
    return httpClient.post(`/batch/export/diligence_batch_detail`, data);
  },
  exportDetail(data = {}): Promise<any> {
    return httpClient.post(`/batch/export/dimension_detail`, data);
  },
  exportReport(data = {}): Promise<any> {
    return httpClient.post(`/batch/export/batch_diligence_pdfs`, data);
  },
  /**
   * 对全部第三方批量排查
   */
  batchScan(): Promise<any> {
    const { selectedModel } = useMultiSettingStore();
    return httpClient.post(BATCH_SCAN, {
      settingId: selectedModel?.value?.id,
    });
  },
  /**
   * 获取当前公司的子维度薪资信息
   */
  getCompanyDimensions(params) {
    return httpClient.post(BATCH_COMPANY_DETAIL, params);
  },
  /**
   * 获取文件上传的数据
   */
  getUploadData(params, moduleType = 'verification') {
    return httpClient.post(`/batch/import/${moduleType}/excel/item/search`, params);
  },
  /**
   * 列表移除公司
   */
  deleteCompany(data, moduleType = 'verification') {
    return httpClient.post(`/batch/import/${moduleType}/excel/item/delete`, data);
  },
  /**
   * 列表更新公司
   */
  updateCompany(params, moduleType = 'verification') {
    return httpClient.post(`/batch/import/${moduleType}/excel/item/update`, params);
  },
  /**
   * 进行排查或者添加
   */
  executeBatch(data, moduleType = 'verification') {
    return httpClient.post(`/batch/import/${moduleType}/excel/execute`, data);
  },
  getResult(data) {
    return httpClient.post(`/batch/import/monitor/excel/search/result`, data);
  },
  /**
   * 获取全量排查的额度信息
   */
  getDiligenceCount() {
    return httpClient.post('/batch/customer/diligence_analyze/check');
  },
  /**
   * 失败任务重试
   */
  getBatchRetry(batchId) {
    return httpClient.post('/batch/retry', { batchId });
  },
  /**
   *
   */
  exportTenderBatchDetail(params) {
    return httpClient.post('/batch/export/tender_diligence_record', params);
  },
  // 批量招标排查维度详情导出
  exportTenderDimensionDetail(data): Promise<any> {
    return httpClient.post('/batch/export/tender_dimension_detail', data);
  },
  // 批量特定利益关系导出
  exportInterestBatchDetail(data): Promise<any> {
    return httpClient.post('/batch/export/specific_batch_record', data);
  },
  // 人企核验列表
  exportIdentityVerification(data): Promise<any> {
    return httpClient.post('/batch/verification/record', data);
  },
  // 批量监控-失败数据-导出
  exportFailedMonitor(data) {
    return httpClient.post('/batch/export/monitor/fail/record', data);
  },
});
