import { isNumber } from 'lodash';

import { HttpClient } from '@/utils/http-client';

export const createService = (httpClient: HttpClient) => ({
  getDetail(params): Promise<Readonly<any>> {
    let person: any = {};
    return httpClient
      .post('/v1/api/person/get-person-detail', params)
      .then((data) => {
        person = data;
        if (params.hasDefaultInfo) {
          return httpClient.post('/v1/api/person/get-person-detail-3in1', params);
        }
        return data;
      })
      .then((defaultInfoRes: any) => {
        if (defaultInfoRes) {
          person.defaultInfo = defaultInfoRes;
          person.CountInfo = defaultInfoRes.CountInfo;
          if (person.CountInfo) {
            person.CountInfo.RiskCountInfoV3 = defaultInfoRes.RiskCountInfoV3;
          }
        }
        return person;
      });
  },
  getPersonId(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/person/get-person-id', params);
  },
  getRelatCompany(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/people/getRelatCompany', {
      params,
    });
  },

  getRelateBenefitPerson(params) {
    return httpClient.get('/proxy/api/QccSearch/List/SameBenefitCoy', { params });
  },

  async getCount() {
    const res = await httpClient.post('/person/count');
    return isNumber(res) ? res : res?.data;
  },

  // 人员信息是否已核实
  getPersonVerifyData(params) {
    return httpClient.post('/person/isVerify', params);
  },

  // 根据人员名称，公司keyNo,获取公司下与该人员匹配的人员列表
  getCom2Person(params) {
    return httpClient.post('/person/company', params);
  },
});
