import { HttpClient } from '@/utils/http-client';

export const createService = (httpClient: HttpClient) => ({
  demensionAutoList(api: string, params: any): Promise<Readonly<any>> {
    return httpClient.get(`/v1/api/datalist/${api}`, {
      params,
    });
  },
  // 失信信息
  shixinlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-shixin-list', params);
  },
  // 限制高消费
  sumptuarylist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-sumptuary-list', params);
  },
  // 终本案件
  endexecutioncaselist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-end-execution-case-list', params);
  },
  // 裁判文书
  wenshulist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-judgement-list', params);
  },
  // 法院公告
  gonggaolist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-announcement-list', params);
  },
  // 开庭公告
  noticelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-court-notice-list', params);
  },
  // 送达公告
  dnoticelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-delivery-notice-list', params);
  },
  // 风险扫描工商变更
  riskchangelist(params): Promise<Readonly<any>> {
    return httpClient.get('/risk/RiskScan/ChangeList', { params });
  },
  // 新闻舆情
  newslist(params: any): Promise<Readonly<any>> {
    const reParams = params;
    reParams.showMapTag = true;
    return httpClient.post('/v1/api/dimension/news/get-list', reParams);
  },
  // 股东信息
  partner(params: any): Promise<Readonly<any>> {
    const reParams = params;
    // 工商登记里二级股东 type: 'Priority'
    reParams.type = reParams.type || 'Partners';
    reParams.pageSize = 50;
    return httpClient.post('/v1/api/dimension/basic-info/get-partner-list', reParams);
  },
  // 企业公告
  sgglist(params: any): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/announcement/get-list', params);
  },
  // 相关公告
  // rgglist(params: any): Promise<Readonly<any>> {
  //   const reParams = params;
  //   reParams.searchKey = params.keyNo;
  //   reParams.type = 'related';
  //   return httpClient.get('/v1/api/datalist/announcementlist', {
  //     params: reParams,
  //   });
  // },
  // 政府公告 TODO: 目前企业主页暂无此维度，可看情况处理
  // zfgglist(params: any): Promise<Readonly<any>> {
  //   return httpClient.get('/v1/api/datalist/zfgglist', {
  //     params,
  //   });
  // },
  // 历史法人
  hisoperlist(params: any): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/hisinfo', {
      params,
    });
  },
  // 历史营业执照信息
  hiscominfo(params: any): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/History/GetCoyHistoryInfo', {
      params,
    });
  },
  // 历史股东镜像
  hisequitysharelist(params: any): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/hisequitysharelist', {
      params,
    });
  },

  // 历史股东信息
  hispartner(params: any): Promise<Readonly<any>> {
    return httpClient.get('v1/api/proxy/data/History/GetCoyHistoryPartnerInfo', {
      params,
    });
  },

  // 上榜榜单
  sbbdlist(params: any): Promise<Readonly<any>> {
    const reParams = params;
    reParams.searchKey = params.keyNo;
    reParams.searchType = 'companykeyword';
    reParams.sortField = 'publishyear';
    reParams.region = 1;
    return httpClient.post('/v1/api/dimension/manage-develop/get-bd-list', reParams);
  },

  // 主要人员
  mainmember(params: any): Promise<Readonly<any>> {
    const reParams = params;
    reParams.type = 'Employees';
    return httpClient.post('/v1/api/dimension/basic-info/get-employee-list', reParams);
  },
  // 历史高管
  hismainmember(params: any): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/history/get-employee-list', params);
    // return httpClient.get('/v1/api/datalist/hismainmember', {
    //   params,
    // });
  },
  // 疑似关系
  suspectlist(params: any): Promise<Readonly<any>> {
    return Promise.all([
      httpClient.post('/v1/api/dimension/basic-info/get-suspect-concat-list', params),
      httpClient.post('/v1/api/dimension/basic-info/get-suspect-compete-list', params),
      httpClient.post('/v1/api/dimension/basic-info/get-suspect-cooperate-list', params),
    ]);
  },
  // 总公司
  parentinfo(params: any): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-head-office-info', params);
  },
  // 分支机构
  branchelist(params: any): Promise<Readonly<any>> {
    const reParams = params;
    reParams.nodeName = 'Branches';
    return httpClient.post('/v1/api/dimension/basic-info/get-branch-list', reParams);
  },
  shangbiaolist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-tm-list', params);
  },
  // 政府约谈
  ngiclist(params): Promise<Readonly<any>> {
    return httpClient.post('v1/api/dimension/gov-supervision/get-gov-interview-list', params);
  },
  // 经营异常
  exceptions(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-exception-list', params);
  },
  // 严重违法
  svlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-serious-violation-list', params);
  },
  // 行政许可
  acolist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-aco-list', params);
  },
  // 税务信用
  taxcreditlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-tax-credit-list', params);
  },
  // 招投标
  tenderlist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/tenderlist', {
      params,
    });
  },
  // 股权出质
  pledgelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-pledge-listV2', params);
  },

  // 股权质押
  spledge(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-stock-right-pledge', params);
  },

  // 股权质押比例筛选
  spledgeFilter(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-stock-right-pledge-proportion', params);
  },

  // 公示催告
  pnlist(params): Promise<Readonly<any>> {
    return httpClient.post('v1/api/dimension/manage-warn/get-public-notice-list', params);
  },

  // 欠税公告
  owenoticelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-tax-notice-list', params);
  },

  // 黑名单
  blackcomlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-black-list', params);
  },

  // 产品召回
  productrecalllist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-product-recall-list', params);
  },

  // 未准入境
  notallowedentrylist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-not-allow-entry-list', params);
  },

  // 知识产权出质
  patentpledgelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-ipr-pledge-list', params);
  },

  // 微博
  weibolist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-wb-list', params);
  },
  // 商标文书
  tmcdslist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-tmDoc-list', params);
  },
  // 专利信息
  zhuanlilist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-patent-list-V2', params);
  },
  // 国际专利
  internationZhuanlilist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-interPatent-list', params);
  },
  // 资质证书
  zhengshulist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-certification-list', params);
  },
  // 行政处罚
  adminpenaltylist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-admin-penalty-list', params);
  },
  // 环保处罚
  envlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-env-penalty-list', params);
  },
  // 税收违法
  taxillegallist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-tax-list', params);
  },
  // 动产抵押
  mpledgelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-mpledge-list', params);
  },
  // 作品著作权
  zzqlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-copyrights-list', params);
  },
  // 软件著作权
  rjzzqlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-copyrights-list', params);
  },
  // 清算信息
  liquidationlist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/liquidationlist', {
      params,
    });
  },
  // 备案网站
  websitelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-website-list', params);
  },
  // app
  applist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-app-list', params);
  },
  // 小程序
  wplist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-miniProgram-list', params);
  },
  // 微信公众号
  wechatlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-wx-list', params);
  },
  // 标准信息
  // return httpClient.post('/v1/api/dimension/manage-develop/get-standard-list', params);
  standardlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-standard-list', params);
  },
  // 商业特许经营
  sytxjylist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-business-management-list', params);
  },
  // 土地抵押
  landmortgagelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-land-mortgage-list', params);
  },
  // 金融
  // A股
  ipobase(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/finance/company/get-ashare-ipo-detail', params);
  },
  // 三板
  sanbanbase(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/finance/company/get-three-board-detail', params);
  },
  // 港股
  hkstockbase(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/finance/company/get-hk-detail', params);
  },
  // 财务
  cwsj(params: any) {
    return httpClient.get('/proxy/api/Dynamic/GetNewFinancingInfo', { params });
  },
  // 注册制
  kcbinfo(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/finance/company/get-register-detail', params);
  },
  // 核准制
  nlistinfo(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/finance/company/get-approval-detail', params);
  },
  financialanalysis(params) {
    return httpClient.get('/proxy/api/Analysis/GetNewFinancialAnalysisInfo', { params });
  },
  bondSearch(params): Promise<Readonly<any>> {
    return httpClient.get('/proxy/api/Bond/Search/Bond', {
      params,
    });
  },
  ipogglist(params): Promise<Readonly<any>> {
    // return httpClient.post('/v1/api/company/usc-info/get-employee-list', params);
    return httpClient.get('/proxy/api/CompanyIPO/GetIpoExecutive', {
      params,
    });
  },
  ipocgkglist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/AShare/Overview/CGKGList', {
      params,
    });
  },
  ipocgkgdetail(params): Promise<Readonly<any>> {
    return httpClient.get('/proxy/api/AShare/Overview/CGKGDetail', {
      params,
    });
  },
  // 劳动仲裁-开庭公告
  adnlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-labor-arbitration-list', params);
  },
  // 劳动仲裁-送达公告
  lbsclist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-labor-arbitration-list', params);
  },
  // 业务竞争
  competitorlist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/Operation/competitorListSearch', {
      params,
    });
  },
  // 招聘
  joblist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-recruitment-list', params);
  },
  // 进出口信用
  ciaxlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-import-export-list', params);
  },
  // 国有土地受让
  landmergelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-land-merge-list', params);
  },
  // 土地转让
  landmarketlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-land-market-deal-list', params);
  },
  // 债券信息
  creditorlist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/creditorlist', {
      params,
    });
  },
  // 债券违约
  bondlist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/bondlist', {
      params,
    });
  },
  // 抽查检查
  spotchecklist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-spot-check-list', params);
  },
  // 软件违规
  wgrjlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-software-violation-list', params);
  },
  // 担保信息
  guarantorinfo(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-guarantor-list', params);
  },
  // 简易注销
  simpleCancellation(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-simple-cancellation-list', params);
  },
  // 历史商标
  getTmHislist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/ipr/get-hisTm-list', params);
  },
  // 电信许可
  telecomlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-telecom-license-list', params);
  },
  // 供应商客户
  supplierCustomer(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-supplier-customer-list', params);
  },
  // 全球关联企业-全球股东
  oversearelated(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-oversea-control-list', params);
  },
  // 全球关联企业-全球参控股企业
  overseapartner(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-oversea-partner-list', params);
  },
  // 人员-所属集团
  pergrouplist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/datalist/pergrouplist', {
      params,
    });
  },
  // 注销备案
  enliqinfo(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-warn/get-enliq-list', params);
  },
  // 广告审查
  adverchecklist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-adverCheck-list', params);
  },
  // 信用评价
  creditrate(params): Promise<Readonly<any>> {
    const { key } = params;
    const apiMap = {
      tax: 'enterprise-qualification/get-tax-credit-list',
      bond: 'enterprise-qualification/get-credit-rating-list',
      other: 'manage-develop/get-credit-list',
      env: 'manage-develop/get-env-credit-list',
    };
    // eslint-disable-next-line
    return httpClient.post(`/v1/api/dimension/${apiMap[key] || ''}`, params);
  },
  // 双随机抽查
  drclist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-double-random-check-list', params);
  },
  drc(params): Promise<Readonly<any>> {
    return httpClient.get('/company/ProductQualityProblem/doubleRandomCheckDetail', { params });
  },
  // 企业发展
  getBasicInfo(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/ECILocal/GetAnnualReportList', {
      params,
    });
  },
  fetchDevelopFinancingInfo(params): Promise<Readonly<any>> {
    // 数据接口不支持数组 ids
    if (params.ids && params.ids.length) {
      params.ids = params.ids.join(',');
    }
    return httpClient.get('/v1/api/proxy/data/QccSearch/BigSearch/SearchFinancings', {
      params,
    });
  },
  // 获取企业发展-投资机构信息
  fetchDevelopInvestagencyinfo(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/InvestAgency/InvestAgencySearch', {
      params,
    });
  },
  // 企业发展-核心人员
  fetchDevelopMemberinfo(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/Operation/GetDetailOfXiNiu', {
      params,
    });
  },

  // 企业发展-企业业务
  fetchProductinfo(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/Product/GetProductsList', {
      params,
    });
  },
  // 企业发展-竞品信息
  fetchGetCompetitiveProductList(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/Operation/GetDetailOfXiNiu', {
      params,
    });
  },
  // 企业发展-私募基金
  fetchSimuinfo(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/PeFund/GetManagerPublicInfo', {
      params,
    });
  },
  // 企业发展-荣誉资质
  fetchTeclist(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/data/QccSearch/List/TecCertification', {
      params,
    });
  },
  //  基础资质-变更记录
  getChangeList(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-change-list', params);
  },
  // 基础资质-控制企业
  getHoldcollist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-control-list', params);
  },
  // 基础资质-间接持股企业
  getIndirectlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-indirect-list', params);
  },
  // 基础资质-最终受益人
  getBenefitList(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-benefit-list', params);
  },
  // 基础资质-实际控制人
  kzrtupu(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/proxy/graph/SKRGraph/GetSuspectedActualController', { params });
  },
  // 一般纳税人
  taxpayerlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-general-tax-payer-list', params);
  },
  // 产权交易
  transactionlist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-property-transaction-list', params);
  },
  // 工商自主公示-股东及出资信息
  getStockholders(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-stock-holder-list', params);
  },
  getStockChange(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-stock-change-list', params);
  },
  getZzlicens(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-eci-licens-list', params);
  },
  // 司法案件
  caselist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-case-list', params);
  },
  // 司法案件
  zhixinglist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-zhixing-list', params);
  },
  // 询价评估列表
  inquiryEvaluation(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-inquiry-evaluation-list', params);
  },
  // 评估机构列表
  evaluationAgency(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-inquiry-evaluation-agency-list', params);
  },
  // 司法拍卖
  judicialsalelist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-judicial-sale-list', params);
  },
  // 股权冻结
  assistancelist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-assistance-list', params);
  },
  // 立案信息
  lianlist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-lian-list', params);
  },
  // 限制出境
  passportbarrierlist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-limit-list', params);
  },
  // 财产悬赏公告
  propertyrewardlist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-property-reward-list', params);
  },
  // 破产重整
  bankruptcylist(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-bank-ruptcy-list', params);
  },
  // 根据Ids获取司法案件列表
  getCaseListByIds(params) {
    return httpClient.post('/company/get-case-list-by-ids', params);
  },
  // 资产拍卖
  assetsalelist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-asset-sale-list', params);
  },
  // 食品安全
  foodsafetylist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-food-safe-list', params);
  },
  // 关联方认定
  glfrd(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-related-firm-info', params);
  },

  // 集团对外投资
  groupInvestmentDetail(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/group/investment/get-list-by-id', params);
  },

  // 集团投资方
  groupInvesterDetail(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/group/invester/get-list-by-id', params);
  },

  // 获取跳转列表所需的Ids
  getRiskListIds({ keyNo, query, pageSize, pageIndex }): Promise<Readonly<any>> {
    return httpClient.post('/risk/RiskScan/RiskListIds', {
      keyNo,
      query,
      pageSize,
      pageIndex,
    });
  },

  // 违规处理
  weiguilist(params): Promise<Readonly<any>> {
    const keyNo = params.keyNo;
    const isPerson = keyNo && keyNo.startsWith('p');
    const path = isPerson ? '/v1/api/person/get-violation-list' : '/v1/api/company/other/get-violation-list';
    return httpClient.post(path, params);
  },
  // 关联司法案件
  relateCaseList(params) {
    return httpClient.post('/company/get-case-list-by-ids', params);
  },
  // 竞争对手
  competitorlist2(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-competition-list', params);
  },
  // 人员关联企业
  relatedcompanylist(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-investment-list', params);
  },
});
