import _ from 'lodash';

import { HttpClient } from '@/utils/http-client';
import { DetailResponse, ListResponse } from '@/interfaces/response.interface';
import { PersonEntity, AchievementEntity, ConstructionTender, CompanyQualification } from '@/interfaces/construction.entity';

export const removeKeyPrefix = <T extends object>(data: T, prefix: string): T =>
  _.reduce(
    data,
    (map, v, k) => {
      let key = k;
      if (_.startsWith(k, prefix)) {
        key = k.replace(prefix, '');
      }

      map[key] = v;

      return map;
    },
    {} as T
  );

export const createService = (httpClient: HttpClient) => ({
  // company start
  // 人员详情
  getPersonDetail(personId: string) {
    return httpClient.request<DetailResponse<PersonEntity>>({
      url: `/construction/member/search/${personId}`,
      method: 'GET',
    });
  },
  getExpress(id, data): Promise<ListResponse<AchievementEntity>> {
    return httpClient.post(`/construction/express/list/${id}`, data);
  },
  getAchievements(data): Promise<ListResponse<AchievementEntity>> {
    return httpClient.post(`/construction/express/search`, data);
  },
  getTenders(data) {
    return httpClient.request<ListResponse<ConstructionTender>>({
      url: `/construction/tender/search`,
      method: 'POST',
      data,
    });
  },
  getCompanyImportantCustomer(id, data): Promise<Readonly<any>> {
    return httpClient.post(`/construction/customer/list/${id}`, data);
  },
  getCompanyPersons(companyId, data): Promise<ListResponse<PersonEntity>> {
    return httpClient.post(`/construction/member/list/${companyId}`, data);
  },
  getCompanyQualifications(companyId, data?): Promise<ListResponse<CompanyQualification>> {
    return httpClient.post(`/construction/qualification`, {
      ...data,
      companyId,
    });
  },
  getPositiveCertificateDetail(companyId): Promise<ListResponse<any>> {
    return httpClient.get(`/construction/positive/certificate/details/${companyId}`);
  },
  getPositiveTech(companyId, data): Promise<ListResponse<any>> {
    return httpClient.post(`/construction/positive/tech/${companyId}`, data);
  },
  getCompanyRecordPlace(id): Promise<Readonly<any>> {
    return httpClient.get(`/construction/record/place/${id}`);
  },
  getCompanyTenderByFilter(id, data): Promise<Readonly<any>> {
    return httpClient.post(`/construction/tender/list/${id}`, data);
  },
  getCompanyManageSystems(companyId, data): Promise<ListResponse<any>> {
    return httpClient.post(`/construction/positive/certificate/${companyId}`, data);
  },
  getCompanyAwards(companyId, data): Promise<ListResponse<any>> {
    return httpClient.post(`/construction/awards/list/${companyId}`, data);
  },
  getPositiveRank(companyId, data): Promise<ListResponse<any>> {
    return httpClient.post(`/construction/positive/rank/${companyId}`, data);
  },
  getPositiveCredit(companyId): Promise<ListResponse<any>> {
    return httpClient.get(`/construction/positive/credit/${companyId}`);
  },
});
