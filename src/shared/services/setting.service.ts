import qs from 'querystring';
import { cloneDeep } from 'lodash';

import type { HttpClient } from '@/utils/http-client';
import { useRoute } from 'vue-router/composables';

export const SETTING_BASE = '/settings';
export const SETTING_INFO = `${SETTING_BASE}`;
export const UPDATE_BASIC_SETTING = `${SETTING_BASE}/basic`;
export const UPDATE_RISK_SETTING = `${SETTING_BASE}/risk`;
export const UPDATE_PARTNER_SETTING = `${SETTING_BASE}/partner`;
export const UPDATE_INNERBLACKLIST_SETTING = `${SETTING_BASE}/innerBlacklist`;
export const UPDATE_OUTERBLACKLIST_SETTING = `${SETTING_BASE}/outerBlacklist`;
export const UPDATE_CONFLICT_SETTING = `${SETTING_BASE}/conflict`;
export const UPDATE_QUALIFICATION_SETTING = `${SETTING_BASE}/qualification`;
export const RISKFILTER_SORT = `${SETTING_BASE}/move`;
export const DILIGENCE_SETTING = `${SETTING_BASE}/configuration`;
export const MONITOR_NOTIFY = `${SETTING_BASE}/user/configuration/monitor_notify`;
export const TENDER_RISK = `${SETTING_BASE}/configuration/tender_risk`;
export const SETTING_COMPARE = `/settings/compare`;
export const MONITOR_SETTING = `${SETTING_BASE}/monitor`;
export const MONITOR_SETTING_REST = `${SETTING_BASE}/monitor/restore`;
export const GENERATERSAKEY = '/user_configuration/monitor_notify2/generatesecretKey';
export const INTERFACEPUSHDEMO = '/monitor/interface_push_demo';
export const RECOVERYDILIGENCESETTING = `${SETTING_BASE}/risk/restoreById`;
export const DILIGENCE_DEFAULT_SETTING = `/user_configuration/diligence_default_setting`;

function transformSettingResponse(response: Record<string, any>) {
  const { content } = response;
  const { version: v, ...settings } = content;
  return settings;
}

const transformSetting = (result: Record<string, any>) => {
  const data = cloneDeep(result).content;
  return data;
};

export const createService = (httpClient: HttpClient) => ({
  async info(params = {}): Promise<any> {
    const res = await httpClient.get(SETTING_INFO, { params });
    return res.content;
  },
  async history(params = {}): Promise<any> {
    const res = await httpClient.get(SETTING_INFO, { params });
    const content = transformSettingResponse(res);
    return {
      ...res,
      content: { ...res.content, ...content },
    };
  },
  // template groupVersion v1普通 v2蔡司
  async template(groupVersion: string) {
    return httpClient.get(`/settings/system?${qs.stringify({ type: 'diligence_risk', groupVersion })}`);
  },
  // 多模板 列表
  async list(data = {}): Promise<any> {
    const res = await httpClient.post('/settings/multi/list', data);
    return res;
  },
  // 多模板 新增
  async create(data): Promise<any> {
    const res = await httpClient.post('/settings/multi/add', {
      ...data,
      content: {
        ...transformSetting(data),
        version: data?.content.version || data.groupVersion,
      },
      id: undefined,
      systemSettingsId: data.systemSettingsId || data.id,
    });
    return res;
  },
  // 多模板 编辑
  async edit(data): Promise<any> {
    return httpClient.post('/settings/multi/update', {
      ...data,
      content: {
        ...transformSetting(data),
        version: data?.content.version || data.groupVersion,
      },
    });
  },
  async remove(id): Promise<any> {
    return httpClient.post(`/settings/multi/delete?id=${id}`);
  },
  async setDefaultModel(version): Promise<any> {
    return httpClient.post(DILIGENCE_DEFAULT_SETTING, {
      settingVersion: version,
    });
  },
  async getDefaultModel(): Promise<any> {
    return httpClient.get(DILIGENCE_DEFAULT_SETTING);
  },
  async getUpgradeInfo(params = { type: 'diligence_risk' } as any): Promise<any> {
    const res = await httpClient.post(`${SETTING_COMPARE}?${qs.stringify(params)}`);
    if (params.type === 'diligence_risk') {
      res.content = transformSettingResponse(res);
    }
    return res;
  },

  async update(data): Promise<any> {
    return httpClient.post(SETTING_INFO, {
      ...data,
      content: {
        ...transformSetting(data),
        version: data?.content.version || data.groupVersion,
      },
    });
  },
  async sort(data): Promise<any> {
    return httpClient.post(`/settings/multi/sort`, data);
  },
  updateBasicSetting(data): Promise<any> {
    return httpClient.post(UPDATE_BASIC_SETTING, data);
  },
  updateRiskSetting(data): Promise<any> {
    return httpClient.post(UPDATE_RISK_SETTING, data);
  },
  updatePartnerSetting(data): Promise<any> {
    return httpClient.post(UPDATE_PARTNER_SETTING, data);
  },
  updateInnerBlackListSetting(data): Promise<any> {
    return httpClient.post(UPDATE_INNERBLACKLIST_SETTING, data);
  },
  updateOuterBlackListSetting(data): Promise<any> {
    return httpClient.post(UPDATE_OUTERBLACKLIST_SETTING, data);
  },
  updateConflictSetting(data): Promise<any> {
    return httpClient.post(UPDATE_CONFLICT_SETTING, data);
  },
  updateQualificationSetting(data): Promise<any> {
    return httpClient.post(UPDATE_QUALIFICATION_SETTING, data);
  },
  sortRiskFilterList(data): Promise<any> {
    return httpClient.post(RISKFILTER_SORT, data);
  },
  getDiligenceSetting(): Promise<any> {
    return httpClient.get(DILIGENCE_SETTING);
  },
  updateDiligenceSetting(data): Promise<any> {
    return httpClient.post(DILIGENCE_SETTING, data);
  },
  getMoniTorSetting(type = MONITOR_NOTIFY): Promise<any> {
    return httpClient.get(`/user_configuration/${type}`);
  },
  updateMoniTorSetting(data): Promise<any> {
    return httpClient.post('/user_configuration/monitor_notify2', data);
  },
  async getTenderSetting(params = {}): Promise<any> {
    const res = await httpClient.get('/settings?type=tender_risk', { params });
    return res.content;
  },
  getTenderSettingHistory(params = {}, type = 'tender_risk'): Promise<any> {
    return httpClient.get(`/settings?type=${type}`, { params });
  },
  saveTenderSetting(data, type = 'tender_risk'): Promise<any> {
    return httpClient.post(`/settings?type=${type}`, data);
  },
  getMonitorSetting(id?): Promise<any> {
    const idStr = id ? `?id=${id}` : '';
    return httpClient.get(`${MONITOR_SETTING}${idStr}`);
  },
  saveMonitorSetting(data): Promise<any> {
    return httpClient.post(`${MONITOR_SETTING}`, data);
  },
  // 恢复出厂设置
  resetMonitorSetting(): Promise<any> {
    return httpClient.post(`${MONITOR_SETTING_REST}`);
  },

  /** 舆情监控设置 */
  getMonitorNewsSetting(): Promise<any> {
    return httpClient.get(`/settings/monitor_news`);
  },

  /** 更新舆情监控设置 */
  updateMonitorNewsSetting(data): Promise<any> {
    return httpClient.post(`/settings/monitor_news`, data);
  },

  /** 接口推送设置生成密钥 */
  generateRSAKey(): Promise<any> {
    return httpClient.get(`${GENERATERSAKEY}`);
  },

  /** 接口推送模拟 */
  interfacePushTest(data): Promise<any> {
    return httpClient.post(`${INTERFACEPUSHDEMO}`, data);
  },

  /** 恢复初始设置 */
  recoverydiligenceSetting(id): Promise<any> {
    return httpClient.post(`${RECOVERYDILIGENCESETTING}?id=${id}`);
  },

  /** 获取模型列表 */
  getModelLists(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/getRiskModelList`, params);
  },
  /** 获取模型列表 */
  getModelCopyLists(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/getRiskModelBranckList`, params);
  },

  /** 获取模型详情 */
  getModelDetail(id): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.get(`/${type}/getRiskModelDetail/${id}`);
  },

  /** 检测修改的是否是发布状态 */
  checkEditStatus(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/editable/check`, params);
  },
  /** copy模型 */
  copyModelStrategy(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/copy`, params);
  },
  /** 客户端模型状态变更 */
  updateModelStatus(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/turn`, params);
  },
  /** 发布模型 */
  publishModel(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/publish`, params);
  },

  /** 废弃模型 */
  deprecate(id): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/deprecateModel/${id}`);
  },

  /** 模型名字和定量指标setting更新 */
  editNameSetting(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    return httpClient.post(`/${type}/edit/resultSetting`, params);
  },

  /**
   * params: {
   *  modelId: number,
   *  strategyIds: [number]
   * }
   */
  async getDimensionHitStrategies(params): Promise<any> {
    const type =
      window.location.pathname.includes('investigation') || window.location.pathname.includes('risk-model')
        ? 'risk-model'
        : 'monitor-model';
    const res = await httpClient.post(`/${type}/getDimensionHitStrategies`, params);
    // 保证组件使用，处理格式
    return res.map((item) => {
      return {
        dimensionHitStrategyEntity: item,
        dimensionStrategyId: item.strategyId,
      };
    });
  },

  /** 更新metric数据,主要是hitStrategy的数据 */
  updateMetricData(params): Promise<any> {
    return httpClient.post(`/metric/update`, params);
  },
  searchMetricData(params): Promise<any> {
    return httpClient.post(`/metric/search`, params);
  },
  /** 更新metra数据,主要是hitStrategyField的数据 */
  updateStrategyField(params): Promise<any> {
    return httpClient.post(`/dimension/strategy/fields/edit`, params);
  },
  /**
   * 检查模型是否可以被废弃
   */
  checkModelDepreacate(params): Promise<any> {
    // 尽调模型均可直接删除
    const { modelType, modelId } = params;
    if (modelType === 1) {
      return Promise.resolve({
        canDeprecate: true,
      });
    }
    return httpClient.post(`/monitor-model/check/deprecate`, {
      riskModelId: modelId,
    });
  },
});
