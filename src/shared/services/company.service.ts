import { upperFirst, omitBy, compact, isEmpty } from 'lodash';

import { HttpClient } from '@/utils';
import { Relation } from '@/interfaces/data.interface';
import { removeRMBFromCurrency, transformCompanyListLogo } from '@/utils/company';
import useStorageWithExpire from '@/utils/storage-with-expire';
import { CompanyLogo } from '@/interfaces/company.interface';

const COMPANY_SEARCH = '/company/search';
const COMPANY_SEARCH_LITEV3 = '/company/search/liteV3';
const COMPANY_MATCH = '/company/GetCompaniesWithFreeText';
const COMPANY_SEARCH_QCC = '/company/searchForQcc';

export const createService = (httpClient: HttpClient) => ({
  async getDetail(params, config = {}): Promise<Readonly<any>> {
    const isOrganism = String(params.keyNo).startsWith('s');
    if (isOrganism) {
      const { Result = {} } = await httpClient.get(`/company/${params.keyNo}/organism`);
      const data = {
        KeyNo: params.keyNo,
        Oper: Result.DJInfo?.Oper || {},
        MultipleOper: {
          OperType: Result.DJInfo?.Oper?.OperType,
          OperList: Result.DJInfo?.Oper ? [Result.DJInfo?.Oper] : [],
        },
        Address: Result.DJInfo?.address,
        ...Object.entries(Result.DJInfo).reduce((acc, [k, v]) => ({ ...acc, [upperFirst(k)]: v })),
        ...Result,
        CertDate: Result.DJInfo?.certificatePeriod || '',
        ShortStatus: Result.DJInfo?.status || '',
        Type: 1,
      };
      return data;
    }
    const res = await httpClient.get(`/company/${params.keyNo}/qcc`);
    return {
      ...res,
      // VTList为空时接口会返回空对象
      VTList: isEmpty(res.VTList) ? [] : res.VTList,
    };
  },
  async getLogoByKeyNos(params) {
    const { getStorage, setStorage } = useStorageWithExpire<CompanyLogo[]>('COMPANY_LOGO');
    let keyNos = params || [];
    const localLogos = getStorage() || [];
    // 过滤请求参数中已存在本地的值
    const data = keyNos.map((k) => localLogos.find((el) => el.KeyNo === k));
    const storedKeyNos = compact(data);
    // 如果所有请求的logo都可以从本地读取，直接返回本地数据
    if (storedKeyNos.length === keyNos.length) {
      return localLogos.filter((el) => keyNos.includes(el.KeyNo));
    }
    // 如果部分可在本地读取，则将剩余的keyNo过滤出来继续请求接口
    keyNos = keyNos.filter((k) => !localLogos.find((el) => el.KeyNo === k));

    const { Result = [] } = await httpClient.post('/company/getLogoByKeyNos', { keyNos });
    // 将新的数据存在本地（有的keyNo接口查不到，为防止这些keyNo重复调用接口，一并存在本地）
    setStorage(localLogos.concat(keyNos.map((k) => Result.find((el) => el.KeyNo === k) || { KeyNo: k })));
    return Result.concat(storedKeyNos);
  },
  async getCompanyDetail(id): Promise<Readonly<any>> {
    const res = await httpClient.get(`/company/${id}/qcc`);
    return {
      ...res,
      VTList: isEmpty(res.VTList) ? [] : res.VTList,
    };
  },
  getCompanyList(params): Promise<Readonly<any>> {
    return httpClient.post(COMPANY_SEARCH, { ...params });
  },
  async getCompanyListQCC(params): Promise<Readonly<any>> {
    const res = await httpClient.post(COMPANY_SEARCH_QCC, { ...params });
    res.Result = await transformCompanyListLogo(isEmpty(res.Result) ? [] : res.Result);
    return res;
  },
  // NOTE: 接口已下线
  // getPersonList(params): Promise<Readonly<any>> {
  //   return httpClient
  //     .get('/proxy/api/person/searchV2', {
  //       params,
  //     })
  //     .then((res: any) => res.Result);
  // },

  batchGetCompanyRelations(groups: [string, string][]): Promise<
    Array<{
      companyId1: string;
      companyId2: string;
      relationships: {
        results?: [Relation];
      };
    }>
  > {
    return httpClient.request({
      url: '/company/findRelationships',
      method: 'POST',
      data: {
        companyGroups: groups.map(([companyId1, companyId2]) => {
          return {
            companyId1,
            companyId2,
          };
        }),
      },
    });
  },
  // 获取营业执照作废
  getLicenseStatement(params) {
    return httpClient.post('/proxy/api/QccSearch/List/LicenseStatement', params);
  },
  searchLite(params = {}) {
    const defaultParams = {
      // "searchKey": "中国",
      pageSize: 20,
    };
    return httpClient
      .post(COMPANY_SEARCH_QCC, {
        ...defaultParams,
        ...params,
        isHighlight: true,
      })
      .then(({ Result = [] }) => transformCompanyListLogo(Result));
  },
  // 通过关键字匹配公司列表
  async matchCompany(params) {
    const res = await httpClient.post(COMPANY_MATCH, params);
    const cList = res.Result;
    const resData: Record<string, any[]> = {
      normalErr: [],
      slurErr: [], // AI解析不是公司的情况
      rupRight: [], // 解析成功，但是存在重名企业
      right: [],
    };

    cList.forEach((cItem, index) => {
      // 保存序号
      cItem.sortIndex = index;
      // StatusCode表示匹配结果  1 匹配成功（新版含模糊匹配出来的）  2 海外不匹配  3 AI解析不是公司 4 AI解析为公司但是搜索库未查到信息 5 换行符
      if (cItem.StatusCode === 1) {
        // if (cItem.LinkCompany.length > 1) {
        //   // 重名企业
        //   resData.rupRightList.push(cItem)
        // } else

        cItem.hasEntSameName = cItem.LinkCompany.length > 1;
        if (cItem.IsLinked || ['product', 'stock'].includes(cItem.Type)) {
          // 存在模糊匹配
          cItem.initMatchType = cItem.IsLinked && !cItem.LinkedCompany?.Linked ? '失败匹配' : '模糊匹配';
          resData.slurErr.push(cItem);
        } else {
          // 精准匹配
          cItem.initMatchType = '精准匹配';
          resData.right.push(cItem);
        }
      } else {
        // 错误排序
        if (cItem.StatusCode === 2 || cItem.StatusCode === 4) {
          cItem.initMatchType = '失败匹配';
          resData.normalErr.push(cItem);
        }
        if (cItem.StatusCode === 3) {
          // 存在模糊匹配
          cItem.initMatchType = '失败匹配';
          resData.slurErr.push(cItem);
        }
      }

      // 分类排序字段：1、2（重名）、3、4
      cItem.sortGroup = 4;
      if (cItem.initMatchType === '失败匹配') {
        cItem.sortGroup = 1;
      } else if (cItem.hasEntSameName) {
        cItem.sortGroup = 2;
      } else if (cItem.initMatchType === '模糊匹配') {
        cItem.sortGroup = 3;
      }
    });
    return resData;
  },
  // 文本模糊匹配
  async fuzzyMatchCompany(params) {
    const res = await httpClient.post('/company/GetLinkCompaniesWithFreeText', params);
    return res.Result || [];
  },
  getCompanyRelation(data) {
    return httpClient.post('/graph/rover/relations', data);
  },
  getCompanyLiteV3(data) {
    return httpClient.post(COMPANY_SEARCH_LITEV3, data).then(({ Result = [] }) => {
      return Result.map((item) => {
        const reccap = removeRMBFromCurrency(item.reccap);
        const registcapi = removeRMBFromCurrency(item.registcapi);
        return {
          ...item,
          reccap,
          registcapi,
        };
      });
    });
  },
  getCompanyDepartList() {
    return httpClient.post('/customers/aggregation?key=customerDepartment');
  },

  getCompanyPersonList() {
    return httpClient.post('/customers/aggregation?key=principal');
  },

  // 证书汇总（用于卡片展示）
  getCertificationList(keyNo, payload?) {
    const sy = Object.keys(omitBy(payload, (v) => !v))
      .map((k) => `&${k}=${payload[k]}`)
      .join('');

    return httpClient.get(`/company/certification/summary?keyNo=${keyNo}${sy}`);
  },

  // 证书列表（用于表格展示）
  getCertificationTable(data) {
    return httpClient.post(`/company/certification/list`, data);
  },

  // 证书详情
  async getCertificationDetail(id) {
    const res = await httpClient.get(`/company/certification/detail?id=${id}`);
    return res.Result || {};
  },

  // 电信资质证书详情
  async getTelecomCertificationDetail(id) {
    const res = await httpClient.get(`/company/telecomLicense/detail?id=${id}`);
    return res.Result || {};
  },

  // 信用汇总（用于卡片展示）
  getCreditList(id) {
    return httpClient.get(`/company/credit/summary?keyNo=${id}`);
  },

  // 证书列表（用于表格展示）
  getCreditTable(data) {
    return httpClient.post(`/company/credit/list`, data);
  },
  // 根据公司搜索对应的供应商客户
  getSupplierCustomer(data) {
    return httpClient.post(`/company/GetSupplierCustomerWithFreeText`, data);
  },
});
