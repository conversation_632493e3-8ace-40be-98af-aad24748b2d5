import { HttpClient } from '@/utils/http-client';

export const createService = (httpClient: HttpClient) => ({
  searchAdvance(params): Promise<Readonly<any>> {
    return httpClient.get('/company/searchAdvance', {
      params,
    });
  },
  getSameContacts(params): Promise<Readonly<any>> {
    return httpClient.get('/company/getSameContacts', {
      params,
    });
  },
  searchMind(params): Promise<Readonly<any>> {
    return httpClient.post('/comprehensive/search', { ...params });
  },
  /**
   * 查询消息列表
   * * */
  messageSearch(params): Promise<Readonly<any>> {
    return httpClient.post('/message/search', { ...params });
  },
  /**
   * 司法案件搜索
   * * */
  caseSearch(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/search/search-case', params);
  },
  /**
   * 查询消息数量
   * * */
  messageCount(params): Promise<Readonly<any>> {
    return httpClient.post('/message/count', { ...params });
  },
  /**
   * 消息已读
   * * */
  singleRead(id): Promise<Readonly<any>> {
    return httpClient.post(`/message/read/${id}`);
  },
  /**
   * 消息已读
   * * */
  readAll(): Promise<Readonly<any>> {
    return httpClient.post('/message/read_all');
  },
});
