import { HttpClient } from '@/utils/http-client';
import { getRelatedTrendsByCompanyHashKey, transformCompanyImageUrl } from '@/utils/company';

export interface BasePaginationRequest {
  pageIndex: number;
  pageSize: number;
}

export interface BasePaginationResponse<T> {
  pageIndex: number;
  pageSize: number;
  total: number;
  data: T[];
}

type RiskMonitorGroup = {
  monitorGroupId: number;
  orgId: number;
  ownerId: number;
  changesCount: number;
  companyCount: number;
  monitorStatus: number;
  name: string;
  pushMethod: string;
  monitorModelId: number;
  pushFrequency: number;
  pushHourAt: number;
  order: number;
  comment: null | string;
  updateDate: string;
  createDate: string;
  product: string;
};

type GroupsResponse = BasePaginationResponse<RiskMonitorGroup>;

type AddGroupRequest = {
  groupName: string;
  monitorModelId: number;
};

type UpdateGroupRequest = {
  name: string;
  pushMethod: string;
  monitorModelId: number;
  pushHourAt: number;
};

type MonitorCompanyEntity = {
  companyId: string;
  companyName: string;
  companyCode?: string;
};

type AddCompanyToGroupRequest = {
  monitorGroupId: number;
  items: MonitorCompanyEntity[];
};

type RemoveCompanyFromGroupRequest = {
  monitorGroupId: number;
  companyIds?: MonitorCompanyEntity['companyId'][];
  monitorCompanyIds?: MonitorCompanyEntity['companyId'][];
};

type MoveCompanyFromGroupRequest = {
  oldGroupId: number;
  newGroupId: number;
  companyIds: MonitorCompanyEntity['companyId'][];
};
export interface SearchCompaniesRequest extends BasePaginationRequest {
  groupId: number;
}

interface SearchDynamicsRequest extends BasePaginationRequest {
  groupId: number;
}

export const createService = (httpClient: HttpClient) => ({
  /**
   * 获取全部metrics
   */
  async getAllMetrics(params) {
    const res = await httpClient.post<GroupsResponse>('/metric/search', params);
    return res;
  },
  /**
   * 获取全部分组
   */
  async getAllGroups(params: BasePaginationRequest) {
    const res = await httpClient.post<GroupsResponse>('/monitor/group/list', params);
    return res;
  },

  /**
   * 添加分组
   */
  addGroup(params: AddGroupRequest): Promise<Readonly<any>> {
    return httpClient.post('/monitor/group/add', params);
  },

  /**
   * 更新分组
   */
  updateGroup(params: UpdateGroupRequest): Promise<Readonly<any>> {
    return httpClient.post('/monitor/group/update', params);
  },

  removeGroup(monitorGroupId): Promise<Readonly<any>> {
    return httpClient.post(`/monitor/group/remove`, { monitorGroupId });
  },
  /**
   * 添加公司至分组
   */
  addCompanyToGroup(params: AddCompanyToGroupRequest): Promise<Readonly<any>> {
    return httpClient.post('/monitor/company/add', params);
  },

  /**
   * 从分组中移除公司
   */
  removeCompanyFromGroup(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/company/delete', params);
  },

  /**
   * 从分组中移除公司
   */
  removeAllCompany(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/company/search/removeAll', params);
  },

  /**
   * 从分组中移除公司
   */
  moveCompanyFromGroup(params: MoveCompanyFromGroupRequest): Promise<Readonly<any>> {
    return httpClient.post('/monitor/company/transfer', params);
  },

  /**
   * 从分组中移除公司
   */
  removeAllRelated(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/company/relatedSearch/removeAll', params);
  },

  /**
   * 通过企业hashKey去查找公司dispalyContent,判断展示关联动态的内容
   */
  async queryRelatedTrendsByCompanyHashKey(hashKeyList): Promise<Readonly<any>> {
    return httpClient.post('/monitor/dynamic/searchDynamicHashKey', hashKeyList);
  },

  /**
   * 企业搜索
   */
  async searchCompanies(params: SearchCompaniesRequest): Promise<Readonly<any>> {
    const res = await httpClient.post('/monitor/company/search', params);
    const resImg = await transformCompanyImageUrl(res);
    return getRelatedTrendsByCompanyHashKey(resImg);
  },

  /**
   * 查询指标指定策略id的动态内容列表 监控动态内容查看更多的弹窗显示数据
   */
  async getDynamicContentList(params): Promise<Readonly<any>> {
    const res = await httpClient.post('/monitor/dynamic/content/search', params);
    return {
      ...res,
      Result: res.data || [],
      Paging: {
        PageIndex: res.pageIndex,
        PageSize: res.pageSize,
        TotalRecords: res.total,
      },
    };
  },

  /**
   * 企业搜索关联方查询
   */
  async searchRelatedCompanies(params: SearchCompaniesRequest): Promise<Readonly<any>> {
    const res = await httpClient.post('/monitor/company/related/search', params);
    res?.data?.forEach((item) => {
      item.companyName = item.monitorCompanyEntity.companyName;
      item.companyId = item.monitorCompanyEntity.companyId;
      item.riskLevel = item.monitorCompanyEntity.riskLevel;
    });
    return transformCompanyImageUrl(res);
  },

  /**
   * 监控动态搜索
   */
  async searchDynamics(params): Promise<Readonly<any>> {
    const res = await httpClient.post('/monitor/dynamic/search', params);
    return transformCompanyImageUrl(res);
  },

  /**
   * 获取关联方列表
   */
  getRelatedCompany(data): Promise<Readonly<any>> {
    return httpClient.post('/monitor/companyRelatedList', data);
  },
  /**
   * 添加关联方
   */
  addRelatedCompany(data): Promise<Readonly<any>> {
    return httpClient.post('/monitor/company/addRelated', data);
  },
  /**
   * 监控全部关联方
   */
  monitorAllRelated(data): Promise<Readonly<any>> {
    return httpClient.post('/monitor/addAllMonitorCompanyRelated', data);
  },
  /**
   * 监控全部关联方总数
   */
  getUnMonitorCompanyRelatedCount(data): Promise<Readonly<any>> {
    return httpClient.post('/monitor/getUnMonitorCompanyRelatedCount', data);
  },

  /**
   * 监控企业动态有变化的关联方
   */
  addAllRelatedMonitorCompanyRelated(data): Promise<Readonly<any>> {
    return httpClient.post('/monitor/relatedDynamic/addAllMonitorCompanyRelated', data);
  },
  /**
   * 监控企业动态有变化的关联方总数
   */
  getRelatedUnMonitorCompanyRelatedCount(data): Promise<Readonly<any>> {
    return httpClient.post('/monitor/relatedDynamic/getUnMonitorCompanyRelatedCount', data);
  },

  /**
   * 监控详情获取公司详情
   */
  async getCompanyDetail(companyId): Promise<Readonly<any>> {
    const res = await httpClient.get(`/company/${companyId}/qcc`);
    const imgURLData = await transformCompanyImageUrl({
      data: [
        {
          companyId,
        },
      ],
    });
    return {
      ...res,
      ...imgURLData.data[0],
    };
  },

  /**
   * 获取监控动态总览数据
   */
  getMonitorOverviewData(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/dynamic/aggs/card', params);
  },

  /**
   * 今日高风险动态分析
   * @param params
   */
  getHighRiskChart(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/dynamic/chart/highRisk', params);
  },

  /**
   * 获取动态的概要分析
   * @param params
   */
  getChartSummary(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/dynamic/chart/summary', params);
  },
  /**
   * 获取动态的思维导图数据
   * @param params
   */
  getTreeChartData(params): Promise<Readonly<any>> {
    return httpClient.post('/monitor/dynamic/search/related/chart', params);
  },

  /** 添加跟进记录 */
  async addFollowUp(data) {
    return httpClient.post('/monitor/dynamic/handle', data);
  },

  /** 查询跟进记录 */
  async getFollowUpList(data) {
    return httpClient.post('/monitor/dynamic/remark', data);
  },

  /** 根据关联方企业获取关联详情 */
  async getRelatedDetail(data) {
    return httpClient.post('/monitor/relatedSearch/relatedDetail', data);
  },

  // getGroupList(params): Promise<Readonly<any>> {
  //   return httpClient.get('/group/all', { params });
  // },

  // queryGroup(groupId): Promise<Readonly<any>> {
  //   return httpClient.get(`/group/query/${groupId}`);
  // },

  // addGroup(params): Promise<Readonly<any>> {
  //   return httpClient.post('/group/add', params);
  // },

  // updateGroup(groupId, params): Promise<Readonly<any>> {
  //   return httpClient.post(`/group/edit/${groupId}`, params);
  // },

  // searchRecycle(params): Promise<Readonly<any>> {
  //   return httpClient.post('/group/recycle/search', params);
  // },

  // recoverGroup(params): Promise<Readonly<any>> {
  //   return httpClient.post('/group/recycle/recover', params);
  // },

  // // 推荐监控
  // getRecommendList(params): Promise<Readonly<any>> {
  //   return httpClient.post('/group-risk/robot-auto-list', params);
  // },

  // // 关键词查询
  // searchWithGroup(params): Promise<any> {
  //   const { groupType, searchKey } = params;
  //   if (groupType === 1) {
  //     return httpClient.get('/company/searchWithGroup', { params: { searchKey } });
  //   }
  //   if (groupType === 2) {
  //     return httpClient.get('/company/personWithGroup', { params: { searchKey } });
  //   }
  //   return Promise.resolve();
  // },

  // /**
  //  * 自选列表
  //  */
  // getZxList(params): Promise<any> {
  //   const { groupType, ...rest } = params;
  //   if (groupType === 1) {
  //     return httpClient.post('/group-item/detail/company', rest);
  //   }
  //   if (groupType === 2) {
  //     return httpClient.post('/group-item/detail/user', rest);
  //   }
  //   return Promise.resolve();
  // },

  // // 添加自选(批量)
  // addMonitor(params): Promise<any> {
  //   const { groupType, ...rest } = params;
  //   if (groupType === 1) {
  //     return httpClient.post('/group-item/add/company', rest);
  //   }
  //   if (groupType === 2) {
  //     return httpClient.post('/group-item/add/user', rest);
  //   }
  //   return Promise.resolve();
  // },

  // // 移动自选
  // updateMonitor(params): Promise<any> {
  //   const { groupType, ...rest } = params;
  //   if (groupType === 1) {
  //     return httpClient.post('/group-item/change/transfer/company', rest);
  //   }
  //   if (groupType === 2) {
  //     return httpClient.post('/group-item/change/transfer/user', rest);
  //   }
  //   return Promise.resolve();
  // },

  // // 批量移动自选
  // batchUpdateMonitor(params): Promise<any> {
  //   const { groupType, ...rest } = params;
  //   if (groupType === 1) {
  //     return httpClient.post('/group-item/change/batch/company', rest);
  //   }
  //   if (groupType === 2) {
  //     return httpClient.post('/group-item/change/batch/user', rest);
  //   }
  //   return Promise.resolve();
  // },

  // // 删除自选(批量)
  // removeMonitor(params): Promise<any> {
  //   const { groupType, ...rest } = params;
  //   if (groupType === 1) {
  //     return httpClient.post('/group-item/delete/company', rest);
  //   }
  //   if (groupType === 2) {
  //     return httpClient.post('/group-item/delete/user', rest);
  //   }
  //   return Promise.resolve();
  // },

  // // 组内关键词match
  // matchGroupItem(params): Promise<Readonly<any>> {
  //   return httpClient.post('/group-item/match', params);
  // },

  // /**
  //  * 监控动态
  //  */
  // getDynamicList(params): Promise<Readonly<any>> {
  //   const { groupId, pageSize = 20, ...rest } = params;
  //   return httpClient.post(`/group-risk/get-dynamic-list/${groupId}`, { ...rest, pageSize });
  // },

  // // 动态汇总
  // getDynamicSummary(params): Promise<Readonly<any>> {
  //   const { groupId } = params;
  //   return httpClient.post(`/group-risk/get-dynamic-summary/${groupId}`);
  // },

  // /**
  //  * 风控模型
  //  */
  // // 风险自定义视图
  // getRiskView(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/risk-view/${groupId}`, rest);
  // },

  // // 设置风险规则
  // setRiskSwitch(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/risk-switch/${groupId}`, rest);
  // },
  // setRiskRule(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/risk-setting/${groupId}`, rest);
  // },
  // setCaseRiskRule(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/case-risk-setting/${groupId}`, rest);
  // },

  // // 风险设置恢复默认
  // recoverRiskRule(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/risk-default/${groupId}`, rest);
  // },

  // // 舆情自定义视图
  // getNewsView(params): Promise<Readonly<any>> {
  //   const { groupId } = params;
  //   return httpClient.post(`/group-risk/news-view/${groupId}`);
  // },

  // // 设置舆情规则
  // setNewsRule(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/news-setting/${groupId}`, rest);
  // },

  // // 舆情设置恢复默认
  // recoverNewsRule(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/news-default/${groupId}`, rest);
  // },

  // /**
  //  * 推送设置
  //  */
  // getPushDetail(params): Promise<Readonly<any>> {
  //   const { groupId } = params;
  //   return httpClient.post(`/group-risk/push-detail/${groupId}`);
  // },

  // // 更新推送设置
  // setPushDetail(params): Promise<Readonly<any>> {
  //   const { groupId, ...rest } = params;
  //   return httpClient.post(`/group-risk/push-setting/${groupId}`, rest);
  // },

  // /**
  //  * 监控报告
  //  */
  // getReportList(params): Promise<Readonly<any>> {
  //   return httpClient.post(`/group-risk/get-report-list`, params);
  // },

  // getReportDetail(params): Promise<Readonly<any>> {
  //   return httpClient.post(`/group-risk/get-report-detail`, params);
  // },

  // getReportDetailBatch(params): Promise<Readonly<any>> {
  //   return httpClient.post(`/group-risk/get-report-detail-batch`, params);
  // },

  // // 获取工作台统计信息
  // getDashboardSummary(): Promise<Readonly<any>> {
  //   return httpClient.get(`/group-dashboard/summary`);
  // },

  // // 获取自选工作台监控日报
  // getDashboardReport(): Promise<Readonly<any>> {
  //   return httpClient.get(`/group-dashboard/daily`);
  // },

  // // 获取自选工作台日报列表
  // getDashboardReportList(params): Promise<Readonly<any>> {
  //   return httpClient.get(`/group-dashboard/daily-list`, { params });
  // },

  // // 获取统计数据
  // getDynamicAnalysis(params): Promise<Readonly<any>> {
  //   return httpClient.post(`/group-dashboard/dynamic-summary`, params);
  // },

  // // 获取最新动态
  // getDashboardDynamic(params = {}): Promise<Readonly<any>> {
  //   return httpClient.get('/group-dashboard/dynamic', { params });
  // },
});
