import { HttpClient } from '@/utils/http-client';

export const MESSAGE_SEARCH = '/message/search';
export const MESSAGE_COUNT = '/message/count';
export const MESSAGE_READ = '/message/read';
export const MESSAGE_READ_ALL = 'message/read_all';

export const createService = (httpClient: HttpClient) => ({
  /**
   * 搜索
   */
  search(data, skipInterceptor = false) {
    return httpClient.post(MESSAGE_SEARCH, data, {
      skipInterceptor: skipInterceptor ? ['auth', 'error'] : [],
    });
  },
  /**
   * 已读
   */
  read(messageId) {
    return httpClient.post(`${MESSAGE_READ}/${messageId}`);
  },
  /**
   * 批量设置已读
   */
  readAll(messageType?: number) {
    return httpClient.post(MESSAGE_READ_ALL, {
      msgType: messageType,
    });
  },
});
