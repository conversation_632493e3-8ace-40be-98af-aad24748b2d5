import { IUserProfile } from '@/interfaces';
import { HttpClient, cache } from '@/utils/http-client';

const USER_BUNDLE = `/enterprise/bundle`;
const USER_ORG = `/enterprise/organization/getOrg`;
const USER_SET_ORG = `/enterprise/organization/updateOrg`;
const USER_SET_ADMIN = `/enterprise/organization/updateAdmin`;
const USER_DEPS = `/enterprise/departments`;
const USER_ADD_DEP = `/enterprise/departments`;
const USER_SEARCH = `/enterprise/users`;
const USER_ROLES = `/enterprise/roles`;
const USER_ADD_ROLE = `/enterprise/role/addRole`;
const USER_PERMISSIONS = `/enterprise/role/getPermissions`;
const USER_PROFILE_INFO = `/enterprise/profile`;
const UPDATE_ORG = `/enterprise/organization/updateOrg`;
const UPDATE_NICKNAME = `/enterprise/account/updateName`;
const UPDATE_EMAIL = `/enterprise/account/updateEmail`;
const UPDATE_CODE = `/enterprise/account/sendUpdatePasswordVerifyCode`;
const UPDATE_PASSWORD = `/enterprise/account/updatePassword`;
const USER_ORGS = `/account/org/list`;
const USER_ORGS_UPDATE = `/enterprise/organizations/info`;
const UPDATE_CURRENT_ORG = `/account/changeCurrentOrg`;
const USER_ADD = `/enterprise/users`;
const ROVER_BUNDLE = `/bundle`;
const ROVER_USAGE = `/bundle/usage`;
const USER_ALL_BUNDLE = '/enterprise/bundles';
const USER_COMPANY_SEARCH = '/enterprise/organizations/searchName';
const USER_DIMENSIONLIST = '/schemas/dimensionsKV';
const USER_TENDER_DIMENSIONLIST = '/schemas/tender/dimensionsKV';

const USER_TRIAL_SEARCH_NAME = `/account/searchName`;
const USER_TRIAL_VERIFY_CODE = '/account/trial/verificationCodeV3'; // 人机验证版本
const USER_TRIAL = `/account/trial/apply`;

// B端隔离帐号接口
const USER_CHANGE_PASSWORD = '/qcc/user/buser/resetPassword'; // 修改密码
const USER_CHANGE_PASSWORD_VERIFY_CODE = '/qcc/user/buser/sendRestPasswordVerifyCodeV3'; // 人机验证版本

const USER_PROFILE = '/profile';

export const createService = (httpClient: HttpClient) => ({
  getBundle(): Promise<Readonly<IUserProfile>> {
    return httpClient.get(USER_BUNDLE);
  },

  getAllBundle(): Promise<Readonly<IUserProfile>> {
    return httpClient.get(USER_ALL_BUNDLE);
  },

  roverBundle(): Promise<Readonly<IUserProfile>> {
    return httpClient.get(ROVER_BUNDLE);
  },

  getOrg(): Promise<Readonly<IUserProfile>> {
    return httpClient.get(USER_ORG);
  },
  setOrg(): Promise<Readonly<IUserProfile>> {
    return httpClient.get(USER_SET_ORG);
  },
  setAdmin(): Promise<Readonly<IUserProfile>> {
    return httpClient.get(USER_SET_ADMIN);
  },
  // 获取部门
  getDeps() {
    return httpClient.get(USER_DEPS);
  },
  getProfileInfo() {
    return httpClient.get(USER_PROFILE_INFO);
  },
  // 新增部门
  addDep(params) {
    return httpClient.post(USER_ADD_DEP, params);
  },
  // 修改部门
  updateDep(depId, data) {
    return httpClient.put(`/enterprise/departments/${depId}`, data);
  },
  // 删除部门
  deleteDep(depId) {
    return httpClient.delete(`/enterprise/departments/${depId}`);
  },
  // 获取成员
  searchUsers(params) {
    return httpClient.get(USER_SEARCH, { params }).then((res) => {
      return {
        ...res,
        data: res.data,
        Paging: {
          PageIndex: res.pageIndex,
          PageSize: res.pageSize,
          TotalRecords: res.total,
        },
      };
    });
  },
  // 获取角色
  getRoles(params) {
    return httpClient.get(USER_ROLES, { params });
  },
  // 新增角色
  addRole(params) {
    return httpClient.post(USER_ADD_ROLE, params);
  },
  // 获取权限
  getPermissions(params?) {
    return httpClient.get(USER_PERMISSIONS, params);
  },
  // 删除角色
  deleteRole(roleId) {
    return httpClient.post(`/enterprise/role/${roleId}/deleteRole`);
  },
  // 修改组织信息
  updateOrg(data) {
    return httpClient.post(UPDATE_ORG, data);
  },
  // 修改成员昵称
  updateNickname(data) {
    return httpClient.post(UPDATE_NICKNAME, data);
  },
  // 修改邮箱
  updateEmail(data) {
    return httpClient.post(UPDATE_EMAIL, data);
  },
  // 修改登录密码：
  updateCode(data) {
    return httpClient.post(UPDATE_CODE, data);
  },
  updatePassword(data) {
    return httpClient.post(UPDATE_PASSWORD, data);
  },
  // 获取组织
  getOrgs(params?) {
    return httpClient.get(USER_ORGS, params);
  },
  updateOrganizationInfo(data) {
    return httpClient.put(USER_ORGS_UPDATE, data);
  },
  // 切换组织
  switchOrganization(currentOrgId, userId) {
    return httpClient.post(`${UPDATE_CURRENT_ORG}`, {
      currentOrg: currentOrgId,
      loginUserId: userId,
      product: 'ROVER',
    });
  },
  // 添加用户
  addUser(data) {
    return httpClient.post(USER_ADD, data);
  },
  // 更新用户
  updateUser(userId, data) {
    return httpClient.put(`enterprise/users/${userId}`, data);
  },
  // 删除用户
  deleteUser(userId) {
    return httpClient.post(`enterprise/users/${userId}/inactive`);
  },
  getUserInfo(userId) {
    return httpClient.get(`enterprise/users/${userId}`);
  },
  /**
   * 搜索企业名称
   */
  companySearch(data: any) {
    return httpClient.post(USER_COMPANY_SEARCH, data);
  },
  sendTrailSmsCode(data) {
    return httpClient.post(USER_TRIAL_VERIFY_CODE, data);
  },

  async getDimensionList() {
    return cache(() => httpClient.get(`${USER_DIMENSIONLIST}`), USER_DIMENSIONLIST);
  },

  async getTenderDimensionList() {
    return cache(() => httpClient.get(`${USER_TENDER_DIMENSIONLIST}`), USER_TENDER_DIMENSIONLIST);
  },

  getProfile(): Promise<Readonly<IUserProfile>> {
    return httpClient.request({
      url: USER_PROFILE,
    });
  },

  searchApplyOrgName(data) {
    return httpClient.get(USER_TRIAL_SEARCH_NAME, { params: data });
  },

  userTrial(data) {
    return httpClient.post(USER_TRIAL, data, {
      skipInterceptor: ['error'],
    });
  },

  roverUsage(depId?): Promise<Readonly<IUserProfile>> {
    const extra = depId ? `?depId=${depId}` : '';
    return httpClient.get(`${ROVER_USAGE}${extra}`);
  },

  // B端帐户隔离

  async sendChangePasswordSmsCode(data) {
    return httpClient.post(USER_CHANGE_PASSWORD_VERIFY_CODE, data, {
      baseURL: '/',
    });
  },

  async changePassword(data) {
    return httpClient.post(USER_CHANGE_PASSWORD, data, {
      baseURL: '/',
    });
  },

  /**
   * @description: 获取部门列表
   */
  getDepList(data?) {
    return httpClient.post(`/depBundles/dep`, data);
  },
  /**
   * @description: 获取用户列表
   */
  async getUserList(data?) {
    const res = await httpClient.post(`/account/user/list`, { all: 1, ...data });
    return res.map((user) => ({
      ...user,
      name: user.active ? user.name : `${user.name}-已离职`,
    }));
  },
});
