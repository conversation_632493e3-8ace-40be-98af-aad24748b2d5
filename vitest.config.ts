import { defineConfig, mergeConfig, coverageConfigDefaults } from 'vitest/config';
import viteConfig from './vite.config';
import { fileURLToPath } from 'node:url';

export default defineConfig((configEnv) =>
  mergeConfig(
    viteConfig(configEnv),
    defineConfig({
      define: {
        __VUE_PROD_DEVTOOLS__: false, // 禁用 DevTools 提示
        // 使用默认环境变量, 在执行测试时为 'test', ant-design-vue 会读取该环境变量来控制是否生成 aria-controls=UUID, 解决快照
        // https://github.com/vueComponent/ant-design-vue/blob/acaad271351ca0966f828fd323ecfa05cefe6537/components/vc-select/util.js#L212
        'process.env.NODE_ENV': JSON.stringify('production'),
        'process.env.MODE': JSON.stringify(configEnv.mode),
      },
      test: {
        css: {
          modules: {
            classNameStrategy: 'non-scoped',
          },
        },
        exclude: ['**/node_modules/**'],
        reporters: [
          'default',
          [
            'html',
            {
              outputFile: './test-report/index.html',
            },
          ],
          [
            'vitest-sonar-reporter',
            {
              outputFile: './test-report/coverage/sonar-reporter.xml',
            },
          ],
        ],
        coverage: {
          all: true,
          enabled: false, // or '--coverage.enabled'
          clean: false, // or '--coverage.clean'
          reporter: ['text', 'lcov', 'html'],
          reportOnFailure: true, // 测试失败也会生成覆盖率报告
          reportsDirectory: './test-report/coverage',
          provider: 'v8',
          include: ['src/**/*.{ts,tsx,vue,js,jsx}'],
          exclude: [
            ...coverageConfigDefaults.exclude,
            '**/node_modules/**',
            // 忽略老旧代码(.vue, .js)
            // 'src/components/modal/dimension/**',
            'src/components/charts/**',
            // 忽略路由配置
            'src/router/**',
            'src/apps/**/routes/**',
            // 忽略非业务逻辑代码
            'src/shared/services/**',
            'src/shared/locales/**',
            'src/store/**',
            'src/test-utils/**',
          ],
        },
        setupFiles: [fileURLToPath(new URL('./tests/unit/vitest.setup.ts', import.meta.url))],
        environment: 'happy-dom',
        globals: true,
      },
    })
  )
);
