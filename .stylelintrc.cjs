/**
 * @type {import('stylelint').Configuration}
 */
module.exports = {
  extends: ['@qcc-ui/stylelint-config'],
  rules: {
    'no-descending-specificity': null, // 不强制顺序
    'selector-class-pattern': null, // 大量遗留样式不符合规则
    'selector-id-pattern': null, // id 命名规则不符合 kebab-case
    'no-duplicate-selectors': null, // 重复选项器
    'declaration-block-no-redundant-longhand-properties': [
      true,
      {
        ignoreShorthands: ['inset'], // grid 相关属性不强制使用简写属性
      },
    ], // 不强制使用简写属性
  },
};
