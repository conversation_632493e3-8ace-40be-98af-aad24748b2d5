/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

/** @type { import("eslint").Linter.LegacyConfig } */
module.exports = {
  root: true,
  extends: [
    'plugin:vue/essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript/recommended',
    '@vue/eslint-config-prettier',
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  env: {
    browser: true,
    jquery: true,
    node: true,
  },
  rules: {
    '@typescript-eslint/no-this-alias': 'off',
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-explicit-any': 'off',
    // Vue
    'vue/multi-word-component-names': 'off',
    'vue/no-v-text-v-html-on-component': 'off',
    'vue/require-v-for-key': 'warn',
    'vue/no-side-effects-in-computed-properties': 'warn',
    // JS
    'no-empty': 'warn',
  },
};
